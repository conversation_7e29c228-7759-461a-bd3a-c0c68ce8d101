---
# =========================================================================
# Jira Service Request Update Handler
# =========================================================================
# This handler updates the Jira Service Request ticket with the AAP job link
# It is triggered after the DNS operations are completed
#
# Author: CES Operational Excellence Team
# Contributor: <PERSON> (7409)
# =========================================================================

- name: Include Jira vars
  include_vars: uat_vars.yml
  no_log: false
  run_once: true

- name: Update grid row in a SR ticket
  uri:
    url: "{{ grid_url }}/{{ var_grid_id }}/issue/{{ var_sr_number }}/"
    headers:
      Authorization: "{{ jira_grid }}"
    validate_certs: no
    method: PUT
    status_code: 204
    body_format: json
    body: |
      {
        "rows":[
          {
            "rowId":"{{ var_row_id }}",
            "columns":{
              "remark": "{{ aap_url }}/#/jobs/playbook/{{ tower_job_id }}/"
            }
          }
        ]
      }
  ignore_errors: true
  delegate_to: localhost
  run_once: true