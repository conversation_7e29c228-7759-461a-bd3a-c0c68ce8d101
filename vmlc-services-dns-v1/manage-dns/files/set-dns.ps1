# =========================================================================
# DNS Management PowerShell Script
# =========================================================================
# This script handles DNS record operations (verify, add, remove) for VM lifecycle
# It manages both A records and corresponding PTR records
# Special handling is implemented for SingHealth domains
#
# Author: CES Operational Excellence Team
# Contributor: <PERSON> (7409)
# =========================================================================

# Script parameters
param(
    # Action to perform: verify, add, or remove
    [Parameter(Mandatory = $false)]
    [string]$Action = "verify",

    # Domain for DNS operations (e.g., healthgrp.com.sg)
    [Parameter(Mandatory = $true)]
    [string]$Domain,

    # Hostname for DNS record (without domain)
    [Parameter(Mandatory = $true)]
    [string]$HostName,

    # IP address for DNS record (required for 'add' action)
    [Parameter(Mandatory = $false)]
    [string]$IpAddress,

    # Time-to-live value in seconds
    [Parameter(Mandatory = $false)]
    [int]$TTL = 3600

)

# Function to format the DNS operation results as a JSON object
# This standardizes the output format for Ansible to parse
function Format-DnsReport {
    param(
        [string]$Action,        # Operation performed (verify, add, remove)
        [string]$RecordStatus,  # Status of the A record operation
        [string]$Domain,        # Domain for the DNS record
        [string]$DnsServer,     # DNS server used for A record operations
        [string]$PtrDnsServer = "", # DNS server used for PTR record operations
        [string]$HostName,      # Hostname for the DNS record
        [string]$IpAddress,     # IP address for the DNS record
        [string]$TTL = "",      # Time-to-live value
        [string]$PTR = "",      # PTR record status or value
        [string]$ErrorMessage = "" # Any error messages encountered
    )

    # Create a hashtable with all the relevant information
    $output = @{
        "action"        = $Action
        "status"        = $RecordStatus
        "domain"        = $Domain
        "dns_server"    = $DnsServer
        "ptr_dns_server" = $PtrDnsServer
        "hostname"      = $HostName
        "ip_address"    = $IpAddress
        "ttl"           = $TTL
        "ptr_record"    = $PTR
        "errors"        = $ErrorMessage
    }

    # Convert the hashtable to a JSON string and return it
    $output | ConvertTo-Json -Compress
}

# Function to determine the appropriate DNS server for a given domain
# This maps each domain to its corresponding DNS server
function Get-DnsServer {
    param(
        [string]$Domain  # Domain for which to find the DNS server
    )

    # Use a switch statement to map domains to their DNS servers
    switch ($Domain.ToLower()) {
        # Development/Staging environments
        "devhealthgrp.com.sg" { return "hisaddcvdutl01.devhealthgrp.com.sg" }  # Dev HealthGrp domain
        "healthgrpexts.com.sg" { return "hisaddcvsutl03.healthgrpexts.com.sg" }  # HealthGrp Exts domain
        "nnstg.local" { return "nhgaddcvssys01.nnstg.local" }  # NNSTG local domain
        "ses.shsu.com.sg" { return "sedcvssys22h1.ses.shsu.com.sg" }  # SingHealth SES staging domain

        # Production environments
        "shses.shs.com.sg" { return "sesdcvpsys01.shses.shs.com.sg" }  # SingHealth SES production domain
        "nhg.local" { return "nhgaddcvpsys03.nhg.local" }  # NHG local domain
        "aic.local" { return "aicaddcvpsys06.aic.local" }  # AIC local domain
        "iltc.healthgrp.com.sg" { return "aicaddcvpsys02.iltc.healthgrp.com.sg" }  # ILTC HealthGrp domain
        "healthgrp.com.sg" { return "hisaddcvputl07.healthgrp.com.sg" }  # HealthGrp domain
        "hcloud.healthgrp.com.sg" { return "hisaddcvputl02.hcloud.healthgrp.com.sg" }  # HCloud HealthGrp domain
        "healthgrpextp.com.sg" { return "hisaddcvputl13.healthgrpextp.com.sg" }  # HealthGrp ExtP domain

        # Throw an error for unknown domains
        default { throw "Unknown domain: $Domain" }
    }
}

# Function to determine the appropriate PTR DNS server for a given domain
# Special handling for SingHealth domains which use different servers for PTR records
function Get-PtrDnsServer {
    param(
        [string]$Domain,     # Domain for which to find the PTR DNS server
        [string]$DnsServer   # The A record DNS server (used as default for most domains)
    )

    # Use a switch statement to map domains to their PTR DNS servers
    switch ($Domain.ToLower()) {
        # Special handling for SingHealth domains
        "ses.shsu.com.sg" { return "shdcvsys22h1.shsu.com.sg" }  # SingHealth SES staging PTR server
        "shses.shs.com.sg" { return "sesdcvpsys11.shs.com.sg" }  # SingHealth SES production PTR server

        # For all other domains, use the same server as for A records
        default { return $DnsServer }
    }
}

# Function to retrieve the PTR record for a given IP address
# This queries the DNS server for the reverse lookup record
function Get-PtrRecord {
    param(
        [string]$IpAddress,    # IP address to look up
        [string]$PtrDnsServer  # DNS server to query for PTR records
    )

    try {
        # Split the IP address into its components
        $ipParts = $IpAddress.Split('.')

        # Construct the reverse lookup zone name (in-addr.arpa format)
        $reverseZone = "$($ipParts[2]).$($ipParts[1]).$($ipParts[0]).in-addr.arpa"

        # The PTR record name is the last octet of the IP address
        $ptrRecordName = "$($ipParts[3])"

        # Query the DNS server for the PTR record
        $ptrRecord = Get-DnsServerResourceRecord -ZoneName $reverseZone -ComputerName $PtrDnsServer -Name $ptrRecordName -RRType "PTR" -ErrorAction Stop

        # Output the retrieved record for debugging
        Write-Host "PTR Record Retrieved:" -ForegroundColor Green
        Write-Host ($ptrRecord | Format-List * | Out-String)

        # Process the retrieved record
        if ($ptrRecord) {
            if ($ptrRecord.RecordData -is [System.Array]) {
                # If the record data is an array, get the first PTR domain name
                $ptrDomainName = $ptrRecord.RecordData[0].PtrDomainName
                return $ptrDomainName
            } else {
                # If the record exists but doesn't have the expected format
                return "PTR Record Not Found"
            }
        } else {
            # If no record was returned
            return "PTR Record Not Found"
        }
    } catch {
        # If an error occurred during the query
        return @{
            "status" = "PTR Record Does Not Exist",
            "error_message" = "Failed to retrieve PTR record: $_"
        }
    }
}

# Function to verify if a DNS record exists
# This checks both the A record and its corresponding PTR record
function Verify-DnsRecord {
    param(
        [string]$Domain,    # Domain for the DNS record
        [string]$HostName,  # Hostname to verify
        [string]$DnsServer  # DNS server to query
    )

    # Initialize variables
    $error_message = ""
    $recordStatus = ""
    $ptrStatus = ""
    $ttl = ""
    $ptrRecordString = ""
    $IpAddress = $null

    # Check if the A record exists
    try {
        # Query the DNS server for the A record
        $aRecord = Get-DnsServerResourceRecord -ZoneName $Domain -ComputerName $DnsServer -Name $HostName -RRType "A" -ErrorAction Stop -Verbose

        if ($aRecord) {
            # If the record exists, get its details
            $recordStatus = "Record Exists"
            $IpAddress = $aRecord.RecordData.IPv4Address.ToString()
            $ttl = $aRecord.TimeToLive.TotalSeconds
        } else {
            # If no record was found
            $recordStatus = "Record Not Found"
        }
    } catch {
        # If an error occurred during the query
        $error_message = "$_"
        $recordStatus = "Record Not Found"
    }

    # Check if the PTR record exists
    $ptrRecord = ""
    try {
        # Get the appropriate PTR DNS server
        $ptrDnsServer = Get-PtrDnsServer -Domain $Domain -DnsServer $DnsServer

        if ($IpAddress) {
            # If we have an IP address from the A record, look up its PTR record
            $ptrRecord = Get-PtrRecord -IpAddress $IpAddress -PtrDnsServer $ptrDnsServer -Verbose
        }

        # Process the PTR record result
        if ($ptrRecord -is [hashtable]) {
            # If the result is a hashtable, it contains an error message
            $ptrRecordString = $ptrRecord["error_message"]
        } elseif ($ptrRecord) {
            # If the result is a string, it's the PTR domain name
            $ptrRecordString = $ptrRecord
        } else {
            # If no result was returned
            $ptrRecordString = "PTR Record Not Found"
        }
    } catch {
        # If an error occurred during the PTR query
        $error_message += "`nPTR Error: $_"
        $ptrRecordString = "PTR Record Not Found"
    }

    # Format and return the results
    Format-DnsReport -Action "Verify" -RecordStatus $recordStatus -Domain $Domain -DnsServer $DnsServer -PtrDnsServer $ptrDnsServer -HostName $HostName -IpAddress $IpAddress -TTL $ttl -PTR $ptrRecordString -ErrorMessage $error_message
}

# Function to add a DNS A record and its corresponding PTR record
# This creates both records if they don't exist
function Add-DnsRecord {
    param(
        [string]$Domain,     # Domain for the DNS record
        [string]$HostName,   # Hostname to add
        [string]$IpAddress,  # IP address for the record
        [string]$DnsServer,  # DNS server to use for A record
        [int]$TTL = 3600,    # Time-to-live for A record
        [int]$PtrTTL = 3600  # Time-to-live for PTR record
    )

    # Initialize variables
    $error_message = ""
    $recordStatus = ""
    $ptrStatus = ""
    $ptrRecordString = ""

    # Check if the A record already exists
    $aRecordExists = Get-DnsServerResourceRecord -ZoneName $Domain -ComputerName $DnsServer -Name $HostName -RRType "A" -ErrorAction SilentlyContinue

    if ($aRecordExists -and $aRecordExists.RecordData.IPv4Address.ToString() -eq $IpAddress) {
        # If the record exists with the same IP address
        $recordStatus = "A Record Already Exists"
    } else {
        try {
            # Add the A record
            Add-DnsServerResourceRecordA -ZoneName $Domain -ComputerName $DnsServer -Name $HostName -IPv4Address $IpAddress -TimeToLive ([TimeSpan]::FromSeconds($TTL)) -ErrorAction Stop
            $recordStatus = "A Record Added"
        } catch {
            # If an error occurred while adding the A record
            $error_message = "A Record Error: $_"
            $recordStatus = "Failed to Add A Record"
        }
    }

    # Get the appropriate PTR DNS server
    $ptrDnsServer = Get-PtrDnsServer -Domain $Domain -DnsServer $DnsServer

    # Check if the PTR record already exists
    $ptrRecord = Get-PtrRecord -IpAddress $IpAddress -PtrDnsServer $ptrDnsServer

    if (-not $ptrRecord -or $ptrRecord -eq "PTR Record Not Found") {
        try {
            # Add the PTR record
            # Construct the reverse lookup zone name and add the record
            Add-DnsServerResourceRecordPtr -Name $IpAddress.Split('.')[3] -ZoneName "$($IpAddress.Split('.')[2]).$($IpAddress.Split('.')[1]).$($IpAddress.Split('.')[0]).in-addr.arpa" -PtrDomainName "$HostName.$Domain" -ComputerName $ptrDnsServer -TimeToLive ([TimeSpan]::FromSeconds($PtrTTL)) -ErrorAction Stop

            # Verify the PTR record was added
            $ptrRecord = Get-PtrRecord -IpAddress $IpAddress -PtrDnsServer $ptrDnsServer
            $ptrStatus = "PTR Record Added"
        } catch {
            # If an error occurred while adding the PTR record
            $ptrStatus = "Failed to Add PTR Record"
            $error_message += "`nPTR Error: $_"
        }
    } else {
        # If the PTR record already exists
        $ptrStatus = "PTR Record Already Exists"
    }

    # Process the PTR record result
    if ($ptrRecord -is [hashtable]) {
        # If the result is a hashtable, it contains an error message
        $ptrRecordString = $ptrRecord["error_message"]
    } elseif ($ptrRecord) {
        # If the result is a string, it's the PTR domain name
        $ptrRecordString = $ptrRecord
    } else {
        # If no result was returned
        $ptrRecordString = "PTR Record Not Found"
    }

    # Format and return the results
    Format-DnsReport -Action "Add" -RecordStatus $recordStatus -Domain $Domain -DnsServer $DnsServer -PtrDnsServer $ptrDnsServer -HostName $HostName -IpAddress $IpAddress -TTL $TTL -PTR $ptrRecordString -ErrorMessage $error_message
}

# Function to remove a DNS A record
# This removes the A record but does not automatically remove the PTR record
function Remove-DnsARecord {
    param(
        [string]$Domain,    # Domain for the DNS record
        [string]$HostName,  # Hostname to remove
        [string]$DnsServer  # DNS server to use
    )

    # Initialize variables
    $error_message = ""
    $recordStatus = ""
    $IpAddress = $null

    # Check if the A record exists
    $aRecordExists = Get-DnsServerResourceRecord -ZoneName $Domain -ComputerName $DnsServer -Name $HostName -RRType "A" -ErrorAction SilentlyContinue

    if ($aRecordExists) {
        try {
            # Get the IP address before removing the record
            $IpAddress = $aRecordExists.RecordData.IPv4Address.ToString()

            # Remove the A record
            Remove-DnsServerResourceRecord -ZoneName $Domain -ComputerName $DnsServer -Name $HostName -RRType "A" -RecordData $IpAddress -Force -Verbose
            $recordStatus = "A Record Removed"
        } catch {
            # If an error occurred while removing the record
            $error_message = "Error Removing A Record: $_"
            $recordStatus = "Failed to Remove A Record"
        }
    } else {
        # If the record doesn't exist
        $recordStatus = "A Record Not Found"
    }

    # Format and return the results
    # Note: PTR record is not automatically removed for safety
    Format-DnsReport -Action "Remove" -RecordStatus $recordStatus -Domain $Domain -DnsServer $DnsServer -HostName $HostName -IpAddress $IpAddress -ErrorMessage $error_message
}

# Main script execution
# Get the appropriate DNS server for the specified domain
$DnsServer = Get-DnsServer -Domain $Domain

# Execute the appropriate function based on the action parameter
switch ($Action.ToLower()) {
    "verify" {
        # Verify if the DNS record exists
        Verify-DnsRecord -Domain $Domain -HostName $HostName -DnsServer $DnsServer
    }
    "add" {
        # Add the DNS record
        Add-DnsRecord -Domain $Domain -HostName $HostName -IpAddress $IpAddress -DnsServer $DnsServer -TTL $TTL
    }
    "remove" {
        # Remove the DNS record
        Remove-DnsARecord -Domain $Domain -HostName $HostName -DnsServer $DnsServer
    }
    default {
        # Handle invalid action
        Write-Host "Invalid action specified. Please use 'verify', 'add', or 'remove'." -ForegroundColor Red
    }
}