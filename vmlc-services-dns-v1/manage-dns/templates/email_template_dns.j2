{#
   Email Template for DNS Management Notifications
   This template is used to send email notifications about DNS operations
   It includes details about the A record, PTR record, and any errors

   Author: CES Operational Excellence Team
   Contributor: <PERSON> (7409)
#}

Dear {{ team_name }},<br><br>

Please be informed that the following VM Lifecycle Non-Windows DNS A record is triggered to <b><u>{{ dns_records.action }}</u></b> via <b>{{ var_ticket }}</b>.<br><br>

<b><u>DNS A Record Status</u></b><br>
- <b>A Record Status:</b> {{ dns_records.status }}<br>
- <b>Domain:</b> {{ dns_records.domain }}<br>
- <b>DNS Server:</b> {{ dns_records.dns_server }}<br>
- <b>Hostname:</b> {{ dns_records.hostname }}<br>
- <b>IP Address:</b> {{ dns_records.ip_address if dns_records.ip_address else 'Nil' }}<br>
- <b>TTL:</b> {{ dns_records.ttl if dns_records.ttl else 'Nil' }}<br><br>

<b><u>DNS PTR Record Status</u></b><br>
- <b>PTR DNS Server:</b> {{ dns_records.ptr_dns_server if dns_records.ptr_dns_server else 'Nil' }}<br>
- <b>PTR Record:</b> {{ dns_records.ptr_record if dns_records.ptr_record else 'Nil' }}<br><br>

<b><u>DNS Processing Errors</u></b><br>
- <b>Errors:</b> {{ dns_records.errors if dns_records.errors else 'No Processing Errors' }}<br><br>

<b><u>Raw Output</u></b><br>
<pre>{{ raw_text | e }}</pre><br><br><br>

To view the job logs of this activity, please visit: <a href="{{ aap_job_link }}">{{ aap_job_link }}</a><br><br><br><br>

Regards,<br>
CES OPERATIONAL EXCELLENCE TEAM<br><br>

[ ] Unclassified, Non-Sensitive<br>
[ ] Restricted, Non-Sensitive<br>
[x] Restricted, Sensitive (Normal)<br>
[ ] Restricted, Sensitive (High)<br><br><br><br>

<i>**This is an auto-generated email, please do not reply.</i>

