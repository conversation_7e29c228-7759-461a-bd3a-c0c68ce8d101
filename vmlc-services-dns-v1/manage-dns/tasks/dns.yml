---
# =========================================================================
# DNS Management Tasks
# =========================================================================
# This file contains all the tasks for DNS record management operations
# It handles the following operations:
# - Server selection based on domain
# - Credential selection based on domain
# - PowerShell script deployment and execution
# - Email notification
# - Cleanup
#
# Author: CES Operational Excellence Team
# Contributor: <PERSON> (7409)
# =========================================================================

# Define mapping of domains to their respective ADMT servers
# This allows dynamic server selection based on the target domain
- name: Defining ADMT servers mapping
  set_fact:
    admt_servers_map:
      devhealthgrp.com.sg: ["HISADMTVDSEC01.devhealthgrp.com.sg"]      # Dev HealthGrp domain
      healthgrpexts.com.sg: ["HISADMTVSSEC01.healthgrpexts.com.sg"]    # HealthGrp Exts domain
      nnstg.local: ["HISADMTVSSEC02.nnstg.local"]                      # NNSTG local domain
      ses.shsu.com.sg: ["SHSADMTVDSEC02.ses.shsu.com.sg"]              # SingHealth SES staging domain
      shses.shs.com.sg: ["SHSADMTVPSEC12.shses.shs.com.sg"]            # SingHealth SES production domain
      nhg.local: ["HISADMTVPSEC11.nhg.local"]                          # NHG local domain
      aic.local: ["HISADMTVPSEC02.aic.local"]                          # AIC local domain
      iltc.healthgrp.com.sg: ["HISADMTVPSEC04.iltc.healthgrp.com.sg"]  # ILTC HealthGrp domain
      healthgrp.com.sg: ["HISADMTVPSEC05.healthgrp.com.sg"]            # HealthGrp domain
      hcloud.healthgrp.com.sg: ["HISADMTVPSEC06.hcloud.healthgrp.com.sg"] # HCloud HealthGrp domain
      healthgrpextp.com.sg: ["HISADMTVPSEC08.healthgrpextp.com.sg"]    # HealthGrp ExtP domain
  run_once: true  # Execute this task only once regardless of the number of hosts

# Select the appropriate ADMT servers based on the domain provided
# This uses the mapping defined above to determine which servers to use
- name: Selecting ADMT servers based on defined domain using dictionary lookup
  set_fact:
    admt_servers: "{{ admt_servers_map[domain] | default([]) }}"  # Lookup servers from the map, default to empty list if domain not found
  when: domain is defined  # Only execute if domain variable is provided
  run_once: true  # Execute this task only once

# Select the appropriate credentials based on the domain
# This ensures the correct service account is used for each domain
- name: Selecting Credentials based on defined domain
  set_fact:
    ansible_user: "{{ var_dns_devhealthgrp_username if domain == 'devhealthgrp.com.sg' else
      var_dns_healthgrpexts_username if domain == 'healthgrpexts.com.sg' else
      var_dns_nnstg_username if domain == 'nnstg.local' else
      var_dns_ses_username if domain == 'ses.shsu.com.sg' else
      var_dns_shses_username if domain == 'shses.shs.com.sg' else
      var_dns_nhg_username if domain == 'nhg.local' else
      var_dns_aic_username if domain == 'aic.local' else
      var_dns_iltc_username if domain == 'iltc.healthgrp.com.sg' else
      var_dns_healthgrp_username if domain == 'healthgrp.com.sg' else
      var_dns_hcloud_username if domain == 'hcloud.healthgrp.com.sg' else
      var_dns_healthgrpextp_username if domain == 'healthgrpextp.com.sg' }}"  # Select username based on domain
    ansible_password: "{{ var_dns_devhealthgrp_password if domain == 'devhealthgrp.com.sg' else
      var_dns_healthgrpexts_password if domain == 'healthgrpexts.com.sg' else
      var_dns_nnstg_password if domain == 'nnstg.local' else
      var_dns_ses_password if domain == 'ses.shsu.com.sg' else
      var_dns_shses_password if domain == 'shses.shs.com.sg' else
      var_dns_nhg_password if domain == 'nhg.local' else
      var_dns_aic_password if domain == 'aic.local' else
      var_dns_iltc_password if domain == 'iltc.healthgrp.com.sg' else
      var_dns_healthgrp_password if domain == 'healthgrp.com.sg' else
      var_dns_hcloud_password if domain == 'hcloud.healthgrp.com.sg' else
      var_dns_healthgrpextp_password if domain == 'healthgrpextp.com.sg' }}"  # Select password based on domain
  no_log: true  # Hide sensitive information from logs
  run_once: true  # Execute this task only once

# Ensure the scripts directory exists on the ADMT servers
# This creates the directory if it doesn't exist
- name: Checking if C:\ansscripts directory exists on {{ admt_servers }}
  ansible.windows.win_file:
    path: C:\ansscripts  # Directory path on Windows servers
    state: directory     # Ensure directory exists
  delegate_to: "{{ item }}"  # Run this task on each ADMT server
  loop: "{{ admt_servers }}"  # Loop through all selected ADMT servers
  run_once: true  # Execute this task only once

# Copy the PowerShell script to the ADMT servers
# This script will be used to perform DNS operations
- name: Preparing the plugins on {{ admt_servers }}
  ansible.builtin.copy:
    src: set-dns.ps1  # Source PowerShell script
    dest: C:\ansscripts\set-dns.ps1  # Destination path on Windows servers
  delegate_to: "{{ item }}"  # Run this task on each ADMT server
  loop: "{{ admt_servers }}"  # Loop through all selected ADMT servers
  run_once: true  # Execute this task only once

# Execute the PowerShell script on the ADMT servers
# This script performs the actual DNS operations (verify, add, remove)
- name: Starting plugin block {{ action }} on {{ admt_servers }}
  ansible.windows.win_shell: |
    PowerShell.exe -ExecutionPolicy bypass `
    -File "C:\ansscripts\set-dns.ps1" `
    -Action {{ action | default('verify') }} `  # Default action is 'verify' if not specified
    -Domain {{ domain }} `  # Domain for DNS operations
    -HostName {{ hostname }} `  # Hostname for DNS record
    {% if ipaddress is defined and ipaddress|length > 0 %} -IpAddress {{ ipaddress }}{% endif %} `  # IP address (only for 'add' action)
    -TTL {{ ttl | default('3600') }}  # TTL value with default of 3600 seconds
  args:
    chdir: C:\ansscripts\  # Working directory for the script
  become: true  # Use privilege escalation
  become_method: runas  # Use runas method for Windows
  become_flags: logon_type=new_credentials logon_flags=netcredentials_only  # Flags for Windows authentication
  delegate_to: "{{ item }}"  # Run this task on each ADMT server
  loop: "{{ admt_servers }}"  # Loop through all selected ADMT servers
  run_once: true  # Execute this task only once
  vars:
    ansible_become_user: "{{ ansible_user }}"  # Use the selected username for authentication
    ansible_become_password: "{{ ansible_password }}"  # Use the selected password for authentication
  register: dns_script_output  # Store the script output for later use
  ignore_errors: yes  # Continue execution even if the script fails

# Display the script output for debugging purposes
- name: Debug Script Output
  debug:
    var: dns_script_output  # Display the entire script output
  run_once: true  # Execute this task only once

# Extract the raw text and JSON parts from the script output
# The PowerShell script returns both text output and a JSON object
- name: Extract raw text and JSON separately
  set_fact:
    raw_text: "{{ dns_script_output.results[0].stdout | regex_replace('({.*})', '') | trim }}"  # Extract everything except the JSON part
    json_part: "{{ dns_script_output.results[0].stdout | regex_search('({.*})') }}"  # Extract only the JSON part
  run_once: true  # Execute this task only once

# Parse the JSON output for use in the email template
- name: Parse the JSON output
  set_fact:
    dns_records: "{{ json_part }}"  # Store the JSON part for later use
  run_once: true  # Execute this task only once

# Send email notification to the relevant teams
# This informs the teams about the DNS operation that was performed
- name: Triggering notifications to relevant teams
  mail:
    host: asmtp.hcloud.healthgrp.com.sg  # SMTP server for email
    port: 25  # SMTP port
    from: "<EMAIL>"  # Sender email address
    to: "<EMAIL>"  # Recipient email address (currently set to a single user for testing)
#    to: >-  # Uncomment to use dynamic recipient selection based on domain
#      {{
#        '<EMAIL>' if domain in ['ses.shsu.com.sg', 'shses.shs.com.sg'] else
#        '<EMAIL>'
#      }}
    bcc: '<EMAIL>'  # BCC recipient
    subject: " {{ var_ticket }} - VM Lifecycle Non-Windows DNS A Record in {{ domain }} [{{ action }}]"  # Email subject
    body: "{{ email_template }}"  # Email body from template
    subtype: html  # Email format
  delegate_to: localhost  # Run this task on the Ansible controller
  when: dns_script_output is defined  # Only send email if script output exists
  run_once: true  # Execute this task only once

# Clean up the PowerShell script from the ADMT servers
# This removes the script after execution for security
- name: Cleaning up the PowerShell script from {{ admt_servers }}
  ansible.windows.win_file:
    path: C:\ansscripts\set-dns.ps1  # Path to the script
    state: absent  # Remove the file
  delegate_to: "{{ item }}"  # Run this task on each ADMT server
  loop: "{{ admt_servers }}"  # Loop through all selected ADMT servers
  run_once: true  # Execute this task only once

# Remove the scripts directory if it's empty
# This ensures no leftover files remain on the servers
- name: Removing C:\ansscripts directory from {{ admt_servers }}
  ansible.windows.win_shell: |
    if (!(Get-ChildItem -Path C:\ansscripts\)) {  # Check if directory is empty
        Remove-Item -Path C:\ansscripts\ -Force  # Remove directory if empty
    }
  delegate_to: "{{ item }}"  # Run this task on each ADMT server
  loop: "{{ admt_servers }}"  # Loop through all selected ADMT servers
  run_once: true  # Execute this task only once