# DNS Management Project (vmlc-services-dns-v1)

## 1. Overview

This project provides DNS management capabilities for VM lifecycle operations across multiple domains. It allows for the verification, addition, and removal of DNS A records and their corresponding PTR records.

**Author:** CES Operational Excellence Team
**Contributor:** <PERSON> (7409)

## 2. Features

- **DNS A Record Management**: Verify, add, and remove A records
- **Automatic PTR Record Management**: Automatically manage corresponding PTR records
- **Multi-Domain Support**: Support for multiple domains with domain-specific DNS servers
- **Special PTR Handling**: Special handling for SingHealth domains (ses.shsu.com.sg and shses.shs.com.sg)
- **Email Notifications**: Automated email notifications to relevant teams
- **Jira Integration**: Update Jira tickets with job details
- **CyberArk Integration**: Retrieve credentials from CyberArk PAM Safe

## 3. Supported Domains

### 3.1 Development/Staging Environments

- devhealthgrp.com.sg
- healthgrpexts.com.sg
- nnstg.local
- ses.shsu.com.sg

### 3.2 Production Environments

- shses.shs.com.sg
- nhg.local
- aic.local
- iltc.healthgrp.com.sg
- healthgrp.com.sg
- hcloud.healthgrp.com.sg
- healthgrpextp.com.sg
- exthealthgrp.com.sg

## 4. Project Structure

```
vmlc-services-dns-v1/
├── main.yml                                # Main playbook entry point
├── docs/                                   # Documentation folder
│   ├── USAGE.md                            # Detailed usage instructions
│   ├── STRUCTURE.md                        # Detailed project structure explanation
│   ├── TROUBLESHOOTING.md                  # Troubleshooting guide
│   └── POWERSHELL_TESTING.md               # Guide for direct PowerShell script testing
├── manage-dns/
│   ├── files/
│   │   └── set-dns.ps1                     # PowerShell script for DNS operations
│   ├── handlers/
│   │   └── update-sr-uat.yml               # Handler for updating Jira tickets
│   ├── tasks/
│   │   ├── main.yml                        # Main tasks entry point
│   │   └── dns.yml                         # DNS management tasks
│   ├── templates/
│   │   └── email_template_dns.j2           # Email notification template
│   └── vars/
│       ├── dns_vars.yml                    # DNS-specific variables
│       ├── prod_vars.yml                   # Production environment variables
│       └── uat_vars.yml                    # UAT environment variables
```

For a detailed explanation of the project structure, see [docs/STRUCTURE.md](docs/STRUCTURE.md).

## 5. Prerequisites

- Ansible Automation Platform (AAP)
- Access to DNS servers via ADMT servers
- Service accounts with appropriate permissions
- CyberArk PAM Safe for credential management

## 6. Usage

The project is designed to be executed from Ansible Automation Platform (AAP) using Job Templates. It cannot be run directly from the command line as users and developers do not have access to run ansible-playbook commands directly on servers.

For detailed usage instructions, see [docs/USAGE.md](docs/USAGE.md).

### 6.1 Direct PowerShell Script Testing

For DNS engineers who need to test or use the PowerShell script directly on ADMT servers, a comprehensive guide is available at [docs/POWERSHELL_TESTING.md](docs/POWERSHELL_TESTING.md). This guide includes:

- Step-by-step instructions for running the script
- Examples for different operations (verify, add, remove)
- Testing scenarios and best practices
- Troubleshooting tips

## 7. Operations

### 7.1 Verify DNS Record

Checks if a DNS A record and its corresponding PTR record exist.

### 7.2 Add DNS Record

Adds a DNS A record and its corresponding PTR record if they don't exist.

### 7.3 Remove DNS Record

Removes a DNS A record. The PTR record is not automatically removed for safety.

## 8. Special Handling for SingHealth Domains

For SingHealth domains, special PTR handling is implemented:

- **ses.shsu.com.sg**: A records are managed on sedcvssys22h1.ses.shsu.com.sg, but PTR records are managed on shdcvsys22h1.shsu.com.sg
- **shses.shs.com.sg**: A records are managed on sesdcvpsys01.shses.shs.com.sg, but PTR records are managed on sesdcvpsys11.shs.com.sg

## 9. Security Considerations

- All credentials are encrypted using Ansible Vault
- PowerShell scripts are cleaned up after execution
- Sensitive information is masked in logs using no_log
- CyberArk integration for secure credential management

## 10. Idempotent Operations

All DNS operations in this project are designed to be idempotent, ensuring safe and predictable behavior regardless of how many times they are executed:

### 10.1 Verification Operations

- Verification operations are inherently idempotent as they only read data and do not modify any records
- Multiple verification operations on the same record will always produce the same result
- Verification checks both A records and their corresponding PTR records

### 10.2 Addition Operations

- Before adding a DNS record, the system checks if the record already exists
- If the record exists with the same IP address, no changes are made
- If the record exists with a different IP address, it will be updated to the new IP address
- PTR records are only added if they don't already exist
- This ensures that running the same addition operation multiple times will not create duplicate records

### 10.3 Removal Operations

- Before removing a DNS record, the system checks if the record exists
- If the record doesn't exist, the operation completes successfully without making any changes
- PTR records are not automatically removed for safety reasons
- This ensures that running the same removal operation multiple times will not cause any issues

### 10.4 Benefits of Idempotency

- **Safety**: Operations can be retried without risk of creating inconsistent or duplicate records
- **Reliability**: Failed operations can be safely retried without manual cleanup
- **Predictability**: The same operation will always produce the same result
- **Auditability**: Each operation generates a detailed report of what was changed (if anything)

## 11. Troubleshooting

For troubleshooting information, see [docs/TROUBLESHOOTING.md](docs/TROUBLESHOOTING.md).

## 12. License

This project is proprietary and confidential. Unauthorized copying, distribution, or use is strictly prohibited.
