---
# =========================================================================
# Main playbook for DNS Management
# =========================================================================
# This playbook orchestrates the DNS management operations for VM lifecycle
# It handles A record and PTR record operations across multiple domains
# Operations supported: verify, add, remove
#
# Author: CES Operational Excellence Team
# Contributor: <PERSON><PERSON><PERSON> (7409)
# =========================================================================

- name: DNS Management
  hosts: all                # Target all hosts in inventory as operations are performed on custom filtered hosts
  gather_facts: false       # Skip fact gathering for performance

  vars_files:
    - manage-dns/vars/dns_vars.yml  # Include DNS-specific variables

  handlers:
    - name: Update Jira Ticket
      include_tasks: manage-dns/handlers/update-sr-uat.yml  # Handler to update Jira ticket with job details

  pre_tasks:
    - name: Retrieve Password from PAM Safe
      ansible.builtin.include_role:
        name: cloud_cpe.cyberark_ccp.retrieve_from_cyberark  # Get credentials from CyberArk

  tasks:
    - name: Include main tasks
      include_tasks: manage-dns/tasks/main.yml  # Include the main DNS management tasks