# DNS Management Project Structure

## 1. Introduction

This document provides a detailed explanation of the project structure for the DNS Management project (vmlc-services-dns-v1).

**Author:** CES Operational Excellence Team
**Contributor:** <PERSON> (7409)

## 2. Project Structure Overview

```
vmlc-services-dns-v1/
├── main.yml                                # Main playbook entry point
├── README.md                               # Project overview and quick reference
├── docs/                                   # Documentation folder
│   ├── USAGE.md                            # Detailed usage instructions
│   ├── STRUCTURE.md                        # Detailed project structure explanation
│   ├── TROUBLESHOOTING.md                  # Troubleshooting guide
│   └── POWERSHELL_TESTING.md               # Guide for direct PowerShell script testing
├── manage-dns/
│   ├── files/
│   │   └── set-dns.ps1                     # PowerShell script for DNS operations
│   ├── handlers/
│   │   └── update-sr-uat.yml               # Handler for updating Jira tickets
│   ├── tasks/
│   │   ├── main.yml                        # Main tasks entry point
│   │   └── dns.yml                         # DNS management tasks
│   ├── templates/
│   │   └── email_template_dns.j2           # Email notification template
│   └── vars/
│       ├── dns_vars.yml                    # DNS-specific variables
│       ├── prod_vars.yml                   # Production environment variables
│       └── uat_vars.yml                    # UAT environment variables
```

## 3. File Descriptions

### 3.1 main.yml

The main playbook entry point that orchestrates the DNS management operations. It includes:

- Variable files
- Handlers for Jira ticket updates
- Pre-tasks for retrieving credentials from CyberArk
- Main tasks for DNS operations

### 3.2 manage-dns/files/set-dns.ps1

A PowerShell script that performs the actual DNS operations on Windows DNS servers. It includes functions for:

- Verifying DNS records
- Adding DNS records
- Removing DNS records
- Managing PTR records
- Special handling for SingHealth domains

### 3.3 manage-dns/handlers/update-sr-uat.yml

A handler that updates Jira Service Request tickets with the AAP job link. It is triggered after the DNS operations are completed.

### 3.4 manage-dns/tasks/main.yml

The main tasks entry point that includes the DNS management tasks.

### 3.5 manage-dns/tasks/dns.yml

The core tasks file that handles DNS operations, including:

- ADMT server selection based on domain
- Credential selection based on domain
- PowerShell script deployment and execution
- Email notification
- Cleanup

### 3.6 manage-dns/templates/email_template_dns.j2

A Jinja2 template for formatting email notifications. It includes sections for:

- A record status
- PTR record status
- Processing errors
- Raw output
- Job link

### 3.7 manage-dns/vars/dns_vars.yml

DNS-specific variables, including:

- Script path
- Ticket information
- Email settings
- Team name selection

### 3.8 manage-dns/vars/prod_vars.yml

Production environment variables, including:

- AAP URL
- Jira URL
- Grid URL

### 3.9 manage-dns/vars/uat_vars.yml

UAT environment variables, similar to prod_vars.yml but for the UAT environment.

### 3.10 docs/POWERSHELL_TESTING.md

A comprehensive guide for DNS engineers on how to directly test and use the PowerShell script on ADMT servers. It includes step-by-step instructions, examples, testing scenarios, and troubleshooting tips.

## 4. Workflow

1. The playbook is triggered from AAP with the required variables (action, domain, hostname, etc.)
2. Credentials are retrieved from CyberArk
3. The appropriate ADMT server is selected based on the domain
4. The PowerShell script is deployed to the ADMT server
5. The script is executed with the provided parameters
6. The script performs the requested DNS operation
7. The results are captured and formatted
8. An email notification is sent to the relevant team
9. The Jira ticket is updated with the AAP job link
10. The script is cleaned up from the ADMT server

## 5. Domain-to-Server Mapping

### 5.1 A Record DNS Servers

| Domain | DNS Server |
|--------|------------|
| devhealthgrp.com.sg | hisaddcvdutl01.devhealthgrp.com.sg |
| healthgrpexts.com.sg | hisaddcvsutl03.healthgrpexts.com.sg |
| nnstg.local | nhgaddcvssys01.nnstg.local |
| ses.shsu.com.sg | sedcvssys22h1.ses.shsu.com.sg |
| shses.shs.com.sg | sesdcvpsys01.shses.shs.com.sg |
| nhg.local | nhgaddcvpsys03.nhg.local |
| aic.local | aicaddcvpsys06.aic.local |
| iltc.healthgrp.com.sg | aicaddcvpsys02.iltc.healthgrp.com.sg |
| healthgrp.com.sg | hisaddcvputl07.healthgrp.com.sg |
| hcloud.healthgrp.com.sg | hisaddcvputl02.hcloud.healthgrp.com.sg |
| healthgrpextp.com.sg | hisaddcvputl13.healthgrpextp.com.sg |

### 5.2 PTR Record DNS Servers

| Domain | PTR DNS Server |
|--------|---------------|
| ses.shsu.com.sg | shdcvsys22h1.shsu.com.sg |
| shses.shs.com.sg | sesdcvpsys11.shs.com.sg |
| All other domains | Same as A record DNS server |

### 5.3 ADMT Servers

| Domain | ADMT Server |
|--------|-------------|
| devhealthgrp.com.sg | HISADMTVDSEC01.devhealthgrp.com.sg |
| healthgrpexts.com.sg | HISADMTVSSEC01.healthgrpexts.com.sg |
| nnstg.local | HISADMTVSSEC02.nnstg.local |
| ses.shsu.com.sg | SHSADMTVDSEC02.ses.shsu.com.sg |
| shses.shs.com.sg | SHSADMTVPSEC12.shses.shs.com.sg |
| nhg.local | HISADMTVPSEC11.nhg.local |
| aic.local | HISADMTVPSEC02.aic.local |
| iltc.healthgrp.com.sg | HISADMTVPSEC04.iltc.healthgrp.com.sg |
| healthgrp.com.sg | HISADMTVPSEC05.healthgrp.com.sg |
| hcloud.healthgrp.com.sg | HISADMTVPSEC06.hcloud.healthgrp.com.sg |
| healthgrpextp.com.sg | HISADMTVPSEC08.healthgrpextp.com.sg |

## 6. Email Notification Recipients

| Domain | Recipient |
|--------|-----------|
| ses.shsu.com.sg, shses.shs.com.sg | <EMAIL> |
| All other domains | <EMAIL> |

## 7. Security Considerations

- PowerShell scripts are cleaned up after execution
- Sensitive information is masked in logs using no_log
- CyberArk integration for secure credential management
