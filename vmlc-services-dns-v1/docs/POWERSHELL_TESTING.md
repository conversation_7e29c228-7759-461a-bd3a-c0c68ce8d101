# PowerShell Script Testing Guide for DNS Engineers

## 1. Introduction

This document provides detailed instructions for DNS engineers on how to directly test and use the `set-dns.ps1` PowerShell script on ADMT servers. This approach allows for quick testing and troubleshooting without going through the Ansible Automation Platform (AAP).

**Author:** CES Operational Excellence Team
**Contributor:** <PERSON> (7409)

## 2. Prerequisites

- Access to the appropriate ADMT server for the domain you want to manage
- Appropriate permissions to run PowerShell scripts and manage DNS records
- Basic knowledge of PowerShell commands
- The `set-dns.ps1` script file

## 3. Script Location

The script should be placed in the following location on the ADMT server:

```
C:\ansscripts\set-dns.ps1
```

If the directory doesn't exist, create it:

```powershell
New-Item -Path "C:\ansscripts" -ItemType Directory -Force
```

## 4. Script Parameters

The script accepts the following parameters:

| Parameter | Type | Required | Default | Description |
|-----------|------|----------|---------|-------------|
| Action | String | No | verify | The action to perform: verify, add, or remove |
| Domain | String | Yes | - | The domain for DNS operations (e.g., healthgrp.com.sg) |
| HostName | String | Yes | - | The hostname for the DNS record (without domain) |
| IpAddress | String | Only for 'add' | - | The IP address for the DNS record |
| TTL | Integer | No | 3600 | Time-to-live value in seconds |

## 5. ADMT Server Selection

Use the appropriate ADMT server based on the domain you want to manage:

| Domain | ADMT Server |
|--------|-------------|
| devhealthgrp.com.sg | HISADMTVDSEC01.devhealthgrp.com.sg |
| healthgrpexts.com.sg | HISADMTVSSEC01.healthgrpexts.com.sg |
| nnstg.local | HISADMTVSSEC02.nnstg.local |
| ses.shsu.com.sg | SHSADMTVDSEC02.ses.shsu.com.sg |
| shses.shs.com.sg | SHSADMTVPSEC12.shses.shs.com.sg |
| nhg.local | HISADMTVPSEC11.nhg.local |
| aic.local | HISADMTVPSEC02.aic.local |
| iltc.healthgrp.com.sg | HISADMTVPSEC04.iltc.healthgrp.com.sg |
| healthgrp.com.sg | HISADMTVPSEC05.healthgrp.com.sg |
| hcloud.healthgrp.com.sg | HISADMTVPSEC06.hcloud.healthgrp.com.sg |
| healthgrpextp.com.sg | HISADMTVPSEC08.healthgrpextp.com.sg |

## 6. Usage Examples

### 6.1 Verifying a DNS Record

To check if a DNS record exists:

```powershell
.\set-dns.ps1 -Action verify -Domain healthgrp.com.sg -HostName server01
```

### 6.2 Adding a DNS Record

To add a new DNS A record and its corresponding PTR record:

```powershell
.\set-dns.ps1 -Action add -Domain healthgrp.com.sg -HostName server01 -IpAddress ******** -TTL 7200
```

### 6.3 Removing a DNS Record

To remove a DNS A record:

```powershell
.\set-dns.ps1 -Action remove -Domain healthgrp.com.sg -HostName server01
```

## 7. Special Cases

### 7.1 SingHealth Domains

For SingHealth domains, special PTR handling is implemented:

- **ses.shsu.com.sg**: A records are managed on sedcvssys22h1.ses.shsu.com.sg, but PTR records are managed on shdcvsys22h1.shsu.com.sg
- **shses.shs.com.sg**: A records are managed on sesdcvpsys01.shses.shs.com.sg, but PTR records are managed on sesdcvpsys11.shs.com.sg

Example for SingHealth staging:

```powershell
.\set-dns.ps1 -Action add -Domain ses.shsu.com.sg -HostName webserver -IpAddress ***********
```

Example for SingHealth production:

```powershell
.\set-dns.ps1 -Action add -Domain shses.shs.com.sg -HostName webserver -IpAddress ***********
```

## 8. Understanding the Output

The script outputs information in two formats:

1. **Text output**: Detailed information about the operation
2. **JSON output**: A structured representation of the results

Example JSON output:

```json
{
  "action": "verify",
  "status": "Record Exists",
  "domain": "healthgrp.com.sg",
  "dns_server": "hisaddcvputl07.healthgrp.com.sg",
  "ptr_dns_server": "hisaddcvputl07.healthgrp.com.sg",
  "hostname": "server01",
  "ip_address": "********",
  "ttl": "3600",
  "ptr_record": "server01.healthgrp.com.sg",
  "errors": ""
}
```

## 9. Troubleshooting

### 9.1 Common Issues

#### Script Execution Policy

If you encounter execution policy restrictions:

```powershell
Set-ExecutionPolicy -Scope Process -ExecutionPolicy Bypass
```

#### DNS Server Connection Issues

If the script cannot connect to the DNS server:

1. Verify network connectivity to the DNS server
2. Check if the DNS server name is correct in the script
3. Verify that the DNS server is running
4. Check if you have appropriate permissions

#### Authentication Issues

If you encounter authentication issues:

1. Ensure you're running the script with an account that has appropriate permissions
2. Try running the script as an administrator

### 9.2 Debugging

To get more detailed output, add the `-Verbose` parameter to the PowerShell commands:

```powershell
.\set-dns.ps1 -Action verify -Domain healthgrp.com.sg -HostName server01 -Verbose
```

## 10. Testing Scenarios

### 10.1 Idempotency Testing

Test that running the same operation multiple times produces the expected result:

```powershell
# Run the add operation twice
.\set-dns.ps1 -Action add -Domain healthgrp.com.sg -HostName server01 -IpAddress ********
.\set-dns.ps1 -Action add -Domain healthgrp.com.sg -HostName server01 -IpAddress ********

# Verify the record
.\set-dns.ps1 -Action verify -Domain healthgrp.com.sg -HostName server01
```

### 10.2 Update Testing

Test updating an existing record with a new IP address:

```powershell
# Add a record with one IP
.\set-dns.ps1 -Action add -Domain healthgrp.com.sg -HostName server01 -IpAddress ********

# Update the record with a different IP
.\set-dns.ps1 -Action add -Domain healthgrp.com.sg -HostName server01 -IpAddress ********

# Verify the record has the new IP
.\set-dns.ps1 -Action verify -Domain healthgrp.com.sg -HostName server01
```

### 10.3 Non-existent Record Testing

Test operations on non-existent records:

```powershell
# Verify a non-existent record
.\set-dns.ps1 -Action verify -Domain healthgrp.com.sg -HostName nonexistent

# Remove a non-existent record
.\set-dns.ps1 -Action remove -Domain healthgrp.com.sg -HostName nonexistent
```

## 11. Best Practices

1. **Always verify first**: Before adding or removing records, use the verify action to check the current state
2. **Use descriptive hostnames**: Follow naming conventions
3. **Document changes**: Keep a log of all changes made directly on the servers
4. **Test in staging first**: Test changes in development/staging environments before applying to production
5. **Check PTR records**: Always verify that PTR records are correctly created or updated
6. **Use appropriate TTL values**: Use shorter TTL values for testing and longer values for production

## 12. Security Considerations

1. **Clean up after testing**: Remove the script from the server after testing
2. **Use least privilege accounts**: Only use accounts with the minimum required permissions
3. **Avoid hardcoding credentials**: Never include credentials in the script
4. **Audit script usage**: Keep track of who uses the script and when

## 13. Contact Information

For additional assistance, please contact:

- **Email**: <EMAIL>
- **Jira**: Create a Service Request ticket with the CES Operational Excellence Team
