---
# =========================================================================
# Main playbook for DNS Management
# =========================================================================
# This playbook orchestrates the DNS management operations for VM lifecycle
# It handles A record and PTR record operations across multiple domains
# Operations supported: verify, add, remove
#
# Author: CES Operational Excellence Team
# Contributor: <PERSON><PERSON> (7409)
# =========================================================================

- name: DNS Management
  hosts: all
  gather_facts: false

  vars_files:
    - manage-dns/vars/dns_vars.yml

  handlers:
    - name: Update Jira Ticket
      include_tasks: manage-dns/handlers/update-sr-uat.yml

  pre_tasks:
    - name: Retrieve Password from PAM Safe
      ansible.builtin.include_role:
        name: cloud_cpe.cyberark_ccp.retrieve_from_cyberark

  tasks:
    - name: Include main tasks
      include_tasks: manage-dns/tasks/main.yml