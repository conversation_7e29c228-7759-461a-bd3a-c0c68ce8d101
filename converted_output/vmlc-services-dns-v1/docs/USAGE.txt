# DNS Management Project Usage Guide

## 1. Introduction

This document provides detailed instructions on how to use the DNS Management project (vmlc-services-dns-v1) through Ansible Automation Platform (AAP) Job Templates.

**Author:** CES Operational Excellence Team
**Contributor:** <PERSON> (7409)

## 2. AAP Job Templates

The following Job Templates should be created in AAP for DNS management operations:

### 2.1 DNS Record Verification

- **Name**: VMLC - DNS Record Verification
- **Description**: Verify if a DNS A record and its corresponding PTR record exist
- **Job Type**: Run
- **Inventory**: Your inventory
- **Project**: vmlc-services-dns-v1
- **Playbook**: main.yml
- **Credentials**: Machine credential with appropriate permissions
- **Extra Variables**:
  ```yaml
  action: verify
  domain: <domain>
  hostname: <hostname>
  ```

### 2.2 DNS Record Addition

- **Name**: VMLC - DNS Record Addition
- **Description**: Add a DNS A record and its corresponding PTR record
- **Job Type**: Run
- **Inventory**: Your inventory
- **Project**: vmlc-services-dns-v1
- **Playbook**: main.yml
- **Credentials**: Machine credential with appropriate permissions
- **Extra Variables**:
  ```yaml
  action: add
  domain: <domain>
  hostname: <hostname>
  ipaddress: <ip_address>
  ttl: <ttl_value>  # Optional, defaults to 3600
  ```

### 2.3 DNS Record Removal

- **Name**: VMLC - DNS Record Removal
- **Description**: Remove a DNS A record
- **Job Type**: Run
- **Inventory**: Your inventory
- **Project**: vmlc-services-dns-v1
- **Playbook**: main.yml
- **Credentials**: Machine credential with appropriate permissions
- **Extra Variables**:
  ```yaml
  action: remove
  domain: <domain>
  hostname: <hostname>
  ```

## 3. Using Extra Variables

### 3.1 Required Variables

- **action**: The operation to perform (verify, add, remove)
- **domain**: The domain for the DNS record (e.g., healthgrp.com.sg)
- **hostname**: The hostname for the DNS record (without the domain)

### 3.2 Optional Variables

- **ipaddress**: The IP address for the DNS record (required for 'add' action)
- **ttl**: The time-to-live value in seconds (defaults to 3600)
- **var_sr_number**: The Jira Service Request number (defaults to SCR-67898)
- **testing_mode**: Set to 'true' to send emails <NAME_EMAIL> for testing

### 3.3 Examples

#### Verify a DNS Record

```yaml
action: verify
domain: healthgrp.com.sg
hostname: server01
```

#### Add a DNS Record

```yaml
action: add
domain: healthgrp.com.sg
hostname: server01
ipaddress: ********
ttl: 7200
```

#### Remove a DNS Record

```yaml
action: remove
domain: healthgrp.com.sg
hostname: server01
```

#### Testing Mode

```yaml
action: add
domain: healthgrp.com.sg
hostname: server01
ipaddress: ********
testing_mode: true
```

## 4. AAP Survey Configuration

Instead of using extra variables, you can also configure AAP Job Template Surveys for a more user-friendly interface.

### 4.1 DNS Record Verification Survey

| Question | Variable Name | Type | Default | Required |
|----------|---------------|------|---------|----------|
| Operation | action | Multiple Choice (verify, add, remove) | verify | Yes |
| Domain | domain | Multiple Choice (list of supported domains) | - | Yes |
| Hostname | hostname | Text | - | Yes |
| Jira SR Number | var_sr_number | Text | SCR-67898 | No |

### 4.2 DNS Record Addition Survey

| Question | Variable Name | Type | Default | Required |
|----------|---------------|------|---------|----------|
| Operation | action | Multiple Choice (verify, add, remove) | add | Yes |
| Domain | domain | Multiple Choice (list of supported domains) | - | Yes |
| Hostname | hostname | Text | - | Yes |
| IP Address | ipaddress | Text | - | Yes |
| TTL (seconds) | ttl | Integer | 3600 | No |
| Jira SR Number | var_sr_number | Text | SCR-67898 | No |

### 4.3 DNS Record Removal Survey

| Question | Variable Name | Type | Default | Required |
|----------|---------------|------|---------|----------|
| Operation | action | Multiple Choice (verify, add, remove) | remove | Yes |
| Domain | domain | Multiple Choice (list of supported domains) | - | Yes |
| Hostname | hostname | Text | - | Yes |
| Jira SR Number | var_sr_number | Text | SCR-67898 | No |

## 5. Email Notifications

After each operation, an email notification is sent to the relevant team:

- For SingHealth domains (ses.shsu.com.sg, shses.shs.com.sg): <EMAIL>
- For all other domains: <EMAIL>

A BCC is always <NAME_EMAIL>.

## 6. Jira Integration

If a Jira SR number is provided, the job will update the Jira ticket with a link to the AAP job.

## 7. Idempotent Operations

All DNS operations in this project are designed to be idempotent, which means they can be run multiple times without causing unintended side effects. This is particularly important for operations that might be interrupted or need to be retried.

### 7.1 How Idempotency Works

- **Verify Operation**: Always safe to run multiple times as it only reads data
- **Add Operation**:
  - Checks if the record exists before adding
  - If it exists with the same IP, no action is taken
  - If it exists with a different IP, it's updated
  - PTR records are only added if they don't exist
- **Remove Operation**:
  - Checks if the record exists before removing
  - If it doesn't exist, completes successfully without error
  - No unexpected errors if run multiple times

### 7.2 Benefits for Operations

- **Safe Retries**: If an operation fails due to network issues, it can be safely retried
- **Consistent State**: The DNS records will be in the expected state regardless of how many times the operation is run
- **Reduced Risk**: No risk of creating duplicate records or causing DNS conflicts
- **Clear Reporting**: The email notification clearly indicates what changes were made (if any)

### 7.3 Example Scenarios

1. **Adding the Same Record Twice**: If you run the add operation for the same hostname and IP address twice, the second operation will detect that the record already exists and will not make any changes.

2. **Removing a Non-existent Record**: If you run the remove operation for a record that doesn't exist, the operation will complete successfully without error.

3. **Interrupted Operation**: If an operation is interrupted (e.g., network failure), it can be safely retried without risk of creating an inconsistent state.

## 8. Limitations

- The project can only be triggered from AAP
- Users and developers do not have access to run ansible-playbook commands directly on servers
- PTR records are not automatically removed when removing A records for safety
- Special handling is required for SingHealth domains
