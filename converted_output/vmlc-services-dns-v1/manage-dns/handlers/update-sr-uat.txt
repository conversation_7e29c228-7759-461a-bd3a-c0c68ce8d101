---
# =========================================================================
# Jira Service Request Update Handler
# =========================================================================
# This handler updates the Jira Service Request ticket with the AAP job link
# It is triggered after the DNS operations are completed
#
# Author: CES Operational Excellence Team
# Contributor: <PERSON> (7409)
# =========================================================================

# Include the Jira-related variables from the UAT environment
- name: Include Jira vars
  include_vars: uat_vars.yml  # Load variables specific to the UAT environment
  no_log: false  # Allow logging for debugging
  run_once: true  # Execute this task only once

# Update the Jira ticket with the AAP job link
# This allows for traceability between Jira tickets and AAP jobs
- name: Update grid row in a SR ticket
  uri:
    url: "{{ grid_url }}/{{ var_grid_id }}/issue/{{ var_sr_number }}/"  # Jira API endpoint
    headers:
      Authorization: "{{ jira_grid }}"  # Authentication token for Jira
    validate_certs: no  # Skip SSL certificate validation
    method: PUT  # HTTP method for the API call
    status_code: 204  # Expected HTTP status code
    body_format: json  # Format of the request body
    body: |
      {
        "rows":[
          {
            "rowId":"{{ var_row_id }}",  # ID of the row to update
            "columns":{
              "remark": "{{ aap_url }}/#/jobs/playbook/{{ tower_job_id }}/"  # AAP job link
            }
          }
        ]
      }
  ignore_errors: true  # Continue execution even if the update fails
  delegate_to: localhost  # Run this task on the Ansible controller
  run_once: yes  # Execute this task only once