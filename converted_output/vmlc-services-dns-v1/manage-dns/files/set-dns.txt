# =========================================================================
# DNS Management PowerShell Script
# =========================================================================
# This script handles DNS record operations (verify, add, remove) for VM lifecycle
# It manages both A records and corresponding PTR records
# Special handling is implemented for SingHealth domains
#
# Functions:
# - Format-DnsReport: Standardizes output format for Ansible parsing
# - Get-DnsServer: Maps domains to their corresponding DNS servers
# - Get-PtrDnsServer: Handles special PTR server mapping for SingHealth domains
# - Get-PtrRecord: Retrieves PTR records for reverse lookup
# - Verify-DnsRecord: Checks existence of A and PTR records
# - Add-DnsRecord: Creates A and PTR records
# - Remove-DnsARecord: Removes A records (PTR records preserved for safety)
#
# Author: CES Operational Excellence Team
# Contributor: <PERSON> (7409)
# =========================================================================

param(
    [Parameter(Mandatory = $false)]
    [string]$Action = "verify",

    [Parameter(Mandatory = $true)]
    [string]$Domain,

    [Parameter(Mandatory = $true)]
    [string]$HostName,

    [Parameter(Mandatory = $false)]
    [string]$IpAddress,

    [Parameter(Mandatory = $false)]
    [int]$TTL = 3600
)

function Format-DnsReport {
    param(
        [string]$Action,
        [string]$RecordStatus,
        [string]$Domain,
        [string]$DnsServer,
        [string]$PtrDnsServer = "",
        [string]$HostName,
        [string]$IpAddress,
        [string]$TTL = "",
        [string]$PTR = "",
        [string]$ErrorMessage = ""
    )

    $output = @{
        "action"        = $Action
        "status"        = $RecordStatus
        "domain"        = $Domain
        "dns_server"    = $DnsServer
        "ptr_dns_server" = $PtrDnsServer
        "hostname"      = $HostName
        "ip_address"    = $IpAddress
        "ttl"           = $TTL
        "ptr_record"    = $PTR
        "errors"        = $ErrorMessage
    }

    $output | ConvertTo-Json -Compress
}

function Get-DnsServer {
    param([string]$Domain)

    switch ($Domain.ToLower()) {
        "devhealthgrp.com.sg" { return "hisaddcvdutl01.devhealthgrp.com.sg" }
        "healthgrpexts.com.sg" { return "hisaddcvsutl03.healthgrpexts.com.sg" }
        "nnstg.local" { return "nhgaddcvssys01.nnstg.local" }
        "ses.shsu.com.sg" { return "sedcvssys22h1.ses.shsu.com.sg" }
        "shses.shs.com.sg" { return "sesdcvpsys01.shses.shs.com.sg" }
        "nhg.local" { return "nhgaddcvpsys03.nhg.local" }
        "aic.local" { return "aicaddcvpsys06.aic.local" }
        "iltc.healthgrp.com.sg" { return "aicaddcvpsys02.iltc.healthgrp.com.sg" }
        "healthgrp.com.sg" { return "hisaddcvputl07.healthgrp.com.sg" }
        "hcloud.healthgrp.com.sg" { return "hisaddcvputl02.hcloud.healthgrp.com.sg" }
        "healthgrpextp.com.sg" { return "hisaddcvputl13.healthgrpextp.com.sg" }
        default { throw "Unknown domain: $Domain" }
    }
}

function Get-PtrDnsServer {
    param(
        [string]$Domain,
        [string]$DnsServer
    )

    switch ($Domain.ToLower()) {
        "ses.shsu.com.sg" { return "shdcvsys22h1.shsu.com.sg" }
        "shses.shs.com.sg" { return "sesdcvpsys11.shs.com.sg" }
        default { return $DnsServer }
    }
}

function Get-PtrRecord {
    param(
        [string]$IpAddress,
        [string]$PtrDnsServer
    )

    try {
        $ipParts = $IpAddress.Split('.')
        $reverseZone = "$($ipParts[2]).$($ipParts[1]).$($ipParts[0]).in-addr.arpa"
        $ptrRecordName = "$($ipParts[3])"

        $ptrRecord = Get-DnsServerResourceRecord -ZoneName $reverseZone -ComputerName $PtrDnsServer -Name $ptrRecordName -RRType "PTR" -ErrorAction Stop

        Write-Host "PTR Record Retrieved:" -ForegroundColor Green
        Write-Host ($ptrRecord | Format-List * | Out-String)

        if ($ptrRecord) {
            if ($ptrRecord.RecordData -is [System.Array]) {
                $ptrDomainName = $ptrRecord.RecordData[0].PtrDomainName
                return $ptrDomainName
            } else {
                return "PTR Record Not Found"
            }
        } else {
            return "PTR Record Not Found"
        }
    } catch {
        return @{
            "status" = "PTR Record Does Not Exist",
            "error_message" = "Failed to retrieve PTR record: $_"
        }
    }
}

function Verify-DnsRecord {
    param(
        [string]$Domain,
        [string]$HostName,
        [string]$DnsServer
    )

    $error_message = ""
    $recordStatus = ""
    $ptrStatus = ""
    $ttl = ""
    $ptrRecordString = ""
    $IpAddress = $null

    try {
        $aRecord = Get-DnsServerResourceRecord -ZoneName $Domain -ComputerName $DnsServer -Name $HostName -RRType "A" -ErrorAction Stop -Verbose

        if ($aRecord) {
            $recordStatus = "Record Exists"
            $IpAddress = $aRecord.RecordData.IPv4Address.ToString()
            $ttl = $aRecord.TimeToLive.TotalSeconds
        } else {
            $recordStatus = "Record Not Found"
        }
    } catch {
        $error_message = "$_"
        $recordStatus = "Record Not Found"
    }

    $ptrRecord = ""
    try {
        $ptrDnsServer = Get-PtrDnsServer -Domain $Domain -DnsServer $DnsServer

        if ($IpAddress) {
            $ptrRecord = Get-PtrRecord -IpAddress $IpAddress -PtrDnsServer $ptrDnsServer -Verbose
        }

        if ($ptrRecord -is [hashtable]) {
            $ptrRecordString = $ptrRecord["error_message"]
        } elseif ($ptrRecord) {
            $ptrRecordString = $ptrRecord
        } else {
            $ptrRecordString = "PTR Record Not Found"
        }
    } catch {
        $error_message += "`nPTR Error: $_"
        $ptrRecordString = "PTR Record Not Found"
    }

    Format-DnsReport -Action "Verify" -RecordStatus $recordStatus -Domain $Domain -DnsServer $DnsServer -PtrDnsServer $ptrDnsServer -HostName $HostName -IpAddress $IpAddress -TTL $ttl -PTR $ptrRecordString -ErrorMessage $error_message
}

function Add-DnsRecord {
    param(
        [string]$Domain,
        [string]$HostName,
        [string]$IpAddress,
        [string]$DnsServer,
        [int]$TTL = 3600,
        [int]$PtrTTL = 3600
    )

    $error_message = ""
    $recordStatus = ""
    $ptrStatus = ""
    $ptrRecordString = ""

    $aRecordExists = Get-DnsServerResourceRecord -ZoneName $Domain -ComputerName $DnsServer -Name $HostName -RRType "A" -ErrorAction SilentlyContinue

    if ($aRecordExists -and $aRecordExists.RecordData.IPv4Address.ToString() -eq $IpAddress) {
        $recordStatus = "A Record Already Exists"
    } else {
        try {
            Add-DnsServerResourceRecordA -ZoneName $Domain -ComputerName $DnsServer -Name $HostName -IPv4Address $IpAddress -TimeToLive ([TimeSpan]::FromSeconds($TTL)) -ErrorAction Stop
            $recordStatus = "A Record Added"
        } catch {
            $error_message = "A Record Error: $_"
            $recordStatus = "Failed to Add A Record"
        }
    }

    $ptrDnsServer = Get-PtrDnsServer -Domain $Domain -DnsServer $DnsServer
    $ptrRecord = Get-PtrRecord -IpAddress $IpAddress -PtrDnsServer $ptrDnsServer

    if (-not $ptrRecord -or $ptrRecord -eq "PTR Record Not Found") {
        try {
            Add-DnsServerResourceRecordPtr -Name $IpAddress.Split('.')[3] -ZoneName "$($IpAddress.Split('.')[2]).$($IpAddress.Split('.')[1]).$($IpAddress.Split('.')[0]).in-addr.arpa" -PtrDomainName "$HostName.$Domain" -ComputerName $ptrDnsServer -TimeToLive ([TimeSpan]::FromSeconds($PtrTTL)) -ErrorAction Stop
            $ptrRecord = Get-PtrRecord -IpAddress $IpAddress -PtrDnsServer $ptrDnsServer
            $ptrStatus = "PTR Record Added"
        } catch {
            $ptrStatus = "Failed to Add PTR Record"
            $error_message += "`nPTR Error: $_"
        }
    } else {
        $ptrStatus = "PTR Record Already Exists"
    }

    if ($ptrRecord -is [hashtable]) {
        $ptrRecordString = $ptrRecord["error_message"]
    } elseif ($ptrRecord) {
        $ptrRecordString = $ptrRecord
    } else {
        $ptrRecordString = "PTR Record Not Found"
    }

    Format-DnsReport -Action "Add" -RecordStatus $recordStatus -Domain $Domain -DnsServer $DnsServer -PtrDnsServer $ptrDnsServer -HostName $HostName -IpAddress $IpAddress -TTL $TTL -PTR $ptrRecordString -ErrorMessage $error_message
}

function Remove-DnsARecord {
    param(
        [string]$Domain,
        [string]$HostName,
        [string]$DnsServer
    )

    $error_message = ""
    $recordStatus = ""
    $IpAddress = $null

    $aRecordExists = Get-DnsServerResourceRecord -ZoneName $Domain -ComputerName $DnsServer -Name $HostName -RRType "A" -ErrorAction SilentlyContinue

    if ($aRecordExists) {
        try {
            $IpAddress = $aRecordExists.RecordData.IPv4Address.ToString()
            Remove-DnsServerResourceRecord -ZoneName $Domain -ComputerName $DnsServer -Name $HostName -RRType "A" -RecordData $IpAddress -Force -Verbose
            $recordStatus = "A Record Removed"
        } catch {
            $error_message = "Error Removing A Record: $_"
            $recordStatus = "Failed to Remove A Record"
        }
    } else {
        $recordStatus = "A Record Not Found"
    }

    Format-DnsReport -Action "Remove" -RecordStatus $recordStatus -Domain $Domain -DnsServer $DnsServer -HostName $HostName -IpAddress $IpAddress -ErrorMessage $error_message
}

$DnsServer = Get-DnsServer -Domain $Domain

switch ($Action.ToLower()) {
    "verify" {
        Verify-DnsRecord -Domain $Domain -HostName $HostName -DnsServer $DnsServer
    }
    "add" {
        Add-DnsRecord -Domain $Domain -HostName $HostName -IpAddress $IpAddress -DnsServer $DnsServer -TTL $TTL
    }
    "remove" {
        Remove-DnsARecord -Domain $Domain -HostName $HostName -DnsServer $DnsServer
    }
    default {
        Write-Host "Invalid action specified. Please use 'verify', 'add', or 'remove'." -ForegroundColor Red
    }
}