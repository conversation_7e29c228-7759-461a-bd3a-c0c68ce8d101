# =========================================================================
# DNS Management PowerShell Script
# =========================================================================
# This script handles DNS record operations (verify, add, remove) for VM lifecycle
# It manages both A records and corresponding PTR records with intelligent zone detection
# Special handling is implemented for SingHealth domains
#
# Key Features:
# - Intelligent PTR zone detection with 3-tier hierarchical fallback (3→2→1 octet zones)
# - Safe automatic PTR removal that only removes matching records
# - Comprehensive error handling that never impacts A record operations
# - Timeout protection for DNS queries to prevent script hanging
# - Enhanced logging with timing information and color-coded output
#
# Functions:
# - Format-DnsReport: Standardizes output format for Ansible parsing
# - Get-DnsServer: Maps domains to their corresponding DNS servers
# - Get-PtrDnsServer: Handles special PTR server mapping for SingHealth domains
# - Find-OptimalPtrZone: Intelligent PTR zone detection with hierarchical fallback
# - Get-PtrRecord: Retrieves PTR records for reverse lookup
# - Verify-DnsRecord: Checks existence of A and PTR records
# - Add-DnsRecord: Creates A and PTR records with intelligent zone management
# - Remove-DnsARecord: Removes A records and matching PTR records safely
#
# Author: CES Operational Excellence Team
# Contributor: Muhammad Syazani Bin Mohamed Khairi (7409)
# =========================================================================

param(
    [Parameter(Mandatory = $false)]
    [string]$Action = "verify",

    [Parameter(Mandatory = $true)]
    [string]$Domain,

    [Parameter(Mandatory = $true)]
    [string]$HostName,

    [Parameter(Mandatory = $false)]
    [string]$IpAddress,

    [Parameter(Mandatory = $false)]
    [int]$TTL = 3600,

    [Parameter(Mandatory = $false)]
    [bool]$ManagePtr = $true
)

function Format-DnsReport {
    param(
        [string]$Action,
        [string]$RecordStatus,
        [string]$Domain,
        [string]$DnsServer,
        [string]$PtrDnsServer = "",
        [string]$HostName,
        [string]$IpAddress,
        [string]$TTL = "",
        [string]$PTR = "",
        [string]$ErrorMessage = "",
        [bool]$PtrManagementEnabled = $true,
        [string]$PtrZoneDetected = "",
        [string]$PtrOperationStatus = ""
    )

    $output = @{
        "action"                = $Action
        "status"                = $RecordStatus
        "domain"                = $Domain
        "dns_server"            = $DnsServer
        "ptr_dns_server"        = $PtrDnsServer
        "hostname"              = $HostName
        "ip_address"            = $IpAddress
        "ttl"                   = $TTL
        "ptr_record"            = $PTR
        "ptr_management_enabled" = $PtrManagementEnabled
        "ptr_zone_detected"     = $PtrZoneDetected
        "ptr_operation_status"  = $PtrOperationStatus
        "errors"                = $ErrorMessage
    }

    $output | ConvertTo-Json -Compress
}

function Get-DnsServer {
    param([string]$Domain)

    switch ($Domain.ToLower()) {
        "devhealthgrp.com.sg" { return "hisaddcvdutl01.devhealthgrp.com.sg" }
        "healthgrpexts.com.sg" { return "hisaddcvsutl03.healthgrpexts.com.sg" }
        "nnstg.local" { return "nhgaddcvssys01.nnstg.local" }
        "ses.shsu.com.sg" { return "sedcvssys22h1.ses.shsu.com.sg" }
        "shses.shs.com.sg" { return "sesdcvpsys01.shses.shs.com.sg" }
        "nhg.local" { return "nhgaddcvpsys03.nhg.local" }
        "aic.local" { return "aicaddcvpsys06.aic.local" }
        "iltc.healthgrp.com.sg" { return "aicaddcvpsys02.iltc.healthgrp.com.sg" }
        "healthgrp.com.sg" { return "hisaddcvputl07.healthgrp.com.sg" }
        "hcloud.healthgrp.com.sg" { return "hisaddcvputl02.hcloud.healthgrp.com.sg" }
        "healthgrpextp.com.sg" { return "hisaddcvputl13.healthgrpextp.com.sg" }
        default { throw "Unknown domain: $Domain" }
    }
}

function Get-PtrDnsServer {
    param(
        [string]$Domain,
        [string]$DnsServer
    )

    switch ($Domain.ToLower()) {
        "ses.shsu.com.sg" { return "shdcvsys22h1.shsu.com.sg" }
        "shses.shs.com.sg" { return "sesdcvpsys11.shs.com.sg" }
        default { return $DnsServer }
    }
}

function Find-OptimalPtrZone {
    param(
        [string]$IpAddress,
        [string]$PtrDnsServer
    )

    $startTime = Get-Date
    Write-Host "Starting PTR zone detection for IP: $IpAddress on server: $PtrDnsServer" -ForegroundColor Cyan

    $ipParts = $IpAddress.Split('.')
    if ($ipParts.Length -ne 4) {
        Write-Host "Invalid IP address format: $IpAddress" -ForegroundColor Red
        return @{
            "zone" = $null
            "record_name" = $null
            "detection_time_ms" = ((Get-Date) - $startTime).TotalMilliseconds
            "error" = "Invalid IP address format"
        }
    }

    $zones = @(
        @{
            "name" = "$($ipParts[2]).$($ipParts[1]).$($ipParts[0]).in-addr.arpa"
            "record_name" = "$($ipParts[3])"
            "type" = "3-octet"
        },
        @{
            "name" = "$($ipParts[1]).$($ipParts[0]).in-addr.arpa"
            "record_name" = "$($ipParts[3]).$($ipParts[2])"
            "type" = "2-octet"
        },
        @{
            "name" = "$($ipParts[0]).in-addr.arpa"
            "record_name" = "$($ipParts[3]).$($ipParts[2]).$($ipParts[1])"
            "type" = "1-octet"
        }
    )

    foreach ($zone in $zones) {
        Write-Host "Checking $($zone.type) reverse zone: $($zone.name)" -ForegroundColor Yellow

        try {
            $timeout = 10
            $job = Start-Job -ScriptBlock {
                param($zoneName, $serverName)
                Get-DnsServerZone -Name $zoneName -ComputerName $serverName -ErrorAction Stop
            } -ArgumentList $zone.name, $PtrDnsServer

            if (Wait-Job $job -Timeout $timeout) {
                $result = Receive-Job $job
                Remove-Job $job

                if ($result) {
                    $detectionTime = ((Get-Date) - $startTime).TotalMilliseconds
                    Write-Host "SUCCESS: Found $($zone.type) zone '$($zone.name)' (Detection time: $([math]::Round($detectionTime, 2))ms)" -ForegroundColor Green

                    return @{
                        "zone" = $zone.name
                        "record_name" = $zone.record_name
                        "zone_type" = $zone.type
                        "detection_time_ms" = $detectionTime
                        "error" = $null
                    }
                }
            } else {
                Remove-Job $job -Force
                Write-Host "TIMEOUT: Zone check for '$($zone.name)' exceeded $timeout seconds" -ForegroundColor Red
            }
        } catch {
            Write-Host "FAILED: Zone '$($zone.name)' not found or inaccessible - $($_.Exception.Message)" -ForegroundColor Red
        }
    }

    $detectionTime = ((Get-Date) - $startTime).TotalMilliseconds
    Write-Host "WARNING: No suitable PTR zones found for IP $IpAddress after checking all possibilities (Total time: $([math]::Round($detectionTime, 2))ms)" -ForegroundColor Magenta

    return @{
        "zone" = $null
        "record_name" = $null
        "zone_type" = "none"
        "detection_time_ms" = $detectionTime
        "error" = "No suitable PTR zones found"
    }
}

function Get-PtrRecord {
    param(
        [string]$IpAddress,
        [string]$PtrDnsServer,
        [hashtable]$ZoneInfo = $null
    )

    try {
        if ($ZoneInfo -and $ZoneInfo.zone) {
            $reverseZone = $ZoneInfo.zone
            $ptrRecordName = $ZoneInfo.record_name
            Write-Host "Using provided zone info: Zone=$reverseZone, Record=$ptrRecordName" -ForegroundColor Cyan
        } else {
            Write-Host "No zone info provided, attempting zone detection..." -ForegroundColor Yellow
            $zoneDetection = Find-OptimalPtrZone -IpAddress $IpAddress -PtrDnsServer $PtrDnsServer

            if (-not $zoneDetection.zone) {
                return @{
                    "status" = "PTR Zone Not Found",
                    "error_message" = $zoneDetection.error
                }
            }

            $reverseZone = $zoneDetection.zone
            $ptrRecordName = $zoneDetection.record_name
        }

        $ptrRecord = Get-DnsServerResourceRecord -ZoneName $reverseZone -ComputerName $PtrDnsServer -Name $ptrRecordName -RRType "PTR" -ErrorAction Stop

        Write-Host "PTR Record Retrieved from zone '$reverseZone':" -ForegroundColor Green
        Write-Host ($ptrRecord | Format-List * | Out-String)

        if ($ptrRecord) {
            if ($ptrRecord.RecordData -is [System.Array]) {
                $ptrDomainName = $ptrRecord.RecordData[0].PtrDomainName
                return $ptrDomainName
            } else {
                return "PTR Record Not Found"
            }
        } else {
            return "PTR Record Not Found"
        }
    } catch {
        return @{
            "status" = "PTR Record Does Not Exist",
            "error_message" = "Failed to retrieve PTR record: $_"
        }
    }
}

function Verify-DnsRecord {
    param(
        [string]$Domain,
        [string]$HostName,
        [string]$DnsServer,
        [bool]$ManagePtr = $true
    )

    $error_message = ""
    $recordStatus = ""
    $ptrStatus = ""
    $ttl = ""
    $ptrRecordString = ""
    $IpAddress = $null
    $ptrZoneDetected = ""
    $ptrOperationStatus = ""

    try {
        $aRecord = Get-DnsServerResourceRecord -ZoneName $Domain -ComputerName $DnsServer -Name $HostName -RRType "A" -ErrorAction Stop -Verbose

        if ($aRecord) {
            $recordStatus = "Record Exists"
            $IpAddress = $aRecord.RecordData.IPv4Address.ToString()
            $ttl = $aRecord.TimeToLive.TotalSeconds
        } else {
            $recordStatus = "Record Not Found"
        }
    } catch {
        $error_message = "$_"
        $recordStatus = "Record Not Found"
    }

    $ptrRecord = ""
    $ptrDnsServer = ""

    if ($ManagePtr -and $IpAddress) {
        Write-Host "PTR management is enabled, checking PTR records..." -ForegroundColor Cyan
        try {
            $ptrDnsServer = Get-PtrDnsServer -Domain $Domain -DnsServer $DnsServer
            $zoneDetection = Find-OptimalPtrZone -IpAddress $IpAddress -PtrDnsServer $ptrDnsServer

            if ($zoneDetection.zone) {
                $ptrZoneDetected = $zoneDetection.zone
                $ptrOperationStatus = "Zone detected: $($zoneDetection.zone_type)"
                $ptrRecord = Get-PtrRecord -IpAddress $IpAddress -PtrDnsServer $ptrDnsServer -ZoneInfo $zoneDetection
            } else {
                $ptrZoneDetected = "None"
                $ptrOperationStatus = "No suitable PTR zones found"
                $ptrRecord = @{
                    "status" = "PTR Zone Not Available",
                    "error_message" = $zoneDetection.error
                }
            }

            if ($ptrRecord -is [hashtable]) {
                $ptrRecordString = $ptrRecord["error_message"]
            } elseif ($ptrRecord) {
                $ptrRecordString = $ptrRecord
            } else {
                $ptrRecordString = "PTR Record Not Found"
            }
        } catch {
            $error_message += "`nPTR Error: $_"
            $ptrRecordString = "PTR Record Not Found"
            $ptrOperationStatus = "PTR check failed"
        }
    } else {
        if (-not $ManagePtr) {
            Write-Host "PTR management is disabled, skipping PTR record checks" -ForegroundColor Yellow
            $ptrOperationStatus = "PTR management disabled"
        } else {
            $ptrOperationStatus = "No IP address available for PTR check"
        }
        $ptrRecordString = "PTR management disabled"
        $ptrDnsServer = Get-PtrDnsServer -Domain $Domain -DnsServer $DnsServer
    }

    Format-DnsReport -Action "Verify" -RecordStatus $recordStatus -Domain $Domain -DnsServer $DnsServer -PtrDnsServer $ptrDnsServer -HostName $HostName -IpAddress $IpAddress -TTL $ttl -PTR $ptrRecordString -ErrorMessage $error_message -PtrManagementEnabled $ManagePtr -PtrZoneDetected $ptrZoneDetected -PtrOperationStatus $ptrOperationStatus
}

function Add-DnsRecord {
    param(
        [string]$Domain,
        [string]$HostName,
        [string]$IpAddress,
        [string]$DnsServer,
        [int]$TTL = 3600,
        [int]$PtrTTL = 3600,
        [bool]$ManagePtr = $true
    )

    $error_message = ""
    $recordStatus = ""
    $ptrStatus = ""
    $ptrRecordString = ""
    $ptrZoneDetected = ""
    $ptrOperationStatus = ""

    Write-Host "Starting DNS A record operation for $HostName.$Domain -> $IpAddress" -ForegroundColor Cyan

    $aRecordExists = Get-DnsServerResourceRecord -ZoneName $Domain -ComputerName $DnsServer -Name $HostName -RRType "A" -ErrorAction SilentlyContinue

    if ($aRecordExists -and $aRecordExists.RecordData.IPv4Address.ToString() -eq $IpAddress) {
        $recordStatus = "A Record Already Exists"
        Write-Host "A record already exists with the same IP address" -ForegroundColor Yellow
    } else {
        try {
            Add-DnsServerResourceRecordA -ZoneName $Domain -ComputerName $DnsServer -Name $HostName -IPv4Address $IpAddress -TimeToLive ([TimeSpan]::FromSeconds($TTL)) -ErrorAction Stop
            $recordStatus = "A Record Added"
            Write-Host "A record added successfully" -ForegroundColor Green
        } catch {
            $error_message = "A Record Error: $_"
            $recordStatus = "Failed to Add A Record"
            Write-Host "Failed to add A record: $_" -ForegroundColor Red
        }
    }

    $ptrDnsServer = Get-PtrDnsServer -Domain $Domain -DnsServer $DnsServer

    if ($ManagePtr) {
        Write-Host "PTR management is enabled, proceeding with PTR operations..." -ForegroundColor Cyan

        try {
            $zoneDetection = Find-OptimalPtrZone -IpAddress $IpAddress -PtrDnsServer $ptrDnsServer

            if ($zoneDetection.zone) {
                $ptrZoneDetected = $zoneDetection.zone
                $ptrOperationStatus = "Zone detected: $($zoneDetection.zone_type)"

                Write-Host "Checking existing PTR record in zone: $($zoneDetection.zone)" -ForegroundColor Cyan
                $ptrRecord = Get-PtrRecord -IpAddress $IpAddress -PtrDnsServer $ptrDnsServer -ZoneInfo $zoneDetection

                if (-not $ptrRecord -or $ptrRecord -eq "PTR Record Not Found" -or ($ptrRecord -is [hashtable] -and $ptrRecord.status -eq "PTR Record Does Not Exist")) {
                    try {
                        Write-Host "Adding PTR record: Name=$($zoneDetection.record_name), Zone=$($zoneDetection.zone), Target=$HostName.$Domain" -ForegroundColor Cyan
                        Add-DnsServerResourceRecordPtr -Name $zoneDetection.record_name -ZoneName $zoneDetection.zone -PtrDomainName "$HostName.$Domain" -ComputerName $ptrDnsServer -TimeToLive ([TimeSpan]::FromSeconds($PtrTTL)) -ErrorAction Stop

                        $ptrRecord = Get-PtrRecord -IpAddress $IpAddress -PtrDnsServer $ptrDnsServer -ZoneInfo $zoneDetection
                        $ptrStatus = "PTR Record Added"
                        $ptrOperationStatus += " - PTR record created successfully"
                        Write-Host "PTR record added successfully" -ForegroundColor Green
                    } catch {
                        $ptrStatus = "Failed to Add PTR Record"
                        $error_message += "`nPTR Error: $_"
                        $ptrOperationStatus += " - PTR creation failed"
                        Write-Host "Failed to add PTR record: $_" -ForegroundColor Red
                    }
                } else {
                    $ptrStatus = "PTR Record Already Exists"
                    $ptrOperationStatus += " - PTR record already exists"
                    Write-Host "PTR record already exists" -ForegroundColor Yellow
                }
            } else {
                $ptrZoneDetected = "None"
                $ptrOperationStatus = "No suitable PTR zones found - PTR operations skipped"
                $ptrStatus = "PTR Zone Not Available"
                $ptrRecord = "PTR zone not available"
                Write-Host "WARNING: No suitable PTR zones found, continuing with A record only" -ForegroundColor Magenta
            }
        } catch {
            $error_message += "`nPTR Zone Detection Error: $_"
            $ptrOperationStatus = "PTR zone detection failed"
            $ptrStatus = "PTR Operation Failed"
            $ptrRecord = "PTR operation failed"
            Write-Host "PTR zone detection failed: $_" -ForegroundColor Red
        }
    } else {
        Write-Host "PTR management is disabled, skipping PTR operations" -ForegroundColor Yellow
        $ptrOperationStatus = "PTR management disabled"
        $ptrStatus = "PTR Management Disabled"
        $ptrRecord = "PTR management disabled"
    }

    if ($ptrRecord -is [hashtable]) {
        $ptrRecordString = $ptrRecord["error_message"]
    } elseif ($ptrRecord) {
        $ptrRecordString = $ptrRecord
    } else {
        $ptrRecordString = "PTR Record Not Found"
    }

    Format-DnsReport -Action "Add" -RecordStatus $recordStatus -Domain $Domain -DnsServer $DnsServer -PtrDnsServer $ptrDnsServer -HostName $HostName -IpAddress $IpAddress -TTL $TTL -PTR $ptrRecordString -ErrorMessage $error_message -PtrManagementEnabled $ManagePtr -PtrZoneDetected $ptrZoneDetected -PtrOperationStatus $ptrOperationStatus
}

function Remove-DnsARecord {
    param(
        [string]$Domain,
        [string]$HostName,
        [string]$DnsServer,
        [bool]$ManagePtr = $true
    )

    $error_message = ""
    $recordStatus = ""
    $IpAddress = $null
    $ptrOperationStatus = ""
    $ptrZoneDetected = ""
    $ptrRecordString = ""

    Write-Host "Starting DNS A record removal for $HostName.$Domain" -ForegroundColor Cyan

    $aRecordExists = Get-DnsServerResourceRecord -ZoneName $Domain -ComputerName $DnsServer -Name $HostName -RRType "A" -ErrorAction SilentlyContinue

    if ($aRecordExists) {
        try {
            $IpAddress = $aRecordExists.RecordData.IPv4Address.ToString()
            Write-Host "Removing A record: $HostName.$Domain -> $IpAddress" -ForegroundColor Yellow
            Remove-DnsServerResourceRecord -ZoneName $Domain -ComputerName $DnsServer -Name $HostName -RRType "A" -RecordData $IpAddress -Force -Verbose
            $recordStatus = "A Record Removed"
            Write-Host "A record removed successfully" -ForegroundColor Green
        } catch {
            $error_message = "Error Removing A Record: $_"
            $recordStatus = "Failed to Remove A Record"
            Write-Host "Failed to remove A record: $_" -ForegroundColor Red
        }
    } else {
        $recordStatus = "A Record Not Found"
        Write-Host "A record not found for removal" -ForegroundColor Yellow
    }

    $ptrDnsServer = Get-PtrDnsServer -Domain $Domain -DnsServer $DnsServer

    if ($ManagePtr -and $IpAddress -and $recordStatus -eq "A Record Removed") {
        Write-Host "PTR management is enabled, proceeding with PTR record removal..." -ForegroundColor Cyan

        try {
            $zoneDetection = Find-OptimalPtrZone -IpAddress $IpAddress -PtrDnsServer $ptrDnsServer

            if ($zoneDetection.zone) {
                $ptrZoneDetected = $zoneDetection.zone
                $ptrOperationStatus = "Zone detected: $($zoneDetection.zone_type)"

                Write-Host "Checking for existing PTR record in zone: $($zoneDetection.zone)" -ForegroundColor Cyan
                $existingPtrRecord = Get-PtrRecord -IpAddress $IpAddress -PtrDnsServer $ptrDnsServer -ZoneInfo $zoneDetection

                if ($existingPtrRecord -and $existingPtrRecord -ne "PTR Record Not Found" -and -not ($existingPtrRecord -is [hashtable])) {
                    Write-Host "Found existing PTR record: $existingPtrRecord" -ForegroundColor Yellow

                    if ($existingPtrRecord -eq "$HostName.$Domain" -or $existingPtrRecord -eq "$HostName.$Domain.") {
                        try {
                            Write-Host "Removing matching PTR record: Name=$($zoneDetection.record_name), Zone=$($zoneDetection.zone)" -ForegroundColor Yellow
                            Remove-DnsServerResourceRecord -ZoneName $zoneDetection.zone -ComputerName $ptrDnsServer -Name $zoneDetection.record_name -RRType "PTR" -Force -Verbose

                            $ptrOperationStatus += " - PTR record removed successfully"
                            $ptrRecordString = "PTR record removed"
                            Write-Host "PTR record removed successfully" -ForegroundColor Green
                        } catch {
                            $error_message += "`nPTR Removal Error: $_"
                            $ptrOperationStatus += " - PTR removal failed"
                            $ptrRecordString = "PTR removal failed"
                            Write-Host "Failed to remove PTR record: $_" -ForegroundColor Red
                        }
                    } else {
                        $ptrOperationStatus += " - PTR record points to different hostname, preserved for safety"
                        $ptrRecordString = "PTR preserved (points to: $existingPtrRecord)"
                        Write-Host "WARNING: PTR record points to different hostname ($existingPtrRecord), preserving for safety" -ForegroundColor Magenta
                    }
                } else {
                    $ptrOperationStatus += " - No matching PTR record found"
                    $ptrRecordString = "No PTR record found to remove"
                    Write-Host "No PTR record found for removal" -ForegroundColor Yellow
                }
            } else {
                $ptrZoneDetected = "None"
                $ptrOperationStatus = "No suitable PTR zones found - PTR removal skipped"
                $ptrRecordString = "PTR zone not available"
                Write-Host "WARNING: No suitable PTR zones found, PTR removal skipped" -ForegroundColor Magenta
            }
        } catch {
            $error_message += "`nPTR Zone Detection Error: $_"
            $ptrOperationStatus = "PTR zone detection failed"
            $ptrRecordString = "PTR operation failed"
            Write-Host "PTR zone detection failed: $_" -ForegroundColor Red
        }
    } else {
        if (-not $ManagePtr) {
            Write-Host "PTR management is disabled, skipping PTR record removal" -ForegroundColor Yellow
            $ptrOperationStatus = "PTR management disabled"
            $ptrRecordString = "PTR management disabled"
        } elseif (-not $IpAddress) {
            $ptrOperationStatus = "No IP address available for PTR removal"
            $ptrRecordString = "No IP address for PTR removal"
        } else {
            $ptrOperationStatus = "A record removal failed - PTR removal skipped for safety"
            $ptrRecordString = "PTR removal skipped (A record removal failed)"
            Write-Host "A record removal failed, skipping PTR removal for safety" -ForegroundColor Yellow
        }
        $ptrZoneDetected = "N/A"
    }

    Format-DnsReport -Action "Remove" -RecordStatus $recordStatus -Domain $Domain -DnsServer $DnsServer -PtrDnsServer $ptrDnsServer -HostName $HostName -IpAddress $IpAddress -PTR $ptrRecordString -ErrorMessage $error_message -PtrManagementEnabled $ManagePtr -PtrZoneDetected $ptrZoneDetected -PtrOperationStatus $ptrOperationStatus
}

Write-Host "=== DNS Management Script Started ===" -ForegroundColor Cyan
Write-Host "Action: $Action" -ForegroundColor White
Write-Host "Domain: $Domain" -ForegroundColor White
Write-Host "Hostname: $HostName" -ForegroundColor White
if ($IpAddress) { Write-Host "IP Address: $IpAddress" -ForegroundColor White }
Write-Host "TTL: $TTL seconds" -ForegroundColor White
Write-Host "PTR Management: $ManagePtr" -ForegroundColor White
Write-Host "======================================" -ForegroundColor Cyan

try {
    $DnsServer = Get-DnsServer -Domain $Domain
    Write-Host "DNS Server selected: $DnsServer" -ForegroundColor Green

    switch ($Action.ToLower()) {
        "verify" {
            Write-Host "Executing DNS record verification..." -ForegroundColor Cyan
            Verify-DnsRecord -Domain $Domain -HostName $HostName -DnsServer $DnsServer -ManagePtr $ManagePtr
        }
        "add" {
            if (-not $IpAddress) {
                Write-Host "ERROR: IP address is required for 'add' action" -ForegroundColor Red
                $errorOutput = @{
                    "action" = "Add"
                    "status" = "Failed"
                    "errors" = "IP address is required for 'add' action"
                    "ptr_management_enabled" = $ManagePtr
                }
                $errorOutput | ConvertTo-Json -Compress
                exit 1
            }
            Write-Host "Executing DNS record addition..." -ForegroundColor Cyan
            Add-DnsRecord -Domain $Domain -HostName $HostName -IpAddress $IpAddress -DnsServer $DnsServer -TTL $TTL -ManagePtr $ManagePtr
        }
        "remove" {
            Write-Host "Executing DNS record removal..." -ForegroundColor Cyan
            Remove-DnsARecord -Domain $Domain -HostName $HostName -DnsServer $DnsServer -ManagePtr $ManagePtr
        }
        default {
            Write-Host "ERROR: Invalid action specified. Please use 'verify', 'add', or 'remove'." -ForegroundColor Red
            $errorOutput = @{
                "action" = $Action
                "status" = "Failed"
                "errors" = "Invalid action specified. Valid actions: verify, add, remove"
                "ptr_management_enabled" = $ManagePtr
            }
            $errorOutput | ConvertTo-Json -Compress
            exit 1
        }
    }
} catch {
    Write-Host "CRITICAL ERROR: $_" -ForegroundColor Red
    $errorOutput = @{
        "action" = $Action
        "status" = "Critical Failure"
        "errors" = "Script execution failed: $_"
        "ptr_management_enabled" = $ManagePtr
    }
    $errorOutput | ConvertTo-Json -Compress
    exit 1
}

Write-Host "=== DNS Management Script Completed ===" -ForegroundColor Cyan