---
# Default values for DNS Management
# Author: CES Operational Excellence Team
# Contributors: <PERSON> (7409)

# Include all configuration files
include_configs:
  - domains.yml
  - domain_credentials.yml
  - credentials_config.yml
  - email_config.yml
  - log_config.yml
  - operations_config.yml
  - powershell_config.yml
  - report_config.yml

# Project defaults
project_defaults:
  # Project information
  project:
    name: "DNS Management"
    version: "2.0.0"
    description: "DNS Management System for managing DNS records across multiple domains"
    author: "CES Operational Excellence Team"
    contributors:
      - "<PERSON> (7409)"

  # Default operation parameters
  operation_params:
    operation: "verify"
    record_type: "a"
    hostname: ""
    domain: ""
    ip_address: ""
    cname_target: ""
    ttl: 3600
    description: "Managed by Ansible DNS Management"
    manage_ptr: true
    force: false
    ticket: ""

  # Multi-domain operation parameters
  multi_domain_params:
    domains: ""
    hostnames: ""
    ip_addresses: ""
    cname_targets: ""

  # Reporting parameters
  reporting:
    generate_report: true
    email_report: false
    email_recipient: ""

  # Logging parameters
  logging:
    log_level: "Info"
    store_logs_target_server: true
    email_logs: false

  # Testing parameters
  testing:
    testing_mode: false
