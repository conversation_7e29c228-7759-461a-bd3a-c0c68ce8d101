---
# Operations configuration for DNS Management
# Author: CES Operational Excellence Team
# Contributors: <PERSON> (7409)

# Operation definitions
operations:
  # Add operation
  add:
    description: "Add a DNS record"
    required_params:
      all: ["hostname", "domain", "ticket"]
      a: ["ip_address"]
      ptr: ["ip_address"]
      cname: ["cname_target"]
    validation:
      check_exists: true
      error_if_exists: true

  # Remove operation
  remove:
    description: "Remove a DNS record"
    required_params:
      all: ["hostname", "domain", "ticket"]
      a: ["ip_address"]
      ptr: ["ip_address"]
      cname: []
    validation:
      check_exists: true
      error_if_not_exists: false
      check_connectivity: true

  # Update operation
  update:
    description: "Update a DNS record"
    required_params:
      all: ["hostname", "domain", "ticket"]
      a: ["ip_address"]
      ptr: ["ip_address"]
      cname: ["cname_target"]
    validation:
      check_exists: true
      error_if_not_exists: true

  # Verify operation
  verify:
    description: "Verify a DNS record exists"
    required_params:
      all: ["hostname", "domain", "ticket"]
      a: []
      ptr: []
      cname: []
    validation:
      check_exists: true
      error_if_not_exists: false

# Record type definitions
record_types:
  # A record
  a:
    description: "Address record mapping hostname to IP address"
    manages_ptr: true
    required_params: ["ip_address"]

  # PTR record
  ptr:
    description: "Pointer record mapping IP address to hostname"
    managed_by_a: true
    required_params: ["ip_address"]

  # CNAME record
  cname:
    description: "Canonical name record mapping alias to target hostname"
    manages_ptr: false
    required_params: ["cname_target"]

# Multi-domain operation settings
multi_domain:
  # Validation rules
  validation:
    match_count: true  # Ensure counts of domains, hostnames, and IP addresses match
    one_to_one: true   # Ensure 1:1 mapping between domains and hostnames

  # Parallel execution settings
  parallel:
    enabled: true
    max_parallel: 5
    timeout: 1800  # 30 minutes

# Operation defaults
operation_defaults:
  # Default operation
  operation: "verify"

  # Default record type
  record_type: "a"

  # Default TTL
  ttl: 3600

  # Default description
  description: "Managed by Ansible DNS Management"

  # Default PTR management
  manage_ptr: true

  # Default force flag
  force: false
