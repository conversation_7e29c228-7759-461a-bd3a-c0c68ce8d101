---
# Credentials configuration for DNS Management
# Author: CES Operational Excellence Team
# Contributors: <PERSON> (7409)

# Credential selection mechanism
credential_selection:
  # Selection strategy
  strategy: "domain"  # Options: domain, environment, manual

  # No default credentials - explicit credentials required for each domain

# Credential storage
credential_storage:
  # Storage method
  method: "ansible_vault"  # Options: ansible_vault, cyberark, hashicorp_vault

  # Vault file
  vault_file: "{{ playbook_dir }}/../vars/vault.yml"

# Credential security
credential_security:
  # Whether to mask credentials in logs
  mask_credentials: true

  # Whether to use no_log for credential-related tasks
  use_no_log: true

  # Whether to clear credentials after use
  clear_after_use: true

# Credential mapping
credential_mapping:
  # Production domains
  healthgrp.com.sg:
    username: "{{ var_dns_healthgrp_username | default('<EMAIL>') }}"
    password: "{{ var_dns_healthgrp_password }}"

  hcloud.healthgrp.com.sg:
    username: "{{ var_dns_hcloud_username | default('<EMAIL>') }}"
    password: "{{ var_dns_hcloud_password }}"

  iltc.healthgrp.com.sg:
    username: "{{ var_dns_iltc_username | default('<EMAIL>') }}"
    password: "{{ var_dns_iltc_password }}"

  healthgrpextp.com.sg:
    username: "{{ var_dns_healthgrpextp_username | default('<EMAIL>') }}"
    password: "{{ var_dns_healthgrpextp_password }}"

  exthealthgrp.com.sg:
    username: "{{ var_dns_exthealthgrp_username | default('<EMAIL>') }}"
    password: "{{ var_dns_exthealthgrp_password }}"

  nhg.local:
    username: "{{ var_dns_nhg_username | default('<EMAIL>') }}"
    password: "{{ var_dns_nhg_password }}"

  aic.local:
    username: "{{ var_dns_aic_username | default('<EMAIL>') }}"
    password: "{{ var_dns_aic_password }}"

  shses.shs.com.sg:
    username: "{{ var_dns_shses_username | default('<EMAIL>') }}"
    password: "{{ var_dns_shses_password }}"

  # Staging domains
  devhealthgrp.com.sg:
    username: "{{ var_dns_devhealthgrp_username | default('<EMAIL>') }}"
    password: "{{ var_dns_devhealthgrp_password }}"

  healthgrpexts.com.sg:
    username: "{{ var_dns_healthgrpexts_username | default('<EMAIL>') }}"
    password: "{{ var_dns_healthgrpexts_password }}"

  nnstg.local:
    username: "{{ var_dns_nnstg_username | default('<EMAIL>') }}"
    password: "{{ var_dns_nnstg_password }}"

  ses.shsu.com.sg:
    username: "{{ var_dns_ses_username | default('<EMAIL>') }}"
    password: "{{ var_dns_ses_password }}"
