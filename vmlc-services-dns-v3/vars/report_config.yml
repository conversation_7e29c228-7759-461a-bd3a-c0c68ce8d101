---
# Report configuration for DNS Management
# Author: CES Operational Excellence Team
# Contributors: <PERSON> (7409)

# Report settings
report_settings:
  # Report directories
  directories:
    reports: "{{ playbook_dir }}/../reports"
    templates: "{{ playbook_dir }}/../templates/reports"

  # Report file naming format
  format:
    date_format: "%Y%m%d"  # YYYYMMDD
    separator: "_"
    extension: ".pdf"

  # Report types
  types:
    standard: "STANDARD"
    consolidated: "CONSOLIDATED"

  # Report templates
  templates:
    standard: "dns_report.html.j2"
    consolidated: "consolidated_report.html.j2"

  # Report content
  content:
    # Standard report sections
    standard_sections:
      - title: "Operation Details"
        fields:
          - name: "Operation"
            value: "{{ operation | default('unknown') | capitalize }}"
          - name: "Record Type"
            value: "{{ record_type | default('unknown') | upper }}"
          - name: "Hostname"
            value: "{{ hostname | default('unknown') }}.{{ domain | default('unknown') }}"
          - name: "Ticket"
            value: "{{ ticket | default('INC-123456') }}"

      - title: "Result Summary"
        fields:
          - name: "Status"
            value: "{{ 'Successful' if dns_operation_result is defined and dns_operation_result.success else 'Failed' }}"
          - name: "Message"
            value: "{{ dns_operation_result.message if dns_operation_result is defined else 'No operation result available' }}"

      - title: "Record Details"
        fields:
          - name: "Domain"
            value: "{{ domain | default('unknown') }}"
          - name: "Hostname"
            value: "{{ hostname | default('unknown') }}"
          - name: "FQDN"
            value: "{{ hostname | default('unknown') }}.{{ domain | default('unknown') }}"
          - name: "Record Type"
            value: "{{ record_type | default('unknown') | upper }}"
          - name: "IP Address"
            value: "N/A"
            condition: "{{ record_type | default('unknown') | lower in ['a', 'ptr'] }}"
          - name: "CNAME Target"
            value: "N/A"
            condition: "{{ record_type | default('unknown') | lower == 'cname' }}"
          - name: "TTL"
            value: "3600"

      - title: "Execution Information"
        fields:
          - name: "Execution Time"
            value: "{{ ansible_date_time.iso8601 }}"
          - name: "Executed By"
            value: "{{ ansible_user_id }}"
          - name: "Target Server"
            value: "{{ dns_server | default('unknown') }}"

    # Consolidated report sections
    consolidated_sections:
      - title: "Operation Summary"
        fields:
          - name: "Operation"
            value: "{{ operation | default('unknown') | capitalize }}"
          - name: "Record Type"
            value: "{{ record_type | default('unknown') | upper }}"
          - name: "Ticket"
            value: "{{ ticket | default('INC-123456') }}"
          - name: "Total Domains"
            value: "{{ domains | default('') | split(',') | length if domains is defined and domains else 0 }}"
          - name: "Successful Operations"
            value: "{{ consolidated_results | default([]) | selectattr('success', 'equalto', true) | list | length if consolidated_results is defined else 0 }}"
          - name: "Failed Operations"
            value: "{{ consolidated_results | default([]) | selectattr('success', 'equalto', false) | list | length if consolidated_results is defined else 0 }}"

  # PDF generation
  pdf:
    page_size: "A4"
    orientation: "portrait"
    margins:
      top: "20mm"
      right: "20mm"
      bottom: "20mm"
      left: "20mm"

# Report configuration flags
report_flags:
  # Whether to generate reports
  generate_report: true

  # Whether to generate PDF reports (currently disabled, only HTML reports are generated)
  # Set to true to see placeholder message, will be implemented in the future
  generate_pdf: false

  # Whether to include detailed information
  include_details: true

  # Whether to include system information
  include_system_info: true

  # Future PDF generation options (not currently used)
  pdf_generation:
    # Method to use for PDF generation (options will be: weasyprint, libreoffice, chrome, api)
    method: "none"

    # Command to use for PDF generation (will be populated based on method)
    command: ""

    # Whether to use a Python script for PDF generation
    use_python_script: false
