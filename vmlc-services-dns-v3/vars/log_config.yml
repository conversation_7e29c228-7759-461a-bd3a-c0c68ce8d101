---
# Log configuration for DNS Management
# Author: CES Operational Excellence Team
# Contributors: <PERSON> (7409)

# Log settings
log_settings:
  # Log directories
  directories:
    ansible: "{{ playbook_dir }}/../logs/ansible"
    powershell: "{{ playbook_dir }}/../logs/powershell"
    progress: "{{ playbook_dir }}/../logs/progress"
    archive: "{{ playbook_dir }}/../logs/archive"

  # Log file naming format
  format:
    date_format: "%Y%m%d"  # YYYYMMDD
    separator: "_"
    extension: ".log"

  # Log types
  types:
    ansible: "ANSIBLE"
    powershell: "POWERSHELL"
    progress: "PROGRESS"
    report: "REPORT"

  # Log levels
  levels:
    error: "ERROR"
    warning: "WARNING"
    info: "INFO"
    debug: "DEBUG"

  # Target server log path
  target_server:
    base_path: "C:\\OE_AAP_LOGS\\"

  # Log rotation
  rotation:
    # Maximum log age (in days)
    max_age: 30

    # Maximum log size (in MB)
    max_size: 10

    # Number of log files to keep
    max_files: 10

  # Log format
  format_string:
    ansible: "[{timestamp}] [{level}] [{operation}] [{record_type}] [{hostname}.{domain}] {message}"
    powershell: "[{timestamp}] [{level}] [{operation}] [{record_type}] [{hostname}.{domain}] {message}"

# Log configuration flags
log_flags:
  # Whether to store logs on target server
  store_logs_target_server: true

  # Whether to archive logs
  archive_logs: true

  # Whether to rotate logs
  rotate_logs: true

  # Default log level
  default_log_level: "Info"

  # Whether to log progress
  log_progress: true

  # Whether to show progress in console
  show_progress: true
