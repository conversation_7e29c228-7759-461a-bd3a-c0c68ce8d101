---
# PowerShell configuration for DNS Management
# Author: CES Operational Excellence Team
# Contributors: <PERSON> (7409)

# PowerShell settings
powershell_settings:
  # Script paths
  scripts:
    dns_management: "{{ playbook_dir }}/../scripts/set-dns.ps1"
    connectivity_test: "{{ playbook_dir }}/../scripts/test-connectivity.ps1"

  # Default parameters
  defaults:
    ttl: 3600
    description: "Managed by Ansible DNS Management"
    log_level: "Info"

  # Log paths
  logs:
    default_path: "C:\\Temp\\dns_management.log"

  # Error handling
  error_handling:
    max_retries: 3
    retry_interval: 5  # seconds

  # Timeouts
  timeouts:
    connection: 30  # seconds
    operation: 120  # seconds
    ping: 1000  # milliseconds

  # Special configurations
  special_configs:
    # Special PTR handling for specific domains
    ptr_handling:
      shses.shs.com.sg: "sesdcvpsys11.shs.com.sg"
      ses.shsu.com.sg: "shdcvsys22h1.shsu.com.sg"

# PowerShell execution flags
powershell_flags:
  # Whether to use verbose output
  verbose: false

  # Whether to manage PTR records
  manage_ptr: true

  # Whether to force operations
  force: false

  # Whether to return results as JSON
  as_json: true
