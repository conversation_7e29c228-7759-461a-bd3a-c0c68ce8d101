---
# Email configuration for DNS Management
# Author: CES Operational Excellence Team
# Contributors: <PERSON> Bin <PERSON> (7409)

# Email settings
email_settings:
  # SMTP server configuration
  smtp:
    # SMTP settings
    port: 25
    from: "<EMAIL>"

    # DC-specific SMTP servers
    dc_servers:
      hdc1: "asmtp.hcloud.healthgrp.com.sg"
      hdc2: "fsmtp.hcloud.healthgrp.com.sg"

    # Default DC to use if no matching DC is found
    default_dc: "hdc1"

  # Email recipients
  recipients:
    # Domain-specific email recipients
    domain_specific:
      # SHS domains
      shses.shs.com.sg: "<EMAIL>"
      ses.shsu.com.sg: "<EMAIL>"

      # Default recipient for all other domains
      default: "<EMAIL>"

    # Testing mode email recipient
    testing: "<EMAIL>"

    # BCC recipient for all emails
    bcc: "m<PERSON><PERSON><PERSON>.<EMAIL>"

  # Email templates
  templates:
    # Template directory
    directory: "{{ playbook_dir }}/../templates/emails"

    # Email subject templates
    subjects:
      # Report email subject template
      report: "DNS Management Report - {operation} {record_type} Record for {hostname}.{domain}"

      # Consolidated report email subject template
      consolidated_report: "DNS Management Consolidated Report - {operation} {record_type} Records"

      # Logs email subject template
      logs: "DNS Management Logs - {operation} {record_type} Record for {hostname}.{domain}"

    # Email body templates
    bodies:
      # Report email body template
      report: "report_email_body.j2"

      # Consolidated report email body template
      consolidated_report: "consolidated_report_email_body.j2"

      # Logs email body template
      logs: "logs_email_body.j2"

  # Attachments
  attachments:
    # Maximum attachment size (in MB)
    max_size: 10

    # Attachment types
    types:
      - "pdf"
      - "log"
      - "html"

# Email configuration flags
email_flags:
  # Whether to generate a report
  generate_report: true

  # Whether to email the report
  email_report: false

  # Whether to email logs
  email_logs: false

  # Whether to use testing mode
  testing_mode: false

  # Whether to send emails only to the test recipient
  send_to_test_only: false

  # Whether to include BCC
  include_bcc: true
