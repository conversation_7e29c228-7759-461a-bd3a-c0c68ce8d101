---
# Task for generating consolidated reports
# Author: CES Operational Excellence Team
# Contributors: <PERSON> (7409)

# Ensure report directory exists
- name: Ensure report directory exists
  ansible.builtin.file:
    path: "{{ report_settings.directories.reports }}"
    state: directory
    mode: '0755'
  delegate_to: localhost
  run_once: true

# Set consolidated report filename
- name: Set consolidated report filename
  ansible.builtin.set_fact:
    consolidated_report_filename: "{{ log_date }}_{{ log_ticket }}_MULTI_DOMAIN_{{ record_type | upper }}_{{ operation | upper }}_{{ report_settings.types.consolidated }}{{ report_settings.format.extension }}"
    consolidated_report_path: "{{ report_settings.directories.reports }}/{{ log_date }}_{{ log_ticket }}_MULTI_DOMAIN_{{ record_type | upper }}_{{ operation | upper }}_{{ report_settings.types.consolidated }}{{ report_settings.format.extension }}"

# Generate consolidated HTML report
- name: Generate consolidated HTML report
  ansible.builtin.template:
    src: "{{ report_settings.templates.consolidated }}"
    dest: "{{ report_settings.directories.reports }}/{{ log_date }}_{{ log_ticket }}_MULTI_DOMAIN_{{ record_type | upper }}_{{ operation | upper }}_{{ report_settings.types.consolidated }}.html"
    mode: '0644'
  delegate_to: localhost
  run_once: true
  register: consolidated_html_report

# PDF generation placeholder for future implementation
- name: Display PDF generation placeholder message
  ansible.builtin.debug:
    msg: "PDF generation is currently disabled. Only HTML consolidated report is available."
  when: report_flags.generate_pdf | bool

# Set report path to HTML report for now
- name: Set consolidated report path to HTML report
  ansible.builtin.set_fact:
    consolidated_report_path: "{{ consolidated_html_report.dest }}"
  when: report_flags.generate_pdf | bool

# Display report generation status
- name: Display report generation status
  ansible.builtin.debug:
    msg: "HTML Consolidated report generated: {{ consolidated_html_report.dest }}"
