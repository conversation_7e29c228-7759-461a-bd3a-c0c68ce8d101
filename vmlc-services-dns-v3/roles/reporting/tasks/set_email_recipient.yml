---
# Tasks for setting email recipient based on domain
# Author: CES Operational Excellence Team
# Contributors: <PERSON> Bin <PERSON> (7409)

- name: Set domain for email recipient selection
  ansible.builtin.set_fact:
    email_domain: "{{ current_domain | default(domain) }}"
  when: current_domain is defined or domain is defined

- name: Set email recipient based on domain (for specific domains)
  ansible.builtin.set_fact:
    dynamic_email_recipient: "{{ email_settings.recipients.domain_specific[email_domain] }}"
    email_bcc: "{{ email_settings.recipients.bcc }}"
  when:
    - email_domain is defined
    - email_domain in email_settings.recipients.domain_specific
    - not testing_mode | default(email_flags.testing_mode) | bool
    - not send_to_test_only | default(email_flags.send_to_test_only) | bool

- name: Set default email recipient (for other domains)
  ansible.builtin.set_fact:
    dynamic_email_recipient: "{{ email_settings.recipients.domain_specific.default }}"
    email_bcc: "{{ email_settings.recipients.bcc }}"
  when:
    - email_domain is defined
    - email_domain not in email_settings.recipients.domain_specific
    - not testing_mode | default(email_flags.testing_mode) | bool
    - not send_to_test_only | default(email_flags.send_to_test_only) | bool

- name: Set testing email recipient (when testing mode or send_to_test_only is enabled)
  ansible.builtin.set_fact:
    dynamic_email_recipient: "{{ email_settings.recipients.testing }}"
    email_bcc: "{{ email_settings.recipients.bcc }}"
  when: testing_mode | default(email_flags.testing_mode) | bool or send_to_test_only | default(email_flags.send_to_test_only) | bool

- name: Override email_recipient with dynamic recipient
  ansible.builtin.set_fact:
    email_recipient: "{{ dynamic_email_recipient }}"
  when: dynamic_email_recipient is defined

- name: Display selected email recipient
  ansible.builtin.debug:
    msg: >-
      Email will be sent to: {{ email_recipient }} (BCC: {{ email_bcc }})
      {% if testing_mode | default(email_flags.testing_mode) | bool %}
      (TESTING MODE)
      {% elif send_to_test_only | default(email_flags.send_to_test_only) | bool %}
      (SENDING TO TEST RECIPIENT ONLY)
      {% endif %}
  when: email_report | default(email_flags.email_report) | bool or email_logs | default(email_flags.email_logs) | bool
