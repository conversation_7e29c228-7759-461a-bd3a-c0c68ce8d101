---
# Tasks for uploading logs to target server based on environment
# Author: CES Operational Excellence Team
# Contributors: <PERSON> Bin <PERSON> (7409)

- name: Include log configuration
  ansible.builtin.include_vars:
    file: "{{ playbook_dir }}/../vars/log_config.yml"

- name: Include standardized log file paths
  ansible.builtin.include_tasks: "{{ roles_path }}/common/tasks/set_log_paths.yml"

- name: Set job ID for log filenames
  ansible.builtin.set_fact:
    job_id: "{{ ansible_job_id | default(lookup('env', 'ANSIBLE_JOB_ID')) | default('NOJOB') }}"

- name: Create target server log file names with job ID
  ansible.builtin.set_fact:
    target_ansible_log_filename: "{{ job_id }}_{{ log_date }}_{{ log_ticket }}_DNS_{{ log_settings.types.ansible }}_{{ log_operation }}.log"
    target_powershell_log_filename: "{{ job_id }}_{{ log_date }}_{{ log_ticket }}_DNS_{{ log_settings.types.powershell }}_{{ log_operation }}.log"

- name: Set target server based on environment
  ansible.builtin.set_fact:
    target_server: "{{ domain_config[domain].admt_server }}"
  when: domain is defined and domain in domain_config and not is_multi_domain

- name: Set target server for multi-domain operations
  ansible.builtin.set_fact:
    target_server: "{{ domain_selection.default_admt_server[environment] }}"
  when: is_multi_domain

- name: Ensure target log directory exists on target server
  ansible.builtin.win_file:
    path: "{{ log_settings.target_server.base_path }}"
    state: directory
  delegate_to: "{{ target_server }}"
  ignore_errors: true

- name: Copy Ansible logs to target server
  ansible.builtin.win_copy:
    src: "{{ ansible_log_path }}"
    dest: "{{ log_settings.target_server.base_path }}{{ target_ansible_log_filename }}"
    remote_src: false
  delegate_to: "{{ target_server }}"
  ignore_errors: true

- name: Copy PowerShell logs to target server
  ansible.builtin.win_copy:
    src: "{{ powershell_log_path }}"
    dest: "{{ log_settings.target_server.base_path }}{{ target_powershell_log_filename }}"
    remote_src: false
  delegate_to: "{{ target_server }}"
  ignore_errors: true
  when: domains is not defined or domains | length == 0

- name: Copy PowerShell logs for multi-domain operations to target server
  ansible.builtin.win_copy:
    src: "{{ item }}"
    dest: "{{ log_settings.target_server.base_path }}{{ job_id }}_{{ log_date }}_{{ log_ticket }}_DNS_{{ log_settings.types.powershell }}_{{ log_operation }}_{{ item | basename }}"
    remote_src: false
  with_fileglob:
    - "{{ log_settings.directories.powershell }}/*{{ log_date }}*{{ log_ticket }}*{{ log_settings.types.powershell }}*.log"
  delegate_to: "{{ target_server }}"
  ignore_errors: true
  when: domains is defined and domains | length > 0

- name: Display log storage information
  ansible.builtin.debug:
    msg: "Logs have been stored on {{ target_server }} in {{ log_settings.target_server.base_path }} directory with filenames {{ target_ansible_log_filename }} and {{ target_powershell_log_filename }}"
