---
# Main tasks file for reporting role
# Author: CES Operational Excellence Team
# Contributors: <PERSON> (7409)

# Generate report based on operation type
- name: Generate report based on operation type
  block:
    # Generate standard report
    - name: Generate standard report
      ansible.builtin.include_tasks: generate_report.yml
      when: not is_multi_domain
      
    # Generate consolidated report
    - name: Generate consolidated report
      ansible.builtin.include_tasks: generate_consolidated_report.yml
      when: is_multi_domain and consolidated_results is defined and consolidated_results | length > 0
  when: generate_report | bool

# Send emails if configured
- name: Send emails if configured
  block:
    # Email report
    - name: Email report
      ansible.builtin.include_tasks: email_report.yml
      when: generate_report | bool and email_report | bool
      
    # Email logs
    - name: Email logs
      ansible.builtin.include_tasks: email_logs.yml
      when: email_logs | bool
  when: email_report | bool or email_logs | bool
  
# Upload logs to target server if configured
- name: Upload logs to target server if configured
  ansible.builtin.include_tasks: upload_logs_to_target_server.yml
  when: store_logs_target_server | bool
