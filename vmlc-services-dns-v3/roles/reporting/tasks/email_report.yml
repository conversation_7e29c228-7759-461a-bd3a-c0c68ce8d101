---
# Task for emailing operation reports
# Author: CES Operational Excellence Team
# Contributors: <PERSON> (7409)

# Include email configuration
- name: Include email configuration
  ansible.builtin.include_vars:
    file: "{{ playbook_dir }}/../vars/email_config.yml"

# Validate email recipient is provided
- name: Validate email recipient is provided
  ansible.builtin.assert:
    that:
      - email_recipient != ""
    fail_msg: "Email recipient is required for sending reports"

# Include dynamic email recipient selection
- name: Include dynamic email recipient selection
  ansible.builtin.include_tasks: set_email_recipient.yml

# Include dynamic SMTP server selection
- name: Include dynamic SMTP server selection
  ansible.builtin.include_tasks: set_smtp_server.yml

# Set initial email subject
- name: Set initial email subject
  ansible.builtin.set_fact:
    email_subject: "{{ email_settings.templates.subjects.report }}"

# Format email subject with operation
- name: Format email subject with operation
  ansible.builtin.set_fact:
    email_subject: "{{ email_subject | replace('{operation}', operation | capitalize) }}"

# Format email subject with record type
- name: Format email subject with record type
  ansible.builtin.set_fact:
    email_subject: "{{ email_subject | replace('{record_type}', record_type | upper) }}"

# Format email subject with hostname
- name: Format email subject with hostname
  ansible.builtin.set_fact:
    email_subject: "{{ email_subject | replace('{hostname}', hostname) }}"

# Format email subject with domain
- name: Format email subject with domain
  ansible.builtin.set_fact:
    email_subject: "{{ email_subject | replace('{domain}', domain) }}"

# Generate email body from template
- name: Generate email body from template
  ansible.builtin.template:
    src: "{{ email_settings.templates.bodies.report }}"
    dest: "{{ report_settings.directories.reports }}/{{ log_date }}_{{ log_ticket }}_{{ hostname | default('unknown') }}_{{ domain | default('unknown') }}_{{ record_type | default('unknown') | upper }}_{{ operation | default('unknown') | upper }}_EMAIL_BODY.html"
    mode: '0644'
  delegate_to: localhost
  register: email_body_file
  ignore_errors: true

# Read email body content
- name: Read email body content
  ansible.builtin.slurp:
    src: "{{ email_body_file.dest }}"
  register: email_body_content
  delegate_to: localhost
  when: email_body_file is defined and email_body_file.dest is defined
  ignore_errors: true

# Determine which report to attach
- name: Determine which report to attach
  ansible.builtin.set_fact:
    report_to_attach: >-
      {% if is_multi_domain %}
        {{ consolidated_html_report.dest if consolidated_html_report is defined else '' }}
      {% else %}
        {{ html_report.dest if html_report is defined else '' }}
      {% endif %}

# Send email with report
- name: Send email with report
  community.general.mail:
    host: "{{ smtp_server }}"
    port: "{{ smtp_port }}"
    from: "{{ smtp_from }}"
    to: "{{ email_recipient }}"
    bcc: "{{ email_settings.recipients.bcc if email_flags.include_bcc | bool else omit }}"
    subject: "{{ email_subject }}"
    body: "{{ email_body_content.content | b64decode if email_body_content is defined and email_body_content.content is defined else 'Email body generation failed. Please check the logs.' }}"
    subtype: html
    attach:
      - "{{ report_to_attach }}"
  ignore_errors: true
  register: email_result
  no_log: true
  delegate_to: localhost
  run_once: true
  when:
    - email_recipient is defined and email_recipient != ''
    - report_to_attach is defined and report_to_attach != ''

# Display email status
- name: Display email status
  ansible.builtin.debug:
    msg: >
      {% if email_result is defined and email_result.changed %}
      Email sent successfully to {{ email_recipient }}
      {% elif email_result is defined and email_result.msg is defined %}
      Failed to send email to {{ email_recipient }}. Error: {{ email_result.msg }}
      {% else %}
      Failed to send email or email not sent
      {% endif %}

# Register email status for summary
- name: Register email status for summary
  ansible.builtin.set_fact:
    email_status: "{{ email_result.msg | default('Unknown error') if email_result is defined and not email_result.changed else 'Success' if email_result is defined and email_result.changed else 'Not attempted' }}"
