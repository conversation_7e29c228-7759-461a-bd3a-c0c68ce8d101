---
# Task for generating operation reports
# Author: CES Operational Excellence Team
# Contributors: <PERSON> (7409)

# Ensure report directory exists
- name: Ensure report directory exists
  ansible.builtin.file:
    path: "{{ report_settings.directories.reports }}"
    state: directory
    mode: '0755'
  delegate_to: localhost
  run_once: true

# Set report filename
- name: Set report filename
  ansible.builtin.set_fact:
    report_filename: "{{ log_date }}_{{ log_ticket }}_{{ hostname }}_{{ domain }}_{{ record_type | upper }}_{{ operation | upper }}_{{ report_settings.types.standard }}{{ report_settings.format.extension }}"
    report_path: "{{ report_settings.directories.reports }}/{{ log_date }}_{{ log_ticket }}_{{ hostname }}_{{ domain }}_{{ record_type | upper }}_{{ operation | upper }}_{{ report_settings.types.standard }}{{ report_settings.format.extension }}"

# Generate HTML report
- name: Generate HTML report
  ansible.builtin.template:
    src: "{{ report_settings.templates.standard }}"
    dest: "{{ report_settings.directories.reports }}/{{ log_date }}_{{ log_ticket }}_{{ hostname }}_{{ domain }}_{{ record_type | upper }}_{{ operation | upper }}_{{ report_settings.types.standard }}.html"
    mode: '0644'
  delegate_to: localhost
  run_once: true
  register: html_report

# PDF generation placeholder for future implementation
- name: Display PDF generation placeholder message
  ansible.builtin.debug:
    msg: "PDF generation is currently disabled. Only HTML report is available."
  when: report_flags.generate_pdf | bool

# Set report path to HTML report for now
- name: Set report path to HTML report
  ansible.builtin.set_fact:
    report_path: "{{ html_report.dest }}"
  when: report_flags.generate_pdf | bool

# Display report generation status
- name: Display report generation status
  ansible.builtin.debug:
    msg: "HTML Report generated: {{ html_report.dest }}"
