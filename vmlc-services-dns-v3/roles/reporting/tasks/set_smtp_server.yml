---
# Tasks for setting SMTP server based on DC location
# Author: CES Operational Excellence Team
# Contributors: <PERSON> Bin <PERSON> (7409)

- name: Determine DC location from domain configuration
  ansible.builtin.set_fact:
    dc_location: "{{ domains[domain].dc | default('') }}"
  when: domain is defined and domain in domains

- name: Set SMTP server based on DC location
  ansible.builtin.set_fact:
    smtp_server: "{{ email_settings.smtp.dc_servers[dc_location] }}"
    smtp_port: "{{ email_settings.smtp.port }}"
    smtp_from: "{{ email_settings.smtp.from }}"
  when: dc_location is defined and dc_location in email_settings.smtp.dc_servers

- name: Set default SMTP server if no DC location is found
  ansible.builtin.set_fact:
    smtp_server: "{{ email_settings.smtp.dc_servers[email_settings.smtp.default_dc] }}"
    smtp_port: "{{ email_settings.smtp.port }}"
    smtp_from: "{{ email_settings.smtp.from }}"
  when: smtp_server is not defined

- name: Display selected SMTP server
  ansible.builtin.debug:
    msg: >
      Using SMTP server: {{ smtp_server }} for DC location: {{ dc_location | default('unknown') }}
      {% if smtp_server == email_settings.smtp.dc_servers[email_settings.smtp.default_dc] and dc_location not in email_settings.smtp.dc_servers %}
      (Using default DC: {{ email_settings.smtp.default_dc }})
      {% endif %}
  when: email_report | bool or email_logs | bool
