# Report Summary
# Generated: {{ ansible_date_time.iso8601 }}
# Author: CES Operational Excellence Team
# Contributors: <PERSON> (7409)

## Report Details
- Report Type: {{ report_type | default('Standard') }}
- Report Path: {{ report_path | default(consolidated_report_path) }}
- Generated: {{ ansible_date_time.iso8601 }}

## Operation Details
- Operation: {{ operation | capitalize }}
- Record Type: {{ record_type | upper }}
{% if not is_multi_domain %}
- Hostname: {{ hostname }}.{{ domain }}
{% else %}
- Domains: {{ domains }}
- Hostnames: {{ hostnames }}
{% endif %}
- Ticket: {{ ticket }}

## Result Summary
{% if not is_multi_domain %}
- Status: {{ 'Success' if dns_operation_result.success else 'Failure' }}
- Message: {{ dns_operation_result.message }}
{% else %}
- Success Rate: {{ success_count }}/{{ total_count }} ({{ (success_count / total_count * 100) | int }}%)
- Successful Domains: {{ consolidated_results | selectattr('success', 'equalto', true) | map(attribute='domain') | list | join(', ') }}
- Failed Domains: {{ consolidated_results | selectattr('success', 'equalto', false) | map(attribute='domain') | list | join(', ') }}
{% endif %}

## Email Information
{% if email_report | bool %}
- Report Email: Sent to {{ email_recipient }}
- SMTP Server: {{ smtp_server }}
{% else %}
- Report Email: Not sent
{% endif %}
{% if email_logs | bool %}
- Logs Email: Sent to {{ email_recipient }}
{% else %}
- Logs Email: Not sent
{% endif %}

## Log Information
- Ansible Log: {{ ansible_log_path }}
- PowerShell Log: {{ powershell_log_path }}
{% if store_logs_target_server | bool %}
- Target Server: {{ target_server }}
- Target Log Path: {{ log_settings.target_server.base_path }}
{% endif %}
