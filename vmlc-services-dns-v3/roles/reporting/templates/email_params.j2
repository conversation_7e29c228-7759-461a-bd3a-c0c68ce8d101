# Email parameters for {{ email_type | default('report') }} email
# Generated: {{ ansible_date_time.iso8601 }}
# Author: CES Operational Excellence Team
# Contributors: <PERSON> (7409)

- host: {{ smtp_server }}
- port: {{ smtp_port }}
- from: {{ smtp_from }}
- to: {{ email_recipient }}
{% if email_flags.include_bcc | bool %}
- bcc: {{ email_settings.recipients.bcc }}
{% endif %}
- subject: {{ email_subject }}
- body: {{ email_body_file.dest }}
- attachments:
{% if email_type | default('report') == 'report' %}
  - {{ report_path if report_flags.generate_pdf | bool else html_report.dest }}
{% elif email_type | default('report') == 'consolidated_report' %}
  - {{ consolidated_report_path if report_flags.generate_pdf | bool else consolidated_html_report.dest }}
{% elif email_type | default('report') == 'logs' %}
  - {{ ansible_log_path }}
  - {{ powershell_log_path }}
{% endif %}
