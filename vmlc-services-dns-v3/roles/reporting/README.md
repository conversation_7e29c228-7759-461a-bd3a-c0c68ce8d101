# VMLC Services - DNS Management - Reporting Role

## Overview

The Reporting role handles report generation, email notifications, and log management for DNS operations. It supports standard and consolidated reports, email notifications, and log uploads.

## Tasks

- **main.yml**: Main entry point for reporting
- **generate_report.yml**: Generates standard reports
- **generate_consolidated_report.yml**: Generates consolidated reports
- **email_report.yml**: Sends report emails
- **email_logs.yml**: Sends log emails
- **set_email_recipient.yml**: Sets email recipient based on domain
- **set_smtp_server.yml**: Sets SMTP server based on DC location
- **upload_logs_to_target_server.yml**: Uploads logs to target server

## Templates

- **email_params.j2**: Template for email parameters
- **report_summary.j2**: Template for report summary

## Variables

### Input Variables

- `generate_report`: Whether to generate reports
- `email_report`: Whether to email reports
- `email_logs`: Whether to email logs
- `email_recipient`: Email recipient
- `testing_mode`: Whether to use testing mode
- `store_logs_target_server`: Whether to store logs on target server

### Output Variables

- `report_path`: Path to generated report
- `consolidated_report_path`: Path to consolidated report
- `email_result`: Result of email operation

## Dependencies

- common

## Author

CES Operational Excellence Team

## Contributors

- Muhammad Syazani Bin Mohamed Khairi (7409)
