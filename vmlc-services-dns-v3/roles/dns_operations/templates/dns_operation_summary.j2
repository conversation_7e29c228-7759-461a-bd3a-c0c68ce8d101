# DNS Operation Summary
# Generated: {{ ansible_date_time.iso8601 }}
# Author: CES Operational Excellence Team
# Contributors: <PERSON> Bin <PERSON> (7409)

## Operation Details
- Operation: {{ operation | capitalize }}
- Record Type: {{ record_type | upper }}
- Hostname: {{ hostname }}
- Domain: {{ domain }}
- FQDN: {{ hostname }}.{{ domain }}
{% if record_type | lower in ['a', 'ptr'] %}
- IP Address: {{ ip_address | default('N/A') }}
{% endif %}
{% if record_type | lower == 'cname' %}
- CNAME Target: {{ cname_target | default('N/A') }}
{% endif %}
- TTL: {{ ttl }}
- Description: {{ description }}
- Ticket: {{ ticket }}

## Result
- Status: {{ 'Success' if dns_operation_result.success else 'Failure' }}
- Message: {{ dns_operation_result.message }}
- Changed: {{ 'Yes' if dns_operation_result.changed else 'No' }}
- Timestamp: {{ dns_operation_result.timestamp }}

## Server Information
- DNS Server: {{ dns_server }}
{% if record_type | lower in ['a', 'ptr'] and manage_ptr | bool %}
- PTR DNS Server: {{ ptr_dns_server }}
- PTR Management: {{ 'Enabled' if manage_ptr | bool else 'Disabled' }}
{% endif %}
- ADMT Server: {{ admt_server }}

## Execution Information
- Execution Time: {{ ansible_date_time.iso8601 }}
- Executed By: {{ ansible_user_id }}
- Environment: {{ environment }}

## Log Files
- Ansible Log: {{ ansible_log_path }}
- PowerShell Log: {{ powershell_log_path }}
{% if report_path is defined %}
- Report: {{ report_path }}
{% endif %}
