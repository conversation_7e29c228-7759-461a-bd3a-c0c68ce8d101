# PowerShell parameters for {{ operation | capitalize }} operation on {{ record_type | upper }} record
# Generated: {{ ansible_date_time.iso8601 }}
# Author: CES Operational Excellence Team
# Contributors: <PERSON> (7409)

-Operation {{ operation | capitalize }} `
-RecordType {{ record_type | capitalize }} `
-Hostname {{ hostname }} `
-Domain {{ domain }} `
{% if record_type | lower in ['a', 'ptr'] and ip_address is defined and ip_address | length > 0 %}
-IPAddress {{ ip_address }} `
{% endif %}
{% if record_type | lower == 'cname' and cname_target is defined and cname_target | length > 0 %}
-Target {{ cname_target }} `
{% endif %}
{% if operation | lower in ['add', 'update'] %}
-TTL {{ ttl }} `
-Description "{{ description }}" `
{% endif %}
-DNSServer {{ dns_server }} `
{% if record_type | lower in ['a', 'ptr'] and manage_ptr | bool %}
-PTRDNSServer {{ ptr_dns_server }} `
-ManagePTR ${{ manage_ptr | bool | string }} `
{% endif %}
{% if operation | lower == 'remove' %}
-Force ${{ force_remove | bool | string }} `
{% endif %}
-AsJson `
-LogPath "{{ powershell_log_path }}" `
-LogLevel {{ current_log_level }}
