---
# Variables for dns_operations role
# Author: CES Operational Excellence Team
# Contributors: <PERSON> (7409)

# DNS operations variables
dns_operations_vars:
  # Operation types
  operation_types:
    - add
    - remove
    - update
    - verify
  
  # Record types
  record_types:
    - a
    - cname
    - ptr
  
  # Default TTL values
  default_ttl:
    a: 3600
    cname: 3600
    ptr: 3600
  
  # Default descriptions
  default_descriptions:
    a: "A record managed by Ansible DNS Management"
    cname: "CNAME record managed by Ansible DNS Management"
    ptr: "PTR record managed by Ansible DNS Management"
  
  # PowerShell script parameters
  powershell_params:
    add:
      a:
        - hostname
        - domain
        - ip_address
        - ttl
        - description
        - dns_server
        - ptr_dns_server
        - manage_ptr
      cname:
        - hostname
        - domain
        - cname_target
        - ttl
        - description
        - dns_server
      ptr:
        - hostname
        - domain
        - ip_address
        - ttl
        - description
        - dns_server
    remove:
      a:
        - hostname
        - domain
        - ip_address
        - dns_server
        - ptr_dns_server
        - manage_ptr
        - force_remove
      cname:
        - hostname
        - domain
        - dns_server
        - force_remove
      ptr:
        - hostname
        - domain
        - ip_address
        - dns_server
        - force_remove
    update:
      a:
        - hostname
        - domain
        - ip_address
        - ttl
        - description
        - dns_server
        - ptr_dns_server
        - manage_ptr
      cname:
        - hostname
        - domain
        - cname_target
        - ttl
        - description
        - dns_server
      ptr:
        - hostname
        - domain
        - ip_address
        - ttl
        - description
        - dns_server
    verify:
      a:
        - hostname
        - domain
        - ip_address
        - dns_server
      cname:
        - hostname
        - domain
        - cname_target
        - dns_server
      ptr:
        - hostname
        - domain
        - ip_address
        - dns_server
