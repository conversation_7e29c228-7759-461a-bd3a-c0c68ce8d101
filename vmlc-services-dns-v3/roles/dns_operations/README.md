# VMLC Services - DNS Management - DNS Operations Role

## Overview

The DNS Operations role handles DNS record operations (add, remove, update, verify) across multiple domains. It supports A, CNAME, and PTR records with comprehensive validation and error handling.

## Tasks

- **main.yml**: Main entry point for DNS operations
- **add.yml**: Adds DNS records
- **remove.yml**: Removes DNS records
- **update.yml**: Updates DNS records
- **verify.yml**: Verifies DNS records
- **process_domain_async.yml**: Processes domains asynchronously for multi-domain operations

## Templates

- **dns_operation_summary.j2**: Template for DNS operation summary
- **powershell_params.j2**: Template for PowerShell script parameters

## Variables

### Input Variables

- `operation`: Operation to perform (add, remove, update, verify)
- `record_type`: Record type (a, cname, ptr)
- `hostname`: Hostname for DNS operations
- `domain`: Domain for DNS operations
- `ip_address`: IP address for A/PTR records
- `cname_target`: Target for CNAME records
- `ttl`: Time to live for DNS records
- `description`: Description for DNS records
- `manage_ptr`: Whether to manage PTR records
- `force_remove`: Whether to force removal of records

### Output Variables

- `dns_operation_result`: Result of DNS operation
- `consolidated_results`: Results of multi-domain operations

## Dependencies

- common

## Author

CES Operational Excellence Team

## Contributors

- Muhammad Syazani Bin Mohamed Khairi (7409)
