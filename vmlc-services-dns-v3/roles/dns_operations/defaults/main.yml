---
# Default variables for dns_operations role
# Author: CES Operational Excellence Team
# Contributors: <PERSON> (7409)

# Default operation
operation: "verify"

# Default record type
record_type: "a"

# Default TTL
ttl: 3600

# Default description
description: "Managed by Ansible DNS Management"

# Default PTR management
manage_ptr: true

# Default force flag
force_remove: false

# Multi-domain operation settings
multi_domain:
  # Validation rules
  validation:
    match_count: true
    one_to_one: true
    
  # Parallel execution settings
  parallel:
    enabled: true
    max_parallel: 5
    timeout: 1800
