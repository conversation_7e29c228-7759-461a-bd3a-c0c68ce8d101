---
# Task for verifying DNS records
# Author: CES Operational Excellence Team
# Contributors: <PERSON> (7409)

# Log operation start
- name: Log verify operation start
  ansible.builtin.debug:
    msg: "Starting verify operation for {{ record_type | upper }} record {{ hostname }}.{{ domain }}"
  when: current_log_level | upper == log_settings.levels.debug and verify_only is not defined

# Ensure Temp directory exists on Windows server
- name: Ensure Temp directory exists on Windows server
  ansible.windows.win_file:
    path: "C:\\Temp"
    state: directory
  become: true
  become_method: runas
  become_flags: logon_type=new_credentials logon_flags=netcredentials_only
  delegate_to: "{{ admt_server }}"
  vars:
    ansible_connection: "winrm"
    ansible_winrm_server_cert_validation: "ignore"
    ansible_user: "{{ domain_credentials[domain].dns_username }}"
    ansible_password: "{{ domain_credentials[domain].dns_password }}"
    ansible_become_user: "{{ domain_credentials[domain].dns_username }}"
    ansible_become_password: "{{ domain_credentials[domain].dns_password }}"

# Copy PowerShell script to Windows server
- name: Copy PowerShell script to Windows server
  ansible.windows.win_copy:
    src: "{{ powershell_settings.scripts.dns_management }}"
    dest: "C:\\Temp\\set-dns.ps1"
  become: true
  become_method: runas
  become_flags: logon_type=new_credentials logon_flags=netcredentials_only
  register: script_copy
  delegate_to: "{{ admt_server }}"
  vars:
    ansible_connection: "winrm"
    ansible_winrm_server_cert_validation: "ignore"
    ansible_user: "{{ domain_credentials[domain].dns_username }}"
    ansible_password: "{{ domain_credentials[domain].dns_password }}"
    ansible_become_user: "{{ domain_credentials[domain].dns_username }}"
    ansible_become_password: "{{ domain_credentials[domain].dns_password }}"

# Ensure log directory exists on Windows server
- name: Ensure log directory exists on Windows server
  ansible.windows.win_file:
    path: "{{ log_settings.target_server.base_path }}"
    state: directory
  become: true
  become_method: runas
  become_flags: logon_type=new_credentials logon_flags=netcredentials_only
  delegate_to: "{{ admt_server }}"
  vars:
    ansible_connection: "winrm"
    ansible_winrm_server_cert_validation: "ignore"
    ansible_user: "{{ domain_credentials[domain].dns_username }}"
    ansible_password: "{{ domain_credentials[domain].dns_password }}"
    ansible_become_user: "{{ domain_credentials[domain].dns_username }}"
    ansible_become_password: "{{ domain_credentials[domain].dns_password }}"

# Execute PowerShell script to verify DNS record
- name: Execute PowerShell script to verify DNS record
  ansible.windows.win_shell: |
    C:\\Temp\\set-dns.ps1 `
      -Operation Verify `
      -RecordType {{ record_type | upper }} `
      -Hostname {{ hostname }} `
      -Domain {{ domain }} `
      {% if record_type | lower in ['a', 'ptr'] %}
      -IPAddress "none" `
      {% endif %}
      {% if record_type | lower == 'cname' %}
      -Target "none" `
      {% endif %}
      -DNSServer {{ dns_server }} `
      -PTRDNSServer {{ ptr_dns_server }} `
      -AsJson $true `
      -Force $false `
      -ManagePTR $true `
      -LogPath "{{ target_powershell_log_path }}" `
      -LogLevel {{ current_log_level | upper }}
  args:
    executable: powershell.exe
  become: true
  become_method: runas
  become_flags: logon_type=new_credentials logon_flags=netcredentials_only
  register: ps_result
  delegate_to: "{{ admt_server }}"
  vars:
    ansible_connection: "winrm"
    ansible_winrm_server_cert_validation: "ignore"
    ansible_user: "{{ domain_credentials[domain].dns_username }}"
    ansible_password: "{{ domain_credentials[domain].dns_password }}"
    ansible_become_user: "{{ domain_credentials[domain].dns_username }}"
    ansible_become_password: "{{ domain_credentials[domain].dns_password }}"
  failed_when: false

# Log PowerShell output for debugging
- name: Log PowerShell output for debugging
  ansible.builtin.debug:
    msg:
      - "PowerShell stdout: {{ ps_result.stdout | default('No stdout') }}"
      - "PowerShell stderr: {{ ps_result.stderr | default('No stderr') }}"
    verbosity: 1

# Check for PowerShell execution errors
- name: Check for PowerShell execution errors
  ansible.builtin.set_fact:
    ps_execution_failed: "{{ ps_result.rc is defined and ps_result.rc != 0 }}"

# Extract JSON from PowerShell output
- name: Extract JSON from PowerShell output
  ansible.builtin.set_fact:
    ps_json_output: "{{ ps_result.stdout | regex_search('{.*}', multiline=True) | default('{}') }}"
  ignore_errors: true

# Parse PowerShell result
- name: Parse PowerShell result
  ansible.builtin.set_fact:
    dns_operation_result: "{{ ps_json_output | from_json }}"
  ignore_errors: true
  when: ps_json_output is defined and ps_json_output != '{}'

# Set error result if PowerShell execution failed
- name: Set error result if PowerShell execution failed
  ansible.builtin.set_fact:
    dns_operation_result:
      success: false
      message: "PowerShell execution failed: {{ ps_result.stderr | default('Unknown error') }}"
      changed: false
      error: true
  when: ps_execution_failed | bool

# Set default operation result if parsing fails
- name: Set default operation result if parsing fails
  ansible.builtin.set_fact:
    dns_operation_result:
      success: false
      message: "Failed to parse PowerShell output. Check the logs for details."
      changed: false
  when: dns_operation_result is not defined or dns_operation_result == {}

# Display operation result
- name: Display operation result
  ansible.builtin.debug:
    msg: "{{ dns_operation_result.message if dns_operation_result is defined and dns_operation_result.message is defined else 'Failed to parse PowerShell result. Check the logs for details.' }}"
  when: verify_only is not defined
