---
# Task for processing domains asynchronously
# Author: CES Operational Excellence Team
# Contributors: <PERSON> (7409)

# Set domain-specific variables
- name: Set domain-specific variables
  ansible.builtin.set_fact:
    current_hostname: "{{ domain_hostname_map[current_domain] }}"
    current_ip_address: "{{ domain_ip_map[current_domain] | default('') }}"
    current_cname_target: "{{ domain_cname_map[current_domain] | default('') }}"

# Display current domain information
- name: Display current domain information
  ansible.builtin.debug:
    msg: "Processing domain: {{ current_domain }} with hostname: {{ current_hostname }}"
  when: current_log_level | upper == log_settings.levels.debug

# Set connection parameters for current domain
- name: Set connection parameters for current domain
  ansible.builtin.set_fact:
    ansible_connection: "winrm"
    ansible_winrm_server_cert_validation: "ignore"
    ansible_user: "{{ domain_credentials[current_domain].dns_username }}"
    ansible_password: "{{ domain_credentials[current_domain].dns_password }}"
  no_log: "{{ credential_security.use_no_log }}"

# Add ADMT server to inventory with connection parameters
- name: Add ADMT server to inventory with connection parameters
  ansible.builtin.add_host:
    name: "{{ domains[current_domain].admt_server }}"
    groups: dns_admt_servers
    ansible_connection: "winrm"
    ansible_winrm_server_cert_validation: "ignore"
    ansible_user: "{{ ansible_user }}"
    ansible_password: "{{ ansible_password }}"
  no_log: "{{ credential_security.use_no_log }}"

# Reset connection parameters for localhost operations
- name: Reset connection parameters for localhost operations
  ansible.builtin.set_fact:
    ansible_connection: "local"
    ansible_user: null
    ansible_password: null
    ansible_winrm_server_cert_validation: null

# Set ADMT server, DNS server, and credentials
- name: Set ADMT server, DNS server, and credentials
  ansible.builtin.set_fact:
    admt_server: "{{ domains[current_domain].admt_server }}"
    dns_server: "{{ dns_servers[current_domain] }}"
    dc_server: "{{ dns_servers[current_domain] }}"
    dc_username: "{{ domain_credentials[current_domain].dns_username }}"
    dc_password: "{{ domain_credentials[current_domain].dns_password }}"
  no_log: "{{ credential_security.use_no_log }}"

# Set PTR DNS server based on domain
- name: Set PTR DNS server based on domain
  ansible.builtin.set_fact:
    ptr_dns_server: "{{ ptr_dns_servers[current_domain] }}"

# Set log paths for this domain
- name: Set log paths for this domain
  ansible.builtin.include_tasks: "{{ playbook_dir }}/../roles/common/tasks/set_log_paths.yml"
  vars:
    domain: "{{ current_domain }}"
    hostname: "{{ current_hostname }}"

# Execute operation asynchronously
- name: Execute operation asynchronously
  ansible.builtin.include_tasks: "{{ operation }}.yml"
  async: "{{ multi_domain.parallel.timeout }}"
  poll: 0
  register: async_task
  vars:
    domain: "{{ current_domain }}"
    hostname: "{{ current_hostname }}"
    ip_address: "{{ current_ip_address }}"
    cname_target: "{{ current_cname_target }}"

# Add async task to results list
- name: Add async task to results list
  ansible.builtin.set_fact:
    async_results: "{{ async_results + [async_task] }}"
