---
# Task for updating DNS records
# Author: CES Operational Excellence Team
# Contributors: <PERSON> (7409)

# Log operation start
- name: Log update operation start
  ansible.builtin.debug:
    msg: "Starting update operation for {{ record_type | upper }} record {{ hostname }}.{{ domain }}"
  when: current_log_level | upper == log_settings.levels.debug

# Validate record exists
- name: Check if record exists
  ansible.builtin.include_tasks: verify.yml
  vars:
    verify_only: true

# Fail if record doesn't exist
- name: Fail if record doesn't exist
  ansible.builtin.fail:
    msg: "{{ record_type | upper }} record {{ hostname }}.{{ domain }} does not exist. Cannot update."
  when:
    - not dns_operation_result.success
    - operations[operation].validation.error_if_not_exists | bool

# Copy PowerShell script to Windows server
- name: Copy PowerShell script to Windows server
  ansible.windows.win_copy:
    src: "{{ powershell_settings.scripts.dns_management }}"
    dest: "C:\\Temp\\set-dns.ps1"
  register: script_copy
  delegate_to: "{{ admt_server }}"
  when: dns_operation_result.success

# Execute PowerShell script to update DNS record
- name: Execute PowerShell script to update DNS record
  ansible.windows.win_shell: |
    C:\\Temp\\set-dns.ps1 `
      -Operation Update `
      -RecordType {{ record_type | upper }} `
      -Hostname {{ hostname }} `
      -Domain {{ domain }} `
      {% if record_type | lower in ['a', 'ptr'] %}
      -IPAddress {{ ip_address }} `
      {% endif %}
      {% if record_type | lower == 'cname' %}
      -Target {{ cname_target }} `
      {% endif %}
      -TTL {{ ttl }} `
      -Description "{{ description }}" `
      -DNSServer {{ dns_server }} `
      -PTRDNSServer {{ ptr_dns_server }} `
      -ManagePTR ${{ manage_ptr | bool | string }} `
      -Force $false `
      -AsJson $true `
      -LogPath "{{ target_powershell_log_path }}" `
      -LogLevel {{ current_log_level | upper }}
  args:
    executable: powershell.exe
  register: ps_result
  delegate_to: "{{ admt_server }}"
  when: dns_operation_result.success

# Extract JSON from PowerShell output
- name: Extract JSON from PowerShell output
  ansible.builtin.set_fact:
    ps_json_output: "{{ ps_result.stdout | regex_search('{.*}', multiline=True) | default('{}') }}"
  ignore_errors: true
  when: dns_operation_result is defined and dns_operation_result.success and ps_result is defined

# Parse PowerShell result
- name: Parse PowerShell result
  ansible.builtin.set_fact:
    dns_operation_result: "{{ ps_json_output | from_json }}"
  ignore_errors: true
  when: dns_operation_result is defined and dns_operation_result.success and ps_json_output is defined and ps_json_output != '{}'

# Set default operation result if parsing fails
- name: Set default operation result if parsing fails
  ansible.builtin.set_fact:
    dns_operation_result:
      success: false
      message: "Failed to parse PowerShell output. Check the logs for details."
      changed: false
  when: dns_operation_result is defined and dns_operation_result.success and (ps_json_output is not defined or ps_json_output == '{}')

# Display operation result
- name: Display operation result
  ansible.builtin.debug:
    msg: "{{ dns_operation_result.message if dns_operation_result is defined and dns_operation_result.message is defined else 'Failed to parse PowerShell result. Check the logs for details.' }}"
