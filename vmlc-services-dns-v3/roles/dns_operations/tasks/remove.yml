---
# Task for removing DNS records
# Author: CES Operational Excellence Team
# Contributors: <PERSON> (7409)

# Log operation start
- name: Log remove operation start
  ansible.builtin.debug:
    msg: "Starting remove operation for {{ record_type | upper }} record {{ hostname }}.{{ domain }}"
  when: current_log_level | upper == log_settings.levels.debug

# Validate record exists
- name: Check if record exists
  ansible.builtin.include_tasks: verify.yml
  vars:
    verify_only: true

# Skip if record doesn't exist
- name: Skip if record doesn't exist
  ansible.builtin.set_fact:
    dns_operation_result:
      success: true
      message: "{{ record_type | upper }} record {{ hostname }}.{{ domain }} does not exist. No action needed."
      record:
        type: "{{ record_type | upper }}"
        hostname: "{{ hostname }}"
        domain: "{{ domain }}"
        fqdn: "{{ hostname }}.{{ domain }}"
      operation: "Remove"
      timestamp: "{{ ansible_date_time.iso8601 }}"
      changed: false
  when:
    - not dns_operation_result.success
    - not operations[operation].validation.error_if_not_exists | bool

# Ensure Temp directory exists on Windows server
- name: Ensure Temp directory exists on Windows server
  ansible.windows.win_file:
    path: "C:\\Temp"
    state: directory
  become: true
  become_method: runas
  become_flags: logon_type=new_credentials logon_flags=netcredentials_only
  delegate_to: "{{ admt_server }}"
  vars:
    ansible_connection: "winrm"
    ansible_winrm_server_cert_validation: "ignore"
    ansible_user: "{{ domain_credentials[domain].dns_username }}"
    ansible_password: "{{ domain_credentials[domain].dns_password }}"
    ansible_become_user: "{{ domain_credentials[domain].dns_username }}"
    ansible_become_password: "{{ domain_credentials[domain].dns_password }}"
  when:
    - dns_operation_result.success
    - record_type | lower in ['a', 'ptr']
    - operations[operation].validation.check_connectivity | bool
    - not force_remove | bool

# Copy connectivity test script to Windows server
- name: Copy connectivity test script to Windows server
  ansible.windows.win_copy:
    src: "{{ powershell_settings.scripts.connectivity_test }}"
    dest: "C:\\Temp\\test-connectivity.ps1"
  become: true
  become_method: runas
  become_flags: logon_type=new_credentials logon_flags=netcredentials_only
  register: connectivity_script_copy
  delegate_to: "{{ admt_server }}"
  vars:
    ansible_connection: "winrm"
    ansible_winrm_server_cert_validation: "ignore"
    ansible_user: "{{ domain_credentials[domain].dns_username }}"
    ansible_password: "{{ domain_credentials[domain].dns_password }}"
    ansible_become_user: "{{ domain_credentials[domain].dns_username }}"
    ansible_become_password: "{{ domain_credentials[domain].dns_password }}"
  when:
    - dns_operation_result.success
    - record_type | lower in ['a', 'ptr']
    - operations[operation].validation.check_connectivity | bool
    - not force_remove | bool

# Test connectivity before removing if configured
- name: Test connectivity before removing
  ansible.windows.win_shell: |
    C:\\Temp\\test-connectivity.ps1 `
      -IPAddress {{ dns_operation_result.record.ip_address }} `
      -TimeoutMilliseconds {{ powershell_settings.timeouts.ping }} `
      -RetryCount 3 `
      -AsJson $true
  args:
    executable: powershell.exe
  become: true
  become_method: runas
  become_flags: logon_type=new_credentials logon_flags=netcredentials_only
  register: connectivity_result
  delegate_to: "{{ admt_server }}"
  vars:
    ansible_connection: "winrm"
    ansible_winrm_server_cert_validation: "ignore"
    ansible_user: "{{ domain_credentials[domain].dns_username }}"
    ansible_password: "{{ domain_credentials[domain].dns_password }}"
    ansible_become_user: "{{ domain_credentials[domain].dns_username }}"
    ansible_become_password: "{{ domain_credentials[domain].dns_password }}"
  when:
    - dns_operation_result.success
    - record_type | lower in ['a', 'ptr']
    - operations[operation].validation.check_connectivity | bool
    - not force_remove | bool

# Parse connectivity result
- name: Parse connectivity result
  ansible.builtin.set_fact:
    connectivity_status: "{{ connectivity_result.stdout | from_json }}"
  when:
    - dns_operation_result.success
    - record_type | lower in ['a', 'ptr']
    - operations[operation].validation.check_connectivity | bool
    - not force_remove | bool
    - connectivity_result is defined

# Fail if host is still responding
- name: Fail if host is still responding
  ansible.builtin.fail:
    msg: "Host {{ dns_operation_result.record.ip_address }} is still responding. Use force_remove=true to override."
  when:
    - dns_operation_result.success
    - record_type | lower in ['a', 'ptr']
    - operations[operation].validation.check_connectivity | bool
    - not force_remove | bool
    - connectivity_status is defined
    - connectivity_status.is_alive | bool

# Copy PowerShell script to Windows server
- name: Copy PowerShell script to Windows server
  ansible.windows.win_copy:
    src: "{{ powershell_settings.scripts.dns_management }}"
    dest: "C:\\Temp\\set-dns.ps1"
  become: true
  become_method: runas
  become_flags: logon_type=new_credentials logon_flags=netcredentials_only
  register: script_copy
  delegate_to: "{{ admt_server }}"
  vars:
    ansible_connection: "winrm"
    ansible_winrm_server_cert_validation: "ignore"
    ansible_user: "{{ domain_credentials[domain].dns_username }}"
    ansible_password: "{{ domain_credentials[domain].dns_password }}"
    ansible_become_user: "{{ domain_credentials[domain].dns_username }}"
    ansible_become_password: "{{ domain_credentials[domain].dns_password }}"
  when: dns_operation_result.success

# Execute PowerShell script to remove DNS record
- name: Execute PowerShell script to remove DNS record
  ansible.windows.win_shell: |
    C:\\Temp\\set-dns.ps1 `
      -Operation Remove `
      -RecordType {{ record_type | upper }} `
      -Hostname {{ hostname }} `
      -Domain {{ domain }} `
      {% if record_type | lower in ['a', 'ptr'] and ip_address is defined and ip_address | length > 0 %}
      -IPAddress {{ ip_address }} `
      {% endif %}
      -DNSServer {{ dns_server }} `
      -PTRDNSServer {{ ptr_dns_server }} `
      -ManagePTR ${{ manage_ptr | bool | string }} `
      -Force ${{ force_remove | bool | string }} `
      -AsJson $true `
      -LogPath "{{ target_powershell_log_path }}" `
      -LogLevel {{ current_log_level | upper }} `
      -TTL 3600 `
      -Description "Managed by Ansible DNS Management"
  args:
    executable: powershell.exe
  become: true
  become_method: runas
  become_flags: logon_type=new_credentials logon_flags=netcredentials_only
  register: ps_result
  delegate_to: "{{ admt_server }}"
  vars:
    ansible_connection: "winrm"
    ansible_winrm_server_cert_validation: "ignore"
    ansible_user: "{{ domain_credentials[domain].dns_username }}"
    ansible_password: "{{ domain_credentials[domain].dns_password }}"
    ansible_become_user: "{{ domain_credentials[domain].dns_username }}"
    ansible_become_password: "{{ domain_credentials[domain].dns_password }}"
  when: dns_operation_result.success

# Extract JSON from PowerShell output
- name: Extract JSON from PowerShell output
  ansible.builtin.set_fact:
    ps_json_output: "{{ ps_result.stdout | regex_search('{.*}', multiline=True) | default('{}') }}"
  ignore_errors: true
  when: dns_operation_result is defined and dns_operation_result.success and ps_result is defined

# Parse PowerShell result
- name: Parse PowerShell result
  ansible.builtin.set_fact:
    dns_operation_result: "{{ ps_json_output | from_json }}"
  ignore_errors: true
  when: dns_operation_result is defined and dns_operation_result.success and ps_json_output is defined and ps_json_output != '{}'

# Set default operation result if parsing fails
- name: Set default operation result if parsing fails
  ansible.builtin.set_fact:
    dns_operation_result:
      success: false
      message: "Failed to parse PowerShell output. Check the logs for details."
      changed: false
  when: dns_operation_result is defined and dns_operation_result.success and (ps_json_output is not defined or ps_json_output == '{}')

# Display operation result
- name: Display operation result
  ansible.builtin.debug:
    msg: "{{ dns_operation_result.message if dns_operation_result is defined and dns_operation_result.message is defined else 'Failed to parse PowerShell result. Check the logs for details.' }}"
