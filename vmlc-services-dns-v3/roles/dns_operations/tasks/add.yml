---
# Task for adding DNS records
# Author: CES Operational Excellence Team
# Contributors: <PERSON> (7409)

# Log operation start
- name: Log add operation start
  ansible.builtin.debug:
    msg: "Starting add operation for {{ record_type | upper }} record {{ hostname }}.{{ domain }}"
  when: current_log_level | upper == log_settings.levels.debug

# Validate record doesn't already exist with different values
- name: Check if record already exists
  ansible.builtin.include_tasks: verify.yml
  vars:
    verify_only: true

# Handle existing records based on idempotency rules
- name: Handle existing records
  ansible.builtin.block:
    - name: Fail if record exists with different values
      ansible.builtin.fail:
        msg: "{{ record_type | upper }} record {{ hostname }}.{{ domain }} already exists with different values"
      when:
        - dns_operation_result.success == false
        - '"already exists" in dns_operation_result.message'

    - name: Skip add operation if record already exists with same values
      ansible.builtin.set_fact:
        skip_add_operation: true
      when:
        - dns_operation_result.success == true
        - dns_operation_result.changed == false

    - name: Log skipping add operation
      ansible.builtin.debug:
        msg: "Skipping add operation as record already exists with same values"
      when: skip_add_operation | default(false)
  when:
    - dns_operation_result is defined
    - operations[operation].validation.check_exists | bool

# Ensure Temp directory exists on Windows server
- name: Ensure Temp directory exists on Windows server
  ansible.windows.win_file:
    path: "C:\\Temp"
    state: directory
  become: true
  become_method: runas
  become_flags: logon_type=new_credentials logon_flags=netcredentials_only
  delegate_to: "{{ admt_server }}"
  vars:
    ansible_connection: "winrm"
    ansible_winrm_server_cert_validation: "ignore"
    ansible_user: "{{ domain_credentials[domain].dns_username }}"
    ansible_password: "{{ domain_credentials[domain].dns_password }}"
    ansible_become_user: "{{ domain_credentials[domain].dns_username }}"
    ansible_become_password: "{{ domain_credentials[domain].dns_password }}"
  when: not skip_add_operation | default(false)

# Copy PowerShell script to Windows server
- name: Copy PowerShell script to Windows server
  ansible.windows.win_copy:
    src: "{{ powershell_settings.scripts.dns_management }}"
    dest: "C:\\Temp\\set-dns.ps1"
  become: true
  become_method: runas
  become_flags: logon_type=new_credentials logon_flags=netcredentials_only
  register: script_copy
  delegate_to: "{{ admt_server }}"
  vars:
    ansible_connection: "winrm"
    ansible_winrm_server_cert_validation: "ignore"
    ansible_user: "{{ domain_credentials[domain].dns_username }}"
    ansible_password: "{{ domain_credentials[domain].dns_password }}"
    ansible_become_user: "{{ domain_credentials[domain].dns_username }}"
    ansible_become_password: "{{ domain_credentials[domain].dns_password }}"
  when: not skip_add_operation | default(false)

# Ensure log directory exists on Windows server
- name: Ensure log directory exists on Windows server
  ansible.windows.win_file:
    path: "{{ log_settings.target_server.base_path }}"
    state: directory
  become: true
  become_method: runas
  become_flags: logon_type=new_credentials logon_flags=netcredentials_only
  delegate_to: "{{ admt_server }}"
  vars:
    ansible_connection: "winrm"
    ansible_winrm_server_cert_validation: "ignore"
    ansible_user: "{{ domain_credentials[domain].dns_username }}"
    ansible_password: "{{ domain_credentials[domain].dns_password }}"
    ansible_become_user: "{{ domain_credentials[domain].dns_username }}"
    ansible_become_password: "{{ domain_credentials[domain].dns_password }}"
  when: not skip_add_operation | default(false)

# Execute PowerShell script to add DNS record
- name: Execute PowerShell script to add DNS record
  ansible.windows.win_shell: |
    C:\\Temp\\set-dns.ps1 `
      -Operation Add `
      -RecordType {{ record_type | upper }} `
      -Hostname {{ hostname }} `
      -Domain {{ domain }} `
      {% if record_type | lower in ['a', 'ptr'] %}
      -IPAddress {{ ip_address }} `
      {% endif %}
      {% if record_type | lower == 'cname' %}
      -Target {{ cname_target }} `
      {% endif %}
      -TTL {{ ttl }} `
      -Description "{{ description }}" `
      -DNSServer {{ dns_server }} `
      -PTRDNSServer {{ ptr_dns_server }} `
      -ManagePTR ${{ manage_ptr | bool | string }} `
      -Force $false `
      -AsJson $true `
      -LogPath "{{ target_powershell_log_path }}" `
      -LogLevel {{ current_log_level | upper }}
  args:
    executable: powershell.exe
  become: true
  become_method: runas
  become_flags: logon_type=new_credentials logon_flags=netcredentials_only
  register: ps_result
  delegate_to: "{{ admt_server }}"
  vars:
    ansible_connection: "winrm"
    ansible_winrm_server_cert_validation: "ignore"
    ansible_user: "{{ domain_credentials[domain].dns_username }}"
    ansible_password: "{{ domain_credentials[domain].dns_password }}"
    ansible_become_user: "{{ domain_credentials[domain].dns_username }}"
    ansible_become_password: "{{ domain_credentials[domain].dns_password }}"
  when: not skip_add_operation | default(false)

# Extract JSON from PowerShell output
- name: Extract JSON from PowerShell output
  ansible.builtin.set_fact:
    ps_json_output: "{{ ps_result.stdout | regex_search('{.*}', multiline=True) | default('{}') }}"
  ignore_errors: true
  when: not skip_add_operation | default(false) and ps_result is defined

# Parse PowerShell result
- name: Parse PowerShell result
  ansible.builtin.set_fact:
    dns_operation_result: "{{ ps_json_output | from_json }}"
  ignore_errors: true
  when: not skip_add_operation | default(false) and ps_json_output is defined and ps_json_output != '{}'

# Set default operation result if parsing fails
- name: Set default operation result if parsing fails
  ansible.builtin.set_fact:
    dns_operation_result:
      success: false
      message: "Failed to parse PowerShell output. Check the logs for details."
      changed: false
  when: (not skip_add_operation | default(false)) and (dns_operation_result is not defined or dns_operation_result == {})

# Set result for skipped operation
- name: Set result for skipped operation
  ansible.builtin.set_fact:
    dns_operation_result: {
      "success": true,
      "message": "{{ record_type | upper }} record {{ hostname }}.{{ domain }} already exists with the correct values",
      "changed": false
    }
  when: skip_add_operation | default(false)

# Display operation result
- name: Display operation result
  ansible.builtin.debug:
    msg: "{{ dns_operation_result.message if dns_operation_result is defined and dns_operation_result.message is defined else 'Failed to parse PowerShell result. Check the logs for details.' }}"

# Set changed flag for Ansible
- name: Set changed flag
  ansible.builtin.set_fact:
    dns_changed: "{{ dns_operation_result.changed | default(false) if dns_operation_result is defined else false }}"

# Log idempotency information
- name: Log idempotency information
  ansible.builtin.debug:
    msg: "Operation was idempotent: {{ not dns_changed }}"
  when: current_log_level | upper == log_settings.levels.debug
