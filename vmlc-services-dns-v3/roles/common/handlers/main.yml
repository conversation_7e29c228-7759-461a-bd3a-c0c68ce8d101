---
# Handlers for common role
# Author: CES Operational Excellence Team
# Contributors: <PERSON> (7409)

- name: Display configuration loaded
  ansible.builtin.debug:
    msg: "Configuration loaded successfully"
  listen: "config_loaded"

- name: Display domain validated
  ansible.builtin.debug:
    msg: "Domain {{ domain }} validated successfully"
  listen: "domain_validated"
  when: domain is defined

- name: Display logging setup
  ansible.builtin.debug:
    msg: "Logging setup successfully"
  listen: "logging_setup"

- name: Display operation validated
  ansible.builtin.debug:
    msg: "Operation {{ operation }} validated successfully"
  listen: "operation_validated"
  when: operation is defined

- name: Display credentials setup
  ansible.builtin.debug:
    msg: "Credentials setup successfully"
  listen: "credentials_setup"
