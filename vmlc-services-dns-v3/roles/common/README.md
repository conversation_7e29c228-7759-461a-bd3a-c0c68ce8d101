# VMLC Services - DNS Management - Common Role

## Overview

The Common role provides shared tasks and functionality used across the DNS Management system. It handles configuration loading, domain validation, logging setup, operation validation, and credential management.

## Tasks

- **load_configuration.yml**: Loads and validates configuration files
- **validate_domain.yml**: Validates domain configuration
- **setup_logging.yml**: Sets up logging with standardized file paths
- **validate_operation.yml**: Validates operation parameters
- **setup_credentials.yml**: Sets up credentials for DNS operations
- **initialize_results.yml**: Initializes result variables
- **handle_errors.yml**: Handles operation errors
- **process_results.yml**: Processes operation results
- **cleanup.yml**: Cleans up after operations
- **display_summary.yml**: Displays operation summary

## Handlers

- **Display configuration loaded**: Notifies when configuration is loaded successfully
- **Display domain validated**: Notifies when domain is validated successfully
- **Display logging setup**: Notifies when logging is set up successfully
- **Display operation validated**: Notifies when operation is validated successfully
- **Display credentials setup**: Notifies when credentials are set up successfully

## Variables

### Input Variables

- `domain`: Domain for DNS operations
- `domains`: Comma-separated list of domains for multi-domain operations
- `hostname`: Hostname for DNS operations
- `hostnames`: Comma-separated list of hostnames for multi-domain operations
- `operation`: Operation to perform (add, remove, update, verify)
- `record_type`: Record type (a, cname, ptr)
- `log_level`: Log level (Error, Warning, Info, Debug)

### Output Variables

- `dns_operation_result`: Result of DNS operation
- `consolidated_results`: Results of multi-domain operations
- `ansible_log_path`: Path to Ansible log file
- `powershell_log_path`: Path to PowerShell log file

## Dependencies

None

## Author

CES Operational Excellence Team

## Contributors

- Muhammad Syazani Bin Mohamed Khairi (7409)
