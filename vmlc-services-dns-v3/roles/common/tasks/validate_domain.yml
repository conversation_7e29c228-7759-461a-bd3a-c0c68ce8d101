---
# Task for validating domain configuration
# Author: CES Operational Excellence Team
# Contributors: <PERSON> (7409)

# Validate single domain
- name: Check if domain exists in domain configuration
  ansible.builtin.set_fact:
    domain_exists: "{{ domain in domain_keys }}"
  when: domain is defined and not is_multi_domain
  tags: always

- name: Validate domain exists in domain configuration
  ansible.builtin.assert:
    that:
      - domain is defined
      - domain_exists | bool
    fail_msg: "Domain '{{ domain }}' not found in domain_config.yml configuration"
  when: domain is defined and not is_multi_domain
  tags: always

# Parse domains for multi-domain operations
- name: Parse domains for multi-domain operations
  ansible.builtin.set_fact:
    domain_list: "{{ domains.split(',') | map('trim') | list }}"
  when: is_multi_domain
  tags: always

# Check if each domain exists in domain configuration for multi-domain operations
- name: Check if each domain exists in domain configuration for multi-domain operations
  ansible.builtin.set_fact:
    domain_item_exists: "{{ item in domain_keys }}"
  loop: "{{ domain_list }}"
  register: domain_existence_checks
  when: is_multi_domain
  tags: always

# Validate all domains exist in domain configuration for multi-domain operations
- name: Validate all domains exist in domain configuration for multi-domain operations
  ansible.builtin.assert:
    that:
      - item.ansible_facts.domain_item_exists | bool
    fail_msg: "Domain '{{ domain_list[idx] }}' not found in domain_config.yml configuration"
  loop: "{{ domain_existence_checks.results }}"
  loop_control:
    index_var: idx
  when: is_multi_domain and domain_existence_checks is defined
  tags: always

# Extract domain information for single domain operations
- name: Extract domain information for single domain
  ansible.builtin.set_fact:
    domain_info: "{{ domains[domain] | default({}) }}"
    dns_server: "{{ dns_servers[domain] | default('') }}"
    ptr_dns_server: "{{ ptr_dns_servers[domain] | default('') }}"
    domain_creds: "{{ domain_credentials[domain] | default({}) }}"
  when: domain is defined and domain_exists | bool and not is_multi_domain

# Get domain environments safely
- name: Get domain environments safely
  ansible.builtin.set_fact:
    domain_environments_list: []
  when: is_multi_domain

# Collect environment for each domain
- name: Collect environment for each domain
  ansible.builtin.set_fact:
    domain_environments_list: "{{ domain_environments_list + [domains[item].environment] }}"
  loop: "{{ domain_list }}"
  when: is_multi_domain and item in domain_keys
  register: domain_environments

- name: Ensure all domains are in the same environment
  ansible.builtin.assert:
    that:
      - domain_environments_list | unique | list | length == 1
    fail_msg: "All domains must be in the same environment (production or staging)"
  when: is_multi_domain and domain_environments_list is defined and domain_environments_list | length > 0

# Set environment based on domain
- name: Set environment based on domain
  ansible.builtin.set_fact:
    environment: "{{ domain_info.environment | default('unknown') if not is_multi_domain else (domain_environments_list[0] if domain_environments_list is defined and domain_environments_list | length > 0 else 'unknown') }}"

- name: Display domain information
  ansible.builtin.debug:
    msg:
      - "Environment: {{ environment | default('unknown') }}"
      - "Domain Information: {{ domain_info if not is_multi_domain else 'Multiple domains' }}"
  notify: "domain_validated"
