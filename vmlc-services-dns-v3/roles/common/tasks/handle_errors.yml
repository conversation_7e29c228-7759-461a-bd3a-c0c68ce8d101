---
# Task for handling errors
# Author: CES Operational Excellence Team
# Contributors: <PERSON> (7409)

# Log error details
- name: Log error details
  ansible.builtin.debug:
    msg: 
      - "Error occurred during {{ operation | capitalize }} operation for {{ record_type | upper }} record{{ 's' if is_multi_domain else '' }}"
      - "Error: {{ ansible_failed_result.msg | default('Unknown error') }}"
  when: ansible_failed_result is defined

# Set error result for single domain operations
- name: Set error result for single domain operations
  ansible.builtin.set_fact:
    dns_operation_result:
      success: false
      message: "{{ ansible_failed_result.msg | default('Operation failed') }}"
      record:
        type: "{{ record_type | upper }}"
        hostname: "{{ hostname }}"
        domain: "{{ domain }}"
        fqdn: "{{ hostname }}.{{ domain }}"
      operation: "{{ operation | capitalize }}"
      timestamp: "{{ ansible_date_time.iso8601 }}"
      error: "{{ ansible_failed_result | default({}) }}"
  when: not is_multi_domain

# Add error to consolidated results for multi-domain operations
- name: Add error to consolidated results for multi-domain operations
  ansible.builtin.set_fact:
    consolidated_results: "{{ consolidated_results + [{'success': false, 'message': ansible_failed_result.msg | default('Operation failed'), 'domain': current_domain | default(domain), 'hostname': domain_hostname_map[current_domain] | default(hostname), 'operation': operation | capitalize, 'record_type': record_type | upper, 'timestamp': ansible_date_time.iso8601, 'error': ansible_failed_result | default({})}] }}"
  when: is_multi_domain and current_domain is defined

# Write error to log file
- name: Write error to log file
  ansible.builtin.copy:
    content: |
      [{{ ansible_date_time.iso8601 }}] [ERROR] [{{ operation | upper }}] [{{ record_type | upper }}] [{{ hostname if not is_multi_domain else 'Multiple hostnames' }}.{{ domain if not is_multi_domain else 'Multiple domains' }}] Operation failed: {{ ansible_failed_result.msg | default('Unknown error') }}
      {{ ansible_failed_result | to_nice_yaml }}
    dest: "{{ ansible_log_path }}"
    mode: '0644'
  delegate_to: localhost
