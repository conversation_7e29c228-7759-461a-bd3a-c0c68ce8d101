---
# Task for processing operation results
# Author: CES Operational Excellence Team
# Contributors: <PERSON> (7409)

# Process async results for multi-domain operations
- name: Check async job status
  ansible.builtin.async_status:
    jid: "{{ item.ansible_job_id }}"
  register: job_result
  until: job_result.finished
  retries: 300  # 30 minutes with 6-second intervals
  delay: 6
  loop: "{{ async_results }}"
  when: is_multi_domain and async_results is defined and async_results | length > 0

# Extract results from async jobs
- name: Extract results from async jobs
  ansible.builtin.set_fact:
    consolidated_results: "{{ consolidated_results + [item.ansible_facts.dns_operation_result] }}"
  loop: "{{ job_result.results }}"
  when: is_multi_domain and job_result is defined and job_result.results is defined

# Calculate success rate for multi-domain operations
- name: Calculate success rate for multi-domain operations
  ansible.builtin.set_fact:
    success_count: "{{ consolidated_results | selectattr('success', 'equalto', true) | list | length }}"
    total_count: "{{ consolidated_results | length }}"
  when: is_multi_domain and consolidated_results is defined and consolidated_results | length > 0

# Display operation results for single domain
- name: Display operation results for single domain
  ansible.builtin.debug:
    msg: 
      - "{{ operation | capitalize }} operation for {{ record_type | upper }} record {{ hostname }}.{{ domain }} {{ 'succeeded' if dns_operation_result.success else 'failed' }}"
      - "Message: {{ dns_operation_result.message }}"
  when: not is_multi_domain

# Display operation results for multi-domain
- name: Display operation results for multi-domain
  ansible.builtin.debug:
    msg: 
      - "{{ operation | capitalize }} operation for {{ record_type | upper }} records completed"
      - "Success rate: {{ success_count }}/{{ total_count }} ({{ (success_count / total_count * 100) | int }}%)"
      - "Successful domains: {{ consolidated_results | selectattr('success', 'equalto', true) | map(attribute='domain') | list | join(', ') }}"
      - "Failed domains: {{ consolidated_results | selectattr('success', 'equalto', false) | map(attribute='domain') | list | join(', ') }}"
  when: is_multi_domain and consolidated_results is defined and consolidated_results | length > 0

# Write results to log file
- name: Write results to log file
  ansible.builtin.copy:
    content: |
      [{{ ansible_date_time.iso8601 }}] [INFO] [{{ operation | upper }}] [{{ record_type | upper }}] [{{ hostname if not is_multi_domain else 'Multiple hostnames' }}.{{ domain if not is_multi_domain else 'Multiple domains' }}] Operation {{ 'succeeded' if dns_operation_result.success | default(false) else 'failed' }}
      {{ dns_operation_result | default(consolidated_results) | to_nice_yaml }}
    dest: "{{ ansible_log_path }}"
    mode: '0644'
  delegate_to: localhost
