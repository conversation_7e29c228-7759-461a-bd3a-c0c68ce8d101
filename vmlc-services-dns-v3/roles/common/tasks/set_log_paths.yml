---
# Tasks for setting standardized log file paths
# Author: CES Operational Excellence Team
# Contributors: <PERSON> (7409)

- name: Format date for log filenames
  ansible.builtin.set_fact:
    log_date: "{{ ansible_date_time.year }}{{ ansible_date_time.month }}{{ ansible_date_time.day }}"

- name: Set ticket number with default
  ansible.builtin.set_fact:
    log_ticket: "{{ ticket | default('INC-123456') }}"

- name: Set hostname and domain for log filenames
  ansible.builtin.set_fact:
    log_hostname: "{{ hostname if hostname is defined and hostname != '' else 'unknown' }}"
    log_domain: "{{ domain if domain is defined and domain != '' else 'unknown' }}"
    log_record_type: "{{ record_type | upper if record_type is defined and record_type != '' else 'UNKNOWN' }}"
    log_operation: "{{ operation | upper if operation is defined and operation != '' else 'UNKNOWN' }}"

- name: Construct base log filename
  ansible.builtin.set_fact:
    log_base_name: "{{ log_date }}_{{ log_ticket }}_{{ log_hostname }}_{{ log_domain }}_{{ log_record_type }}_{{ log_operation }}"

- name: Set specific log file paths
  ansible.builtin.set_fact:
    ansible_log_path: "{{ log_settings.directories.ansible }}/{{ log_base_name }}_{{ log_settings.types.ansible }}{{ log_settings.format.extension }}"
    powershell_log_path: "{{ log_settings.directories.powershell }}/{{ log_base_name }}_{{ log_settings.types.powershell }}{{ log_settings.format.extension }}"
    progress_log_path: "{{ log_settings.directories.progress }}/{{ log_base_name }}_{{ log_settings.types.progress }}{{ log_settings.format.extension }}"
    report_log_path: "{{ log_settings.directories.ansible }}/{{ log_base_name }}_{{ log_settings.types.report }}{{ log_settings.format.extension }}"

- name: Set target server log paths
  ansible.builtin.set_fact:
    target_ansible_log_path: "{{ log_settings.target_server.base_path }}{{ log_base_name }}_{{ log_settings.types.ansible }}{{ log_settings.format.extension }}"
    target_powershell_log_path: "{{ log_settings.target_server.base_path }}{{ log_base_name }}_{{ log_settings.types.powershell }}{{ log_settings.format.extension }}"

- name: Display log paths
  ansible.builtin.debug:
    msg:
      - "Local log files: {{ ansible_log_path }} and {{ powershell_log_path }}"
      - "Target server log files: {{ target_ansible_log_path }} and {{ target_powershell_log_path }}"
  # Enable this for debugging
  when: current_log_level | default('INFO') | upper == 'DEBUG'
