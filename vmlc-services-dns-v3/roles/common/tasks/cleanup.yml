---
# Task for cleaning up after operations
# Author: CES Operational Excellence Team
# Contributors: <PERSON> (7409)

# Clear sensitive variables if configured
- name: Clear sensitive variables
  ansible.builtin.set_fact:
    ansible_user: null
    ansible_password: null
    dc_username: null
    dc_password: null
    domain_credential_map: null
  when: credential_security.clear_after_use | bool
  no_log: "{{ credential_security.use_no_log }}"

# Archive logs if configured
- name: Archive logs if configured
  ansible.builtin.archive:
    path:
      - "{{ ansible_log_path }}"
      - "{{ powershell_log_path }}"
      - "{{ progress_log_path }}"
    dest: "{{ log_settings.directories.archive }}/{{ log_base_name }}_ARCHIVE.tar.gz"
    format: gz
    remove: false
  when: log_flags.archive_logs | bool
  delegate_to: localhost
  ignore_errors: true
