---
# Tasks for rotating logs
# Author: CES Operational Excellence Team
# Contributors: <PERSON> (7409)

- name: Find old log files
  ansible.builtin.find:
    paths: 
      - "{{ log_settings.directories.ansible }}"
      - "{{ log_settings.directories.powershell }}"
      - "{{ log_settings.directories.progress }}"
    patterns: "*.log"
    age: "{{ log_settings.rotation.max_age }}d"
    recurse: no
  register: old_logs
  delegate_to: localhost
  run_once: true

- name: Archive old log files
  ansible.builtin.archive:
    path: "{{ item.path }}"
    dest: "{{ log_settings.directories.archive }}/{{ item.path | basename }}.{{ ansible_date_time.date }}.gz"
    format: gz
    remove: true
  loop: "{{ old_logs.files }}"
  when: old_logs.matched > 0
  delegate_to: localhost
  run_once: true

- name: Display log rotation information
  ansible.builtin.debug:
    msg: "Archived {{ old_logs.matched }} log files older than {{ log_settings.rotation.max_age }} days"
  when: old_logs.matched > 0 and log_flags.show_progress | bool
  delegate_to: localhost
  run_once: true
