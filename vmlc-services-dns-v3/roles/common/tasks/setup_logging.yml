---
# Task for setting up logging
# Author: CES Operational Excellence Team
# Contributors: <PERSON> (7409)

# Ensure log directories exist
- name: Ensure log directories exist
  ansible.builtin.file:
    path: "{{ item }}"
    state: directory
    mode: '0755'
  loop:
    - "{{ log_settings.directories.ansible }}"
    - "{{ log_settings.directories.powershell }}"
    - "{{ log_settings.directories.progress }}"
    - "{{ log_settings.directories.archive }}"
  delegate_to: localhost
  run_once: true
  vars:
    ansible_connection: "local"

# Set log file paths
- name: Include standardized log file paths
  ansible.builtin.include_tasks: set_log_paths.yml

# Rotate logs if enabled
- name: Rotate logs if enabled
  ansible.builtin.include_tasks: rotate_logs.yml
  when: log_flags.rotate_logs | bool

# Display logging information
- name: Display logging information
  ansible.builtin.debug:
    msg:
      - "Log Level: {{ current_log_level }}"
      - "Ansible Log: {{ ansible_log_path }}"
      - "PowerShell Log: {{ powershell_log_path }}"
      - "Progress Log: {{ progress_log_path }}"
  when: log_flags.show_progress | bool
  notify: "logging_setup"
