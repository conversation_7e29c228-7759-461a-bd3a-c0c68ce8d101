---
# Main tasks file for common role
# Author: CES Operational Excellence Team
# Contributors: <PERSON> (7409)

# Load configuration
- name: Load configuration
  ansible.builtin.include_tasks: load_configuration.yml
  
# Validate domain
- name: Validate domain configuration
  ansible.builtin.include_tasks: validate_domain.yml
  
# Set up logging
- name: Set up logging
  ansible.builtin.include_tasks: setup_logging.yml
  
# Validate operation parameters
- name: Validate operation parameters
  ansible.builtin.include_tasks: validate_operation.yml
  
# Set up credentials
- name: Set up credentials
  ansible.builtin.include_tasks: setup_credentials.yml
  no_log: "{{ credential_security.use_no_log }}"
