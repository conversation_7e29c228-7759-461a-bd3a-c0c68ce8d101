---
# Task for initializing results
# Author: CES Operational Excellence Team
# Contributors: <PERSON> (7409)

# Initialize result variables for single domain operations
- name: Initialize result variables for single domain operations
  ansible.builtin.set_fact:
    dns_operation_result:
      success: false
      message: "Operation not yet executed"
      record:
        type: "{{ record_type | upper }}"
        hostname: "{{ hostname }}"
        domain: "{{ domain }}"
        fqdn: "{{ hostname }}.{{ domain }}"
      operation: "{{ operation | capitalize }}"
      timestamp: "{{ ansible_date_time.iso8601 }}"
  when: not is_multi_domain

# Initialize consolidated results variable for multi-domain operations
- name: Initialize consolidated results variable for multi-domain operations
  ansible.builtin.set_fact:
    consolidated_results: []
    async_results: []
  when: is_multi_domain

# Create domain to hostname mapping for multi-domain operations
- name: Create domain to hostname mapping
  ansible.builtin.set_fact:
    domain_hostname_map: "{{ dict(domains.split(',') | map('trim') | list | zip(hostnames.split(',') | map('trim') | list)) }}"
  when: is_multi_domain

# Create domain to IP address mapping for A/PTR records in multi-domain operations
- name: Create domain to IP address mapping for A/PTR records
  ansible.builtin.set_fact:
    domain_ip_map: "{{ dict(domains.split(',') | map('trim') | list | zip(ip_addresses.split(',') | map('trim') | list)) }}"
  when: is_multi_domain and record_type | lower in ['a', 'ptr'] and ip_addresses is defined and ip_addresses | length > 0

# Create domain to CNAME target mapping for CNAME records in multi-domain operations
- name: Create domain to CNAME target mapping for CNAME records
  ansible.builtin.set_fact:
    domain_cname_map: "{{ dict(domains.split(',') | map('trim') | list | zip(cname_targets.split(',') | map('trim') | list)) }}"
  when: is_multi_domain and record_type | lower == 'cname' and cname_targets is defined and cname_targets | length > 0

# Log operation start
- name: Log operation start
  ansible.builtin.debug:
    msg: "Starting {{ operation | capitalize }} operation for {{ record_type | upper }} record{{ 's' if is_multi_domain else '' }}"
