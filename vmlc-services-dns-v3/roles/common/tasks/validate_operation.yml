---
# Task for validating operation parameters
# Author: CES Operational Excellence Team
# Contributors: <PERSON> (7409)

# Validate operation
- name: Validate operation
  ansible.builtin.assert:
    that:
      - operation in operations
    fail_msg: "Invalid operation: {{ operation }}. Valid operations are: {{ operations.keys() | list | join(', ') }}"

# Validate record type
- name: Validate record type
  ansible.builtin.assert:
    that:
      - record_type in record_types
    fail_msg: "Invalid record type: {{ record_type }}. Valid record types are: {{ record_types.keys() | list | join(', ') }}"

# Validate required parameters for single domain operations
- name: Validate required parameters for single domain operations
  ansible.builtin.assert:
    that:
      - domain != ""
      - hostname != ""
      - true  # Removed ticket validation to avoid recursion
    fail_msg: "Domain and hostname parameters are required"
  when: not is_multi_domain

# Validate operation-specific parameters for single domain
- name: Validate operation-specific parameters for single domain
  ansible.builtin.assert:
    that:
      - item in vars
      - vars[item] != ""
    fail_msg: "{{ item }} is required for {{ operation }} operation with {{ record_type }} record type"
  loop: "{{ operations[operation].required_params[record_type] }}"
  when: not is_multi_domain

# Validate multi-domain parameters
- name: Validate multi-domain parameters
  ansible.builtin.assert:
    that:
      - domains != ""
      - hostnames != ""
      - domains.split(',') | map('trim') | list | length == hostnames.split(',') | map('trim') | list | length
    fail_msg: "For multi-domain operations, domains and hostnames must be provided and have the same number of items"
  when: is_multi_domain

# Validate operation-specific parameters for multi-domain
- name: Validate operation-specific parameters for multi-domain (A/PTR records)
  ansible.builtin.assert:
    that:
      - ip_addresses is defined
      - ip_addresses != ""
      - ip_addresses.split(',') | map('trim') | list | length == domains.split(',') | map('trim') | list | length
    fail_msg: "For multi-domain operations with A/PTR records, ip_addresses must be provided and match the number of domains"
  when: is_multi_domain and record_type in ['a', 'ptr'] and operation in ['add', 'update']

# Validate operation-specific parameters for multi-domain (CNAME records)
- name: Validate operation-specific parameters for multi-domain (CNAME records)
  ansible.builtin.assert:
    that:
      - cname_targets is defined
      - cname_targets != ""
      - cname_targets.split(',') | map('trim') | list | length == domains.split(',') | map('trim') | list | length
    fail_msg: "For multi-domain operations with CNAME records, cname_targets must be provided and match the number of domains"
  when: is_multi_domain and record_type == 'cname' and operation == 'add'
  notify: "operation_validated"
