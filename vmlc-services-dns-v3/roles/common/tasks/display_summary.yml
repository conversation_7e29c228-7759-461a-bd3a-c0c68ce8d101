---
# Task for displaying operation summary
# Author: CES Operational Excellence Team
# Contributors: <PERSON> (7409)

# Display operation summary for single domain
- name: Display operation summary for single domain
  ansible.builtin.debug:
    msg:
      - "Operation Summary:"
      - "  Operation: {{ operation | capitalize }}"
      - "  Record Type: {{ record_type | upper }}"
      - "  Hostname: {{ hostname }}.{{ domain }}"
      - "  Result: {{ 'Success' if dns_operation_result.success else 'Failure' }}"
      - "  Message: {{ dns_operation_result.message }}"
      - "  Logs: {{ ansible_log_path }}"
      - "  PowerShell Log: {{ powershell_log_path }}"
      - "  Report: {{ report_path | default('Not generated') }}"
      - "  ADMT Server: {{ admt_server }}"
      - "  DNS Server: {{ dns_server }}"
      - "  PTR DNS Server: {{ ptr_dns_server }}"
  when: not is_multi_domain

# Display operation summary for multi-domain
- name: Display operation summary for multi-domain
  ansible.builtin.debug:
    msg:
      - "Operation Summary:"
      - "  Operation: {{ operation | capitalize }}"
      - "  Record Type: {{ record_type | upper }}"
      - "  Domains: {{ domains }}"
      - "  Hostnames: {{ hostnames }}"
      - "  Success Rate: {{ success_count }}/{{ total_count }} ({{ (success_count / total_count * 100) | int }}%)"
      - "  Logs: {{ ansible_log_path }}"
      - "  Consolidated Report: {{ consolidated_report_path | default('Not generated') }}"
  when: is_multi_domain and consolidated_results is defined and consolidated_results | length > 0

# Display PowerShell execution information
- name: Display PowerShell execution information
  ansible.builtin.debug:
    msg:
      - "PowerShell Execution Information:"
      - "  Return Code: {{ ps_result.rc | default('Unknown') }}"
      - "  Execution Status: {{ 'Success' if ps_result.rc is defined and ps_result.rc == 0 else 'Failed' if ps_result.rc is defined else 'Unknown' }}"
      - "  PowerShell Log: {{ powershell_log_path }}"
  when: ps_result is defined and not is_multi_domain

# Display email information if emails were sent
- name: Display email information
  ansible.builtin.debug:
    msg:
      - "Email Information:"
      - "  Report Email: {{ 'Successfully sent to ' + email_recipient if email_report | bool and email_result is defined and email_result.changed else 'Failed to send to ' + email_recipient if email_report | bool else 'Not sent' }}"
      - "  Logs Email: {{ 'Successfully sent to ' + email_recipient if email_logs | bool and email_result is defined and email_result.changed else 'Failed to send to ' + email_recipient if email_logs | bool else 'Not sent' }}"
      - "  SMTP Server: {{ smtp_server | default('Not used') }}"
      - "  Email Status: {{ 'Success' if email_result is defined and email_result.changed else 'Failed' if (email_report | bool or email_logs | bool) else 'Not attempted' }}"
  when: email_report | bool or email_logs | bool

# Display log storage information if logs were stored on target server
- name: Display log storage information
  ansible.builtin.debug:
    msg:
      - "Log Storage Information:"
      - "  Target Server: {{ target_server | default('Not used') }}"
      - "  Log Path: {{ log_settings.target_server.base_path }}"
      - "  Ansible Log: {{ target_ansible_log_filename | default('Not stored') }}"
      - "  PowerShell Log: {{ target_powershell_log_filename | default('Not stored') }}"
  when: store_logs_target_server | bool
