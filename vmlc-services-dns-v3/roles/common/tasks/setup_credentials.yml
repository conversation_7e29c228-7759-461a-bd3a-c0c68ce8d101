---
# Task for setting up credentials
# Author: CES Operational Excellence Team
# Contributors: <PERSON> (7409)

# Check if we're in developer testing mode
- name: Check if developer testing configuration is available
  ansible.builtin.stat:
    path: "{{ playbook_dir }}/../dev/vars/testing_config.yml"
  register: testing_config_stat

# Load testing configuration if available
- name: Load testing configuration if available
  ansible.builtin.include_vars:
    file: "{{ playbook_dir }}/../dev/vars/testing_config.yml"
  when: testing_config_stat.stat.exists

# Set developer mode flag
- name: Set developer mode flag
  ansible.builtin.set_fact:
    developer_mode: "{{ developer_testing is defined and developer_testing.use_mock_credentials | default(false) | bool }}"

# Load vault file with credentials
- name: Load vault file with credentials
  ansible.builtin.include_vars:
    file: "{{ credential_storage.vault_file }}"
  when: not developer_mode | bool

# Display developer mode information
- name: Display developer mode information
  ansible.builtin.debug:
    msg: "Running in developer mode with mock credentials"
  when: developer_mode | bool

# Set connection parameters for ADMT server (single domain)
- name: Set connection parameters for ADMT server
  ansible.builtin.set_fact:
    ansible_connection: "winrm"
    ansible_winrm_server_cert_validation: "ignore"
    ansible_user: "{{ credential_mapping[domain].username }}"
    ansible_password: "{{ credential_mapping[domain].password }}"
  when: domain is defined and domain in domain_keys and not is_multi_domain
  no_log: "{{ credential_security.use_no_log }}"

# Add ADMT server to inventory with connection parameters (single domain)
- name: Add ADMT server to inventory with connection parameters
  ansible.builtin.add_host:
    name: "{{ domains[domain].admt_server }}"
    groups: dns_admt_servers
    ansible_connection: "winrm"
    ansible_winrm_server_cert_validation: "ignore"
    ansible_user: "{{ domain_credentials[domain].dns_username }}"
    ansible_password: "{{ domain_credentials[domain].dns_password }}"
    ansible_winrm_transport: "ntlm"
    ansible_winrm_message_encryption: "auto"
    ansible_winrm_operation_timeout_sec: 120
    ansible_winrm_read_timeout_sec: 150
  when: domain is defined and domain in domain_keys and not is_multi_domain
  no_log: "{{ credential_security.use_no_log }}"

# Reset connection parameters for localhost operations
- name: Reset connection parameters for localhost operations
  ansible.builtin.set_fact:
    ansible_connection: "local"
    ansible_user: null
    ansible_password: null
    ansible_winrm_server_cert_validation: null
  when: domain is defined and domain in domain_keys and not is_multi_domain

# Set ADMT server, DNS server, and credentials (single domain)
- name: Set ADMT server, DNS server, and credentials
  ansible.builtin.set_fact:
    admt_server: "{{ domains[domain].admt_server }}"
    dns_server: "{{ dns_servers[domain] }}"
    dc_server: "{{ dns_servers[domain] }}"  # Set dc_server to match dns_server for consistency
    dc_username: "{{ domain_credentials[domain].dns_username }}"
    dc_password: "{{ domain_credentials[domain].dns_password }}"
  when: domain is defined and domain in domain_keys and not is_multi_domain
  no_log: "{{ credential_security.use_no_log }}"

# Set PTR DNS server based on domain (single domain)
- name: Set PTR DNS server based on domain
  ansible.builtin.set_fact:
    ptr_dns_server: "{{ ptr_dns_servers[domain] }}"
  when: domain is defined and domain in domain_keys and not is_multi_domain

# Create domain to credential mapping for multi-domain operations
- name: Create domain to credential mapping for multi-domain operations
  ansible.builtin.set_fact:
    domain_credential_map: "{{ domain_credential_map | default({}) | combine({item: {'username': domain_credentials[item].dns_username, 'password': domain_credentials[item].dns_password}}) }}"
  loop: "{{ domain_list }}"
  when: is_multi_domain and item in domain_keys
  no_log: "{{ credential_security.use_no_log }}"

# Display credential information (masked)
- name: Display credential information
  ansible.builtin.debug:
    msg:
      - "Using credentials for domain: {{ domain if not is_multi_domain else 'Multiple domains' }}"
      - "ADMT Server: {{ admt_server if not is_multi_domain else 'Multiple ADMT servers' }}"
      - "DNS Server: {{ dns_server if not is_multi_domain else 'Multiple DNS servers' }}"
  when: current_log_level | upper == log_settings.levels.debug
  notify: "credentials_setup"
