---
# Task for loading configuration
# Author: CES Operational Excellence Team
# Contributors: <PERSON> (7409)

- name: Display project information
  ansible.builtin.debug:
    msg:
      - "{{ project_defaults.project.name }} v{{ project_defaults.project.version }}"
      - "{{ project_defaults.project.description }}"
      - "Author: {{ project_defaults.project.author }}"
      - "Contributors: {{ project_defaults.project.contributors | join(', ') }}"
  run_once: true

- name: Validate configuration files
  ansible.builtin.stat:
    path: "{{ playbook_dir }}/../vars/{{ item }}"
  loop: "{{ include_configs }}"
  register: config_files
  run_once: true

- name: Ensure all configuration files exist
  ansible.builtin.assert:
    that: item.stat.exists
    fail_msg: "Configuration file {{ item.item }} does not exist"
  loop: "{{ config_files.results }}"
  run_once: true

# Load unified domain configuration
- name: Load unified domain configuration
  ansible.builtin.include_vars:
    file: "{{ playbook_dir }}/../vars/domains.yml"

# Load domain credentials
- name: Load domain credentials
  ansible.builtin.include_vars:
    file: "{{ playbook_dir }}/../vars/domain_credentials.yml"

- name: Set multi-domain variables
  ansible.builtin.set_fact:
    domains: "{{ input_domains | default('') }}"
    hostnames: "{{ input_hostnames | default('') }}"
    ip_addresses: "{{ input_ip_addresses | default('') }}"
    cname_targets: "{{ input_cname_targets | default('') }}"

- name: Set operation type
  ansible.builtin.set_fact:
    is_multi_domain: "{{ input_domains is defined and input_domains | length > 0 and input_hostnames is defined and input_hostnames | length > 0 }}"
    operation_type: "{{ 'multi-domain' if input_domains is defined and input_domains | length > 0 and input_hostnames is defined and input_hostnames | length > 0 else 'single-domain' }}"

- name: Display operation information
  ansible.builtin.debug:
    msg:
      - "Operation: {{ operation | capitalize }}"
      - "Record Type: {{ record_type | upper }}"
      - "Operation Type: {{ operation_type }}"
      - "Domain: {{ domain if not is_multi_domain else 'Multiple domains: ' + domains }}"
      - "Hostname: {{ hostname if not is_multi_domain else 'Multiple hostnames: ' + hostnames }}"
  notify: "config_loaded"
