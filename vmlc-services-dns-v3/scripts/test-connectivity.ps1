# Connectivity Test PowerShell Script
# Author: CES Operational Excellence Team
# Contributors: <PERSON> (7409)

[CmdletBinding()]
param (
    [Parameter(Mandatory = $true)]
    [string]$IPAddress,

    [Parameter(Mandatory = $false)]
    [int]$TimeoutMilliseconds = 1000,

    [Parameter(Mandatory = $false)]
    [int]$RetryCount = 3,

    [Parameter(Mandatory = $false)]
    [bool]$AsJson = $true,

    [Parameter(Mandatory = $false)]
    [string]$LogPath,

    [Parameter(Mandatory = $false)]
    [ValidateSet("Error", "Warning", "Info", "Debug")]
    [string]$LogLevel = "Info"
)

# Initialize result object
$result = @{
    success = $false
    message = ""
    changed = $false
    ip_address = $IPAddress
    is_alive = $false
    ping_results = @()
    dns_resolution = @{
        success = $false
        hostname = ""
        error = ""
    }
    timestamp = (Get-Date).ToString("yyyy-MM-ddTHH:mm:ss")
}

# Function to write log
function Write-Log {
    param (
        [Parameter(Mandatory = $true)]
        [string]$Message,

        [Parameter(Mandatory = $false)]
        [ValidateSet("Error", "Warning", "Info", "Debug")]
        [string]$Level = "Info"
    )

    $levelMap = @{
        "Error" = 1
        "Warning" = 2
        "Info" = 3
        "Debug" = 4
    }

    $currentLevelValue = $levelMap[$LogLevel]
    $messageLevelValue = $levelMap[$Level]

    if ($messageLevelValue -le $currentLevelValue) {
        $timestamp = (Get-Date).ToString("yyyy-MM-dd HH:mm:ss")
        $logMessage = "[$timestamp] [$Level] [Connectivity] [$IPAddress] $Message"

        Write-Host $logMessage

        if ($LogPath) {
            Add-Content -Path $LogPath -Value $logMessage
        }
    }
}

# Main script logic
try {
    Write-Log -Message "Testing connectivity to $IPAddress" -Level "Info"

    # Test ping connectivity
    for ($i = 1; $i -le $RetryCount; $i++) {
        Write-Log -Message "Ping attempt $i of $RetryCount" -Level "Debug"

        $ping = Test-Connection -ComputerName $IPAddress -Count 1 -Quiet -TimeoutSeconds ($TimeoutMilliseconds / 1000)

        $pingResult = @{
            attempt = $i
            success = $ping
            timestamp = (Get-Date).ToString("yyyy-MM-ddTHH:mm:ss")
        }

        $result.ping_results += $pingResult

        if ($ping) {
            $result.is_alive = $true
            Write-Log -Message "Ping successful on attempt $i" -Level "Info"
            break
        }
        else {
            Write-Log -Message "Ping failed on attempt $i" -Level "Debug"
        }
    }

    # Try to resolve IP address to hostname
    try {
        $hostname = [System.Net.Dns]::GetHostEntry($IPAddress).HostName
        $result.dns_resolution.success = $true
        $result.dns_resolution.hostname = $hostname
        Write-Log -Message "Resolved $IPAddress to $hostname" -Level "Info"
    }
    catch {
        $result.dns_resolution.error = $_.Exception.Message
        Write-Log -Message "Failed to resolve $IPAddress: $_" -Level "Warning"
    }

    # Final result message
    if ($result.is_alive) {
        $result.success = $true
        $result.message = "Host $IPAddress is responding to ping"
        Write-Log -Message $result.message -Level "Info"
    }
    else {
        $result.success = $false
        $result.message = "Host $IPAddress is not responding to ping"
        Write-Log -Message $result.message -Level "Warning"
    }
}
catch {
    $errorMessage = "Error testing connectivity: $_"
    Write-Log -Message $errorMessage -Level "Error"
    $result.success = $false
    $result.message = $errorMessage
    $result.error = $_.Exception.Message
}

# Output result
if ($AsJson) {
    $result | ConvertTo-Json -Depth 10
}
else {
    $result
}
