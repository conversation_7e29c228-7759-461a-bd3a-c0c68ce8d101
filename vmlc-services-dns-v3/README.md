# VMLC Services - DNS Management

## Table of Contents

1. [Overview](#1-overview)
2. [Features](#2-features)
3. [Project Structure](#3-project-structure)
4. [Configuration Files](#4-configuration-files)
   - [Domain Configuration](#41-domain-configuration-domain_configyml)
   - [Credentials Configuration](#42-credentials-configuration-credentials_configyml)
   - [Email Configuration](#43-email-configuration-email_configyml)
   - [Log Configuration](#44-log-configuration-log_configyml)
   - [Operations Configuration](#45-operations-configuration-operations_configyml)
   - [PowerShell Configuration](#46-powershell-configuration-powershell_configyml)
   - [Report Configuration](#47-report-configuration-report_configyml)
   - [Testing Configuration](#48-testing-configuration-testing_configyml)
5. [Usage](#5-usage)
   - [Ansible Automation Platform (AAP)](#51-ansible-automation-platform-aap)
   - [Local Development and Testing](#52-local-development-and-testing)
   - [Detailed Usage Documentation](#53-detailed-usage-documentation)
6. [Benefits of Standardization](#6-benefits-of-standardization)
   - [Centralized Configuration](#61-centralized-configuration)
   - [Consistent Structure](#62-consistent-structure)
   - [Improved Error Handling](#63-improved-error-handling)
   - [Enhanced Documentation](#64-enhanced-documentation)
   - [Best of Both Worlds Approach](#65-best-of-both-worlds-approach)
   - [Idempotent Operations](#66-idempotent-operations)
7. [Contributing](#7-contributing)
8. [License](#8-license)

## 1. Overview

The DNS Management System is a comprehensive solution for managing DNS records across multiple domains. It provides a standardized approach to DNS operations, with centralized configuration, consistent logging, and robust error handling.

**Author:** CES Operational Excellence Team
**Contributors:** Muhammad Syazani Bin Mohamed Khairi (7409)

## 2. Features

- **Centralized Configuration:** All configuration is centralized in dedicated configuration files
- **Standardized Logging:** Consistent log file naming and formatting across all operations
- **Robust Error Handling:** Comprehensive error handling and reporting
- **Multi-Domain Support:** Support for operations across multiple domains
- **Reporting:** Detailed reports for all operations
- **Email Notifications:** Configurable email notifications for operation results
- **Domain Groups:** Logical grouping of domains for easier management
- **Credential Management:** Secure credential handling with multiple storage options
- **PowerShell Integration:** Seamless integration with PowerShell scripts for DNS operations
- **Idempotent Operations:** All operations are idempotent, ensuring they can be safely repeated

## 3. Project Structure

```
vmlc-services-dns/
│
├── README.md                                  # Project overview and documentation
│
├── dev/                                       # Development-specific files
│   ├── playbooks/                             # Development playbooks
│   │   └── dev_manage_dns.yml                 # Developer-friendly wrapper playbook
│   ├── scripts/                               # Development scripts
│   │   └── run_dns_management.sh              # Helper script for developers
│   ├── vars/                                  # Development variables
│   │   └── mock_credentials.yml               # Mock credentials for testing
│   ├── .gitignore                             # Git ignore file for development
│   ├── README.md                              # Developer documentation
│   └── setup.sh                               # Development environment setup script
│
├── playbooks/                                 # Ansible playbooks
│   └── manage_dns.yml                         # Main playbook for DNS management
│
├── roles/                                     # Ansible roles
│   ├── common/                                # Common tasks shared across roles
│   │   ├── README.md                          # Common role documentation
│   │   ├── defaults/                          # Default variables
│   │   │   └── main.yml                       # Default variables for common role
│   │   ├── handlers/                          # Handlers
│   │   │   └── main.yml                       # Handlers for common role
│   │   ├── meta/                              # Role metadata
│   │   │   └── main.yml                       # Metadata for common role
│   │   ├── tasks/                             # Task files
│   │   │   ├── cleanup.yml                    # Cleanup tasks
│   │   │   ├── display_summary.yml            # Display summary tasks
│   │   │   ├── handle_errors.yml              # Error handling tasks
│   │   │   ├── initialize_results.yml         # Initialize results tasks
│   │   │   ├── load_configuration.yml         # Load configuration tasks
│   │   │   ├── main.yml                       # Main tasks file
│   │   │   ├── process_results.yml            # Process results tasks
│   │   │   ├── rotate_logs.yml                # Rotate logs tasks
│   │   │   ├── set_log_paths.yml              # Set log paths tasks
│   │   │   ├── setup_credentials.yml          # Setup credentials tasks
│   │   │   ├── setup_logging.yml              # Setup logging tasks
│   │   │   ├── validate_domain.yml            # Validate domain tasks
│   │   │   └── validate_operation.yml         # Validate operation tasks
│   │   └── vars/                              # Variables
│   │       └── main.yml                       # Variables for common role
│   │
│   ├── dns_operations/                        # DNS operations role
│   │   ├── README.md                          # DNS operations role documentation
│   │   ├── defaults/                          # Default variables
│   │   │   └── main.yml                       # Default variables for DNS operations role
│   │   ├── handlers/                          # Handlers
│   │   │   └── main.yml                       # Handlers for DNS operations role
│   │   ├── meta/                              # Role metadata
│   │   │   └── main.yml                       # Metadata for DNS operations role
│   │   ├── tasks/                             # Task files
│   │   │   ├── add.yml                        # Add DNS record tasks
│   │   │   ├── main.yml                       # Main tasks file
│   │   │   ├── process_domain_async.yml       # Process domain async tasks
│   │   │   ├── remove.yml                     # Remove DNS record tasks
│   │   │   ├── update.yml                     # Update DNS record tasks
│   │   │   └── verify.yml                     # Verify DNS record tasks
│   │   ├── templates/                         # Templates
│   │   │   ├── dns_operation_summary.j2       # DNS operation summary template
│   │   │   └── powershell_params.j2           # PowerShell parameters template
│   │   └── vars/                              # Variables
│   │       └── main.yml                       # Variables for DNS operations role
│   │
│   └── reporting/                             # Reporting role
│       ├── README.md                          # Reporting role documentation
│       ├── defaults/                          # Default variables
│       │   └── main.yml                       # Default variables for reporting role
│       ├── handlers/                          # Handlers
│       │   └── main.yml                       # Handlers for reporting role
│       ├── meta/                              # Role metadata
│       │   └── main.yml                       # Metadata for reporting role
│       ├── tasks/                             # Task files
│       │   ├── email_logs.yml                 # Email logs tasks
│       │   ├── email_report.yml               # Email report tasks
│       │   ├── generate_consolidated_report.yml # Generate consolidated report tasks
│       │   ├── generate_report.yml            # Generate report tasks
│       │   ├── main.yml                       # Main tasks file
│       │   ├── set_email_recipient.yml        # Set email recipient tasks
│       │   ├── set_smtp_server.yml            # Set SMTP server tasks
│       │   └── upload_logs_to_target_server.yml # Upload logs tasks
│       ├── templates/                         # Templates
│       │   ├── email_params.j2                # Email parameters template
│       │   └── report_summary.j2              # Report summary template
│       └── vars/                              # Variables
│           └── main.yml                       # Variables for reporting role
│
├── scripts/                                   # PowerShell scripts
│   ├── set-dns.ps1                            # Main script for DNS operations
│   └── test-connectivity.ps1                  # Script for testing connectivity
│
├── vars/                                      # Variable files
│   ├── defaults.yml                           # Default variables
│   ├── domains.yml                            # Unified domain configuration
│   ├── domain_credentials.yml                 # Domain credentials
│   ├── credentials_config.yml                 # Credential configuration
│   ├── email_config.yml                       # Email configuration
│   ├── log_config.yml                         # Log configuration
│   ├── operations_config.yml                  # Operations configuration
│   ├── powershell_config.yml                  # PowerShell configuration
│   └── report_config.yml                      # Report configuration
│
├── logs/                                      # Log files
│   ├── ansible/                               # Ansible logs
│   │   └── .gitkeep                           # Placeholder for empty directory
│   ├── powershell/                            # PowerShell logs
│   │   └── .gitkeep                           # Placeholder for empty directory
│   ├── progress/                              # Progress logs
│   │   └── .gitkeep                           # Placeholder for empty directory
│   ├── archive/                               # Archived logs
│   │   └── .gitkeep                           # Placeholder for empty directory
│   └── .gitkeep                               # Placeholder for empty directory
│
├── reports/                                   # Reports
│   └── .gitkeep                               # Placeholder for empty directory
│
├── templates/                                 # Templates
│   ├── reports/                               # Report templates
│   │   ├── consolidated_report.html.j2        # Consolidated report template
│   │   └── dns_report.html.j2                 # DNS report template
│   └── emails/                                # Email templates
│       ├── consolidated_report_email_body.j2  # Consolidated report email template
│       ├── logs_email_body.j2                 # Logs email template
│       └── report_email_body.j2               # Report email template
│
├── docs/                                      # Documentation
│   ├── USAGE.md                               # Usage documentation
│   ├── TECHNICAL.md                           # Technical documentation
│   └── IDEMPOTENCY.md                         # Idempotency documentation
│
└── tests/                                     # Tests
    └── molecule/                              # Molecule tests
        ├── default/                           # Default test scenario
        │   ├── converge.yml                   # Default converge playbook
        │   ├── molecule.yml                   # Default molecule configuration
        │   ├── verify.yml                     # Default verify playbook
        │   └── molecule/                      # Default molecule directory
        │       └── .gitkeep                   # Placeholder for empty directory
        ├── add/                               # Add operation test scenario
        │   ├── converge.yml                   # Add converge playbook
        │   ├── molecule.yml                   # Add molecule configuration
        │   ├── verify.yml                     # Add verify playbook
        │   └── molecule/                      # Add molecule directory
        │       └── .gitkeep                   # Placeholder for empty directory
        ├── remove/                            # Remove operation test scenario
        │   ├── converge.yml                   # Remove converge playbook
        │   ├── molecule.yml                   # Remove molecule configuration
        │   ├── verify.yml                     # Remove verify playbook
        │   └── molecule/                      # Remove molecule directory
        │       └── .gitkeep                   # Placeholder for empty directory
        ├── update/                            # Update operation test scenario
        │   ├── converge.yml                   # Update converge playbook
        │   ├── molecule.yml                   # Update molecule configuration
        │   ├── verify.yml                     # Update verify playbook
        │   └── molecule/                      # Update molecule directory
        │       └── .gitkeep                   # Placeholder for empty directory
        └── verify/                            # Verify operation test scenario
            ├── converge.yml                   # Verify converge playbook
            ├── molecule.yml                   # Verify molecule configuration
            ├── verify.yml                     # Verify verify playbook
            └── molecule/                      # Verify molecule directory
                └── .gitkeep                   # Placeholder for empty directory
```

## 4. Configuration Files

### 4.1. Unified Domain Configuration (`domains.yml`)

Contains all non-sensitive domain-related configuration, including:
- Domain metadata (environment, network zone, data center)
- ADMT server information
- DNS server information
- PTR DNS server information
- Domain relationships
- Domain groups
- Domain keys for validation
- Default DNS settings

This file serves as the single source of truth for all domain-related configuration, making it easier to update DNS server and PTR DNS server values in one centralized location.

### 4.2. Domain Credentials (`domain_credentials.yml`)

Contains all sensitive domain credential information, including:
- DNS username variables for each domain
- DNS password variables for each domain

This file is separate from the main domain configuration to improve security and make credential management easier.

### 4.3. Credentials Configuration (`credentials_config.yml`)

Contains all credential-related configuration, including:
- Credential selection strategy
- Default credentials
- Environment-specific defaults
- Credential storage method
- Credential security settings
- Domain-to-credential mapping

### 4.4. Email Configuration (`email_config.yml`)

Contains all email-related configuration, including:
- SMTP server configuration
- Email recipients
- Email templates
- Attachment settings
- Email flags

### 4.5. Log Configuration (`log_config.yml`)

Contains all log-related configuration, including:
- Log directories
- Log file naming format
- Log types
- Log levels
- Target server log paths
- Log rotation settings
- Log format strings
- Log flags

### 4.6. Operations Configuration (`operations_config.yml`)

Contains all operation-related configuration, including:
- Operation definitions
- Required parameters
- Validation rules
- Record type definitions
- Multi-domain operation settings
- Operation defaults

### 4.7. PowerShell Configuration (`powershell_config.yml`)

Contains all PowerShell-related configuration, including:
- Script paths
- Default parameters
- Log paths
- Error handling settings
- Timeouts
- Special configurations
- PowerShell flags

### 4.8. Report Configuration (`report_config.yml`)

Contains all report-related configuration, including:
- Report directories
- Report file naming format
- Report types
- Report templates
- Report content
- PDF generation settings
- Report flags

### 4.9. Testing Configuration (`dev/vars/testing_config.yml`)

Contains all testing-related configuration, including:
- Test environments
- Test cases for different operations
- Molecule configuration
- Test platforms
- Test sequence

This file is located in the `dev/vars` directory to maintain a clean separation between production and development code.

## 5. Usage

### 5.1. Ansible Automation Platform (AAP)

The DNS Management System is primarily designed to be used through Ansible Automation Platform (AAP). This provides a user-friendly interface with proper access controls and audit trails.

#### Using the AAP Survey Form

The DNS Management job template includes a survey form with fields for all required parameters:

- Operation (verify, add, remove, update)
- Record Type (a, cname, ptr)
- Hostname
- Domain
- IP Address (for A or PTR records)
- CNAME Target (for CNAME records)
- TTL
- Description
- Ticket Number

#### Using Extra Variables

Advanced users can also launch jobs with extra variables in JSON format:

```json
{
  "operation": "verify",
  "record_type": "a",
  "hostname": "server01",
  "domain": "example.com",
  "ticket": "INC123456"
}
```

### 5.2. Local Development and Testing

This project includes a dedicated `dev` directory with tools and resources specifically designed for local development and testing. This separation ensures a clean production codebase while providing developers with the flexibility they need.

#### Development Directory Structure

```
dev/
├── playbooks/                 # Development playbooks
│   └── dev_manage_dns.yml     # Developer-friendly wrapper playbook
├── scripts/                   # Development scripts
│   └── run_dns_management.sh  # Helper script for developers
├── vars/                      # Development variables
│   ├── mock_credentials.yml   # Mock credentials for testing
│   └── testing_config.yml     # Testing configuration
├── .gitignore                 # Git ignore file for development
├── README.md                  # Developer documentation
└── setup.sh                   # Development environment setup script
```

#### First-time Setup

Developers should run the setup script to configure their local environment:

```bash
./dev/setup.sh
```

This script will:
- Create a symlink for the run_dns_management.sh script in the project root
- Check if you have a vault password file and offer to create one
- Install Python requirements if a requirements.txt file exists
- Install Ansible collections if a collections/requirements.yml file exists

#### Using the Developer Helper Script

The recommended way to run the playbook for local development is using the helper script:

```bash
./run_dns_management.sh --dev -e "operation=verify record_type=a domain=devhealthgrp.com.sg hostname=ANSORTEST"
```

#### Using the Developer Playbook Directly

Alternatively, you can run the developer playbook directly:

```bash
ansible-playbook dev/playbooks/dev_manage_dns.yml -e "operation=verify record_type=a domain=devhealthgrp.com.sg hostname=ANSORTEST"
```

#### Mock Credentials for Local Testing

The development environment includes mock credentials for local testing, which can be found in `dev/vars/mock_credentials.yml`. These are used when `developer_testing.use_mock_credentials` is set to `true` in `dev/vars/testing_config.yml`.

#### Vault Password Handling

When working with encrypted vault files, you have several options:

1. **Using the `--ask-vault-pass` flag**:
   ```
   ansible-playbook playbooks/manage_dns.yml --ask-vault-pass -e "operation=verify record_type=a domain=devhealthgrp.com.sg hostname=ANSORTEST"
   ```

2. **Using a vault password file**:
   ```
   ansible-playbook playbooks/manage_dns.yml --vault-password-file=~/.vault_pass.txt -e "operation=verify record_type=a domain=devhealthgrp.com.sg hostname=ANSORTEST"
   ```

3. **Using an environment variable**:
   ```
   export ANSIBLE_VAULT_PASSWORD_FILE=~/.vault_pass.txt
   ansible-playbook playbooks/manage_dns.yml -e "operation=verify record_type=a domain=devhealthgrp.com.sg hostname=ANSORTEST"
   ```

#### Developer Documentation

For comprehensive information about local development and testing, see the dedicated developer documentation:

[**→ Developer Guide (dev/README.md)**](dev/README.md)

### 5.3. Detailed Usage Documentation

For comprehensive usage instructions, see the [USAGE.md](docs/USAGE.md) documentation.

## 6. Benefits of Standardization

### 6.1. Centralized Configuration

All configuration is centralized in dedicated configuration files, making it easier to manage and update. This approach:
- Reduces duplication
- Improves maintainability
- Makes it easier to understand the system
- Allows for easier updates

The DNS server and PTR DNS server configuration is particularly well-centralized in the `domains.yml` file, providing a single source of truth for these critical values. This makes it easy to update DNS server information without having to modify multiple files.

### 6.2. Consistent Structure

The project follows a consistent structure, with:
- Clear separation of concerns
- Standardized naming conventions
- Consistent file organization
- Logical grouping of related files

### 6.3. Improved Error Handling

The system includes comprehensive error handling, with:
- Detailed error messages
- Consistent error reporting
- Proper error logging
- Error recovery mechanisms

### 6.4. Enhanced Documentation

The project includes detailed documentation, with:
- Clear usage instructions
- Technical documentation
- Inline comments
- Standardized documentation format

### 6.5. Best of Both Worlds Approach

The project is designed with a "best of both worlds" approach, providing:

- **Clean Configuration for AAP:** The main codebase is optimized for Ansible Automation Platform with clean, production-ready configuration.

- **Flexibility for Local Development:** The dedicated `dev` directory provides tools and resources specifically for local development and testing.

This approach offers several benefits:

- **Separation of Concerns:** Production code is kept separate from development utilities
- **Simplified Onboarding:** New developers can quickly set up their environment
- **Consistent Testing:** Development tools ensure consistent testing across environments
- **Reduced Friction:** Developers can work efficiently without compromising production standards
- **Maintainability:** Changes to development tools don't affect the production codebase

### 6.6. Idempotent Operations

All operations in the system are designed to be idempotent, meaning they can be safely repeated without causing unintended side effects. This provides several benefits:

- **Safety:** Operations can be retried without risk of duplicate records or other issues
- **Reliability:** Failed operations can be safely retried
- **Auditability:** Operations can be verified without changing the system state
- **Automation:** Scripts and scheduled tasks can safely run multiple times

For detailed information about idempotency guarantees, see the [IDEMPOTENCY.md](docs/IDEMPOTENCY.md) documentation.

## 7. Contributing

Please follow these guidelines when contributing to the project:
- Use the standardized file structure
- Follow the naming conventions
- Update the appropriate configuration files
- Add proper documentation
- Include tests for new features

## 8. License

This project is proprietary and confidential. Unauthorized copying, distribution, or use is strictly prohibited.
