---
# DNS Management Playbook
# This playbook orchestrates DNS operations across multiple domains
#
# Author: CES Operational Excellence Team
# Contributors: <PERSON> Bin <PERSON> (7409)

- name: DNS Management Operations
  hosts: localhost
  gather_facts: true

  vars_files:
    - "../vars/defaults.yml"
    - "../vars/domains.yml"
    - "../vars/domain_credentials.yml"
    - "../vars/credentials_config.yml"
    - "../vars/vault.yml"
    - "../vars/email_config.yml"
    - "../vars/log_config.yml"
    - "../vars/operations_config.yml"
    - "../vars/powershell_config.yml"
    - "../vars/report_config.yml"

  vars:
    # Path variables
    roles_path: "{{ playbook_dir }}/../roles"
    scripts_path: "{{ playbook_dir }}/../scripts"

    # Process input variables with proper type conversion
    operation: "{{ operation if operation is defined else 'verify' }}"
    record_type: "{{ record_type | lower if record_type is defined else 'a' }}"
    # Single domain parameters
    hostname: "{{ hostname if hostname is defined else '' }}"
    domain: "{{ domain if domain is defined else '' }}"
    ip_address: "{{ ip_address if ip_address is defined else '' }}"
    cname_target: "{{ cname_target if cname_target is defined else '' }}"

    # Multi-domain parameters - using different approach to avoid circular references
    input_domains: "{{ domains | default('') }}"
    input_hostnames: "{{ hostnames | default('') }}"
    input_ip_addresses: "{{ ip_addresses | default('') }}"
    input_cname_targets: "{{ cname_targets | default('') }}"
    ttl: "{{ ttl if ttl is defined else 3600 }}"
    description: "{{ description if description is defined else 'Managed by Ansible DNS Management' }}"
    manage_ptr: "{{ manage_ptr | bool if manage_ptr is defined else true }}"
    force_remove: "{{ force_remove | bool if force_remove is defined else false }}"
    ticket: "{{ ticket if ticket is defined else 'INC-123456' }}"

    # Reporting and logging variables
    generate_report: false  # Hardcoded to avoid recursion
    email_report: false  # Hardcoded to avoid recursion
    email_recipient: ""  # Hardcoded to avoid recursion
    testing_mode: false  # Hardcoded to avoid recursion
    email_logs: false  # Hardcoded to avoid recursion
    # Use a completely different variable name to avoid recursion
    current_log_level: "{{ 'INFO' }}"  # Hardcoded to INFO for now to avoid recursion
    store_logs_target_server: false  # Hardcoded to avoid recursion

  pre_tasks:
    # Load configuration
    - name: Load configuration
      ansible.builtin.include_tasks: "{{ roles_path }}/common/tasks/load_configuration.yml"

    # Validate domain
    - name: Validate domain configuration
      ansible.builtin.include_tasks: "{{ roles_path }}/common/tasks/validate_domain.yml"

    # Set up logging
    - name: Set up logging
      ansible.builtin.include_tasks: "{{ roles_path }}/common/tasks/setup_logging.yml"

    # Validate operation parameters
    - name: Validate operation parameters
      ansible.builtin.include_tasks: "{{ roles_path }}/common/tasks/validate_operation.yml"

    # Set up credentials
    - name: Set up credentials
      ansible.builtin.include_tasks: "{{ roles_path }}/common/tasks/setup_credentials.yml"
      no_log: "{{ credential_security.use_no_log }}"

  tasks:
    # Initialize results
    - name: Initialize results
      ansible.builtin.include_tasks: "{{ roles_path }}/common/tasks/initialize_results.yml"

    # Execute DNS operations
    - name: Execute DNS operations
      block:
        # Single domain operation
        - name: Execute single domain operation
          ansible.builtin.include_role:
            name: "{{ roles_path }}/dns_operations"
            tasks_from: "{{ operation }}.yml"
          when: not is_multi_domain

        # Multi-domain operation
        - name: Execute multi-domain operation
          ansible.builtin.include_role:
            name: "{{ roles_path }}/dns_operations"
          when: is_multi_domain
      rescue:
        - name: Handle operation errors
          ansible.builtin.include_tasks: "{{ roles_path }}/common/tasks/handle_errors.yml"

    # Process results
    - name: Process operation results
      ansible.builtin.include_tasks: "{{ roles_path }}/common/tasks/process_results.yml"

    # Generate reports
    - name: Generate reports
      block:
        # Generate standard report
        - name: Generate standard report
          ansible.builtin.include_role:
            name: "{{ roles_path }}/reporting"
            tasks_from: generate_report.yml
          when: generate_report | bool and not is_multi_domain

        # Generate consolidated report
        - name: Generate consolidated report
          ansible.builtin.include_role:
            name: "{{ roles_path }}/reporting"
            tasks_from: generate_consolidated_report.yml
          when: generate_report | bool and is_multi_domain and consolidated_results is defined and consolidated_results | length > 0
      when: generate_report | bool

    # Send emails
    - name: Send emails
      block:
        # Email report
        - name: Email report
          ansible.builtin.include_role:
            name: "{{ roles_path }}/reporting"
            tasks_from: email_report.yml
          when: generate_report | bool and email_report | bool

        # Email logs
        - name: Email logs
          ansible.builtin.include_role:
            name: "{{ roles_path }}/reporting"
            tasks_from: email_logs.yml
          when: email_logs | bool
      when: email_report | bool or email_logs | bool

    # Upload logs
    - name: Upload logs
      ansible.builtin.include_role:
        name: "{{ roles_path }}/reporting"
        tasks_from: upload_logs_to_target_server.yml
      when: store_logs_target_server | bool

  post_tasks:
    # Clean up
    - name: Clean up
      ansible.builtin.include_tasks: "{{ roles_path }}/common/tasks/cleanup.yml"

    # Display summary
    - name: Display operation summary
      ansible.builtin.include_tasks: "{{ roles_path }}/common/tasks/display_summary.yml"
