#!/bin/bash
# Development environment setup script
# Author: CES Operational Excellence Team
# Contributors: <PERSON> (7409)

# Get the directory of this script
SCRIPT_DIR="$( cd "$( dirname "${BASH_SOURCE[0]}" )" && pwd )"
PROJECT_ROOT="$( cd "$SCRIPT_DIR/.." && pwd )"

echo "Setting up development environment for DNS Management"
echo "Project root: $PROJECT_ROOT"

# Create symlink for the run script
echo "Creating symlink for run_dns_management.sh..."
ln -sf "$PROJECT_ROOT/dev/scripts/run_dns_management.sh" "$PROJECT_ROOT/run_dns_management.sh"
chmod +x "$PROJECT_ROOT/dev/scripts/run_dns_management.sh"

# Check if vault password file exists
VAULT_FILE="$HOME/.vault_pass.txt"
if [ ! -f "$VAULT_FILE" ]; then
  echo "Vault password file not found at $VAULT_FILE"
  read -p "Would you like to create it? (y/n) " -n 1 -r
  echo
  if [[ $REPLY =~ ^[Yy]$ ]]; then
    read -s -p "Enter vault password: " VAULT_PASSWORD
    echo
    echo "$VAULT_PASSWORD" > "$VAULT_FILE"
    chmod 600 "$VAULT_FILE"
    echo "Vault password file created at $VAULT_FILE"
  fi
fi

# Check if Python requirements are installed
echo "Checking Python requirements..."
if [ -f "$PROJECT_ROOT/requirements.txt" ]; then
  pip install -r "$PROJECT_ROOT/requirements.txt"
else
  echo "No requirements.txt found, skipping Python dependencies"
fi

# Check if Ansible collections are installed
echo "Checking Ansible collections..."
if [ -f "$PROJECT_ROOT/collections/requirements.yml" ]; then
  ansible-galaxy collection install -r "$PROJECT_ROOT/collections/requirements.yml"
else
  echo "No collections/requirements.yml found, skipping Ansible collections"
fi

echo "Development environment setup complete!"
echo
echo "To run the DNS Management playbook in developer mode:"
echo "  $PROJECT_ROOT/run_dns_management.sh --dev -e \"operation=verify record_type=a domain=devhealthgrp.com.sg hostname=ANSORTEST\""
echo
echo "For more information, see the developer documentation:"
echo "  $PROJECT_ROOT/dev/README.md"
