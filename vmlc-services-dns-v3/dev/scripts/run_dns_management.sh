#!/bin/bash
# Helper script for running DNS Management playbook
# Author: CES Operational Excellence Team
# Contributors: <PERSON> Bin <PERSON> (7409)

# Get the directory of this script
SCRIPT_DIR="$( cd "$( dirname "${BASH_SOURCE[0]}" )" && pwd )"
PROJECT_ROOT="$( cd "$SCRIPT_DIR/../.." && pwd )"

# Default values
PLAYBOOK="$PROJECT_ROOT/playbooks/manage_dns.yml"
DEVELOPER_MODE=false
VAULT_METHOD="prompt"
VAULT_FILE="~/.vault_pass.txt"
EXTRA_VARS=""

# Parse command line arguments
while [[ $# -gt 0 ]]; do
  case $1 in
    --dev)
      DEVELOPER_MODE=true
      PLAYBOOK="$PROJECT_ROOT/dev/playbooks/dev_manage_dns.yml"
      shift
      ;;
    --vault-method=*)
      VAULT_METHOD="${1#*=}"
      shift
      ;;
    --vault-file=*)
      VAULT_FILE="${1#*=}"
      shift
      ;;
    -e|--extra-vars)
      EXTRA_VARS="$2"
      shift
      shift
      ;;
    *)
      echo "Unknown option: $1"
      exit 1
      ;;
  esac
done

# Set vault password options
VAULT_OPTS=""
if [ "$DEVELOPER_MODE" = false ]; then
  case $VAULT_METHOD in
    file)
      VAULT_OPTS="--vault-password-file=$VAULT_FILE"
      ;;
    prompt)
      VAULT_OPTS="--ask-vault-pass"
      ;;
    environment)
      # No options needed, relies on ANSIBLE_VAULT_PASSWORD_FILE environment variable
      ;;
    *)
      echo "Unknown vault method: $VAULT_METHOD"
      exit 1
      ;;
  esac
fi

# Display information
echo "Running DNS Management playbook"
echo "Project root: $PROJECT_ROOT"
echo "Playbook: $PLAYBOOK"
echo "Developer mode: $DEVELOPER_MODE"
if [ "$DEVELOPER_MODE" = false ]; then
  echo "Vault method: $VAULT_METHOD"
fi

# Run the playbook
if [ -n "$EXTRA_VARS" ]; then
  ansible-playbook $PLAYBOOK $VAULT_OPTS -e "$EXTRA_VARS"
else
  ansible-playbook $PLAYBOOK $VAULT_OPTS
fi
