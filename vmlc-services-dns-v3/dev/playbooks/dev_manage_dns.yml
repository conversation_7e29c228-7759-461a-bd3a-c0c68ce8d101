---
# Developer-friendly DNS Management Playbook
# This playbook is a wrapper around manage_dns.yml for local development and testing
#
# Author: CES Operational Excellence Team
# Contributors: <PERSON> (7409)

- name: Load testing configuration
  hosts: localhost
  gather_facts: false

  vars_files:
    - "../vars/testing_config.yml"
    - "../vars/mock_credentials.yml"

  tasks:
    - name: Display developer mode information
      ansible.builtin.debug:
        msg:
          - "Running in developer mode"
          - "Mock credentials: {{ developer_testing.use_mock_credentials | bool }}"
          - "Vault password method: {{ developer_testing.vault_password_options.method }}"

    - name: Set mock credentials if enabled
      ansible.builtin.set_fact:
        var_dns_devhealthgrp_username: "{{ mock_credentials.var_dns_devhealthgrp_username }}"
        var_dns_devhealthgrp_password: "{{ mock_credentials.var_dns_devhealthgrp_password }}"
        var_dns_healthgrp_username: "{{ mock_credentials.var_dns_healthgrp_username }}"
        var_dns_healthgrp_password: "{{ mock_credentials.var_dns_healthgrp_password }}"
      when: developer_testing.use_mock_credentials | bool

    - name: Include main DNS management playbook
      ansible.builtin.include_playbook: ../../playbooks/manage_dns.yml
