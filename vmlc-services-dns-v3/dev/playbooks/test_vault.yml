---
# Test playbook for vault variables
# Author: CES Operational Excellence Team
# Contributors: <PERSON> (7409)

- name: Test Vault Variables
  hosts: localhost
  gather_facts: false
  
  tasks:
    - name: Load vault file
      ansible.builtin.include_vars:
        file: "{{ playbook_dir }}/../../vars/vault.yml"
      
    - name: Display vault variables
      ansible.builtin.debug:
        msg: 
          - "Username: {{ var_dns_devhealthgrp_username }}"
          - "Password: {{ var_dns_devhealthgrp_password }}"
