---
# Simple test playbook for DNS Management
# Author: CES Operational Excellence Team
# Contributors: <PERSON> (7409)

- name: Simple Test
  hosts: localhost
  gather_facts: false
  
  vars:
    domain: "devhealthgrp.com.sg"
    hostname: "ANSORTEST"
    record_type: "a"
    operation: "verify"
    
  tasks:
    - name: Load vault file
      ansible.builtin.include_vars:
        file: "../../vars/vault.yml"
        
    - name: Display vault variables
      ansible.builtin.debug:
        msg: 
          - "Username: {{ var_dns_devhealthgrp_username }}"
          - "Password: {{ var_dns_devhealthgrp_password }}"
          
    - name: Display operation information
      ansible.builtin.debug:
        msg:
          - "Operation: {{ operation | capitalize }}"
          - "Record Type: {{ record_type | upper }}"
          - "Domain: {{ domain }}"
          - "Hostname: {{ hostname }}"
          
    - name: Set connection parameters
      ansible.builtin.set_fact:
        ansible_connection: "winrm"
        ansible_winrm_server_cert_validation: "ignore"
        ansible_user: "{{ var_dns_devhealthgrp_username }}"
        ansible_password: "{{ var_dns_devhealthgrp_password }}"
        
    - name: Display connection parameters
      ansible.builtin.debug:
        msg:
          - "Connection: {{ ansible_connection }}"
          - "Username: {{ ansible_user }}"
          - "Password: {{ ansible_password | default('***') }}"
          
    - name: Simulate DNS verification
      ansible.builtin.debug:
        msg: "Simulating verification of {{ record_type | upper }} record {{ hostname }}.{{ domain }}"
        
    - name: Set simulated result
      ansible.builtin.set_fact:
        dns_operation_result:
          success: true
          message: "Record {{ hostname }}.{{ domain }} exists"
          record_exists: true
          record_details:
            name: "{{ hostname }}.{{ domain }}"
            type: "{{ record_type | upper }}"
            ttl: 3600
            data: "***********"
            
    - name: Display operation result
      ansible.builtin.debug:
        msg: "{{ dns_operation_result.message }}"
