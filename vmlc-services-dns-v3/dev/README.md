# Developer Guide for VMLC Services - DNS Management

## Overview
This directory contains development-specific files and tools for the DNS Management project. It is designed to make local development and testing easier while keeping the main project structure clean.

## Table of Contents
1. [Directory Structure](#directory-structure)
2. [Getting Started](#getting-started)
   - [Setup](#setup)
   - [Running the Playbook](#running-the-playbook)
3. [Development Workflow](#development-workflow)
4. [Testing](#testing)
   - [Running Molecule Tests](#running-molecule-tests)
   - [Running Individual Test Scenarios](#running-individual-test-scenarios)
5. [Credential Management](#credential-management)
   - [Mock Credentials](#mock-credentials)
   - [Vault Password Handling](#vault-password-handling)
6. [Troubleshooting](#troubleshooting)
   - [Common Issues](#common-issues)
   - [Getting Help](#getting-help)

## Directory Structure
```
dev/
├── playbooks/                 # Development playbooks
│   ├── dev_manage_dns.yml     # Developer-friendly wrapper playbook
│   ├── simple_test.yml        # Basic test playbook
│   └── test_vault.yml         # Vault testing playbook
├── scripts/                   # Development scripts
│   └── run_dns_management.sh  # Helper script for developers
├── vars/                      # Development variables
│   ├── mock_credentials.yml   # Mock credentials for testing
│   └── testing_config.yml     # Testing configuration
├── .gitignore                 # Git ignore file for development
├── README.md                  # This file
└── setup.sh                   # Development environment setup script
```

## Getting Started

### Setup
Run the setup script to configure your development environment:

```bash
./dev/setup.sh
```

This script will:
- Create a symlink for the run_dns_management.sh script in the project root
- Check if you have a vault password file and offer to create one
- Install Python requirements if a requirements.txt file exists
- Install Ansible collections if a collections/requirements.yml file exists

### Running the Playbook
Use the helper script to run the DNS Management playbook in developer mode:

```bash
./run_dns_management.sh --dev -e "operation=verify record_type=a domain=devhealthgrp.com.sg hostname=ANSORTEST"
```

## Development Workflow

1. **Make changes** to the codebase
2. **Test locally** using the developer mode
3. **Run molecule tests** to ensure everything works
4. **Commit changes** to version control

## Testing

### Running Molecule Tests
```bash
cd tests/molecule/default
molecule test
```

### Running Individual Test Scenarios
```bash
cd tests/molecule/verify
molecule test
```

## Credential Management

### Mock Credentials
For local testing, you can use mock credentials defined in `dev/vars/mock_credentials.yml`. These are used when `developer_testing.use_mock_credentials` is set to `true` in `dev/vars/testing_config.yml`.

### Vault Password Handling
When working with encrypted vault files, you have several options:

1. **Using the `--ask-vault-pass` flag**:
   ```
   ansible-playbook playbooks/manage_dns.yml --ask-vault-pass -e "operation=verify record_type=a domain=devhealthgrp.com.sg hostname=ANSORTEST"
   ```

2. **Using a vault password file**:
   ```
   ansible-playbook playbooks/manage_dns.yml --vault-password-file=~/.vault_pass.txt -e "operation=verify record_type=a domain=devhealthgrp.com.sg hostname=ANSORTEST"
   ```

3. **Using an environment variable**:
   ```
   export ANSIBLE_VAULT_PASSWORD_FILE=~/.vault_pass.txt
   ansible-playbook playbooks/manage_dns.yml -e "operation=verify record_type=a domain=devhealthgrp.com.sg hostname=ANSORTEST"
   ```

## Troubleshooting

### Common Issues

1. **Vault Password Issues**:
   - Ensure you have the correct vault password
   - Check that the vault password method in dev/vars/testing_config.yml is set correctly

2. **Connection Issues**:
   - For local testing, ensure mock credentials are enabled
   - Check that the DNS and ADMT servers are correctly configured

3. **Variable Recursion Issues**:
   - Avoid using the same variable names in different files
   - Use the credential_mapping structure from credentials_config.yml

### Getting Help
For additional help, contact the CES Operational Excellence Team.

## Development Playbooks

### dev_manage_dns.yml
This is a developer-friendly wrapper for the main manage_dns.yml playbook. It loads testing configuration and mock credentials, and sets up the environment for local development.

### test_vault.yml
A simple playbook to test vault variable loading and display.

### simple_test.yml
A basic test playbook that simulates DNS operations for local testing.
