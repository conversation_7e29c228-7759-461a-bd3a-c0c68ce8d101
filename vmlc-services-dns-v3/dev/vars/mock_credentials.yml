---
# Mock credentials for local development and testing
# Author: CES Operational Excellence Team
# Contributors: <PERSON> (7409)

# Mock credentials for local testing
mock_credentials:
  # STAGING ACCOUNTS
  var_dns_devhealthgrp_username: "<EMAIL>"
  var_dns_devhealthgrp_password: "mock_password"
  var_dns_healthgrpexts_username: "<EMAIL>"
  var_dns_healthgrpexts_password: "mock_password"
  var_dns_nnstg_username: "<EMAIL>"
  var_dns_nnstg_password: "mock_password"
  var_dns_ses_username: "<EMAIL>"
  var_dns_ses_password: "mock_password"

  # PRODUCTION ACCOUNTS
  var_dns_shses_username: "<EMAIL>"
  var_dns_shses_password: "mock_password"
  var_dns_aic_username: "<EMAIL>"
  var_dns_aic_password: "mock_password"
  var_dns_nhg_username: "<EMAIL>"
  var_dns_nhg_password: "mock_password"
  var_dns_hcloud_username: "<EMAIL>"
  var_dns_hcloud_password: "mock_password"
  var_dns_healthgrp_username: "<EMAIL>"
  var_dns_healthgrp_password: "mock_password"
  var_dns_healthgrpextp_username: "<EMAIL>"
  var_dns_healthgrpextp_password: "mock_password"
  var_dns_iltc_username: "<EMAIL>"
  var_dns_iltc_password: "mock_password"
  var_dns_exthealthgrp_username: "<EMAIL>"
  var_dns_exthealthgrp_password: "mock_password"
