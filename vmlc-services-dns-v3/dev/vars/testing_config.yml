---
# Testing configuration for DNS Management
# Author: CES Operational Excellence Team
# Contributors: <PERSON> (7409)

# Test environments
test_environments:
  # Local testing environment
  local:
    description: "Local testing environment"
    dns_server: "localhost"
    admt_server: "localhost"
    domains:
      - test.local
      - example.test
    credentials:
      username: "testuser"
      password: "testpassword"

# Developer testing configuration
developer_testing:
  # Whether to use local mock credentials instead of vault
  use_mock_credentials: false

  # Mock credentials are now in dev/vars/mock_credentials.yml

  # Vault password handling options
  vault_password_options:
    # Options: file, prompt, environment
    method: "prompt"
    # Only used when method is file
    file_path: "~/.vault_pass.txt"

  # Development testing environment
  development:
    description: "Development testing environment"
    dns_server: "dev-dns.example.com"
    admt_server: "dev-admt.example.com"
    domains:
      - dev.example.com
      - test.dev.example.com
    credentials:
      username: "devuser"
      password: "devpassword"

  # Staging testing environment
  staging:
    description: "Staging testing environment"
    dns_server: "stg-dns.example.com"
    admt_server: "stg-admt.example.com"
    domains:
      - stg.example.com
      - test.stg.example.com
    credentials:
      username: "stguser"
      password: "stgpassword"

# Test cases
test_cases:
  # Verify operation test cases
  verify:
    - name: "verify_existing_a_record"
      description: "Verify an existing A record"
      operation: "verify"
      record_type: "a"
      hostname: "server01"
      domain: "test.local"
      expected_result: true

    - name: "verify_nonexistent_a_record"
      description: "Verify a non-existent A record"
      operation: "verify"
      record_type: "a"
      hostname: "nonexistent"
      domain: "test.local"
      expected_result: false

    - name: "verify_existing_cname_record"
      description: "Verify an existing CNAME record"
      operation: "verify"
      record_type: "cname"
      hostname: "www"
      domain: "test.local"
      expected_result: true

    - name: "verify_existing_ptr_record"
      description: "Verify an existing PTR record"
      operation: "verify"
      record_type: "ptr"
      hostname: "server01"
      domain: "test.local"
      ip_address: "************"
      expected_result: true

  # Add operation test cases
  add:
    - name: "add_a_record"
      description: "Add an A record"
      operation: "add"
      record_type: "a"
      hostname: "server02"
      domain: "test.local"
      ip_address: "************"
      expected_result: true

    - name: "add_duplicate_a_record"
      description: "Add a duplicate A record"
      operation: "add"
      record_type: "a"
      hostname: "server01"
      domain: "test.local"
      ip_address: "************"
      expected_result: false

    - name: "add_cname_record"
      description: "Add a CNAME record"
      operation: "add"
      record_type: "cname"
      hostname: "alias"
      domain: "test.local"
      cname_target: "server01.test.local"
      expected_result: true

    - name: "add_ptr_record"
      description: "Add a PTR record"
      operation: "add"
      record_type: "ptr"
      hostname: "server03"
      domain: "test.local"
      ip_address: "************"
      expected_result: true

  # Remove operation test cases
  remove:
    - name: "remove_a_record"
      description: "Remove an A record"
      operation: "remove"
      record_type: "a"
      hostname: "server01"
      domain: "test.local"
      expected_result: true

    - name: "remove_nonexistent_a_record"
      description: "Remove a non-existent A record"
      operation: "remove"
      record_type: "a"
      hostname: "nonexistent"
      domain: "test.local"
      expected_result: true

    - name: "remove_cname_record"
      description: "Remove a CNAME record"
      operation: "remove"
      record_type: "cname"
      hostname: "www"
      domain: "test.local"
      expected_result: true

    - name: "remove_ptr_record"
      description: "Remove a PTR record"
      operation: "remove"
      record_type: "ptr"
      hostname: "server01"
      domain: "test.local"
      ip_address: "************"
      expected_result: true

  # Update operation test cases
  update:
    - name: "update_a_record"
      description: "Update an A record"
      operation: "update"
      record_type: "a"
      hostname: "server01"
      domain: "test.local"
      ip_address: "************"
      expected_result: true

    - name: "update_nonexistent_a_record"
      description: "Update a non-existent A record"
      operation: "update"
      record_type: "a"
      hostname: "nonexistent"
      domain: "test.local"
      ip_address: "************"
      expected_result: false

    - name: "update_cname_record"
      description: "Update a CNAME record"
      operation: "update"
      record_type: "cname"
      hostname: "www"
      domain: "test.local"
      cname_target: "server02.test.local"
      expected_result: true

    - name: "update_ptr_record"
      description: "Update a PTR record"
      operation: "update"
      record_type: "ptr"
      hostname: "server01"
      domain: "test.local"
      ip_address: "************"
      expected_result: true

# Molecule configuration
molecule:
  # Default driver
  driver: docker

  # Default platforms
  platforms:
    - name: ubuntu-20.04
      image: geerlingguy/docker-ubuntu2004-ansible
      pre_build_image: true
    - name: centos-8
      image: geerlingguy/docker-centos8-ansible
      pre_build_image: true

  # Default provisioner
  provisioner:
    name: ansible
    log: true

  # Default verifier
  verifier:
    name: ansible

  # Default test sequence
  test_sequence:
    - dependency
    - lint
    - cleanup
    - destroy
    - syntax
    - create
    - prepare
    - converge
    - idempotence
    - verify
    - cleanup
    - destroy
