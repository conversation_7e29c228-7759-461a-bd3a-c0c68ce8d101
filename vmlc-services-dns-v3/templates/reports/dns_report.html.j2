<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>DNS Management Report - {{ operation | capitalize }} {{ record_type | upper }} Record</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            line-height: 1.6;
            color: #333;
            margin: 0;
            padding: 20px;
        }
        .container {
            max-width: 800px;
            margin: 0 auto;
            border: 1px solid #ddd;
            padding: 20px;
            box-shadow: 0 0 10px rgba(0,0,0,0.1);
        }
        .header {
            text-align: center;
            margin-bottom: 30px;
            border-bottom: 2px solid #0056b3;
            padding-bottom: 10px;
        }
        .logo {
            max-width: 200px;
            margin-bottom: 20px;
        }
        h1 {
            color: #0056b3;
            margin: 0;
            font-size: 24px;
        }
        h2 {
            color: #0056b3;
            font-size: 18px;
            margin-top: 30px;
            border-bottom: 1px solid #ddd;
            padding-bottom: 5px;
        }
        .section {
            margin-bottom: 30px;
        }
        table {
            width: 100%;
            border-collapse: collapse;
            margin-bottom: 20px;
        }
        th, td {
            padding: 12px 15px;
            border-bottom: 1px solid #ddd;
            text-align: left;
        }
        th {
            background-color: #f8f9fa;
            font-weight: bold;
        }
        tr:hover {
            background-color: #f5f5f5;
        }
        .success {
            color: #28a745;
            font-weight: bold;
        }
        .failure {
            color: #dc3545;
            font-weight: bold;
        }
        .footer {
            margin-top: 50px;
            text-align: center;
            font-size: 12px;
            color: #666;
            border-top: 1px solid #ddd;
            padding-top: 20px;
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1>DNS Management Report</h1>
            <p>{{ ansible_date_time.date }} {{ ansible_date_time.time }}</p>
        </div>

        <div class="section">
            <h2>Operation Details</h2>
            <table>
                <tr>
                    <th>Operation</th>
                    <td>{{ operation | capitalize }}</td>
                </tr>
                <tr>
                    <th>Record Type</th>
                    <td>{{ record_type | upper }}</td>
                </tr>
                <tr>
                    <th>Hostname</th>
                    <td>{{ hostname }}.{{ domain }}</td>
                </tr>
                <tr>
                    <th>Ticket</th>
                    <td>{{ ticket }}</td>
                </tr>
            </table>
        </div>

        <div class="section">
            <h2>Result Summary</h2>
            <table>
                <tr>
                    <th>Status</th>
                    <td class="{% if dns_operation_result.success %}success{% else %}failure{% endif %}">
                        {{ 'Successful' if dns_operation_result.success else 'Failed' }}
                    </td>
                </tr>
                <tr>
                    <th>Message</th>
                    <td>{{ dns_operation_result.message }}</td>
                </tr>
                <tr>
                    <th>Changed</th>
                    <td>{{ 'Yes' if dns_operation_result.changed else 'No' }}</td>
                </tr>
                <tr>
                    <th>Timestamp</th>
                    <td>{{ dns_operation_result.timestamp }}</td>
                </tr>
            </table>
        </div>

        <div class="section">
            <h2>Record Details</h2>
            <table>
                <tr>
                    <th>Domain</th>
                    <td>{{ domain }}</td>
                </tr>
                <tr>
                    <th>Hostname</th>
                    <td>{{ hostname }}</td>
                </tr>
                <tr>
                    <th>FQDN</th>
                    <td>{{ hostname }}.{{ domain }}</td>
                </tr>
                <tr>
                    <th>Record Type</th>
                    <td>{{ record_type | upper }}</td>
                </tr>
                {% if record_type | lower in ['a', 'ptr'] %}
                <tr>
                    <th>IP Address</th>
                    <td>{{ dns_operation_result.record.ip_address | default(ip_address) | default('N/A') }}</td>
                </tr>
                {% endif %}
                {% if record_type | lower == 'cname' %}
                <tr>
                    <th>CNAME Target</th>
                    <td>{{ dns_operation_result.record.target | default(cname_target) | default('N/A') }}</td>
                </tr>
                {% endif %}
                <tr>
                    <th>TTL</th>
                    <td>{{ ttl }}</td>
                </tr>
                <tr>
                    <th>Description</th>
                    <td>{{ description }}</td>
                </tr>
            </table>
        </div>

        <div class="section">
            <h2>Execution Information</h2>
            <table>
                <tr>
                    <th>Execution Time</th>
                    <td>{{ ansible_date_time.iso8601 }}</td>
                </tr>
                <tr>
                    <th>Executed By</th>
                    <td>{{ ansible_user_id }}</td>
                </tr>
                <tr>
                    <th>Target Server</th>
                    <td>{{ dns_server }}</td>
                </tr>
                {% if record_type | lower in ['a', 'ptr'] and manage_ptr | bool %}
                <tr>
                    <th>PTR DNS Server</th>
                    <td>{{ ptr_dns_server }}</td>
                </tr>
                <tr>
                    <th>PTR Management</th>
                    <td>{{ 'Enabled' if manage_ptr | bool else 'Disabled' }}</td>
                </tr>
                {% endif %}
            </table>
        </div>

        <div class="footer">
            <p>Generated by DNS Management System</p>
            <p>CES Operational Excellence Team</p>
            <p>Contributors: Muhammad Syazani Bin Mohamed Khairi (7409)</p>
        </div>
    </div>
</body>
</html>
