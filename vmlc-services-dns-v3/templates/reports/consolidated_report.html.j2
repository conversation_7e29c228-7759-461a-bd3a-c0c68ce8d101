<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>DNS Management Consolidated Report - {{ operation | capitalize }} {{ record_type | upper }} Records</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            line-height: 1.6;
            color: #333;
            margin: 0;
            padding: 20px;
        }
        .container {
            max-width: 1000px;
            margin: 0 auto;
            border: 1px solid #ddd;
            padding: 20px;
            box-shadow: 0 0 10px rgba(0,0,0,0.1);
        }
        .header {
            text-align: center;
            margin-bottom: 30px;
            border-bottom: 2px solid #0056b3;
            padding-bottom: 10px;
        }
        .logo {
            max-width: 200px;
            margin-bottom: 20px;
        }
        h1 {
            color: #0056b3;
            margin: 0;
            font-size: 24px;
        }
        h2 {
            color: #0056b3;
            font-size: 18px;
            margin-top: 30px;
            border-bottom: 1px solid #ddd;
            padding-bottom: 5px;
        }
        .section {
            margin-bottom: 30px;
        }
        table {
            width: 100%;
            border-collapse: collapse;
            margin-bottom: 20px;
        }
        th, td {
            padding: 12px 15px;
            border-bottom: 1px solid #ddd;
            text-align: left;
        }
        th {
            background-color: #f8f9fa;
            font-weight: bold;
        }
        tr:hover {
            background-color: #f5f5f5;
        }
        .success {
            color: #28a745;
            font-weight: bold;
        }
        .failure {
            color: #dc3545;
            font-weight: bold;
        }
        .summary-box {
            display: flex;
            justify-content: space-between;
            margin-bottom: 30px;
        }
        .summary-item {
            flex: 1;
            padding: 15px;
            text-align: center;
            border: 1px solid #ddd;
            margin: 0 10px;
            border-radius: 5px;
        }
        .summary-item h3 {
            margin-top: 0;
            font-size: 16px;
        }
        .summary-item p {
            font-size: 24px;
            font-weight: bold;
            margin: 10px 0;
        }
        .success-rate {
            background-color: #f8f9fa;
        }
        .success-count {
            background-color: #d4edda;
        }
        .failure-count {
            background-color: #f8d7da;
        }
        .footer {
            margin-top: 50px;
            text-align: center;
            font-size: 12px;
            color: #666;
            border-top: 1px solid #ddd;
            padding-top: 20px;
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1>DNS Management Consolidated Report</h1>
            <p>{{ ansible_date_time.date }} {{ ansible_date_time.time }}</p>
        </div>

        <div class="section">
            <h2>Operation Summary</h2>
            <table>
                <tr>
                    <th>Operation</th>
                    <td>{{ operation | capitalize }}</td>
                </tr>
                <tr>
                    <th>Record Type</th>
                    <td>{{ record_type | upper }}</td>
                </tr>
                <tr>
                    <th>Ticket</th>
                    <td>{{ ticket }}</td>
                </tr>
                <tr>
                    <th>Total Domains</th>
                    <td>{{ domains.split(',') | length }}</td>
                </tr>
                <tr>
                    <th>Total Hostnames</th>
                    <td>{{ hostnames.split(',') | length }}</td>
                </tr>
            </table>
        </div>

        <div class="summary-box">
            <div class="summary-item success-rate">
                <h3>Success Rate</h3>
                <p>{{ ((consolidated_results | selectattr('success', 'equalto', true) | list | length / consolidated_results | length) * 100) | int }}%</p>
            </div>
            <div class="summary-item success-count">
                <h3>Successful Operations</h3>
                <p>{{ consolidated_results | selectattr('success', 'equalto', true) | list | length }}</p>
            </div>
            <div class="summary-item failure-count">
                <h3>Failed Operations</h3>
                <p>{{ consolidated_results | selectattr('success', 'equalto', false) | list | length }}</p>
            </div>
        </div>

        <div class="section">
            <h2>Successful Operations</h2>
            {% set successful_results = consolidated_results | selectattr('success', 'equalto', true) | list %}
            {% if successful_results | length > 0 %}
            <table>
                <thead>
                    <tr>
                        <th>Domain</th>
                        <th>Hostname</th>
                        <th>FQDN</th>
                        {% if record_type | lower in ['a', 'ptr'] %}
                        <th>IP Address</th>
                        {% endif %}
                        {% if record_type | lower == 'cname' %}
                        <th>Target</th>
                        {% endif %}
                        <th>Message</th>
                    </tr>
                </thead>
                <tbody>
                    {% for result in successful_results %}
                    <tr>
                        <td>{{ result.domain }}</td>
                        <td>{{ result.hostname }}</td>
                        <td>{{ result.hostname }}.{{ result.domain }}</td>
                        {% if record_type | lower in ['a', 'ptr'] %}
                        <td>{{ result.record.ip_address | default('N/A') }}</td>
                        {% endif %}
                        {% if record_type | lower == 'cname' %}
                        <td>{{ result.record.target | default('N/A') }}</td>
                        {% endif %}
                        <td>{{ result.message }}</td>
                    </tr>
                    {% endfor %}
                </tbody>
            </table>
            {% else %}
            <p>No successful operations.</p>
            {% endif %}
        </div>

        <div class="section">
            <h2>Failed Operations</h2>
            {% set failed_results = consolidated_results | selectattr('success', 'equalto', false) | list %}
            {% if failed_results | length > 0 %}
            <table>
                <thead>
                    <tr>
                        <th>Domain</th>
                        <th>Hostname</th>
                        <th>FQDN</th>
                        <th>Error Message</th>
                    </tr>
                </thead>
                <tbody>
                    {% for result in failed_results %}
                    <tr>
                        <td>{{ result.domain }}</td>
                        <td>{{ result.hostname }}</td>
                        <td>{{ result.hostname }}.{{ result.domain }}</td>
                        <td>{{ result.message }}</td>
                    </tr>
                    {% endfor %}
                </tbody>
            </table>
            {% else %}
            <p>No failed operations.</p>
            {% endif %}
        </div>

        <div class="section">
            <h2>Execution Information</h2>
            <table>
                <tr>
                    <th>Execution Time</th>
                    <td>{{ ansible_date_time.iso8601 }}</td>
                </tr>
                <tr>
                    <th>Executed By</th>
                    <td>{{ ansible_user_id }}</td>
                </tr>
                <tr>
                    <th>Environment</th>
                    <td>{{ environment }}</td>
                </tr>
            </table>
        </div>

        <div class="footer">
            <p>Generated by DNS Management System</p>
            <p>CES Operational Excellence Team</p>
            <p>Contributors: Muhammad Syazani Bin Mohamed Khairi (7409)</p>
        </div>
    </div>
</body>
</html>
