<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>DNS Management Report</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            line-height: 1.6;
            color: #333;
            margin: 0;
            padding: 20px;
        }
        .container {
            max-width: 600px;
            margin: 0 auto;
        }
        .header {
            text-align: center;
            margin-bottom: 20px;
            border-bottom: 2px solid #0056b3;
            padding-bottom: 10px;
        }
        h1 {
            color: #0056b3;
            margin: 0;
            font-size: 24px;
        }
        h2 {
            color: #0056b3;
            font-size: 18px;
            margin-top: 20px;
            border-bottom: 1px solid #ddd;
            padding-bottom: 5px;
        }
        .section {
            margin-bottom: 20px;
        }
        table {
            width: 100%;
            border-collapse: collapse;
            margin-bottom: 20px;
        }
        th, td {
            padding: 10px;
            border-bottom: 1px solid #ddd;
            text-align: left;
        }
        th {
            background-color: #f8f9fa;
            font-weight: bold;
        }
        .success {
            color: #28a745;
            font-weight: bold;
        }
        .failure {
            color: #dc3545;
            font-weight: bold;
        }
        .footer {
            margin-top: 30px;
            text-align: center;
            font-size: 12px;
            color: #666;
            border-top: 1px solid #ddd;
            padding-top: 10px;
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1>DNS Management Report</h1>
            <p>{{ ansible_date_time.date }} {{ ansible_date_time.time }}</p>
        </div>

        <div class="section">
            <p>Hello,</p>
            <p>Please find attached the DNS Management report for the following operation:</p>

            <table>
                <tr>
                    <th>Operation</th>
                    <td>{{ operation | capitalize }}</td>
                </tr>
                <tr>
                    <th>Record Type</th>
                    <td>{{ record_type | upper }}</td>
                </tr>
                <tr>
                    <th>Hostname</th>
                    <td>{{ hostname }}.{{ domain }}</td>
                </tr>
                <tr>
                    <th>Ticket</th>
                    <td>{{ ticket }}</td>
                </tr>
                <tr>
                    <th>Status</th>
                    <td class="{% if dns_operation_result.success %}success{% else %}failure{% endif %}">
                        {{ 'Successful' if dns_operation_result.success else 'Failed' }}
                    </td>
                </tr>
                <tr>
                    <th>Message</th>
                    <td>{{ dns_operation_result.message }}</td>
                </tr>
            </table>

            <p>Please see the attached PDF report for complete details.</p>

            <p>Best regards,<br>DNS Management System</p>
        </div>

        <div class="footer">
            <p>This is an automated message from the DNS Management System</p>
            <p>CES Operational Excellence Team</p>
            <p>Contributors: Muhammad Syazani Bin Mohamed Khairi (7409)</p>
        </div>
    </div>
</body>
</html>
