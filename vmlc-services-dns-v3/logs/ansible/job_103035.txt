ansible-playbook [core 2.15.8]
  config file = /etc/ansible/ansible.cfg
  configured module search path = ['/home/<USER>/.ansible/plugins/modules', '/usr/share/ansible/plugins/modules']
  ansible python module location = /usr/lib/python3.9/site-packages/ansible
  ansible collection location = /runner/requirements_collections:/home/<USER>/.ansible/collections:/usr/share/ansible/collections
  executable location = /usr/bin/ansible-playbook
  python version = 3.9.18 (main, Sep 22 2023, 17:58:34) [GCC 8.5.0 20210514 (Red Hat 8.5.0-20)] (/usr/bin/python3.9)
  jinja version = 3.1.2
  libyaml = True
Using /etc/ansible/ansible.cfg as config file
Vault password: 
Skipping callback 'awx_display', as we already have a stdout callback.
Skipping callback 'default', as we already have a stdout callback.
Skipping callback 'minimal', as we already have a stdout callback.
Skipping callback 'oneline', as we already have a stdout callback.

PLAYBOOK: manage_dns.yml *******************************************************
1 plays in vmlc-services-dns-v3/playbooks/manage_dns.yml

PLAY [DNS Management Operations] ***********************************************

TASK [Gathering Facts] *********************************************************
task path: /runner/project/vmlc-services-dns-v3/playbooks/manage_dns.yml:8
ok: [localhost]

TASK [Load configuration] ******************************************************
task path: /runner/project/vmlc-services-dns-v3/playbooks/manage_dns.yml:61
included: /runner/project/vmlc-services-dns-v3/roles/common/tasks/load_configuration.yml for localhost

TASK [Display project information] *********************************************
task path: /runner/project/vmlc-services-dns-v3/roles/common/tasks/load_configuration.yml:6
ok: [localhost] => {
    "msg": [
        "DNS Management v2.0.0",
        "DNS Management System for managing DNS records across multiple domains",
        "Author: CES Operational Excellence Team",
        "Contributors: Muhammad Syazani Bin Mohamed Khairi (7409)"
    ]
}

TASK [Validate configuration files] ********************************************
task path: /runner/project/vmlc-services-dns-v3/roles/common/tasks/load_configuration.yml:15
ok: [localhost] => (item=domains.yml) => {"ansible_loop_var": "item", "changed": false, "item": "domains.yml", "stat": {"atime": 1745549089.6758099, "attr_flags": "", "attributes": [], "block_size": 4096, "blocks": 16, "charset": "unknown", "checksum": "c7ba92656a954da27682ba9f1300b02c046d61b3", "ctime": 1745549087.430804, "dev": 64778, "device_type": 0, "executable": false, "exists": true, "gid": 0, "gr_name": "root", "inode": 67109039, "isblk": false, "ischr": false, "isdir": false, "isfifo": false, "isgid": false, "islnk": false, "isreg": true, "issock": false, "isuid": false, "mimetype": "unknown", "mode": "0644", "mtime": 1745443858.0, "nlink": 1, "path": "/runner/project/vmlc-services-dns-v3/playbooks/../vars/domains.yml", "pw_name": "root", "readable": true, "rgrp": true, "roth": true, "rusr": true, "size": 6756, "uid": 0, "version": null, "wgrp": false, "woth": false, "writeable": true, "wusr": true, "xgrp": false, "xoth": false, "xusr": false}}
ok: [localhost] => (item=domain_credentials.yml) => {"ansible_loop_var": "item", "changed": false, "item": "domain_credentials.yml", "stat": {"atime": 1745549089.67881, "attr_flags": "", "attributes": [], "block_size": 4096, "blocks": 8, "charset": "unknown", "checksum": "f9c559b39f52e639225682fe008d969bb61a6792", "ctime": 1745549087.430804, "dev": 64778, "device_type": 0, "executable": false, "exists": true, "gid": 0, "gr_name": "root", "inode": 67109038, "isblk": false, "ischr": false, "isdir": false, "isfifo": false, "isgid": false, "islnk": false, "isreg": true, "issock": false, "isuid": false, "mimetype": "unknown", "mode": "0644", "mtime": 1745443858.0, "nlink": 1, "path": "/runner/project/vmlc-services-dns-v3/playbooks/../vars/domain_credentials.yml", "pw_name": "root", "readable": true, "rgrp": true, "roth": true, "rusr": true, "size": 1749, "uid": 0, "version": null, "wgrp": false, "woth": false, "writeable": true, "wusr": true, "xgrp": false, "xoth": false, "xusr": false}}
ok: [localhost] => (item=credentials_config.yml) => {"ansible_loop_var": "item", "changed": false, "item": "credentials_config.yml", "stat": {"atime": 1745549089.6798098, "attr_flags": "", "attributes": [], "block_size": 4096, "blocks": 8, "charset": "unknown", "checksum": "5db2da05958ec7d0816eebc45b6423c43ef99035", "ctime": 1745549087.430804, "dev": 64778, "device_type": 0, "executable": false, "exists": true, "gid": 0, "gr_name": "root", "inode": 67109037, "isblk": false, "ischr": false, "isdir": false, "isfifo": false, "isgid": false, "islnk": false, "isreg": true, "issock": false, "isuid": false, "mimetype": "unknown", "mode": "0644", "mtime": 1745443858.0, "nlink": 1, "path": "/runner/project/vmlc-services-dns-v3/playbooks/../vars/credentials_config.yml", "pw_name": "root", "readable": true, "rgrp": true, "roth": true, "rusr": true, "size": 2859, "uid": 0, "version": null, "wgrp": false, "woth": false, "writeable": true, "wusr": true, "xgrp": false, "xoth": false, "xusr": false}}
ok: [localhost] => (item=email_config.yml) => {"ansible_loop_var": "item", "changed": false, "item": "email_config.yml", "stat": {"atime": 1745549089.68181, "attr_flags": "", "attributes": [], "block_size": 4096, "blocks": 8, "charset": "unknown", "checksum": "8595e0bc9e2fd7025f01f6db48a0337fcb2c4c09", "ctime": 1745549087.430804, "dev": 64778, "device_type": 0, "executable": false, "exists": true, "gid": 0, "gr_name": "root", "inode": 67109041, "isblk": false, "ischr": false, "isdir": false, "isfifo": false, "isgid": false, "islnk": false, "isreg": true, "issock": false, "isuid": false, "mimetype": "unknown", "mode": "0644", "mtime": 1745516376.0, "nlink": 1, "path": "/runner/project/vmlc-services-dns-v3/playbooks/../vars/email_config.yml", "pw_name": "root", "readable": true, "rgrp": true, "roth": true, "rusr": true, "size": 2425, "uid": 0, "version": null, "wgrp": false, "woth": false, "writeable": true, "wusr": true, "xgrp": false, "xoth": false, "xusr": false}}
ok: [localhost] => (item=log_config.yml) => {"ansible_loop_var": "item", "changed": false, "item": "log_config.yml", "stat": {"atime": 1745549089.6828098, "attr_flags": "", "attributes": [], "block_size": 4096, "blocks": 8, "charset": "unknown", "checksum": "c6880f6d6b16eddf32d5ba8946e9e2e729088bd1", "ctime": 1745549087.430804, "dev": 64778, "device_type": 0, "executable": false, "exists": true, "gid": 0, "gr_name": "root", "inode": 67109032, "isblk": false, "ischr": false, "isdir": false, "isfifo": false, "isgid": false, "islnk": false, "isreg": true, "issock": false, "isuid": false, "mimetype": "unknown", "mode": "0644", "mtime": 1745429178.0, "nlink": 1, "path": "/runner/project/vmlc-services-dns-v3/playbooks/../vars/log_config.yml", "pw_name": "root", "readable": true, "rgrp": true, "roth": true, "rusr": true, "size": 1634, "uid": 0, "version": null, "wgrp": false, "woth": false, "writeable": true, "wusr": true, "xgrp": false, "xoth": false, "xusr": false}}
ok: [localhost] => (item=operations_config.yml) => {"ansible_loop_var": "item", "changed": false, "item": "operations_config.yml", "stat": {"atime": 1745549089.6828098, "attr_flags": "", "attributes": [], "block_size": 4096, "blocks": 8, "charset": "unknown", "checksum": "92368efc94ffe4a29f42bf52adce03b3b343b963", "ctime": 1745549087.430804, "dev": 64778, "device_type": 0, "executable": false, "exists": true, "gid": 0, "gr_name": "root", "inode": 67109033, "isblk": false, "ischr": false, "isdir": false, "isfifo": false, "isgid": false, "islnk": false, "isreg": true, "issock": false, "isuid": false, "mimetype": "unknown", "mode": "0644", "mtime": 1745429178.0, "nlink": 1, "path": "/runner/project/vmlc-services-dns-v3/playbooks/../vars/operations_config.yml", "pw_name": "root", "readable": true, "rgrp": true, "roth": true, "rusr": true, "size": 2488, "uid": 0, "version": null, "wgrp": false, "woth": false, "writeable": true, "wusr": true, "xgrp": false, "xoth": false, "xusr": false}}
ok: [localhost] => (item=powershell_config.yml) => {"ansible_loop_var": "item", "changed": false, "item": "powershell_config.yml", "stat": {"atime": 1745549089.6848097, "attr_flags": "", "attributes": [], "block_size": 4096, "blocks": 8, "charset": "unknown", "checksum": "13602fdc714a05e83fb29569e42617c820125c0f", "ctime": 1745549087.430804, "dev": 64778, "device_type": 0, "executable": false, "exists": true, "gid": 0, "gr_name": "root", "inode": 67109034, "isblk": false, "ischr": false, "isdir": false, "isfifo": false, "isgid": false, "islnk": false, "isreg": true, "issock": false, "isuid": false, "mimetype": "unknown", "mode": "0644", "mtime": 1745429178.0, "nlink": 1, "path": "/runner/project/vmlc-services-dns-v3/playbooks/../vars/powershell_config.yml", "pw_name": "root", "readable": true, "rgrp": true, "roth": true, "rusr": true, "size": 1237, "uid": 0, "version": null, "wgrp": false, "woth": false, "writeable": true, "wusr": true, "xgrp": false, "xoth": false, "xusr": false}}
ok: [localhost] => (item=report_config.yml) => {"ansible_loop_var": "item", "changed": false, "item": "report_config.yml", "stat": {"atime": 1745549089.6858099, "attr_flags": "", "attributes": [], "block_size": 4096, "blocks": 8, "charset": "unknown", "checksum": "277fde3f7cc2e9923c2c884557d583bed0568aa6", "ctime": 1745549087.430804, "dev": 64778, "device_type": 0, "executable": false, "exists": true, "gid": 0, "gr_name": "root", "inode": 67109040, "isblk": false, "ischr": false, "isdir": false, "isfifo": false, "isgid": false, "islnk": false, "isreg": true, "issock": false, "isuid": false, "mimetype": "unknown", "mode": "0644", "mtime": 1745515916.0, "nlink": 1, "path": "/runner/project/vmlc-services-dns-v3/playbooks/../vars/report_config.yml", "pw_name": "root", "readable": true, "rgrp": true, "roth": true, "rusr": true, "size": 4035, "uid": 0, "version": null, "wgrp": false, "woth": false, "writeable": true, "wusr": true, "xgrp": false, "xoth": false, "xusr": false}}

TASK [Ensure all configuration files exist] ************************************
task path: /runner/project/vmlc-services-dns-v3/roles/common/tasks/load_configuration.yml:22
ok: [localhost] => (item={'changed': False, 'stat': {'exists': True, 'path': '/runner/project/vmlc-services-dns-v3/playbooks/../vars/domains.yml', 'mode': '0644', 'isdir': False, 'ischr': False, 'isblk': False, 'isreg': True, 'isfifo': False, 'islnk': False, 'issock': False, 'uid': 0, 'gid': 0, 'size': 6756, 'inode': 67109039, 'dev': 64778, 'nlink': 1, 'atime': 1745549089.6758099, 'mtime': 1745443858.0, 'ctime': 1745549087.430804, 'wusr': True, 'rusr': True, 'xusr': False, 'wgrp': False, 'rgrp': True, 'xgrp': False, 'woth': False, 'roth': True, 'xoth': False, 'isuid': False, 'isgid': False, 'blocks': 16, 'block_size': 4096, 'device_type': 0, 'readable': True, 'writeable': True, 'executable': False, 'pw_name': 'root', 'gr_name': 'root', 'checksum': 'c7ba92656a954da27682ba9f1300b02c046d61b3', 'mimetype': 'unknown', 'charset': 'unknown', 'version': None, 'attributes': [], 'attr_flags': ''}, 'invocation': {'module_args': {'path': '/runner/project/vmlc-services-dns-v3/playbooks/../vars/domains.yml', 'follow': False, 'get_md5': False, 'get_checksum': True, 'get_mime': True, 'get_attributes': True, 'checksum_algorithm': 'sha1'}}, 'failed': False, 'item': 'domains.yml', 'ansible_loop_var': 'item'}) => {
    "ansible_loop_var": "item",
    "changed": false,
    "item": {
        "ansible_loop_var": "item",
        "changed": false,
        "failed": false,
        "invocation": {
            "module_args": {
                "checksum_algorithm": "sha1",
                "follow": false,
                "get_attributes": true,
                "get_checksum": true,
                "get_md5": false,
                "get_mime": true,
                "path": "/runner/project/vmlc-services-dns-v3/playbooks/../vars/domains.yml"
            }
        },
        "item": "domains.yml",
        "stat": {
            "atime": 1745549089.6758099,
            "attr_flags": "",
            "attributes": [],
            "block_size": 4096,
            "blocks": 16,
            "charset": "unknown",
            "checksum": "c7ba92656a954da27682ba9f1300b02c046d61b3",
            "ctime": 1745549087.430804,
            "dev": 64778,
            "device_type": 0,
            "executable": false,
            "exists": true,
            "gid": 0,
            "gr_name": "root",
            "inode": 67109039,
            "isblk": false,
            "ischr": false,
            "isdir": false,
            "isfifo": false,
            "isgid": false,
            "islnk": false,
            "isreg": true,
            "issock": false,
            "isuid": false,
            "mimetype": "unknown",
            "mode": "0644",
            "mtime": 1745443858.0,
            "nlink": 1,
            "path": "/runner/project/vmlc-services-dns-v3/playbooks/../vars/domains.yml",
            "pw_name": "root",
            "readable": true,
            "rgrp": true,
            "roth": true,
            "rusr": true,
            "size": 6756,
            "uid": 0,
            "version": null,
            "wgrp": false,
            "woth": false,
            "writeable": true,
            "wusr": true,
            "xgrp": false,
            "xoth": false,
            "xusr": false
        }
    },
    "msg": "All assertions passed"
}
ok: [localhost] => (item={'changed': False, 'stat': {'exists': True, 'path': '/runner/project/vmlc-services-dns-v3/playbooks/../vars/domain_credentials.yml', 'mode': '0644', 'isdir': False, 'ischr': False, 'isblk': False, 'isreg': True, 'isfifo': False, 'islnk': False, 'issock': False, 'uid': 0, 'gid': 0, 'size': 1749, 'inode': 67109038, 'dev': 64778, 'nlink': 1, 'atime': 1745549089.67881, 'mtime': 1745443858.0, 'ctime': 1745549087.430804, 'wusr': True, 'rusr': True, 'xusr': False, 'wgrp': False, 'rgrp': True, 'xgrp': False, 'woth': False, 'roth': True, 'xoth': False, 'isuid': False, 'isgid': False, 'blocks': 8, 'block_size': 4096, 'device_type': 0, 'readable': True, 'writeable': True, 'executable': False, 'pw_name': 'root', 'gr_name': 'root', 'checksum': 'f9c559b39f52e639225682fe008d969bb61a6792', 'mimetype': 'unknown', 'charset': 'unknown', 'version': None, 'attributes': [], 'attr_flags': ''}, 'invocation': {'module_args': {'path': '/runner/project/vmlc-services-dns-v3/playbooks/../vars/domain_credentials.yml', 'follow': False, 'get_md5': False, 'get_checksum': True, 'get_mime': True, 'get_attributes': True, 'checksum_algorithm': 'sha1'}}, 'failed': False, 'item': 'domain_credentials.yml', 'ansible_loop_var': 'item'}) => {
    "ansible_loop_var": "item",
    "changed": false,
    "item": {
        "ansible_loop_var": "item",
        "changed": false,
        "failed": false,
        "invocation": {
            "module_args": {
                "checksum_algorithm": "sha1",
                "follow": false,
                "get_attributes": true,
                "get_checksum": true,
                "get_md5": false,
                "get_mime": true,
                "path": "/runner/project/vmlc-services-dns-v3/playbooks/../vars/domain_credentials.yml"
            }
        },
        "item": "domain_credentials.yml",
        "stat": {
            "atime": 1745549089.67881,
            "attr_flags": "",
            "attributes": [],
            "block_size": 4096,
            "blocks": 8,
            "charset": "unknown",
            "checksum": "f9c559b39f52e639225682fe008d969bb61a6792",
            "ctime": 1745549087.430804,
            "dev": 64778,
            "device_type": 0,
            "executable": false,
            "exists": true,
            "gid": 0,
            "gr_name": "root",
            "inode": 67109038,
            "isblk": false,
            "ischr": false,
            "isdir": false,
            "isfifo": false,
            "isgid": false,
            "islnk": false,
            "isreg": true,
            "issock": false,
            "isuid": false,
            "mimetype": "unknown",
            "mode": "0644",
            "mtime": 1745443858.0,
            "nlink": 1,
            "path": "/runner/project/vmlc-services-dns-v3/playbooks/../vars/domain_credentials.yml",
            "pw_name": "root",
            "readable": true,
            "rgrp": true,
            "roth": true,
            "rusr": true,
            "size": 1749,
            "uid": 0,
            "version": null,
            "wgrp": false,
            "woth": false,
            "writeable": true,
            "wusr": true,
            "xgrp": false,
            "xoth": false,
            "xusr": false
        }
    },
    "msg": "All assertions passed"
}
ok: [localhost] => (item={'changed': False, 'stat': {'exists': True, 'path': '/runner/project/vmlc-services-dns-v3/playbooks/../vars/credentials_config.yml', 'mode': '0644', 'isdir': False, 'ischr': False, 'isblk': False, 'isreg': True, 'isfifo': False, 'islnk': False, 'issock': False, 'uid': 0, 'gid': 0, 'size': 2859, 'inode': 67109037, 'dev': 64778, 'nlink': 1, 'atime': 1745549089.6798098, 'mtime': 1745443858.0, 'ctime': 1745549087.430804, 'wusr': True, 'rusr': True, 'xusr': False, 'wgrp': False, 'rgrp': True, 'xgrp': False, 'woth': False, 'roth': True, 'xoth': False, 'isuid': False, 'isgid': False, 'blocks': 8, 'block_size': 4096, 'device_type': 0, 'readable': True, 'writeable': True, 'executable': False, 'pw_name': 'root', 'gr_name': 'root', 'checksum': '5db2da05958ec7d0816eebc45b6423c43ef99035', 'mimetype': 'unknown', 'charset': 'unknown', 'version': None, 'attributes': [], 'attr_flags': ''}, 'invocation': {'module_args': {'path': '/runner/project/vmlc-services-dns-v3/playbooks/../vars/credentials_config.yml', 'follow': False, 'get_md5': False, 'get_checksum': True, 'get_mime': True, 'get_attributes': True, 'checksum_algorithm': 'sha1'}}, 'failed': False, 'item': 'credentials_config.yml', 'ansible_loop_var': 'item'}) => {
    "ansible_loop_var": "item",
    "changed": false,
    "item": {
        "ansible_loop_var": "item",
        "changed": false,
        "failed": false,
        "invocation": {
            "module_args": {
                "checksum_algorithm": "sha1",
                "follow": false,
                "get_attributes": true,
                "get_checksum": true,
                "get_md5": false,
                "get_mime": true,
                "path": "/runner/project/vmlc-services-dns-v3/playbooks/../vars/credentials_config.yml"
            }
        },
        "item": "credentials_config.yml",
        "stat": {
            "atime": 1745549089.6798098,
            "attr_flags": "",
            "attributes": [],
            "block_size": 4096,
            "blocks": 8,
            "charset": "unknown",
            "checksum": "5db2da05958ec7d0816eebc45b6423c43ef99035",
            "ctime": 1745549087.430804,
            "dev": 64778,
            "device_type": 0,
            "executable": false,
            "exists": true,
            "gid": 0,
            "gr_name": "root",
            "inode": 67109037,
            "isblk": false,
            "ischr": false,
            "isdir": false,
            "isfifo": false,
            "isgid": false,
            "islnk": false,
            "isreg": true,
            "issock": false,
            "isuid": false,
            "mimetype": "unknown",
            "mode": "0644",
            "mtime": 1745443858.0,
            "nlink": 1,
            "path": "/runner/project/vmlc-services-dns-v3/playbooks/../vars/credentials_config.yml",
            "pw_name": "root",
            "readable": true,
            "rgrp": true,
            "roth": true,
            "rusr": true,
            "size": 2859,
            "uid": 0,
            "version": null,
            "wgrp": false,
            "woth": false,
            "writeable": true,
            "wusr": true,
            "xgrp": false,
            "xoth": false,
            "xusr": false
        }
    },
    "msg": "All assertions passed"
}
ok: [localhost] => (item={'changed': False, 'stat': {'exists': True, 'path': '/runner/project/vmlc-services-dns-v3/playbooks/../vars/email_config.yml', 'mode': '0644', 'isdir': False, 'ischr': False, 'isblk': False, 'isreg': True, 'isfifo': False, 'islnk': False, 'issock': False, 'uid': 0, 'gid': 0, 'size': 2425, 'inode': 67109041, 'dev': 64778, 'nlink': 1, 'atime': 1745549089.68181, 'mtime': 1745516376.0, 'ctime': 1745549087.430804, 'wusr': True, 'rusr': True, 'xusr': False, 'wgrp': False, 'rgrp': True, 'xgrp': False, 'woth': False, 'roth': True, 'xoth': False, 'isuid': False, 'isgid': False, 'blocks': 8, 'block_size': 4096, 'device_type': 0, 'readable': True, 'writeable': True, 'executable': False, 'pw_name': 'root', 'gr_name': 'root', 'checksum': '8595e0bc9e2fd7025f01f6db48a0337fcb2c4c09', 'mimetype': 'unknown', 'charset': 'unknown', 'version': None, 'attributes': [], 'attr_flags': ''}, 'invocation': {'module_args': {'path': '/runner/project/vmlc-services-dns-v3/playbooks/../vars/email_config.yml', 'follow': False, 'get_md5': False, 'get_checksum': True, 'get_mime': True, 'get_attributes': True, 'checksum_algorithm': 'sha1'}}, 'failed': False, 'item': 'email_config.yml', 'ansible_loop_var': 'item'}) => {
    "ansible_loop_var": "item",
    "changed": false,
    "item": {
        "ansible_loop_var": "item",
        "changed": false,
        "failed": false,
        "invocation": {
            "module_args": {
                "checksum_algorithm": "sha1",
                "follow": false,
                "get_attributes": true,
                "get_checksum": true,
                "get_md5": false,
                "get_mime": true,
                "path": "/runner/project/vmlc-services-dns-v3/playbooks/../vars/email_config.yml"
            }
        },
        "item": "email_config.yml",
        "stat": {
            "atime": 1745549089.68181,
            "attr_flags": "",
            "attributes": [],
            "block_size": 4096,
            "blocks": 8,
            "charset": "unknown",
            "checksum": "8595e0bc9e2fd7025f01f6db48a0337fcb2c4c09",
            "ctime": 1745549087.430804,
            "dev": 64778,
            "device_type": 0,
            "executable": false,
            "exists": true,
            "gid": 0,
            "gr_name": "root",
            "inode": 67109041,
            "isblk": false,
            "ischr": false,
            "isdir": false,
            "isfifo": false,
            "isgid": false,
            "islnk": false,
            "isreg": true,
            "issock": false,
            "isuid": false,
            "mimetype": "unknown",
            "mode": "0644",
            "mtime": 1745516376.0,
            "nlink": 1,
            "path": "/runner/project/vmlc-services-dns-v3/playbooks/../vars/email_config.yml",
            "pw_name": "root",
            "readable": true,
            "rgrp": true,
            "roth": true,
            "rusr": true,
            "size": 2425,
            "uid": 0,
            "version": null,
            "wgrp": false,
            "woth": false,
            "writeable": true,
            "wusr": true,
            "xgrp": false,
            "xoth": false,
            "xusr": false
        }
    },
    "msg": "All assertions passed"
}
ok: [localhost] => (item={'changed': False, 'stat': {'exists': True, 'path': '/runner/project/vmlc-services-dns-v3/playbooks/../vars/log_config.yml', 'mode': '0644', 'isdir': False, 'ischr': False, 'isblk': False, 'isreg': True, 'isfifo': False, 'islnk': False, 'issock': False, 'uid': 0, 'gid': 0, 'size': 1634, 'inode': 67109032, 'dev': 64778, 'nlink': 1, 'atime': 1745549089.6828098, 'mtime': 1745429178.0, 'ctime': 1745549087.430804, 'wusr': True, 'rusr': True, 'xusr': False, 'wgrp': False, 'rgrp': True, 'xgrp': False, 'woth': False, 'roth': True, 'xoth': False, 'isuid': False, 'isgid': False, 'blocks': 8, 'block_size': 4096, 'device_type': 0, 'readable': True, 'writeable': True, 'executable': False, 'pw_name': 'root', 'gr_name': 'root', 'checksum': 'c6880f6d6b16eddf32d5ba8946e9e2e729088bd1', 'mimetype': 'unknown', 'charset': 'unknown', 'version': None, 'attributes': [], 'attr_flags': ''}, 'invocation': {'module_args': {'path': '/runner/project/vmlc-services-dns-v3/playbooks/../vars/log_config.yml', 'follow': False, 'get_md5': False, 'get_checksum': True, 'get_mime': True, 'get_attributes': True, 'checksum_algorithm': 'sha1'}}, 'failed': False, 'item': 'log_config.yml', 'ansible_loop_var': 'item'}) => {
    "ansible_loop_var": "item",
    "changed": false,
    "item": {
        "ansible_loop_var": "item",
        "changed": false,
        "failed": false,
        "invocation": {
            "module_args": {
                "checksum_algorithm": "sha1",
                "follow": false,
                "get_attributes": true,
                "get_checksum": true,
                "get_md5": false,
                "get_mime": true,
                "path": "/runner/project/vmlc-services-dns-v3/playbooks/../vars/log_config.yml"
            }
        },
        "item": "log_config.yml",
        "stat": {
            "atime": 1745549089.6828098,
            "attr_flags": "",
            "attributes": [],
            "block_size": 4096,
            "blocks": 8,
            "charset": "unknown",
            "checksum": "c6880f6d6b16eddf32d5ba8946e9e2e729088bd1",
            "ctime": 1745549087.430804,
            "dev": 64778,
            "device_type": 0,
            "executable": false,
            "exists": true,
            "gid": 0,
            "gr_name": "root",
            "inode": 67109032,
            "isblk": false,
            "ischr": false,
            "isdir": false,
            "isfifo": false,
            "isgid": false,
            "islnk": false,
            "isreg": true,
            "issock": false,
            "isuid": false,
            "mimetype": "unknown",
            "mode": "0644",
            "mtime": 1745429178.0,
            "nlink": 1,
            "path": "/runner/project/vmlc-services-dns-v3/playbooks/../vars/log_config.yml",
            "pw_name": "root",
            "readable": true,
            "rgrp": true,
            "roth": true,
            "rusr": true,
            "size": 1634,
            "uid": 0,
            "version": null,
            "wgrp": false,
            "woth": false,
            "writeable": true,
            "wusr": true,
            "xgrp": false,
            "xoth": false,
            "xusr": false
        }
    },
    "msg": "All assertions passed"
}
ok: [localhost] => (item={'changed': False, 'stat': {'exists': True, 'path': '/runner/project/vmlc-services-dns-v3/playbooks/../vars/operations_config.yml', 'mode': '0644', 'isdir': False, 'ischr': False, 'isblk': False, 'isreg': True, 'isfifo': False, 'islnk': False, 'issock': False, 'uid': 0, 'gid': 0, 'size': 2488, 'inode': 67109033, 'dev': 64778, 'nlink': 1, 'atime': 1745549089.6828098, 'mtime': 1745429178.0, 'ctime': 1745549087.430804, 'wusr': True, 'rusr': True, 'xusr': False, 'wgrp': False, 'rgrp': True, 'xgrp': False, 'woth': False, 'roth': True, 'xoth': False, 'isuid': False, 'isgid': False, 'blocks': 8, 'block_size': 4096, 'device_type': 0, 'readable': True, 'writeable': True, 'executable': False, 'pw_name': 'root', 'gr_name': 'root', 'checksum': '92368efc94ffe4a29f42bf52adce03b3b343b963', 'mimetype': 'unknown', 'charset': 'unknown', 'version': None, 'attributes': [], 'attr_flags': ''}, 'invocation': {'module_args': {'path': '/runner/project/vmlc-services-dns-v3/playbooks/../vars/operations_config.yml', 'follow': False, 'get_md5': False, 'get_checksum': True, 'get_mime': True, 'get_attributes': True, 'checksum_algorithm': 'sha1'}}, 'failed': False, 'item': 'operations_config.yml', 'ansible_loop_var': 'item'}) => {
    "ansible_loop_var": "item",
    "changed": false,
    "item": {
        "ansible_loop_var": "item",
        "changed": false,
        "failed": false,
        "invocation": {
            "module_args": {
                "checksum_algorithm": "sha1",
                "follow": false,
                "get_attributes": true,
                "get_checksum": true,
                "get_md5": false,
                "get_mime": true,
                "path": "/runner/project/vmlc-services-dns-v3/playbooks/../vars/operations_config.yml"
            }
        },
        "item": "operations_config.yml",
        "stat": {
            "atime": 1745549089.6828098,
            "attr_flags": "",
            "attributes": [],
            "block_size": 4096,
            "blocks": 8,
            "charset": "unknown",
            "checksum": "92368efc94ffe4a29f42bf52adce03b3b343b963",
            "ctime": 1745549087.430804,
            "dev": 64778,
            "device_type": 0,
            "executable": false,
            "exists": true,
            "gid": 0,
            "gr_name": "root",
            "inode": 67109033,
            "isblk": false,
            "ischr": false,
            "isdir": false,
            "isfifo": false,
            "isgid": false,
            "islnk": false,
            "isreg": true,
            "issock": false,
            "isuid": false,
            "mimetype": "unknown",
            "mode": "0644",
            "mtime": 1745429178.0,
            "nlink": 1,
            "path": "/runner/project/vmlc-services-dns-v3/playbooks/../vars/operations_config.yml",
            "pw_name": "root",
            "readable": true,
            "rgrp": true,
            "roth": true,
            "rusr": true,
            "size": 2488,
            "uid": 0,
            "version": null,
            "wgrp": false,
            "woth": false,
            "writeable": true,
            "wusr": true,
            "xgrp": false,
            "xoth": false,
            "xusr": false
        }
    },
    "msg": "All assertions passed"
}
ok: [localhost] => (item={'changed': False, 'stat': {'exists': True, 'path': '/runner/project/vmlc-services-dns-v3/playbooks/../vars/powershell_config.yml', 'mode': '0644', 'isdir': False, 'ischr': False, 'isblk': False, 'isreg': True, 'isfifo': False, 'islnk': False, 'issock': False, 'uid': 0, 'gid': 0, 'size': 1237, 'inode': 67109034, 'dev': 64778, 'nlink': 1, 'atime': 1745549089.6848097, 'mtime': 1745429178.0, 'ctime': 1745549087.430804, 'wusr': True, 'rusr': True, 'xusr': False, 'wgrp': False, 'rgrp': True, 'xgrp': False, 'woth': False, 'roth': True, 'xoth': False, 'isuid': False, 'isgid': False, 'blocks': 8, 'block_size': 4096, 'device_type': 0, 'readable': True, 'writeable': True, 'executable': False, 'pw_name': 'root', 'gr_name': 'root', 'checksum': '13602fdc714a05e83fb29569e42617c820125c0f', 'mimetype': 'unknown', 'charset': 'unknown', 'version': None, 'attributes': [], 'attr_flags': ''}, 'invocation': {'module_args': {'path': '/runner/project/vmlc-services-dns-v3/playbooks/../vars/powershell_config.yml', 'follow': False, 'get_md5': False, 'get_checksum': True, 'get_mime': True, 'get_attributes': True, 'checksum_algorithm': 'sha1'}}, 'failed': False, 'item': 'powershell_config.yml', 'ansible_loop_var': 'item'}) => {
    "ansible_loop_var": "item",
    "changed": false,
    "item": {
        "ansible_loop_var": "item",
        "changed": false,
        "failed": false,
        "invocation": {
            "module_args": {
                "checksum_algorithm": "sha1",
                "follow": false,
                "get_attributes": true,
                "get_checksum": true,
                "get_md5": false,
                "get_mime": true,
                "path": "/runner/project/vmlc-services-dns-v3/playbooks/../vars/powershell_config.yml"
            }
        },
        "item": "powershell_config.yml",
        "stat": {
            "atime": 1745549089.6848097,
            "attr_flags": "",
            "attributes": [],
            "block_size": 4096,
            "blocks": 8,
            "charset": "unknown",
            "checksum": "13602fdc714a05e83fb29569e42617c820125c0f",
            "ctime": 1745549087.430804,
            "dev": 64778,
            "device_type": 0,
            "executable": false,
            "exists": true,
            "gid": 0,
            "gr_name": "root",
            "inode": 67109034,
            "isblk": false,
            "ischr": false,
            "isdir": false,
            "isfifo": false,
            "isgid": false,
            "islnk": false,
            "isreg": true,
            "issock": false,
            "isuid": false,
            "mimetype": "unknown",
            "mode": "0644",
            "mtime": 1745429178.0,
            "nlink": 1,
            "path": "/runner/project/vmlc-services-dns-v3/playbooks/../vars/powershell_config.yml",
            "pw_name": "root",
            "readable": true,
            "rgrp": true,
            "roth": true,
            "rusr": true,
            "size": 1237,
            "uid": 0,
            "version": null,
            "wgrp": false,
            "woth": false,
            "writeable": true,
            "wusr": true,
            "xgrp": false,
            "xoth": false,
            "xusr": false
        }
    },
    "msg": "All assertions passed"
}
ok: [localhost] => (item={'changed': False, 'stat': {'exists': True, 'path': '/runner/project/vmlc-services-dns-v3/playbooks/../vars/report_config.yml', 'mode': '0644', 'isdir': False, 'ischr': False, 'isblk': False, 'isreg': True, 'isfifo': False, 'islnk': False, 'issock': False, 'uid': 0, 'gid': 0, 'size': 4035, 'inode': 67109040, 'dev': 64778, 'nlink': 1, 'atime': 1745549089.6858099, 'mtime': 1745515916.0, 'ctime': 1745549087.430804, 'wusr': True, 'rusr': True, 'xusr': False, 'wgrp': False, 'rgrp': True, 'xgrp': False, 'woth': False, 'roth': True, 'xoth': False, 'isuid': False, 'isgid': False, 'blocks': 8, 'block_size': 4096, 'device_type': 0, 'readable': True, 'writeable': True, 'executable': False, 'pw_name': 'root', 'gr_name': 'root', 'checksum': '277fde3f7cc2e9923c2c884557d583bed0568aa6', 'mimetype': 'unknown', 'charset': 'unknown', 'version': None, 'attributes': [], 'attr_flags': ''}, 'invocation': {'module_args': {'path': '/runner/project/vmlc-services-dns-v3/playbooks/../vars/report_config.yml', 'follow': False, 'get_md5': False, 'get_checksum': True, 'get_mime': True, 'get_attributes': True, 'checksum_algorithm': 'sha1'}}, 'failed': False, 'item': 'report_config.yml', 'ansible_loop_var': 'item'}) => {
    "ansible_loop_var": "item",
    "changed": false,
    "item": {
        "ansible_loop_var": "item",
        "changed": false,
        "failed": false,
        "invocation": {
            "module_args": {
                "checksum_algorithm": "sha1",
                "follow": false,
                "get_attributes": true,
                "get_checksum": true,
                "get_md5": false,
                "get_mime": true,
                "path": "/runner/project/vmlc-services-dns-v3/playbooks/../vars/report_config.yml"
            }
        },
        "item": "report_config.yml",
        "stat": {
            "atime": 1745549089.6858099,
            "attr_flags": "",
            "attributes": [],
            "block_size": 4096,
            "blocks": 8,
            "charset": "unknown",
            "checksum": "277fde3f7cc2e9923c2c884557d583bed0568aa6",
            "ctime": 1745549087.430804,
            "dev": 64778,
            "device_type": 0,
            "executable": false,
            "exists": true,
            "gid": 0,
            "gr_name": "root",
            "inode": 67109040,
            "isblk": false,
            "ischr": false,
            "isdir": false,
            "isfifo": false,
            "isgid": false,
            "islnk": false,
            "isreg": true,
            "issock": false,
            "isuid": false,
            "mimetype": "unknown",
            "mode": "0644",
            "mtime": 1745515916.0,
            "nlink": 1,
            "path": "/runner/project/vmlc-services-dns-v3/playbooks/../vars/report_config.yml",
            "pw_name": "root",
            "readable": true,
            "rgrp": true,
            "roth": true,
            "rusr": true,
            "size": 4035,
            "uid": 0,
            "version": null,
            "wgrp": false,
            "woth": false,
            "writeable": true,
            "wusr": true,
            "xgrp": false,
            "xoth": false,
            "xusr": false
        }
    },
    "msg": "All assertions passed"
}

TASK [Load unified domain configuration] ***************************************
task path: /runner/project/vmlc-services-dns-v3/roles/common/tasks/load_configuration.yml:30
ok: [localhost] => {"ansible_facts": {"dns_defaults": {"description": "Managed by Ansible DNS Management", "ttl": 3600}, "dns_servers": {"aic.local": "aicaddcvpsys06.aic.local", "devhealthgrp.com.sg": "hisaddcvdutl01.devhealthgrp.com.sg", "exthealthgrp.com.sg": "dc01.exthealthgrp.com.sg", "hcloud.healthgrp.com.sg": "hisaddcvputl02.hcloud.healthgrp.com.sg", "healthgrp.com.sg": "hisaddcvputl07.healthgrp.com.sg", "healthgrpextp.com.sg": "hisaddcvputl13.healthgrpextp.com.sg", "healthgrpexts.com.sg": "hisaddcvsutl03.healthgrpexts.com.sg", "iltc.healthgrp.com.sg": "aicaddcvpsys02.iltc.healthgrp.com.sg", "nhg.local": "nhgaddcvpsys03.nhg.local", "nnstg.local": "nhgaddcvssys01.nnstg.local", "ses.shsu.com.sg": "sedcvssys22h1.ses.shsu.com.sg", "shses.shs.com.sg": "sesdcvpsys01.shses.shs.com.sg"}, "domain_groups": {"healthgrp": ["healthgrp.com.sg", "hcloud.healthgrp.com.sg", "iltc.healthgrp.com.sg", "healthgrpextp.com.sg", "exthealthgrp.com.sg", "devhealthgrp.com.sg", "healthgrpexts.com.sg"], "local": ["nhg.local", "aic.local", "nnstg.local"], "production": ["healthgrp.com.sg", "hcloud.healthgrp.com.sg", "iltc.healthgrp.com.sg", "healthgrpextp.com.sg", "exthealthgrp.com.sg", "nhg.local", "aic.local", "shses.shs.com.sg"], "shs": ["shses.shs.com.sg", "ses.shsu.com.sg"], "staging": ["devhealthgrp.com.sg", "healthgrpexts.com.sg", "nnstg.local", "ses.shsu.com.sg"]}, "domain_keys": ["healthgrp.com.sg", "hcloud.healthgrp.com.sg", "iltc.healthgrp.com.sg", "healthgrpextp.com.sg", "exthealthgrp.com.sg", "nhg.local", "aic.local", "shses.shs.com.sg", "devhealthgrp.com.sg", "healthgrpexts.com.sg", "nnstg.local", "ses.shsu.com.sg"], "domain_selection": {"default_admt_server": {"prd": "HISADMTVPSEC05.healthgrp.com.sg", "stg": "HISADMTVDSEC01.devhealthgrp.com.sg"}, "default_domain": {"prd": "healthgrp.com.sg", "stg": "devhealthgrp.com.sg"}}, "domains": {"aic.local": {"admt_server": "HISADMTVPSEC02.aic.local", "dc": "hdc1", "description": "AIC Local Domain", "environment": "prd", "network_zone": "mgt", "os": "win"}, "devhealthgrp.com.sg": {"admt_server": "HISADMTVDSEC01.devhealthgrp.com.sg", "dc": "hdc1", "description": "Development Health Group Domain", "environment": "stg", "network_zone": "mgt", "os": "win"}, "exthealthgrp.com.sg": {"admt_server": "HISADMTVPSEC07.exthealthgrp.com.sg", "dc": "hdc1", "description": "External Health Group Domain", "environment": "prd", "network_zone": "mgt", "os": "win"}, "hcloud.healthgrp.com.sg": {"admt_server": "HISADMTVPSEC06.hcloud.healthgrp.com.sg", "dc": "hdc1", "description": "Health Group Cloud Domain", "environment": "prd", "network_zone": "mgt", "os": "win", "related_domains": ["healthgrp.com.sg"]}, "healthgrp.com.sg": {"admt_server": "HISADMTVPSEC05.healthgrp.com.sg", "dc": "hdc2", "description": "Health Group Domain", "environment": "prd", "network_zone": "mgt", "os": "win", "related_domains": ["hcloud.healthgrp.com.sg", "iltc.healthgrp.com.sg"]}, "healthgrpextp.com.sg": {"admt_server": "HISADMTVPSEC08.healthgrpextp.com.sg", "dc": "hdc1", "description": "Health Group External Production Domain", "environment": "prd", "network_zone": "mgt", "os": "win", "related_domains": ["healthgrpexts.com.sg"]}, "healthgrpexts.com.sg": {"admt_server": "HISADMTVSSEC01.healthgrpexts.com.sg", "dc": "hdc2", "description": "Health Group External Staging Domain", "environment": "stg", "network_zone": "mgt", "os": "win", "related_domains": ["healthgrpextp.com.sg"]}, "iltc.healthgrp.com.sg": {"admt_server": "HISADMTVPSEC04.iltc.healthgrp.com.sg", "dc": "hdc1", "description": "ILTC Health Group Domain", "environment": "prd", "network_zone": "mgt", "os": "win", "related_domains": ["healthgrp.com.sg"]}, "nhg.local": {"admt_server": "HISADMTVPSEC11.nhg.local", "dc": "hdc2", "description": "NHG Local Domain", "environment": "prd", "network_zone": "mgt", "os": "win"}, "nnstg.local": {"admt_server": "HISADMTVSSEC02.nnstg.local", "dc": "hdc1", "description": "NNSTG Local Domain", "environment": "stg", "network_zone": "mgt", "os": "win"}, "ses.shsu.com.sg": {"admt_server": "SHSADMTVDSEC02.ses.shsu.com.sg", "dc": "hdc2", "description": "SES SHSU Domain", "environment": "stg", "network_zone": "mgt", "os": "win", "related_domains": ["shses.shs.com.sg"]}, "shses.shs.com.sg": {"admt_server": "SHSADMTVPSEC12.shses.shs.com.sg", "dc": "hdc1", "description": "SHSES Domain", "environment": "prd", "network_zone": "tsz", "os": "win", "related_domains": ["ses.shsu.com.sg"]}}, "ptr_dns_servers": {"aic.local": "aicaddcvpsys06.aic.local", "devhealthgrp.com.sg": "hisaddcvdutl01.devhealthgrp.com.sg", "exthealthgrp.com.sg": "dc01.exthealthgrp.com.sg", "hcloud.healthgrp.com.sg": "hisaddcvputl02.hcloud.healthgrp.com.sg", "healthgrp.com.sg": "hisaddcvputl07.healthgrp.com.sg", "healthgrpextp.com.sg": "hisaddcvputl13.healthgrpextp.com.sg", "healthgrpexts.com.sg": "hisaddcvsutl03.healthgrpexts.com.sg", "iltc.healthgrp.com.sg": "aicaddcvpsys02.iltc.healthgrp.com.sg", "nhg.local": "nhgaddcvpsys03.nhg.local", "nnstg.local": "nhgaddcvssys01.nnstg.local", "ses.shsu.com.sg": "shdcvsys22h1.shsu.com.sg", "shses.shs.com.sg": "sesdcvpsys11.shs.com.sg"}}, "ansible_included_var_files": ["/runner/project/vmlc-services-dns-v3/vars/domains.yml"], "changed": false}

TASK [Load domain credentials] *************************************************
task path: /runner/project/vmlc-services-dns-v3/roles/common/tasks/load_configuration.yml:35
ok: [localhost] => {"ansible_facts": {"domain_credentials": {"aic.local": {"dns_password": "{{ var_dns_aic_password }}", "dns_username": "{{ var_dns_aic_username }}"}, "devhealthgrp.com.sg": {"dns_password": "{{ var_dns_devhealthgrp_password }}", "dns_username": "{{ var_dns_devhealthgrp_username }}"}, "exthealthgrp.com.sg": {"dns_password": "{{ var_dns_exthealthgrp_password }}", "dns_username": "{{ var_dns_exthealthgrp_username }}"}, "hcloud.healthgrp.com.sg": {"dns_password": "{{ var_dns_hcloud_password }}", "dns_username": "{{ var_dns_hcloud_username }}"}, "healthgrp.com.sg": {"dns_password": "{{ var_dns_healthgrp_password }}", "dns_username": "{{ var_dns_healthgrp_username }}"}, "healthgrpextp.com.sg": {"dns_password": "{{ var_dns_healthgrpextp_password }}", "dns_username": "{{ var_dns_healthgrpextp_username }}"}, "healthgrpexts.com.sg": {"dns_password": "{{ var_dns_healthgrpexts_password }}", "dns_username": "{{ var_dns_healthgrpexts_username }}"}, "iltc.healthgrp.com.sg": {"dns_password": "{{ var_dns_iltc_password }}", "dns_username": "{{ var_dns_iltc_username }}"}, "nhg.local": {"dns_password": "{{ var_dns_nhg_password }}", "dns_username": "{{ var_dns_nhg_username }}"}, "nnstg.local": {"dns_password": "{{ var_dns_nnstg_password }}", "dns_username": "{{ var_dns_nnstg_username }}"}, "ses.shsu.com.sg": {"dns_password": "{{ var_dns_ses_password }}", "dns_username": "{{ var_dns_ses_username }}"}, "shses.shs.com.sg": {"dns_password": "{{ var_dns_shses_password }}", "dns_username": "{{ var_dns_shses_username }}"}}}, "ansible_included_var_files": ["/runner/project/vmlc-services-dns-v3/vars/domain_credentials.yml"], "changed": false}

TASK [Set multi-domain variables] **********************************************
task path: /runner/project/vmlc-services-dns-v3/roles/common/tasks/load_configuration.yml:39
ok: [localhost] => {"ansible_facts": {"cname_targets": "", "domains": {"aic.local": {"admt_server": "HISADMTVPSEC02.aic.local", "dc": "hdc1", "description": "AIC Local Domain", "environment": "prd", "network_zone": "mgt", "os": "win"}, "devhealthgrp.com.sg": {"admt_server": "HISADMTVDSEC01.devhealthgrp.com.sg", "dc": "hdc1", "description": "Development Health Group Domain", "environment": "stg", "network_zone": "mgt", "os": "win"}, "exthealthgrp.com.sg": {"admt_server": "HISADMTVPSEC07.exthealthgrp.com.sg", "dc": "hdc1", "description": "External Health Group Domain", "environment": "prd", "network_zone": "mgt", "os": "win"}, "hcloud.healthgrp.com.sg": {"admt_server": "HISADMTVPSEC06.hcloud.healthgrp.com.sg", "dc": "hdc1", "description": "Health Group Cloud Domain", "environment": "prd", "network_zone": "mgt", "os": "win", "related_domains": ["healthgrp.com.sg"]}, "healthgrp.com.sg": {"admt_server": "HISADMTVPSEC05.healthgrp.com.sg", "dc": "hdc2", "description": "Health Group Domain", "environment": "prd", "network_zone": "mgt", "os": "win", "related_domains": ["hcloud.healthgrp.com.sg", "iltc.healthgrp.com.sg"]}, "healthgrpextp.com.sg": {"admt_server": "HISADMTVPSEC08.healthgrpextp.com.sg", "dc": "hdc1", "description": "Health Group External Production Domain", "environment": "prd", "network_zone": "mgt", "os": "win", "related_domains": ["healthgrpexts.com.sg"]}, "healthgrpexts.com.sg": {"admt_server": "HISADMTVSSEC01.healthgrpexts.com.sg", "dc": "hdc2", "description": "Health Group External Staging Domain", "environment": "stg", "network_zone": "mgt", "os": "win", "related_domains": ["healthgrpextp.com.sg"]}, "iltc.healthgrp.com.sg": {"admt_server": "HISADMTVPSEC04.iltc.healthgrp.com.sg", "dc": "hdc1", "description": "ILTC Health Group Domain", "environment": "prd", "network_zone": "mgt", "os": "win", "related_domains": ["healthgrp.com.sg"]}, "nhg.local": {"admt_server": "HISADMTVPSEC11.nhg.local", "dc": "hdc2", "description": "NHG Local Domain", "environment": "prd", "network_zone": "mgt", "os": "win"}, "nnstg.local": {"admt_server": "HISADMTVSSEC02.nnstg.local", "dc": "hdc1", "description": "NNSTG Local Domain", "environment": "stg", "network_zone": "mgt", "os": "win"}, "ses.shsu.com.sg": {"admt_server": "SHSADMTVDSEC02.ses.shsu.com.sg", "dc": "hdc2", "description": "SES SHSU Domain", "environment": "stg", "network_zone": "mgt", "os": "win", "related_domains": ["shses.shs.com.sg"]}, "shses.shs.com.sg": {"admt_server": "SHSADMTVPSEC12.shses.shs.com.sg", "dc": "hdc1", "description": "SHSES Domain", "environment": "prd", "network_zone": "tsz", "os": "win", "related_domains": ["ses.shsu.com.sg"]}}, "hostnames": "", "ip_addresses": ""}, "changed": false}

TASK [Set operation type] ******************************************************
task path: /runner/project/vmlc-services-dns-v3/roles/common/tasks/load_configuration.yml:46
ok: [localhost] => {"ansible_facts": {"is_multi_domain": false, "operation_type": "single-domain"}, "changed": false}

TASK [Display operation information] *******************************************
task path: /runner/project/vmlc-services-dns-v3/roles/common/tasks/load_configuration.yml:51
ok: [localhost] => {
    "msg": [
        "Operation: Verify",
        "Record Type: A",
        "Operation Type: single-domain",
        "Domain: devhealthgrp.com.sg",
        "Hostname: ANSORTEST"
    ]
}

TASK [Validate domain configuration] *******************************************
task path: /runner/project/vmlc-services-dns-v3/playbooks/manage_dns.yml:65
included: /runner/project/vmlc-services-dns-v3/roles/common/tasks/validate_domain.yml for localhost

TASK [Check if domain exists in domain configuration] **************************
task path: /runner/project/vmlc-services-dns-v3/roles/common/tasks/validate_domain.yml:7
ok: [localhost] => {"ansible_facts": {"domain_exists": true}, "changed": false}

TASK [Validate domain exists in domain configuration] **************************
task path: /runner/project/vmlc-services-dns-v3/roles/common/tasks/validate_domain.yml:13
ok: [localhost] => {
    "changed": false,
    "msg": "All assertions passed"
}

TASK [Parse domains for multi-domain operations] *******************************
task path: /runner/project/vmlc-services-dns-v3/roles/common/tasks/validate_domain.yml:23
skipping: [localhost] => {"changed": false, "false_condition": "is_multi_domain", "skip_reason": "Conditional result was False"}

TASK [Check if each domain exists in domain configuration for multi-domain operations] ***
task path: /runner/project/vmlc-services-dns-v3/roles/common/tasks/validate_domain.yml:30
skipping: [localhost] => {"changed": false, "false_condition": "is_multi_domain", "skip_reason": "Conditional result was False"}

TASK [Validate all domains exist in domain configuration for multi-domain operations] ***
task path: /runner/project/vmlc-services-dns-v3/roles/common/tasks/validate_domain.yml:39
skipping: [localhost] => {"changed": false, "false_condition": "is_multi_domain and domain_existence_checks is defined", "skip_reason": "Conditional result was False"}

TASK [Extract domain information for single domain] ****************************
task path: /runner/project/vmlc-services-dns-v3/roles/common/tasks/validate_domain.yml:51
ok: [localhost] => {"ansible_facts": {"dns_server": "hisaddcvdutl01.devhealthgrp.com.sg", "domain_creds": {"dns_password": "sDKncjgDFk4$52#ds", "dns_username": "<EMAIL>"}, "domain_info": {"admt_server": "HISADMTVDSEC01.devhealthgrp.com.sg", "dc": "hdc1", "description": "Development Health Group Domain", "environment": "stg", "network_zone": "mgt", "os": "win"}, "ptr_dns_server": "hisaddcvdutl01.devhealthgrp.com.sg"}, "changed": false}

TASK [Get domain environments safely] ******************************************
task path: /runner/project/vmlc-services-dns-v3/roles/common/tasks/validate_domain.yml:60
skipping: [localhost] => {"changed": false, "false_condition": "is_multi_domain", "skip_reason": "Conditional result was False"}

TASK [Collect environment for each domain] *************************************
task path: /runner/project/vmlc-services-dns-v3/roles/common/tasks/validate_domain.yml:66
skipping: [localhost] => {"changed": false, "false_condition": "is_multi_domain and item in domain_keys", "skip_reason": "Conditional result was False"}

TASK [Ensure all domains are in the same environment] **************************
task path: /runner/project/vmlc-services-dns-v3/roles/common/tasks/validate_domain.yml:73
skipping: [localhost] => {"changed": false, "false_condition": "is_multi_domain and domain_environments_list is defined and domain_environments_list | length > 0", "skip_reason": "Conditional result was False"}

TASK [Set environment based on domain] *****************************************
task path: /runner/project/vmlc-services-dns-v3/roles/common/tasks/validate_domain.yml:81
ok: [localhost] => {"ansible_facts": {"environment": "stg"}, "changed": false}

TASK [Display domain information] **********************************************
task path: /runner/project/vmlc-services-dns-v3/roles/common/tasks/validate_domain.yml:85
ok: [localhost] => {
    "msg": [
        "Environment: []",
        "Domain Information: {'description': 'Development Health Group Domain', 'environment': 'stg', 'network_zone': 'mgt', 'dc': 'hdc1', 'admt_server': 'HISADMTVDSEC01.devhealthgrp.com.sg', 'os': 'win'}"
    ]
}

TASK [Set up logging] **********************************************************
task path: /runner/project/vmlc-services-dns-v3/playbooks/manage_dns.yml:69
included: /runner/project/vmlc-services-dns-v3/roles/common/tasks/setup_logging.yml for localhost

TASK [Ensure log directories exist] ********************************************
task path: /runner/project/vmlc-services-dns-v3/roles/common/tasks/setup_logging.yml:7
ok: [localhost] => (item=/runner/project/vmlc-services-dns-v3/playbooks/../logs/ansible) => {"ansible_loop_var": "item", "changed": false, "gid": 0, "group": "root", "item": "/runner/project/vmlc-services-dns-v3/playbooks/../logs/ansible", "mode": "0755", "owner": "root", "path": "/runner/project/vmlc-services-dns-v3/playbooks/../logs/ansible", "size": 22, "state": "directory", "uid": 0}
ok: [localhost] => (item=/runner/project/vmlc-services-dns-v3/playbooks/../logs/powershell) => {"ansible_loop_var": "item", "changed": false, "gid": 0, "group": "root", "item": "/runner/project/vmlc-services-dns-v3/playbooks/../logs/powershell", "mode": "0755", "owner": "root", "path": "/runner/project/vmlc-services-dns-v3/playbooks/../logs/powershell", "size": 22, "state": "directory", "uid": 0}
ok: [localhost] => (item=/runner/project/vmlc-services-dns-v3/playbooks/../logs/progress) => {"ansible_loop_var": "item", "changed": false, "gid": 0, "group": "root", "item": "/runner/project/vmlc-services-dns-v3/playbooks/../logs/progress", "mode": "0755", "owner": "root", "path": "/runner/project/vmlc-services-dns-v3/playbooks/../logs/progress", "size": 22, "state": "directory", "uid": 0}
ok: [localhost] => (item=/runner/project/vmlc-services-dns-v3/playbooks/../logs/archive) => {"ansible_loop_var": "item", "changed": false, "gid": 0, "group": "root", "item": "/runner/project/vmlc-services-dns-v3/playbooks/../logs/archive", "mode": "0755", "owner": "root", "path": "/runner/project/vmlc-services-dns-v3/playbooks/../logs/archive", "size": 103, "state": "directory", "uid": 0}

TASK [Include standardized log file paths] *************************************
task path: /runner/project/vmlc-services-dns-v3/roles/common/tasks/setup_logging.yml:23
included: /runner/project/vmlc-services-dns-v3/playbooks/../roles/common/tasks/set_log_paths.yml for localhost

TASK [Format date for log filenames] *******************************************
task path: /runner/project/vmlc-services-dns-v3/roles/common/tasks/set_log_paths.yml:6
ok: [localhost] => {"ansible_facts": {"log_date": "20250425"}, "changed": false}

TASK [Set ticket number with default] ******************************************
task path: /runner/project/vmlc-services-dns-v3/roles/common/tasks/set_log_paths.yml:10
ok: [localhost] => {"ansible_facts": {"log_ticket": "INC-123456"}, "changed": false}

TASK [Set hostname and domain for log filenames] *******************************
task path: /runner/project/vmlc-services-dns-v3/roles/common/tasks/set_log_paths.yml:14
ok: [localhost] => {"ansible_facts": {"log_domain": "devhealthgrp.com.sg", "log_hostname": "ANSORTEST", "log_operation": "VERIFY", "log_record_type": "A"}, "changed": false}

TASK [Construct base log filename] *********************************************
task path: /runner/project/vmlc-services-dns-v3/roles/common/tasks/set_log_paths.yml:21
ok: [localhost] => {"ansible_facts": {"log_base_name": "20250425_INC-123456_ANSORTEST_devhealthgrp.com.sg_A_VERIFY"}, "changed": false}

TASK [Set specific log file paths] *********************************************
task path: /runner/project/vmlc-services-dns-v3/roles/common/tasks/set_log_paths.yml:25
ok: [localhost] => {"ansible_facts": {"ansible_log_path": "/runner/project/vmlc-services-dns-v3/playbooks/../logs/ansible/20250425_INC-123456_ANSORTEST_devhealthgrp.com.sg_A_VERIFY_ANSIBLE.log", "powershell_log_path": "/runner/project/vmlc-services-dns-v3/playbooks/../logs/powershell/20250425_INC-123456_ANSORTEST_devhealthgrp.com.sg_A_VERIFY_POWERSHELL.log", "progress_log_path": "/runner/project/vmlc-services-dns-v3/playbooks/../logs/progress/20250425_INC-123456_ANSORTEST_devhealthgrp.com.sg_A_VERIFY_PROGRESS.log", "report_log_path": "/runner/project/vmlc-services-dns-v3/playbooks/../logs/ansible/20250425_INC-123456_ANSORTEST_devhealthgrp.com.sg_A_VERIFY_REPORT.log"}, "changed": false}

TASK [Set target server log paths] *********************************************
task path: /runner/project/vmlc-services-dns-v3/roles/common/tasks/set_log_paths.yml:32
ok: [localhost] => {"ansible_facts": {"target_ansible_log_path": "C:\\\\OE_AAP_LOGS\\\\20250425_INC-123456_ANSORTEST_devhealthgrp.com.sg_A_VERIFY_ANSIBLE.log", "target_powershell_log_path": "C:\\\\OE_AAP_LOGS\\\\20250425_INC-123456_ANSORTEST_devhealthgrp.com.sg_A_VERIFY_POWERSHELL.log"}, "changed": false}

TASK [Display log paths] *******************************************************
task path: /runner/project/vmlc-services-dns-v3/roles/common/tasks/set_log_paths.yml:37
skipping: [localhost] => {"false_condition": "current_log_level | default('INFO') | upper == 'DEBUG'"}

TASK [Rotate logs if enabled] **************************************************
task path: /runner/project/vmlc-services-dns-v3/roles/common/tasks/setup_logging.yml:27
redirecting (type: modules) ansible.builtin.archive to community.general.archive
included: /runner/project/vmlc-services-dns-v3/playbooks/../roles/common/tasks/rotate_logs.yml for localhost

TASK [Find old log files] ******************************************************
task path: /runner/project/vmlc-services-dns-v3/roles/common/tasks/rotate_logs.yml:6
ok: [localhost] => {"changed": false, "examined": 3, "files": [], "matched": 0, "msg": "All paths examined", "skipped_paths": {}}

TASK [Archive old log files] ***************************************************
task path: /runner/project/vmlc-services-dns-v3/roles/common/tasks/rotate_logs.yml:19
skipping: [localhost] => {"changed": false, "skipped_reason": "No items in the list"}

TASK [Display log rotation information] ****************************************
task path: /runner/project/vmlc-services-dns-v3/roles/common/tasks/rotate_logs.yml:30
skipping: [localhost] => {"false_condition": "old_logs.matched > 0 and log_flags.show_progress | bool"}

TASK [Display logging information] *********************************************
task path: /runner/project/vmlc-services-dns-v3/roles/common/tasks/setup_logging.yml:32
ok: [localhost] => {
    "msg": [
        "Log Level: INFO",
        "Ansible Log: /runner/project/vmlc-services-dns-v3/playbooks/../logs/ansible/20250425_INC-123456_ANSORTEST_devhealthgrp.com.sg_A_VERIFY_ANSIBLE.log",
        "PowerShell Log: /runner/project/vmlc-services-dns-v3/playbooks/../logs/powershell/20250425_INC-123456_ANSORTEST_devhealthgrp.com.sg_A_VERIFY_POWERSHELL.log",
        "Progress Log: /runner/project/vmlc-services-dns-v3/playbooks/../logs/progress/20250425_INC-123456_ANSORTEST_devhealthgrp.com.sg_A_VERIFY_PROGRESS.log"
    ]
}

TASK [Validate operation parameters] *******************************************
task path: /runner/project/vmlc-services-dns-v3/playbooks/manage_dns.yml:73
included: /runner/project/vmlc-services-dns-v3/roles/common/tasks/validate_operation.yml for localhost

TASK [Validate operation] ******************************************************
task path: /runner/project/vmlc-services-dns-v3/roles/common/tasks/validate_operation.yml:7
ok: [localhost] => {
    "changed": false,
    "msg": "All assertions passed"
}

TASK [Validate record type] ****************************************************
task path: /runner/project/vmlc-services-dns-v3/roles/common/tasks/validate_operation.yml:14
ok: [localhost] => {
    "changed": false,
    "msg": "All assertions passed"
}

TASK [Validate required parameters for single domain operations] ***************
task path: /runner/project/vmlc-services-dns-v3/roles/common/tasks/validate_operation.yml:21
ok: [localhost] => {
    "changed": false,
    "msg": "All assertions passed"
}

TASK [Validate operation-specific parameters for single domain] ****************
task path: /runner/project/vmlc-services-dns-v3/roles/common/tasks/validate_operation.yml:31
skipping: [localhost] => {"changed": false, "skipped_reason": "No items in the list"}

TASK [Validate multi-domain parameters] ****************************************
task path: /runner/project/vmlc-services-dns-v3/roles/common/tasks/validate_operation.yml:41
skipping: [localhost] => {"changed": false, "false_condition": "is_multi_domain", "skip_reason": "Conditional result was False"}

TASK [Validate operation-specific parameters for multi-domain (A/PTR records)] ***
task path: /runner/project/vmlc-services-dns-v3/roles/common/tasks/validate_operation.yml:51
skipping: [localhost] => {"changed": false, "false_condition": "is_multi_domain and record_type in ['a', 'ptr'] and operation in ['add', 'update']", "skip_reason": "Conditional result was False"}

TASK [Validate operation-specific parameters for multi-domain (CNAME records)] ***
task path: /runner/project/vmlc-services-dns-v3/roles/common/tasks/validate_operation.yml:61
skipping: [localhost] => {"changed": false, "false_condition": "is_multi_domain and record_type == 'cname' and operation == 'add'", "skip_reason": "Conditional result was False"}

TASK [Set up credentials] ******************************************************
task path: /runner/project/vmlc-services-dns-v3/playbooks/manage_dns.yml:77
included: /runner/project/vmlc-services-dns-v3/roles/common/tasks/setup_credentials.yml for localhost => (item=(censored due to no_log))

TASK [Check if developer testing configuration is available] *******************
task path: /runner/project/vmlc-services-dns-v3/roles/common/tasks/setup_credentials.yml:7
ok: [localhost] => {"changed": false, "stat": {"atime": 1745549087.107803, "attr_flags": "", "attributes": [], "block_size": 4096, "blocks": 16, "charset": "unknown", "checksum": "d8ad98692547c5510f45652dc3f885cb8ab33c1a", "ctime": 1745549087.428804, "dev": 64778, "device_type": 0, "executable": false, "exists": true, "gid": 0, "gr_name": "root", "inode": 191, "isblk": false, "ischr": false, "isdir": false, "isfifo": false, "isgid": false, "islnk": false, "isreg": true, "issock": false, "isuid": false, "mimetype": "unknown", "mode": "0644", "mtime": 1745445360.0, "nlink": 1, "path": "/runner/project/vmlc-services-dns-v3/playbooks/../dev/vars/testing_config.yml", "pw_name": "root", "readable": true, "rgrp": true, "roth": true, "rusr": true, "size": 6070, "uid": 0, "version": null, "wgrp": false, "woth": false, "writeable": true, "wusr": true, "xgrp": false, "xoth": false, "xusr": false}}

TASK [Load testing configuration if available] *********************************
task path: /runner/project/vmlc-services-dns-v3/roles/common/tasks/setup_credentials.yml:13
ok: [localhost] => {"ansible_facts": {"developer_testing": {"development": {"admt_server": "dev-admt.example.com", "credentials": {"password": "devpassword", "username": "devuser"}, "description": "Development testing environment", "dns_server": "dev-dns.example.com", "domains": ["dev.example.com", "test.dev.example.com"]}, "staging": {"admt_server": "stg-admt.example.com", "credentials": {"password": "stgpassword", "username": "stguser"}, "description": "Staging testing environment", "dns_server": "stg-dns.example.com", "domains": ["stg.example.com", "test.stg.example.com"]}, "use_mock_credentials": false, "vault_password_options": {"file_path": "~/.vault_pass.txt", "method": "prompt"}}, "molecule": {"driver": "docker", "platforms": [{"image": "geerlingguy/docker-ubuntu2004-ansible", "name": "ubuntu-20.04", "pre_build_image": true}, {"image": "geerlingguy/docker-centos8-ansible", "name": "centos-8", "pre_build_image": true}], "provisioner": {"log": true, "name": "ansible"}, "test_sequence": ["dependency", "lint", "cleanup", "destroy", "syntax", "create", "prepare", "converge", "idempotence", "verify", "cleanup", "destroy"], "verifier": {"name": "ansible"}}, "test_cases": {"add": [{"description": "Add an A record", "domain": "test.local", "expected_result": true, "hostname": "server02", "ip_address": "************", "name": "add_a_record", "operation": "add", "record_type": "a"}, {"description": "Add a duplicate A record", "domain": "test.local", "expected_result": false, "hostname": "server01", "ip_address": "************", "name": "add_duplicate_a_record", "operation": "add", "record_type": "a"}, {"cname_target": "server01.test.local", "description": "Add a CNAME record", "domain": "test.local", "expected_result": true, "hostname": "alias", "name": "add_cname_record", "operation": "add", "record_type": "cname"}, {"description": "Add a PTR record", "domain": "test.local", "expected_result": true, "hostname": "server03", "ip_address": "************", "name": "add_ptr_record", "operation": "add", "record_type": "ptr"}], "remove": [{"description": "Remove an A record", "domain": "test.local", "expected_result": true, "hostname": "server01", "name": "remove_a_record", "operation": "remove", "record_type": "a"}, {"description": "Remove a non-existent A record", "domain": "test.local", "expected_result": true, "hostname": "nonexistent", "name": "remove_nonexistent_a_record", "operation": "remove", "record_type": "a"}, {"description": "Remove a CNAME record", "domain": "test.local", "expected_result": true, "hostname": "www", "name": "remove_cname_record", "operation": "remove", "record_type": "cname"}, {"description": "Remove a PTR record", "domain": "test.local", "expected_result": true, "hostname": "server01", "ip_address": "************", "name": "remove_ptr_record", "operation": "remove", "record_type": "ptr"}], "update": [{"description": "Update an A record", "domain": "test.local", "expected_result": true, "hostname": "server01", "ip_address": "************", "name": "update_a_record", "operation": "update", "record_type": "a"}, {"description": "Update a non-existent A record", "domain": "test.local", "expected_result": false, "hostname": "nonexistent", "ip_address": "************", "name": "update_nonexistent_a_record", "operation": "update", "record_type": "a"}, {"cname_target": "server02.test.local", "description": "Update a CNAME record", "domain": "test.local", "expected_result": true, "hostname": "www", "name": "update_cname_record", "operation": "update", "record_type": "cname"}, {"description": "Update a PTR record", "domain": "test.local", "expected_result": true, "hostname": "server01", "ip_address": "************", "name": "update_ptr_record", "operation": "update", "record_type": "ptr"}], "verify": [{"description": "Verify an existing A record", "domain": "test.local", "expected_result": true, "hostname": "server01", "name": "verify_existing_a_record", "operation": "verify", "record_type": "a"}, {"description": "Verify a non-existent A record", "domain": "test.local", "expected_result": false, "hostname": "nonexistent", "name": "verify_nonexistent_a_record", "operation": "verify", "record_type": "a"}, {"description": "Verify an existing CNAME record", "domain": "test.local", "expected_result": true, "hostname": "www", "name": "verify_existing_cname_record", "operation": "verify", "record_type": "cname"}, {"description": "Verify an existing PTR record", "domain": "test.local", "expected_result": true, "hostname": "server01", "ip_address": "************", "name": "verify_existing_ptr_record", "operation": "verify", "record_type": "ptr"}]}, "test_environments": {"local": {"admt_server": "localhost", "credentials": {"password": "testpassword", "username": "testuser"}, "description": "Local testing environment", "dns_server": "localhost", "domains": ["test.local", "example.test"]}}}, "ansible_included_var_files": ["/runner/project/vmlc-services-dns-v3/dev/vars/testing_config.yml"], "changed": false}

TASK [Set developer mode flag] *************************************************
task path: /runner/project/vmlc-services-dns-v3/roles/common/tasks/setup_credentials.yml:19
ok: [localhost] => {"ansible_facts": {"developer_mode": false}, "changed": false}

TASK [Load vault file with credentials] ****************************************
task path: /runner/project/vmlc-services-dns-v3/roles/common/tasks/setup_credentials.yml:24
ok: [localhost] => {"ansible_facts": {"var_dns_aic_password": {"__ansible_vault": "$ANSIBLE_VAULT;1.1;AES256\\n35383266326533646664303235626139636365313063623534343361356538386663353165386161\\n3331373439656565303663326236616163333732346634610a343932353532343364663730303030\\n31613436393036363231643933306237616137346465316261636565373166356661373134303534\\n3061616637373633610a323733636661396337633162336365373335313332393334303736316237\\n64303136646531653634663339373436653732653534366534346137326461663339\\n"}, "var_dns_aic_username": "<EMAIL>", "var_dns_devhealthgrp_password": {"__ansible_vault": "$ANSIBLE_VAULT;1.1;AES256\\n39333835316566303565626562383565336461636565393039303937653133333062383765623036\\n3962333337666331633739336632623035373838663035390a306465646333613837393431643134\\n35306239656637363239356562306366653765323937373237366363613466366161653732653961\\n6636323962303261650a376165623965343132386561643464303638643965323935323163373161\\n64343363323061383838623566626138316562306638316464333333316231336438\\n"}, "var_dns_devhealthgrp_username": "<EMAIL>", "var_dns_exthealthgrp_password": {"__ansible_vault": "$ANSIBLE_VAULT;1.1;AES256\\n30666566383632323639633537363238616531626263323536386631333664353934396530636561\\n6463666332353136376264643338636336656433623064660a643536353033343134336164613632\\n31326362623336663535356636343165383637316237383665393231306532613433373733626263\\n6633343064313734390a386331386663393661353063303639643064636630616638633832303634\\n37393462333761393631326461626336326233343764633061643962356363633761\\n"}, "var_dns_exthealthgrp_username": "<EMAIL>", "var_dns_hcloud_password": {"__ansible_vault": "$ANSIBLE_VAULT;1.1;AES256\\n30616130343838333964663939363035633635393034346332343431653833653237633338623665\\n3537656535373134666339396365386237353039636338360a386362326334656131663564646637\\n62653036366164363534363935336663653136626436393566363734636134393131303465313831\\n6132663662653032330a313864333136643630366238383236396166386234333231366130623461\\n63313464613033303464353564386633623238306538303362363637666533363835\\n"}, "var_dns_hcloud_username": "<EMAIL>", "var_dns_healthgrp_password": {"__ansible_vault": "$ANSIBLE_VAULT;1.1;AES256\\n35626566653732666430353135643763373538663437623231656565313631643835346430343833\\n3137323834313431366635333163326536313232616162390a616339313132343063323031313564\\n30323161346233313236386434303737656532643636306435623935663033656535353161376538\\n3366646631313261390a636433616563616638313637333635353963356431396539373963343633\\n34623534346537653439366637653662336538653231333562303430623939326562\\n"}, "var_dns_healthgrp_username": "<EMAIL>", "var_dns_healthgrpextp_password": {"__ansible_vault": "$ANSIBLE_VAULT;1.1;AES256\\n64663330343238363463303232393133313834613130323237383530383038396633626362336631\\n3961396430333632376138666134363438393439346461300a663431336234336330373931393937\\n66373230636230343137346232343838343237313730393564613162663934616566663165393838\\n6231623533323161610a323137326430633133353635393434316233376134633564353430323061\\n37663365623762626235316533656364373763326130636434393032393936316433\\n"}, "var_dns_healthgrpextp_username": "<EMAIL>", "var_dns_healthgrpexts_password": {"__ansible_vault": "$ANSIBLE_VAULT;1.1;AES256\\n62623037653335363761396362393066626238613438653661323733366536386164336234366164\\n6165363237366337363231633964626365633131623236390a613561626239336463653934343964\\n66396364313762656364346530646532323333386637373238393064663364353839346433303738\\n3830366463663461620a306138356466616130663639363438356237326362383162653566343431\\n39356134326565613238366136306430386435666263646138306264653861623137\\n"}, "var_dns_healthgrpexts_username": "<EMAIL>", "var_dns_iltc_password": {"__ansible_vault": "$ANSIBLE_VAULT;1.1;AES256\\n30616238353261333834386263366537653037633831393332346261646439633331643138373164\\n3434333461303564623261313965376566346338376234370a636561396233313334653864663233\\n62656438376565396639336639373032326531383933353930303131636332343737633134373932\\n3338353237646636360a336133366238373438383530313832653762643432663333663630663132\\n35326530633337653565343039383165653964616263653434333164383732663863\\n"}, "var_dns_iltc_username": "<EMAIL>", "var_dns_nhg_password": {"__ansible_vault": "$ANSIBLE_VAULT;1.1;AES256\\n30353361663035343038316638373663343632346530376464633465623762633237613037373935\\n3033373434373438306539646139623031663364393135350a323931643862383836303161646263\\n36333536316436653762326632663638613037393136613437633261653064303039653864353335\\n3337386430663566320a663365333634383262346563663362643437396237333466626163663061\\n61363532616666643532623438393965626633633762383866623837393031396265\\n"}, "var_dns_nhg_username": "<EMAIL>", "var_dns_nnstg_password": {"__ansible_vault": "$ANSIBLE_VAULT;1.1;AES256\\n33323431636430366639666531663130313838316536623664646634356633636364653536656332\\n3261636435613436613130323761626264376530313234370a326330383630376430356638386536\\n34613832626536663837363131313133646165346436313562303162653935343863323735303261\\n3631383931303064650a383462353536626432643763646539623464643932323837376535656462\\n37653564663332383166306233363332343133666532613234323165356337323564\\n"}, "var_dns_nnstg_username": "<EMAIL>", "var_dns_ses_password": {"__ansible_vault": "$ANSIBLE_VAULT;1.1;AES256\\n32306632363763313964626433633930323038303462626631626364633938623336633334373032\\n3638336334306561333239383464666465333336373963620a346138633463346266646132663064\\n64316631373063316339353566653766366465323862663064326433373361616463323235356461\\n6662346634356231320a353639613563613434613561313133326662386238343661356536623338\\n33313465323231353531623937376234353166653663343264383033353365363532\\n"}, "var_dns_ses_username": "<EMAIL>", "var_dns_shses_password": {"__ansible_vault": "$ANSIBLE_VAULT;1.1;AES256\\n34323538316462613765396634333663323637303165376634303331303066383661306537346665\\n3031353866326233316264356138653335386135373764640a343766363530643132613532373761\\n34343731613132356634323839623733623630636135623034626539663566616661643335643032\\n3835623363363239340a383835636261343461616634383432306131623961363034666163343338\\n34653662616630386166666635633236643330383765343838613633343464323066\\n"}, "var_dns_shses_username": "<EMAIL>"}, "ansible_included_var_files": ["/runner/project/vmlc-services-dns-v3/vars/vault.yml"], "changed": false}

TASK [Display developer mode information] **************************************
task path: /runner/project/vmlc-services-dns-v3/roles/common/tasks/setup_credentials.yml:30
skipping: [localhost] => {"false_condition": "developer_mode | bool"}

TASK [Set connection parameters for ADMT server] *******************************
task path: /runner/project/vmlc-services-dns-v3/roles/common/tasks/setup_credentials.yml:36
ok: [localhost] => {"censored": "the output has been hidden due to the fact that 'no_log: true' was specified for this result", "changed": false}

TASK [Add ADMT server to inventory with connection parameters] *****************
task path: /runner/project/vmlc-services-dns-v3/roles/common/tasks/setup_credentials.yml:46
creating host via 'add_host': hostname=HISADMTVDSEC01.devhealthgrp.com.sg
changed: [localhost] => {"censored": "the output has been hidden due to the fact that 'no_log: true' was specified for this result", "changed": true}

TASK [Reset connection parameters for localhost operations] ********************
task path: /runner/project/vmlc-services-dns-v3/roles/common/tasks/setup_credentials.yml:62
ok: [localhost] => {"ansible_facts": {"ansible_connection": "local", "ansible_password": null, "ansible_user": null, "ansible_winrm_server_cert_validation": null}, "changed": false}

TASK [Set ADMT server, DNS server, and credentials] ****************************
task path: /runner/project/vmlc-services-dns-v3/roles/common/tasks/setup_credentials.yml:71
ok: [localhost] => {"censored": "the output has been hidden due to the fact that 'no_log: true' was specified for this result", "changed": false}

TASK [Set PTR DNS server based on domain] **************************************
task path: /runner/project/vmlc-services-dns-v3/roles/common/tasks/setup_credentials.yml:82
ok: [localhost] => {"ansible_facts": {"ptr_dns_server": "hisaddcvdutl01.devhealthgrp.com.sg"}, "changed": false}

TASK [Create domain to credential mapping for multi-domain operations] *********
task path: /runner/project/vmlc-services-dns-v3/roles/common/tasks/setup_credentials.yml:88
skipping: [localhost] => {"censored": "the output has been hidden due to the fact that 'no_log: true' was specified for this result", "changed": false}

TASK [Display credential information] ******************************************
task path: /runner/project/vmlc-services-dns-v3/roles/common/tasks/setup_credentials.yml:96
skipping: [localhost] => {"false_condition": "current_log_level | upper == log_settings.levels.debug"}

TASK [Initialize results] ******************************************************
task path: /runner/project/vmlc-services-dns-v3/playbooks/manage_dns.yml:83
included: /runner/project/vmlc-services-dns-v3/roles/common/tasks/initialize_results.yml for localhost

TASK [Initialize result variables for single domain operations] ****************
task path: /runner/project/vmlc-services-dns-v3/roles/common/tasks/initialize_results.yml:7
ok: [localhost] => {"ansible_facts": {"dns_operation_result": {"message": "Operation not yet executed", "operation": "Verify", "record": {"domain": "devhealthgrp.com.sg", "fqdn": "ANSORTEST.devhealthgrp.com.sg", "hostname": "ANSORTEST", "type": "A"}, "success": false, "timestamp": "2025-04-25T02:44:51Z"}}, "changed": false}

TASK [Initialize consolidated results variable for multi-domain operations] ****
task path: /runner/project/vmlc-services-dns-v3/roles/common/tasks/initialize_results.yml:22
skipping: [localhost] => {"changed": false, "false_condition": "is_multi_domain", "skip_reason": "Conditional result was False"}

TASK [Create domain to hostname mapping] ***************************************
task path: /runner/project/vmlc-services-dns-v3/roles/common/tasks/initialize_results.yml:29
skipping: [localhost] => {"changed": false, "false_condition": "is_multi_domain", "skip_reason": "Conditional result was False"}

TASK [Create domain to IP address mapping for A/PTR records] *******************
task path: /runner/project/vmlc-services-dns-v3/roles/common/tasks/initialize_results.yml:35
skipping: [localhost] => {"changed": false, "false_condition": "is_multi_domain and record_type | lower in ['a', 'ptr'] and ip_addresses is defined and ip_addresses | length > 0", "skip_reason": "Conditional result was False"}

TASK [Create domain to CNAME target mapping for CNAME records] *****************
task path: /runner/project/vmlc-services-dns-v3/roles/common/tasks/initialize_results.yml:41
skipping: [localhost] => {"changed": false, "false_condition": "is_multi_domain and record_type | lower == 'cname' and cname_targets is defined and cname_targets | length > 0", "skip_reason": "Conditional result was False"}

TASK [Log operation start] *****************************************************
task path: /runner/project/vmlc-services-dns-v3/roles/common/tasks/initialize_results.yml:47
ok: [localhost] => {
    "msg": "Starting Verify operation for A record"
}

TASK [Execute single domain operation] *****************************************
task path: /runner/project/vmlc-services-dns-v3/playbooks/manage_dns.yml:90

TASK [common : Load configuration] *********************************************
task path: /runner/project/vmlc-services-dns-v3/roles/common/tasks/main.yml:7
included: /runner/project/vmlc-services-dns-v3/roles/common/tasks/load_configuration.yml for localhost

TASK [common : Display project information] ************************************
task path: /runner/project/vmlc-services-dns-v3/roles/common/tasks/load_configuration.yml:6
ok: [localhost] => {
    "msg": [
        "DNS Management v2.0.0",
        "DNS Management System for managing DNS records across multiple domains",
        "Author: CES Operational Excellence Team",
        "Contributors: Muhammad Syazani Bin Mohamed Khairi (7409)"
    ]
}

TASK [common : Validate configuration files] ***********************************
task path: /runner/project/vmlc-services-dns-v3/roles/common/tasks/load_configuration.yml:15
ok: [localhost] => (item=domains.yml) => {"ansible_loop_var": "item", "changed": false, "item": "domains.yml", "stat": {"atime": 1745549089.6758099, "attr_flags": "", "attributes": [], "block_size": 4096, "blocks": 16, "charset": "unknown", "checksum": "c7ba92656a954da27682ba9f1300b02c046d61b3", "ctime": 1745549087.430804, "dev": 64778, "device_type": 0, "executable": false, "exists": true, "gid": 0, "gr_name": "root", "inode": 67109039, "isblk": false, "ischr": false, "isdir": false, "isfifo": false, "isgid": false, "islnk": false, "isreg": true, "issock": false, "isuid": false, "mimetype": "unknown", "mode": "0644", "mtime": 1745443858.0, "nlink": 1, "path": "/runner/project/vmlc-services-dns-v3/playbooks/../vars/domains.yml", "pw_name": "root", "readable": true, "rgrp": true, "roth": true, "rusr": true, "size": 6756, "uid": 0, "version": null, "wgrp": false, "woth": false, "writeable": true, "wusr": true, "xgrp": false, "xoth": false, "xusr": false}}
ok: [localhost] => (item=domain_credentials.yml) => {"ansible_loop_var": "item", "changed": false, "item": "domain_credentials.yml", "stat": {"atime": 1745549089.67881, "attr_flags": "", "attributes": [], "block_size": 4096, "blocks": 8, "charset": "unknown", "checksum": "f9c559b39f52e639225682fe008d969bb61a6792", "ctime": 1745549087.430804, "dev": 64778, "device_type": 0, "executable": false, "exists": true, "gid": 0, "gr_name": "root", "inode": 67109038, "isblk": false, "ischr": false, "isdir": false, "isfifo": false, "isgid": false, "islnk": false, "isreg": true, "issock": false, "isuid": false, "mimetype": "unknown", "mode": "0644", "mtime": 1745443858.0, "nlink": 1, "path": "/runner/project/vmlc-services-dns-v3/playbooks/../vars/domain_credentials.yml", "pw_name": "root", "readable": true, "rgrp": true, "roth": true, "rusr": true, "size": 1749, "uid": 0, "version": null, "wgrp": false, "woth": false, "writeable": true, "wusr": true, "xgrp": false, "xoth": false, "xusr": false}}
ok: [localhost] => (item=credentials_config.yml) => {"ansible_loop_var": "item", "changed": false, "item": "credentials_config.yml", "stat": {"atime": 1745549089.6798098, "attr_flags": "", "attributes": [], "block_size": 4096, "blocks": 8, "charset": "unknown", "checksum": "5db2da05958ec7d0816eebc45b6423c43ef99035", "ctime": 1745549087.430804, "dev": 64778, "device_type": 0, "executable": false, "exists": true, "gid": 0, "gr_name": "root", "inode": 67109037, "isblk": false, "ischr": false, "isdir": false, "isfifo": false, "isgid": false, "islnk": false, "isreg": true, "issock": false, "isuid": false, "mimetype": "unknown", "mode": "0644", "mtime": 1745443858.0, "nlink": 1, "path": "/runner/project/vmlc-services-dns-v3/playbooks/../vars/credentials_config.yml", "pw_name": "root", "readable": true, "rgrp": true, "roth": true, "rusr": true, "size": 2859, "uid": 0, "version": null, "wgrp": false, "woth": false, "writeable": true, "wusr": true, "xgrp": false, "xoth": false, "xusr": false}}
ok: [localhost] => (item=email_config.yml) => {"ansible_loop_var": "item", "changed": false, "item": "email_config.yml", "stat": {"atime": 1745549089.68181, "attr_flags": "", "attributes": [], "block_size": 4096, "blocks": 8, "charset": "unknown", "checksum": "8595e0bc9e2fd7025f01f6db48a0337fcb2c4c09", "ctime": 1745549087.430804, "dev": 64778, "device_type": 0, "executable": false, "exists": true, "gid": 0, "gr_name": "root", "inode": 67109041, "isblk": false, "ischr": false, "isdir": false, "isfifo": false, "isgid": false, "islnk": false, "isreg": true, "issock": false, "isuid": false, "mimetype": "unknown", "mode": "0644", "mtime": 1745516376.0, "nlink": 1, "path": "/runner/project/vmlc-services-dns-v3/playbooks/../vars/email_config.yml", "pw_name": "root", "readable": true, "rgrp": true, "roth": true, "rusr": true, "size": 2425, "uid": 0, "version": null, "wgrp": false, "woth": false, "writeable": true, "wusr": true, "xgrp": false, "xoth": false, "xusr": false}}
ok: [localhost] => (item=log_config.yml) => {"ansible_loop_var": "item", "changed": false, "item": "log_config.yml", "stat": {"atime": 1745549089.6828098, "attr_flags": "", "attributes": [], "block_size": 4096, "blocks": 8, "charset": "unknown", "checksum": "c6880f6d6b16eddf32d5ba8946e9e2e729088bd1", "ctime": 1745549087.430804, "dev": 64778, "device_type": 0, "executable": false, "exists": true, "gid": 0, "gr_name": "root", "inode": 67109032, "isblk": false, "ischr": false, "isdir": false, "isfifo": false, "isgid": false, "islnk": false, "isreg": true, "issock": false, "isuid": false, "mimetype": "unknown", "mode": "0644", "mtime": 1745429178.0, "nlink": 1, "path": "/runner/project/vmlc-services-dns-v3/playbooks/../vars/log_config.yml", "pw_name": "root", "readable": true, "rgrp": true, "roth": true, "rusr": true, "size": 1634, "uid": 0, "version": null, "wgrp": false, "woth": false, "writeable": true, "wusr": true, "xgrp": false, "xoth": false, "xusr": false}}
ok: [localhost] => (item=operations_config.yml) => {"ansible_loop_var": "item", "changed": false, "item": "operations_config.yml", "stat": {"atime": 1745549089.6828098, "attr_flags": "", "attributes": [], "block_size": 4096, "blocks": 8, "charset": "unknown", "checksum": "92368efc94ffe4a29f42bf52adce03b3b343b963", "ctime": 1745549087.430804, "dev": 64778, "device_type": 0, "executable": false, "exists": true, "gid": 0, "gr_name": "root", "inode": 67109033, "isblk": false, "ischr": false, "isdir": false, "isfifo": false, "isgid": false, "islnk": false, "isreg": true, "issock": false, "isuid": false, "mimetype": "unknown", "mode": "0644", "mtime": 1745429178.0, "nlink": 1, "path": "/runner/project/vmlc-services-dns-v3/playbooks/../vars/operations_config.yml", "pw_name": "root", "readable": true, "rgrp": true, "roth": true, "rusr": true, "size": 2488, "uid": 0, "version": null, "wgrp": false, "woth": false, "writeable": true, "wusr": true, "xgrp": false, "xoth": false, "xusr": false}}
ok: [localhost] => (item=powershell_config.yml) => {"ansible_loop_var": "item", "changed": false, "item": "powershell_config.yml", "stat": {"atime": 1745549089.6848097, "attr_flags": "", "attributes": [], "block_size": 4096, "blocks": 8, "charset": "unknown", "checksum": "13602fdc714a05e83fb29569e42617c820125c0f", "ctime": 1745549087.430804, "dev": 64778, "device_type": 0, "executable": false, "exists": true, "gid": 0, "gr_name": "root", "inode": 67109034, "isblk": false, "ischr": false, "isdir": false, "isfifo": false, "isgid": false, "islnk": false, "isreg": true, "issock": false, "isuid": false, "mimetype": "unknown", "mode": "0644", "mtime": 1745429178.0, "nlink": 1, "path": "/runner/project/vmlc-services-dns-v3/playbooks/../vars/powershell_config.yml", "pw_name": "root", "readable": true, "rgrp": true, "roth": true, "rusr": true, "size": 1237, "uid": 0, "version": null, "wgrp": false, "woth": false, "writeable": true, "wusr": true, "xgrp": false, "xoth": false, "xusr": false}}
ok: [localhost] => (item=report_config.yml) => {"ansible_loop_var": "item", "changed": false, "item": "report_config.yml", "stat": {"atime": 1745549089.6858099, "attr_flags": "", "attributes": [], "block_size": 4096, "blocks": 8, "charset": "unknown", "checksum": "277fde3f7cc2e9923c2c884557d583bed0568aa6", "ctime": 1745549087.430804, "dev": 64778, "device_type": 0, "executable": false, "exists": true, "gid": 0, "gr_name": "root", "inode": 67109040, "isblk": false, "ischr": false, "isdir": false, "isfifo": false, "isgid": false, "islnk": false, "isreg": true, "issock": false, "isuid": false, "mimetype": "unknown", "mode": "0644", "mtime": 1745515916.0, "nlink": 1, "path": "/runner/project/vmlc-services-dns-v3/playbooks/../vars/report_config.yml", "pw_name": "root", "readable": true, "rgrp": true, "roth": true, "rusr": true, "size": 4035, "uid": 0, "version": null, "wgrp": false, "woth": false, "writeable": true, "wusr": true, "xgrp": false, "xoth": false, "xusr": false}}

TASK [common : Ensure all configuration files exist] ***************************
task path: /runner/project/vmlc-services-dns-v3/roles/common/tasks/load_configuration.yml:22
ok: [localhost] => (item={'changed': False, 'stat': {'exists': True, 'path': '/runner/project/vmlc-services-dns-v3/playbooks/../vars/domains.yml', 'mode': '0644', 'isdir': False, 'ischr': False, 'isblk': False, 'isreg': True, 'isfifo': False, 'islnk': False, 'issock': False, 'uid': 0, 'gid': 0, 'size': 6756, 'inode': 67109039, 'dev': 64778, 'nlink': 1, 'atime': 1745549089.6758099, 'mtime': 1745443858.0, 'ctime': 1745549087.430804, 'wusr': True, 'rusr': True, 'xusr': False, 'wgrp': False, 'rgrp': True, 'xgrp': False, 'woth': False, 'roth': True, 'xoth': False, 'isuid': False, 'isgid': False, 'blocks': 16, 'block_size': 4096, 'device_type': 0, 'readable': True, 'writeable': True, 'executable': False, 'pw_name': 'root', 'gr_name': 'root', 'checksum': 'c7ba92656a954da27682ba9f1300b02c046d61b3', 'mimetype': 'unknown', 'charset': 'unknown', 'version': None, 'attributes': [], 'attr_flags': ''}, 'invocation': {'module_args': {'path': '/runner/project/vmlc-services-dns-v3/playbooks/../vars/domains.yml', 'follow': False, 'get_md5': False, 'get_checksum': True, 'get_mime': True, 'get_attributes': True, 'checksum_algorithm': 'sha1'}}, 'failed': False, 'item': 'domains.yml', 'ansible_loop_var': 'item'}) => {
    "ansible_loop_var": "item",
    "changed": false,
    "item": {
        "ansible_loop_var": "item",
        "changed": false,
        "failed": false,
        "invocation": {
            "module_args": {
                "checksum_algorithm": "sha1",
                "follow": false,
                "get_attributes": true,
                "get_checksum": true,
                "get_md5": false,
                "get_mime": true,
                "path": "/runner/project/vmlc-services-dns-v3/playbooks/../vars/domains.yml"
            }
        },
        "item": "domains.yml",
        "stat": {
            "atime": 1745549089.6758099,
            "attr_flags": "",
            "attributes": [],
            "block_size": 4096,
            "blocks": 16,
            "charset": "unknown",
            "checksum": "c7ba92656a954da27682ba9f1300b02c046d61b3",
            "ctime": 1745549087.430804,
            "dev": 64778,
            "device_type": 0,
            "executable": false,
            "exists": true,
            "gid": 0,
            "gr_name": "root",
            "inode": 67109039,
            "isblk": false,
            "ischr": false,
            "isdir": false,
            "isfifo": false,
            "isgid": false,
            "islnk": false,
            "isreg": true,
            "issock": false,
            "isuid": false,
            "mimetype": "unknown",
            "mode": "0644",
            "mtime": 1745443858.0,
            "nlink": 1,
            "path": "/runner/project/vmlc-services-dns-v3/playbooks/../vars/domains.yml",
            "pw_name": "root",
            "readable": true,
            "rgrp": true,
            "roth": true,
            "rusr": true,
            "size": 6756,
            "uid": 0,
            "version": null,
            "wgrp": false,
            "woth": false,
            "writeable": true,
            "wusr": true,
            "xgrp": false,
            "xoth": false,
            "xusr": false
        }
    },
    "msg": "All assertions passed"
}
ok: [localhost] => (item={'changed': False, 'stat': {'exists': True, 'path': '/runner/project/vmlc-services-dns-v3/playbooks/../vars/domain_credentials.yml', 'mode': '0644', 'isdir': False, 'ischr': False, 'isblk': False, 'isreg': True, 'isfifo': False, 'islnk': False, 'issock': False, 'uid': 0, 'gid': 0, 'size': 1749, 'inode': 67109038, 'dev': 64778, 'nlink': 1, 'atime': 1745549089.67881, 'mtime': 1745443858.0, 'ctime': 1745549087.430804, 'wusr': True, 'rusr': True, 'xusr': False, 'wgrp': False, 'rgrp': True, 'xgrp': False, 'woth': False, 'roth': True, 'xoth': False, 'isuid': False, 'isgid': False, 'blocks': 8, 'block_size': 4096, 'device_type': 0, 'readable': True, 'writeable': True, 'executable': False, 'pw_name': 'root', 'gr_name': 'root', 'checksum': 'f9c559b39f52e639225682fe008d969bb61a6792', 'mimetype': 'unknown', 'charset': 'unknown', 'version': None, 'attributes': [], 'attr_flags': ''}, 'invocation': {'module_args': {'path': '/runner/project/vmlc-services-dns-v3/playbooks/../vars/domain_credentials.yml', 'follow': False, 'get_md5': False, 'get_checksum': True, 'get_mime': True, 'get_attributes': True, 'checksum_algorithm': 'sha1'}}, 'failed': False, 'item': 'domain_credentials.yml', 'ansible_loop_var': 'item'}) => {
    "ansible_loop_var": "item",
    "changed": false,
    "item": {
        "ansible_loop_var": "item",
        "changed": false,
        "failed": false,
        "invocation": {
            "module_args": {
                "checksum_algorithm": "sha1",
                "follow": false,
                "get_attributes": true,
                "get_checksum": true,
                "get_md5": false,
                "get_mime": true,
                "path": "/runner/project/vmlc-services-dns-v3/playbooks/../vars/domain_credentials.yml"
            }
        },
        "item": "domain_credentials.yml",
        "stat": {
            "atime": 1745549089.67881,
            "attr_flags": "",
            "attributes": [],
            "block_size": 4096,
            "blocks": 8,
            "charset": "unknown",
            "checksum": "f9c559b39f52e639225682fe008d969bb61a6792",
            "ctime": 1745549087.430804,
            "dev": 64778,
            "device_type": 0,
            "executable": false,
            "exists": true,
            "gid": 0,
            "gr_name": "root",
            "inode": 67109038,
            "isblk": false,
            "ischr": false,
            "isdir": false,
            "isfifo": false,
            "isgid": false,
            "islnk": false,
            "isreg": true,
            "issock": false,
            "isuid": false,
            "mimetype": "unknown",
            "mode": "0644",
            "mtime": 1745443858.0,
            "nlink": 1,
            "path": "/runner/project/vmlc-services-dns-v3/playbooks/../vars/domain_credentials.yml",
            "pw_name": "root",
            "readable": true,
            "rgrp": true,
            "roth": true,
            "rusr": true,
            "size": 1749,
            "uid": 0,
            "version": null,
            "wgrp": false,
            "woth": false,
            "writeable": true,
            "wusr": true,
            "xgrp": false,
            "xoth": false,
            "xusr": false
        }
    },
    "msg": "All assertions passed"
}
ok: [localhost] => (item={'changed': False, 'stat': {'exists': True, 'path': '/runner/project/vmlc-services-dns-v3/playbooks/../vars/credentials_config.yml', 'mode': '0644', 'isdir': False, 'ischr': False, 'isblk': False, 'isreg': True, 'isfifo': False, 'islnk': False, 'issock': False, 'uid': 0, 'gid': 0, 'size': 2859, 'inode': 67109037, 'dev': 64778, 'nlink': 1, 'atime': 1745549089.6798098, 'mtime': 1745443858.0, 'ctime': 1745549087.430804, 'wusr': True, 'rusr': True, 'xusr': False, 'wgrp': False, 'rgrp': True, 'xgrp': False, 'woth': False, 'roth': True, 'xoth': False, 'isuid': False, 'isgid': False, 'blocks': 8, 'block_size': 4096, 'device_type': 0, 'readable': True, 'writeable': True, 'executable': False, 'pw_name': 'root', 'gr_name': 'root', 'checksum': '5db2da05958ec7d0816eebc45b6423c43ef99035', 'mimetype': 'unknown', 'charset': 'unknown', 'version': None, 'attributes': [], 'attr_flags': ''}, 'invocation': {'module_args': {'path': '/runner/project/vmlc-services-dns-v3/playbooks/../vars/credentials_config.yml', 'follow': False, 'get_md5': False, 'get_checksum': True, 'get_mime': True, 'get_attributes': True, 'checksum_algorithm': 'sha1'}}, 'failed': False, 'item': 'credentials_config.yml', 'ansible_loop_var': 'item'}) => {
    "ansible_loop_var": "item",
    "changed": false,
    "item": {
        "ansible_loop_var": "item",
        "changed": false,
        "failed": false,
        "invocation": {
            "module_args": {
                "checksum_algorithm": "sha1",
                "follow": false,
                "get_attributes": true,
                "get_checksum": true,
                "get_md5": false,
                "get_mime": true,
                "path": "/runner/project/vmlc-services-dns-v3/playbooks/../vars/credentials_config.yml"
            }
        },
        "item": "credentials_config.yml",
        "stat": {
            "atime": 1745549089.6798098,
            "attr_flags": "",
            "attributes": [],
            "block_size": 4096,
            "blocks": 8,
            "charset": "unknown",
            "checksum": "5db2da05958ec7d0816eebc45b6423c43ef99035",
            "ctime": 1745549087.430804,
            "dev": 64778,
            "device_type": 0,
            "executable": false,
            "exists": true,
            "gid": 0,
            "gr_name": "root",
            "inode": 67109037,
            "isblk": false,
            "ischr": false,
            "isdir": false,
            "isfifo": false,
            "isgid": false,
            "islnk": false,
            "isreg": true,
            "issock": false,
            "isuid": false,
            "mimetype": "unknown",
            "mode": "0644",
            "mtime": 1745443858.0,
            "nlink": 1,
            "path": "/runner/project/vmlc-services-dns-v3/playbooks/../vars/credentials_config.yml",
            "pw_name": "root",
            "readable": true,
            "rgrp": true,
            "roth": true,
            "rusr": true,
            "size": 2859,
            "uid": 0,
            "version": null,
            "wgrp": false,
            "woth": false,
            "writeable": true,
            "wusr": true,
            "xgrp": false,
            "xoth": false,
            "xusr": false
        }
    },
    "msg": "All assertions passed"
}
ok: [localhost] => (item={'changed': False, 'stat': {'exists': True, 'path': '/runner/project/vmlc-services-dns-v3/playbooks/../vars/email_config.yml', 'mode': '0644', 'isdir': False, 'ischr': False, 'isblk': False, 'isreg': True, 'isfifo': False, 'islnk': False, 'issock': False, 'uid': 0, 'gid': 0, 'size': 2425, 'inode': 67109041, 'dev': 64778, 'nlink': 1, 'atime': 1745549089.68181, 'mtime': 1745516376.0, 'ctime': 1745549087.430804, 'wusr': True, 'rusr': True, 'xusr': False, 'wgrp': False, 'rgrp': True, 'xgrp': False, 'woth': False, 'roth': True, 'xoth': False, 'isuid': False, 'isgid': False, 'blocks': 8, 'block_size': 4096, 'device_type': 0, 'readable': True, 'writeable': True, 'executable': False, 'pw_name': 'root', 'gr_name': 'root', 'checksum': '8595e0bc9e2fd7025f01f6db48a0337fcb2c4c09', 'mimetype': 'unknown', 'charset': 'unknown', 'version': None, 'attributes': [], 'attr_flags': ''}, 'invocation': {'module_args': {'path': '/runner/project/vmlc-services-dns-v3/playbooks/../vars/email_config.yml', 'follow': False, 'get_md5': False, 'get_checksum': True, 'get_mime': True, 'get_attributes': True, 'checksum_algorithm': 'sha1'}}, 'failed': False, 'item': 'email_config.yml', 'ansible_loop_var': 'item'}) => {
    "ansible_loop_var": "item",
    "changed": false,
    "item": {
        "ansible_loop_var": "item",
        "changed": false,
        "failed": false,
        "invocation": {
            "module_args": {
                "checksum_algorithm": "sha1",
                "follow": false,
                "get_attributes": true,
                "get_checksum": true,
                "get_md5": false,
                "get_mime": true,
                "path": "/runner/project/vmlc-services-dns-v3/playbooks/../vars/email_config.yml"
            }
        },
        "item": "email_config.yml",
        "stat": {
            "atime": 1745549089.68181,
            "attr_flags": "",
            "attributes": [],
            "block_size": 4096,
            "blocks": 8,
            "charset": "unknown",
            "checksum": "8595e0bc9e2fd7025f01f6db48a0337fcb2c4c09",
            "ctime": 1745549087.430804,
            "dev": 64778,
            "device_type": 0,
            "executable": false,
            "exists": true,
            "gid": 0,
            "gr_name": "root",
            "inode": 67109041,
            "isblk": false,
            "ischr": false,
            "isdir": false,
            "isfifo": false,
            "isgid": false,
            "islnk": false,
            "isreg": true,
            "issock": false,
            "isuid": false,
            "mimetype": "unknown",
            "mode": "0644",
            "mtime": 1745516376.0,
            "nlink": 1,
            "path": "/runner/project/vmlc-services-dns-v3/playbooks/../vars/email_config.yml",
            "pw_name": "root",
            "readable": true,
            "rgrp": true,
            "roth": true,
            "rusr": true,
            "size": 2425,
            "uid": 0,
            "version": null,
            "wgrp": false,
            "woth": false,
            "writeable": true,
            "wusr": true,
            "xgrp": false,
            "xoth": false,
            "xusr": false
        }
    },
    "msg": "All assertions passed"
}
ok: [localhost] => (item={'changed': False, 'stat': {'exists': True, 'path': '/runner/project/vmlc-services-dns-v3/playbooks/../vars/log_config.yml', 'mode': '0644', 'isdir': False, 'ischr': False, 'isblk': False, 'isreg': True, 'isfifo': False, 'islnk': False, 'issock': False, 'uid': 0, 'gid': 0, 'size': 1634, 'inode': 67109032, 'dev': 64778, 'nlink': 1, 'atime': 1745549089.6828098, 'mtime': 1745429178.0, 'ctime': 1745549087.430804, 'wusr': True, 'rusr': True, 'xusr': False, 'wgrp': False, 'rgrp': True, 'xgrp': False, 'woth': False, 'roth': True, 'xoth': False, 'isuid': False, 'isgid': False, 'blocks': 8, 'block_size': 4096, 'device_type': 0, 'readable': True, 'writeable': True, 'executable': False, 'pw_name': 'root', 'gr_name': 'root', 'checksum': 'c6880f6d6b16eddf32d5ba8946e9e2e729088bd1', 'mimetype': 'unknown', 'charset': 'unknown', 'version': None, 'attributes': [], 'attr_flags': ''}, 'invocation': {'module_args': {'path': '/runner/project/vmlc-services-dns-v3/playbooks/../vars/log_config.yml', 'follow': False, 'get_md5': False, 'get_checksum': True, 'get_mime': True, 'get_attributes': True, 'checksum_algorithm': 'sha1'}}, 'failed': False, 'item': 'log_config.yml', 'ansible_loop_var': 'item'}) => {
    "ansible_loop_var": "item",
    "changed": false,
    "item": {
        "ansible_loop_var": "item",
        "changed": false,
        "failed": false,
        "invocation": {
            "module_args": {
                "checksum_algorithm": "sha1",
                "follow": false,
                "get_attributes": true,
                "get_checksum": true,
                "get_md5": false,
                "get_mime": true,
                "path": "/runner/project/vmlc-services-dns-v3/playbooks/../vars/log_config.yml"
            }
        },
        "item": "log_config.yml",
        "stat": {
            "atime": 1745549089.6828098,
            "attr_flags": "",
            "attributes": [],
            "block_size": 4096,
            "blocks": 8,
            "charset": "unknown",
            "checksum": "c6880f6d6b16eddf32d5ba8946e9e2e729088bd1",
            "ctime": 1745549087.430804,
            "dev": 64778,
            "device_type": 0,
            "executable": false,
            "exists": true,
            "gid": 0,
            "gr_name": "root",
            "inode": 67109032,
            "isblk": false,
            "ischr": false,
            "isdir": false,
            "isfifo": false,
            "isgid": false,
            "islnk": false,
            "isreg": true,
            "issock": false,
            "isuid": false,
            "mimetype": "unknown",
            "mode": "0644",
            "mtime": 1745429178.0,
            "nlink": 1,
            "path": "/runner/project/vmlc-services-dns-v3/playbooks/../vars/log_config.yml",
            "pw_name": "root",
            "readable": true,
            "rgrp": true,
            "roth": true,
            "rusr": true,
            "size": 1634,
            "uid": 0,
            "version": null,
            "wgrp": false,
            "woth": false,
            "writeable": true,
            "wusr": true,
            "xgrp": false,
            "xoth": false,
            "xusr": false
        }
    },
    "msg": "All assertions passed"
}
ok: [localhost] => (item={'changed': False, 'stat': {'exists': True, 'path': '/runner/project/vmlc-services-dns-v3/playbooks/../vars/operations_config.yml', 'mode': '0644', 'isdir': False, 'ischr': False, 'isblk': False, 'isreg': True, 'isfifo': False, 'islnk': False, 'issock': False, 'uid': 0, 'gid': 0, 'size': 2488, 'inode': 67109033, 'dev': 64778, 'nlink': 1, 'atime': 1745549089.6828098, 'mtime': 1745429178.0, 'ctime': 1745549087.430804, 'wusr': True, 'rusr': True, 'xusr': False, 'wgrp': False, 'rgrp': True, 'xgrp': False, 'woth': False, 'roth': True, 'xoth': False, 'isuid': False, 'isgid': False, 'blocks': 8, 'block_size': 4096, 'device_type': 0, 'readable': True, 'writeable': True, 'executable': False, 'pw_name': 'root', 'gr_name': 'root', 'checksum': '92368efc94ffe4a29f42bf52adce03b3b343b963', 'mimetype': 'unknown', 'charset': 'unknown', 'version': None, 'attributes': [], 'attr_flags': ''}, 'invocation': {'module_args': {'path': '/runner/project/vmlc-services-dns-v3/playbooks/../vars/operations_config.yml', 'follow': False, 'get_md5': False, 'get_checksum': True, 'get_mime': True, 'get_attributes': True, 'checksum_algorithm': 'sha1'}}, 'failed': False, 'item': 'operations_config.yml', 'ansible_loop_var': 'item'}) => {
    "ansible_loop_var": "item",
    "changed": false,
    "item": {
        "ansible_loop_var": "item",
        "changed": false,
        "failed": false,
        "invocation": {
            "module_args": {
                "checksum_algorithm": "sha1",
                "follow": false,
                "get_attributes": true,
                "get_checksum": true,
                "get_md5": false,
                "get_mime": true,
                "path": "/runner/project/vmlc-services-dns-v3/playbooks/../vars/operations_config.yml"
            }
        },
        "item": "operations_config.yml",
        "stat": {
            "atime": 1745549089.6828098,
            "attr_flags": "",
            "attributes": [],
            "block_size": 4096,
            "blocks": 8,
            "charset": "unknown",
            "checksum": "92368efc94ffe4a29f42bf52adce03b3b343b963",
            "ctime": 1745549087.430804,
            "dev": 64778,
            "device_type": 0,
            "executable": false,
            "exists": true,
            "gid": 0,
            "gr_name": "root",
            "inode": 67109033,
            "isblk": false,
            "ischr": false,
            "isdir": false,
            "isfifo": false,
            "isgid": false,
            "islnk": false,
            "isreg": true,
            "issock": false,
            "isuid": false,
            "mimetype": "unknown",
            "mode": "0644",
            "mtime": 1745429178.0,
            "nlink": 1,
            "path": "/runner/project/vmlc-services-dns-v3/playbooks/../vars/operations_config.yml",
            "pw_name": "root",
            "readable": true,
            "rgrp": true,
            "roth": true,
            "rusr": true,
            "size": 2488,
            "uid": 0,
            "version": null,
            "wgrp": false,
            "woth": false,
            "writeable": true,
            "wusr": true,
            "xgrp": false,
            "xoth": false,
            "xusr": false
        }
    },
    "msg": "All assertions passed"
}
ok: [localhost] => (item={'changed': False, 'stat': {'exists': True, 'path': '/runner/project/vmlc-services-dns-v3/playbooks/../vars/powershell_config.yml', 'mode': '0644', 'isdir': False, 'ischr': False, 'isblk': False, 'isreg': True, 'isfifo': False, 'islnk': False, 'issock': False, 'uid': 0, 'gid': 0, 'size': 1237, 'inode': 67109034, 'dev': 64778, 'nlink': 1, 'atime': 1745549089.6848097, 'mtime': 1745429178.0, 'ctime': 1745549087.430804, 'wusr': True, 'rusr': True, 'xusr': False, 'wgrp': False, 'rgrp': True, 'xgrp': False, 'woth': False, 'roth': True, 'xoth': False, 'isuid': False, 'isgid': False, 'blocks': 8, 'block_size': 4096, 'device_type': 0, 'readable': True, 'writeable': True, 'executable': False, 'pw_name': 'root', 'gr_name': 'root', 'checksum': '13602fdc714a05e83fb29569e42617c820125c0f', 'mimetype': 'unknown', 'charset': 'unknown', 'version': None, 'attributes': [], 'attr_flags': ''}, 'invocation': {'module_args': {'path': '/runner/project/vmlc-services-dns-v3/playbooks/../vars/powershell_config.yml', 'follow': False, 'get_md5': False, 'get_checksum': True, 'get_mime': True, 'get_attributes': True, 'checksum_algorithm': 'sha1'}}, 'failed': False, 'item': 'powershell_config.yml', 'ansible_loop_var': 'item'}) => {
    "ansible_loop_var": "item",
    "changed": false,
    "item": {
        "ansible_loop_var": "item",
        "changed": false,
        "failed": false,
        "invocation": {
            "module_args": {
                "checksum_algorithm": "sha1",
                "follow": false,
                "get_attributes": true,
                "get_checksum": true,
                "get_md5": false,
                "get_mime": true,
                "path": "/runner/project/vmlc-services-dns-v3/playbooks/../vars/powershell_config.yml"
            }
        },
        "item": "powershell_config.yml",
        "stat": {
            "atime": 1745549089.6848097,
            "attr_flags": "",
            "attributes": [],
            "block_size": 4096,
            "blocks": 8,
            "charset": "unknown",
            "checksum": "13602fdc714a05e83fb29569e42617c820125c0f",
            "ctime": 1745549087.430804,
            "dev": 64778,
            "device_type": 0,
            "executable": false,
            "exists": true,
            "gid": 0,
            "gr_name": "root",
            "inode": 67109034,
            "isblk": false,
            "ischr": false,
            "isdir": false,
            "isfifo": false,
            "isgid": false,
            "islnk": false,
            "isreg": true,
            "issock": false,
            "isuid": false,
            "mimetype": "unknown",
            "mode": "0644",
            "mtime": 1745429178.0,
            "nlink": 1,
            "path": "/runner/project/vmlc-services-dns-v3/playbooks/../vars/powershell_config.yml",
            "pw_name": "root",
            "readable": true,
            "rgrp": true,
            "roth": true,
            "rusr": true,
            "size": 1237,
            "uid": 0,
            "version": null,
            "wgrp": false,
            "woth": false,
            "writeable": true,
            "wusr": true,
            "xgrp": false,
            "xoth": false,
            "xusr": false
        }
    },
    "msg": "All assertions passed"
}
ok: [localhost] => (item={'changed': False, 'stat': {'exists': True, 'path': '/runner/project/vmlc-services-dns-v3/playbooks/../vars/report_config.yml', 'mode': '0644', 'isdir': False, 'ischr': False, 'isblk': False, 'isreg': True, 'isfifo': False, 'islnk': False, 'issock': False, 'uid': 0, 'gid': 0, 'size': 4035, 'inode': 67109040, 'dev': 64778, 'nlink': 1, 'atime': 1745549089.6858099, 'mtime': 1745515916.0, 'ctime': 1745549087.430804, 'wusr': True, 'rusr': True, 'xusr': False, 'wgrp': False, 'rgrp': True, 'xgrp': False, 'woth': False, 'roth': True, 'xoth': False, 'isuid': False, 'isgid': False, 'blocks': 8, 'block_size': 4096, 'device_type': 0, 'readable': True, 'writeable': True, 'executable': False, 'pw_name': 'root', 'gr_name': 'root', 'checksum': '277fde3f7cc2e9923c2c884557d583bed0568aa6', 'mimetype': 'unknown', 'charset': 'unknown', 'version': None, 'attributes': [], 'attr_flags': ''}, 'invocation': {'module_args': {'path': '/runner/project/vmlc-services-dns-v3/playbooks/../vars/report_config.yml', 'follow': False, 'get_md5': False, 'get_checksum': True, 'get_mime': True, 'get_attributes': True, 'checksum_algorithm': 'sha1'}}, 'failed': False, 'item': 'report_config.yml', 'ansible_loop_var': 'item'}) => {
    "ansible_loop_var": "item",
    "changed": false,
    "item": {
        "ansible_loop_var": "item",
        "changed": false,
        "failed": false,
        "invocation": {
            "module_args": {
                "checksum_algorithm": "sha1",
                "follow": false,
                "get_attributes": true,
                "get_checksum": true,
                "get_md5": false,
                "get_mime": true,
                "path": "/runner/project/vmlc-services-dns-v3/playbooks/../vars/report_config.yml"
            }
        },
        "item": "report_config.yml",
        "stat": {
            "atime": 1745549089.6858099,
            "attr_flags": "",
            "attributes": [],
            "block_size": 4096,
            "blocks": 8,
            "charset": "unknown",
            "checksum": "277fde3f7cc2e9923c2c884557d583bed0568aa6",
            "ctime": 1745549087.430804,
            "dev": 64778,
            "device_type": 0,
            "executable": false,
            "exists": true,
            "gid": 0,
            "gr_name": "root",
            "inode": 67109040,
            "isblk": false,
            "ischr": false,
            "isdir": false,
            "isfifo": false,
            "isgid": false,
            "islnk": false,
            "isreg": true,
            "issock": false,
            "isuid": false,
            "mimetype": "unknown",
            "mode": "0644",
            "mtime": 1745515916.0,
            "nlink": 1,
            "path": "/runner/project/vmlc-services-dns-v3/playbooks/../vars/report_config.yml",
            "pw_name": "root",
            "readable": true,
            "rgrp": true,
            "roth": true,
            "rusr": true,
            "size": 4035,
            "uid": 0,
            "version": null,
            "wgrp": false,
            "woth": false,
            "writeable": true,
            "wusr": true,
            "xgrp": false,
            "xoth": false,
            "xusr": false
        }
    },
    "msg": "All assertions passed"
}

TASK [common : Load unified domain configuration] ******************************
task path: /runner/project/vmlc-services-dns-v3/roles/common/tasks/load_configuration.yml:30
ok: [localhost] => {"ansible_facts": {"dns_defaults": {"description": "Managed by Ansible DNS Management", "ttl": 3600}, "dns_servers": {"aic.local": "aicaddcvpsys06.aic.local", "devhealthgrp.com.sg": "hisaddcvdutl01.devhealthgrp.com.sg", "exthealthgrp.com.sg": "dc01.exthealthgrp.com.sg", "hcloud.healthgrp.com.sg": "hisaddcvputl02.hcloud.healthgrp.com.sg", "healthgrp.com.sg": "hisaddcvputl07.healthgrp.com.sg", "healthgrpextp.com.sg": "hisaddcvputl13.healthgrpextp.com.sg", "healthgrpexts.com.sg": "hisaddcvsutl03.healthgrpexts.com.sg", "iltc.healthgrp.com.sg": "aicaddcvpsys02.iltc.healthgrp.com.sg", "nhg.local": "nhgaddcvpsys03.nhg.local", "nnstg.local": "nhgaddcvssys01.nnstg.local", "ses.shsu.com.sg": "sedcvssys22h1.ses.shsu.com.sg", "shses.shs.com.sg": "sesdcvpsys01.shses.shs.com.sg"}, "domain_groups": {"healthgrp": ["healthgrp.com.sg", "hcloud.healthgrp.com.sg", "iltc.healthgrp.com.sg", "healthgrpextp.com.sg", "exthealthgrp.com.sg", "devhealthgrp.com.sg", "healthgrpexts.com.sg"], "local": ["nhg.local", "aic.local", "nnstg.local"], "production": ["healthgrp.com.sg", "hcloud.healthgrp.com.sg", "iltc.healthgrp.com.sg", "healthgrpextp.com.sg", "exthealthgrp.com.sg", "nhg.local", "aic.local", "shses.shs.com.sg"], "shs": ["shses.shs.com.sg", "ses.shsu.com.sg"], "staging": ["devhealthgrp.com.sg", "healthgrpexts.com.sg", "nnstg.local", "ses.shsu.com.sg"]}, "domain_keys": ["healthgrp.com.sg", "hcloud.healthgrp.com.sg", "iltc.healthgrp.com.sg", "healthgrpextp.com.sg", "exthealthgrp.com.sg", "nhg.local", "aic.local", "shses.shs.com.sg", "devhealthgrp.com.sg", "healthgrpexts.com.sg", "nnstg.local", "ses.shsu.com.sg"], "domain_selection": {"default_admt_server": {"prd": "HISADMTVPSEC05.healthgrp.com.sg", "stg": "HISADMTVDSEC01.devhealthgrp.com.sg"}, "default_domain": {"prd": "healthgrp.com.sg", "stg": "devhealthgrp.com.sg"}}, "domains": {"aic.local": {"admt_server": "HISADMTVPSEC02.aic.local", "dc": "hdc1", "description": "AIC Local Domain", "environment": "prd", "network_zone": "mgt", "os": "win"}, "devhealthgrp.com.sg": {"admt_server": "HISADMTVDSEC01.devhealthgrp.com.sg", "dc": "hdc1", "description": "Development Health Group Domain", "environment": "stg", "network_zone": "mgt", "os": "win"}, "exthealthgrp.com.sg": {"admt_server": "HISADMTVPSEC07.exthealthgrp.com.sg", "dc": "hdc1", "description": "External Health Group Domain", "environment": "prd", "network_zone": "mgt", "os": "win"}, "hcloud.healthgrp.com.sg": {"admt_server": "HISADMTVPSEC06.hcloud.healthgrp.com.sg", "dc": "hdc1", "description": "Health Group Cloud Domain", "environment": "prd", "network_zone": "mgt", "os": "win", "related_domains": ["healthgrp.com.sg"]}, "healthgrp.com.sg": {"admt_server": "HISADMTVPSEC05.healthgrp.com.sg", "dc": "hdc2", "description": "Health Group Domain", "environment": "prd", "network_zone": "mgt", "os": "win", "related_domains": ["hcloud.healthgrp.com.sg", "iltc.healthgrp.com.sg"]}, "healthgrpextp.com.sg": {"admt_server": "HISADMTVPSEC08.healthgrpextp.com.sg", "dc": "hdc1", "description": "Health Group External Production Domain", "environment": "prd", "network_zone": "mgt", "os": "win", "related_domains": ["healthgrpexts.com.sg"]}, "healthgrpexts.com.sg": {"admt_server": "HISADMTVSSEC01.healthgrpexts.com.sg", "dc": "hdc2", "description": "Health Group External Staging Domain", "environment": "stg", "network_zone": "mgt", "os": "win", "related_domains": ["healthgrpextp.com.sg"]}, "iltc.healthgrp.com.sg": {"admt_server": "HISADMTVPSEC04.iltc.healthgrp.com.sg", "dc": "hdc1", "description": "ILTC Health Group Domain", "environment": "prd", "network_zone": "mgt", "os": "win", "related_domains": ["healthgrp.com.sg"]}, "nhg.local": {"admt_server": "HISADMTVPSEC11.nhg.local", "dc": "hdc2", "description": "NHG Local Domain", "environment": "prd", "network_zone": "mgt", "os": "win"}, "nnstg.local": {"admt_server": "HISADMTVSSEC02.nnstg.local", "dc": "hdc1", "description": "NNSTG Local Domain", "environment": "stg", "network_zone": "mgt", "os": "win"}, "ses.shsu.com.sg": {"admt_server": "SHSADMTVDSEC02.ses.shsu.com.sg", "dc": "hdc2", "description": "SES SHSU Domain", "environment": "stg", "network_zone": "mgt", "os": "win", "related_domains": ["shses.shs.com.sg"]}, "shses.shs.com.sg": {"admt_server": "SHSADMTVPSEC12.shses.shs.com.sg", "dc": "hdc1", "description": "SHSES Domain", "environment": "prd", "network_zone": "tsz", "os": "win", "related_domains": ["ses.shsu.com.sg"]}}, "ptr_dns_servers": {"aic.local": "aicaddcvpsys06.aic.local", "devhealthgrp.com.sg": "hisaddcvdutl01.devhealthgrp.com.sg", "exthealthgrp.com.sg": "dc01.exthealthgrp.com.sg", "hcloud.healthgrp.com.sg": "hisaddcvputl02.hcloud.healthgrp.com.sg", "healthgrp.com.sg": "hisaddcvputl07.healthgrp.com.sg", "healthgrpextp.com.sg": "hisaddcvputl13.healthgrpextp.com.sg", "healthgrpexts.com.sg": "hisaddcvsutl03.healthgrpexts.com.sg", "iltc.healthgrp.com.sg": "aicaddcvpsys02.iltc.healthgrp.com.sg", "nhg.local": "nhgaddcvpsys03.nhg.local", "nnstg.local": "nhgaddcvssys01.nnstg.local", "ses.shsu.com.sg": "shdcvsys22h1.shsu.com.sg", "shses.shs.com.sg": "sesdcvpsys11.shs.com.sg"}}, "ansible_included_var_files": ["/runner/project/vmlc-services-dns-v3/vars/domains.yml"], "changed": false}

TASK [common : Load domain credentials] ****************************************
task path: /runner/project/vmlc-services-dns-v3/roles/common/tasks/load_configuration.yml:35
ok: [localhost] => {"ansible_facts": {"domain_credentials": {"aic.local": {"dns_password": "{{ var_dns_aic_password }}", "dns_username": "{{ var_dns_aic_username }}"}, "devhealthgrp.com.sg": {"dns_password": "{{ var_dns_devhealthgrp_password }}", "dns_username": "{{ var_dns_devhealthgrp_username }}"}, "exthealthgrp.com.sg": {"dns_password": "{{ var_dns_exthealthgrp_password }}", "dns_username": "{{ var_dns_exthealthgrp_username }}"}, "hcloud.healthgrp.com.sg": {"dns_password": "{{ var_dns_hcloud_password }}", "dns_username": "{{ var_dns_hcloud_username }}"}, "healthgrp.com.sg": {"dns_password": "{{ var_dns_healthgrp_password }}", "dns_username": "{{ var_dns_healthgrp_username }}"}, "healthgrpextp.com.sg": {"dns_password": "{{ var_dns_healthgrpextp_password }}", "dns_username": "{{ var_dns_healthgrpextp_username }}"}, "healthgrpexts.com.sg": {"dns_password": "{{ var_dns_healthgrpexts_password }}", "dns_username": "{{ var_dns_healthgrpexts_username }}"}, "iltc.healthgrp.com.sg": {"dns_password": "{{ var_dns_iltc_password }}", "dns_username": "{{ var_dns_iltc_username }}"}, "nhg.local": {"dns_password": "{{ var_dns_nhg_password }}", "dns_username": "{{ var_dns_nhg_username }}"}, "nnstg.local": {"dns_password": "{{ var_dns_nnstg_password }}", "dns_username": "{{ var_dns_nnstg_username }}"}, "ses.shsu.com.sg": {"dns_password": "{{ var_dns_ses_password }}", "dns_username": "{{ var_dns_ses_username }}"}, "shses.shs.com.sg": {"dns_password": "{{ var_dns_shses_password }}", "dns_username": "{{ var_dns_shses_username }}"}}}, "ansible_included_var_files": ["/runner/project/vmlc-services-dns-v3/vars/domain_credentials.yml"], "changed": false}

TASK [common : Set multi-domain variables] *************************************
task path: /runner/project/vmlc-services-dns-v3/roles/common/tasks/load_configuration.yml:39
ok: [localhost] => {"ansible_facts": {"cname_targets": "", "domains": {"aic.local": {"admt_server": "HISADMTVPSEC02.aic.local", "dc": "hdc1", "description": "AIC Local Domain", "environment": "prd", "network_zone": "mgt", "os": "win"}, "devhealthgrp.com.sg": {"admt_server": "HISADMTVDSEC01.devhealthgrp.com.sg", "dc": "hdc1", "description": "Development Health Group Domain", "environment": "stg", "network_zone": "mgt", "os": "win"}, "exthealthgrp.com.sg": {"admt_server": "HISADMTVPSEC07.exthealthgrp.com.sg", "dc": "hdc1", "description": "External Health Group Domain", "environment": "prd", "network_zone": "mgt", "os": "win"}, "hcloud.healthgrp.com.sg": {"admt_server": "HISADMTVPSEC06.hcloud.healthgrp.com.sg", "dc": "hdc1", "description": "Health Group Cloud Domain", "environment": "prd", "network_zone": "mgt", "os": "win", "related_domains": ["healthgrp.com.sg"]}, "healthgrp.com.sg": {"admt_server": "HISADMTVPSEC05.healthgrp.com.sg", "dc": "hdc2", "description": "Health Group Domain", "environment": "prd", "network_zone": "mgt", "os": "win", "related_domains": ["hcloud.healthgrp.com.sg", "iltc.healthgrp.com.sg"]}, "healthgrpextp.com.sg": {"admt_server": "HISADMTVPSEC08.healthgrpextp.com.sg", "dc": "hdc1", "description": "Health Group External Production Domain", "environment": "prd", "network_zone": "mgt", "os": "win", "related_domains": ["healthgrpexts.com.sg"]}, "healthgrpexts.com.sg": {"admt_server": "HISADMTVSSEC01.healthgrpexts.com.sg", "dc": "hdc2", "description": "Health Group External Staging Domain", "environment": "stg", "network_zone": "mgt", "os": "win", "related_domains": ["healthgrpextp.com.sg"]}, "iltc.healthgrp.com.sg": {"admt_server": "HISADMTVPSEC04.iltc.healthgrp.com.sg", "dc": "hdc1", "description": "ILTC Health Group Domain", "environment": "prd", "network_zone": "mgt", "os": "win", "related_domains": ["healthgrp.com.sg"]}, "nhg.local": {"admt_server": "HISADMTVPSEC11.nhg.local", "dc": "hdc2", "description": "NHG Local Domain", "environment": "prd", "network_zone": "mgt", "os": "win"}, "nnstg.local": {"admt_server": "HISADMTVSSEC02.nnstg.local", "dc": "hdc1", "description": "NNSTG Local Domain", "environment": "stg", "network_zone": "mgt", "os": "win"}, "ses.shsu.com.sg": {"admt_server": "SHSADMTVDSEC02.ses.shsu.com.sg", "dc": "hdc2", "description": "SES SHSU Domain", "environment": "stg", "network_zone": "mgt", "os": "win", "related_domains": ["shses.shs.com.sg"]}, "shses.shs.com.sg": {"admt_server": "SHSADMTVPSEC12.shses.shs.com.sg", "dc": "hdc1", "description": "SHSES Domain", "environment": "prd", "network_zone": "tsz", "os": "win", "related_domains": ["ses.shsu.com.sg"]}}, "hostnames": "", "ip_addresses": ""}, "changed": false}

TASK [common : Set operation type] *********************************************
task path: /runner/project/vmlc-services-dns-v3/roles/common/tasks/load_configuration.yml:46
ok: [localhost] => {"ansible_facts": {"is_multi_domain": false, "operation_type": "single-domain"}, "changed": false}

TASK [common : Display operation information] **********************************
task path: /runner/project/vmlc-services-dns-v3/roles/common/tasks/load_configuration.yml:51
ok: [localhost] => {
    "msg": [
        "Operation: Verify",
        "Record Type: A",
        "Operation Type: single-domain",
        "Domain: devhealthgrp.com.sg",
        "Hostname: ANSORTEST"
    ]
}

TASK [common : Validate domain configuration] **********************************
task path: /runner/project/vmlc-services-dns-v3/roles/common/tasks/main.yml:11
included: /runner/project/vmlc-services-dns-v3/roles/common/tasks/validate_domain.yml for localhost

TASK [common : Check if domain exists in domain configuration] *****************
task path: /runner/project/vmlc-services-dns-v3/roles/common/tasks/validate_domain.yml:7
ok: [localhost] => {"ansible_facts": {"domain_exists": true}, "changed": false}

TASK [common : Validate domain exists in domain configuration] *****************
task path: /runner/project/vmlc-services-dns-v3/roles/common/tasks/validate_domain.yml:13
ok: [localhost] => {
    "changed": false,
    "msg": "All assertions passed"
}

TASK [common : Parse domains for multi-domain operations] **********************
task path: /runner/project/vmlc-services-dns-v3/roles/common/tasks/validate_domain.yml:23
skipping: [localhost] => {"changed": false, "false_condition": "is_multi_domain", "skip_reason": "Conditional result was False"}

TASK [common : Check if each domain exists in domain configuration for multi-domain operations] ***
task path: /runner/project/vmlc-services-dns-v3/roles/common/tasks/validate_domain.yml:30
skipping: [localhost] => {"changed": false, "false_condition": "is_multi_domain", "skip_reason": "Conditional result was False"}

TASK [common : Validate all domains exist in domain configuration for multi-domain operations] ***
task path: /runner/project/vmlc-services-dns-v3/roles/common/tasks/validate_domain.yml:39
skipping: [localhost] => {"changed": false, "false_condition": "is_multi_domain and domain_existence_checks is defined", "skip_reason": "Conditional result was False"}

TASK [common : Extract domain information for single domain] *******************
task path: /runner/project/vmlc-services-dns-v3/roles/common/tasks/validate_domain.yml:51
ok: [localhost] => {"ansible_facts": {"dns_server": "hisaddcvdutl01.devhealthgrp.com.sg", "domain_creds": {"dns_password": "sDKncjgDFk4$52#ds", "dns_username": "<EMAIL>"}, "domain_info": {"admt_server": "HISADMTVDSEC01.devhealthgrp.com.sg", "dc": "hdc1", "description": "Development Health Group Domain", "environment": "stg", "network_zone": "mgt", "os": "win"}, "ptr_dns_server": "hisaddcvdutl01.devhealthgrp.com.sg"}, "changed": false}

TASK [common : Get domain environments safely] *********************************
task path: /runner/project/vmlc-services-dns-v3/roles/common/tasks/validate_domain.yml:60
skipping: [localhost] => {"changed": false, "false_condition": "is_multi_domain", "skip_reason": "Conditional result was False"}

TASK [common : Collect environment for each domain] ****************************
task path: /runner/project/vmlc-services-dns-v3/roles/common/tasks/validate_domain.yml:66
skipping: [localhost] => {"changed": false, "false_condition": "is_multi_domain and item in domain_keys", "skip_reason": "Conditional result was False"}

TASK [common : Ensure all domains are in the same environment] *****************
task path: /runner/project/vmlc-services-dns-v3/roles/common/tasks/validate_domain.yml:73
skipping: [localhost] => {"changed": false, "false_condition": "is_multi_domain and domain_environments_list is defined and domain_environments_list | length > 0", "skip_reason": "Conditional result was False"}

TASK [common : Set environment based on domain] ********************************
task path: /runner/project/vmlc-services-dns-v3/roles/common/tasks/validate_domain.yml:81
ok: [localhost] => {"ansible_facts": {"environment": "stg"}, "changed": false}

TASK [common : Display domain information] *************************************
task path: /runner/project/vmlc-services-dns-v3/roles/common/tasks/validate_domain.yml:85
ok: [localhost] => {
    "msg": [
        "Environment: []",
        "Domain Information: {'description': 'Development Health Group Domain', 'environment': 'stg', 'network_zone': 'mgt', 'dc': 'hdc1', 'admt_server': 'HISADMTVDSEC01.devhealthgrp.com.sg', 'os': 'win'}"
    ]
}

TASK [common : Set up logging] *************************************************
task path: /runner/project/vmlc-services-dns-v3/roles/common/tasks/main.yml:15
included: /runner/project/vmlc-services-dns-v3/roles/common/tasks/setup_logging.yml for localhost

TASK [common : Ensure log directories exist] ***********************************
task path: /runner/project/vmlc-services-dns-v3/roles/common/tasks/setup_logging.yml:7
ok: [localhost] => (item=/runner/project/vmlc-services-dns-v3/playbooks/../logs/ansible) => {"ansible_loop_var": "item", "changed": false, "gid": 0, "group": "root", "item": "/runner/project/vmlc-services-dns-v3/playbooks/../logs/ansible", "mode": "0755", "owner": "root", "path": "/runner/project/vmlc-services-dns-v3/playbooks/../logs/ansible", "size": 22, "state": "directory", "uid": 0}
ok: [localhost] => (item=/runner/project/vmlc-services-dns-v3/playbooks/../logs/powershell) => {"ansible_loop_var": "item", "changed": false, "gid": 0, "group": "root", "item": "/runner/project/vmlc-services-dns-v3/playbooks/../logs/powershell", "mode": "0755", "owner": "root", "path": "/runner/project/vmlc-services-dns-v3/playbooks/../logs/powershell", "size": 22, "state": "directory", "uid": 0}
ok: [localhost] => (item=/runner/project/vmlc-services-dns-v3/playbooks/../logs/progress) => {"ansible_loop_var": "item", "changed": false, "gid": 0, "group": "root", "item": "/runner/project/vmlc-services-dns-v3/playbooks/../logs/progress", "mode": "0755", "owner": "root", "path": "/runner/project/vmlc-services-dns-v3/playbooks/../logs/progress", "size": 22, "state": "directory", "uid": 0}
ok: [localhost] => (item=/runner/project/vmlc-services-dns-v3/playbooks/../logs/archive) => {"ansible_loop_var": "item", "changed": false, "gid": 0, "group": "root", "item": "/runner/project/vmlc-services-dns-v3/playbooks/../logs/archive", "mode": "0755", "owner": "root", "path": "/runner/project/vmlc-services-dns-v3/playbooks/../logs/archive", "size": 103, "state": "directory", "uid": 0}

TASK [common : Include standardized log file paths] ****************************
task path: /runner/project/vmlc-services-dns-v3/roles/common/tasks/setup_logging.yml:23
included: /runner/project/vmlc-services-dns-v3/roles/common/tasks/set_log_paths.yml for localhost

TASK [common : Format date for log filenames] **********************************
task path: /runner/project/vmlc-services-dns-v3/roles/common/tasks/set_log_paths.yml:6
ok: [localhost] => {"ansible_facts": {"log_date": "20250425"}, "changed": false}

TASK [common : Set ticket number with default] *********************************
task path: /runner/project/vmlc-services-dns-v3/roles/common/tasks/set_log_paths.yml:10
ok: [localhost] => {"ansible_facts": {"log_ticket": "INC-123456"}, "changed": false}

TASK [common : Set hostname and domain for log filenames] **********************
task path: /runner/project/vmlc-services-dns-v3/roles/common/tasks/set_log_paths.yml:14
ok: [localhost] => {"ansible_facts": {"log_domain": "devhealthgrp.com.sg", "log_hostname": "ANSORTEST", "log_operation": "VERIFY", "log_record_type": "A"}, "changed": false}

TASK [common : Construct base log filename] ************************************
task path: /runner/project/vmlc-services-dns-v3/roles/common/tasks/set_log_paths.yml:21
ok: [localhost] => {"ansible_facts": {"log_base_name": "20250425_INC-123456_ANSORTEST_devhealthgrp.com.sg_A_VERIFY"}, "changed": false}

TASK [common : Set specific log file paths] ************************************
task path: /runner/project/vmlc-services-dns-v3/roles/common/tasks/set_log_paths.yml:25
ok: [localhost] => {"ansible_facts": {"ansible_log_path": "/runner/project/vmlc-services-dns-v3/playbooks/../logs/ansible/20250425_INC-123456_ANSORTEST_devhealthgrp.com.sg_A_VERIFY_ANSIBLE.log", "powershell_log_path": "/runner/project/vmlc-services-dns-v3/playbooks/../logs/powershell/20250425_INC-123456_ANSORTEST_devhealthgrp.com.sg_A_VERIFY_POWERSHELL.log", "progress_log_path": "/runner/project/vmlc-services-dns-v3/playbooks/../logs/progress/20250425_INC-123456_ANSORTEST_devhealthgrp.com.sg_A_VERIFY_PROGRESS.log", "report_log_path": "/runner/project/vmlc-services-dns-v3/playbooks/../logs/ansible/20250425_INC-123456_ANSORTEST_devhealthgrp.com.sg_A_VERIFY_REPORT.log"}, "changed": false}

TASK [common : Set target server log paths] ************************************
task path: /runner/project/vmlc-services-dns-v3/roles/common/tasks/set_log_paths.yml:32
ok: [localhost] => {"ansible_facts": {"target_ansible_log_path": "C:\\\\OE_AAP_LOGS\\\\20250425_INC-123456_ANSORTEST_devhealthgrp.com.sg_A_VERIFY_ANSIBLE.log", "target_powershell_log_path": "C:\\\\OE_AAP_LOGS\\\\20250425_INC-123456_ANSORTEST_devhealthgrp.com.sg_A_VERIFY_POWERSHELL.log"}, "changed": false}

TASK [common : Display log paths] **********************************************
task path: /runner/project/vmlc-services-dns-v3/roles/common/tasks/set_log_paths.yml:37
skipping: [localhost] => {"false_condition": "current_log_level | default('INFO') | upper == 'DEBUG'"}

TASK [common : Rotate logs if enabled] *****************************************
task path: /runner/project/vmlc-services-dns-v3/roles/common/tasks/setup_logging.yml:27
redirecting (type: modules) ansible.builtin.archive to community.general.archive
included: /runner/project/vmlc-services-dns-v3/roles/common/tasks/rotate_logs.yml for localhost

TASK [common : Find old log files] *********************************************
task path: /runner/project/vmlc-services-dns-v3/roles/common/tasks/rotate_logs.yml:6
ok: [localhost] => {"changed": false, "examined": 3, "files": [], "matched": 0, "msg": "All paths examined", "skipped_paths": {}}

TASK [common : Archive old log files] ******************************************
task path: /runner/project/vmlc-services-dns-v3/roles/common/tasks/rotate_logs.yml:19
skipping: [localhost] => {"changed": false, "skipped_reason": "No items in the list"}

TASK [common : Display log rotation information] *******************************
task path: /runner/project/vmlc-services-dns-v3/roles/common/tasks/rotate_logs.yml:30
skipping: [localhost] => {"false_condition": "old_logs.matched > 0 and log_flags.show_progress | bool"}

TASK [common : Display logging information] ************************************
task path: /runner/project/vmlc-services-dns-v3/roles/common/tasks/setup_logging.yml:32
ok: [localhost] => {
    "msg": [
        "Log Level: INFO",
        "Ansible Log: /runner/project/vmlc-services-dns-v3/playbooks/../logs/ansible/20250425_INC-123456_ANSORTEST_devhealthgrp.com.sg_A_VERIFY_ANSIBLE.log",
        "PowerShell Log: /runner/project/vmlc-services-dns-v3/playbooks/../logs/powershell/20250425_INC-123456_ANSORTEST_devhealthgrp.com.sg_A_VERIFY_POWERSHELL.log",
        "Progress Log: /runner/project/vmlc-services-dns-v3/playbooks/../logs/progress/20250425_INC-123456_ANSORTEST_devhealthgrp.com.sg_A_VERIFY_PROGRESS.log"
    ]
}

TASK [common : Validate operation parameters] **********************************
task path: /runner/project/vmlc-services-dns-v3/roles/common/tasks/main.yml:19
included: /runner/project/vmlc-services-dns-v3/roles/common/tasks/validate_operation.yml for localhost

TASK [common : Validate operation] *********************************************
task path: /runner/project/vmlc-services-dns-v3/roles/common/tasks/validate_operation.yml:7
ok: [localhost] => {
    "changed": false,
    "msg": "All assertions passed"
}

TASK [common : Validate record type] *******************************************
task path: /runner/project/vmlc-services-dns-v3/roles/common/tasks/validate_operation.yml:14
ok: [localhost] => {
    "changed": false,
    "msg": "All assertions passed"
}

TASK [common : Validate required parameters for single domain operations] ******
task path: /runner/project/vmlc-services-dns-v3/roles/common/tasks/validate_operation.yml:21
ok: [localhost] => {
    "changed": false,
    "msg": "All assertions passed"
}

TASK [common : Validate operation-specific parameters for single domain] *******
task path: /runner/project/vmlc-services-dns-v3/roles/common/tasks/validate_operation.yml:31
skipping: [localhost] => {"changed": false, "skipped_reason": "No items in the list"}

TASK [common : Validate multi-domain parameters] *******************************
task path: /runner/project/vmlc-services-dns-v3/roles/common/tasks/validate_operation.yml:41
skipping: [localhost] => {"changed": false, "false_condition": "is_multi_domain", "skip_reason": "Conditional result was False"}

TASK [common : Validate operation-specific parameters for multi-domain (A/PTR records)] ***
task path: /runner/project/vmlc-services-dns-v3/roles/common/tasks/validate_operation.yml:51
skipping: [localhost] => {"changed": false, "false_condition": "is_multi_domain and record_type in ['a', 'ptr'] and operation in ['add', 'update']", "skip_reason": "Conditional result was False"}

TASK [common : Validate operation-specific parameters for multi-domain (CNAME records)] ***
task path: /runner/project/vmlc-services-dns-v3/roles/common/tasks/validate_operation.yml:61
skipping: [localhost] => {"changed": false, "false_condition": "is_multi_domain and record_type == 'cname' and operation == 'add'", "skip_reason": "Conditional result was False"}

TASK [common : Set up credentials] *********************************************
task path: /runner/project/vmlc-services-dns-v3/roles/common/tasks/main.yml:23
included: /runner/project/vmlc-services-dns-v3/roles/common/tasks/setup_credentials.yml for localhost => (item=(censored due to no_log))

TASK [common : Check if developer testing configuration is available] **********
task path: /runner/project/vmlc-services-dns-v3/roles/common/tasks/setup_credentials.yml:7
ok: [localhost] => {"changed": false, "stat": {"atime": 1745549099.7408361, "attr_flags": "", "attributes": [], "block_size": 4096, "blocks": 16, "charset": "unknown", "checksum": "d8ad98692547c5510f45652dc3f885cb8ab33c1a", "ctime": 1745549087.428804, "dev": 64778, "device_type": 0, "executable": false, "exists": true, "gid": 0, "gr_name": "root", "inode": 191, "isblk": false, "ischr": false, "isdir": false, "isfifo": false, "isgid": false, "islnk": false, "isreg": true, "issock": false, "isuid": false, "mimetype": "unknown", "mode": "0644", "mtime": 1745445360.0, "nlink": 1, "path": "/runner/project/vmlc-services-dns-v3/playbooks/../dev/vars/testing_config.yml", "pw_name": "root", "readable": true, "rgrp": true, "roth": true, "rusr": true, "size": 6070, "uid": 0, "version": null, "wgrp": false, "woth": false, "writeable": true, "wusr": true, "xgrp": false, "xoth": false, "xusr": false}}

TASK [common : Load testing configuration if available] ************************
task path: /runner/project/vmlc-services-dns-v3/roles/common/tasks/setup_credentials.yml:13
ok: [localhost] => {"ansible_facts": {"developer_testing": {"development": {"admt_server": "dev-admt.example.com", "credentials": {"password": "devpassword", "username": "devuser"}, "description": "Development testing environment", "dns_server": "dev-dns.example.com", "domains": ["dev.example.com", "test.dev.example.com"]}, "staging": {"admt_server": "stg-admt.example.com", "credentials": {"password": "stgpassword", "username": "stguser"}, "description": "Staging testing environment", "dns_server": "stg-dns.example.com", "domains": ["stg.example.com", "test.stg.example.com"]}, "use_mock_credentials": false, "vault_password_options": {"file_path": "~/.vault_pass.txt", "method": "prompt"}}, "molecule": {"driver": "docker", "platforms": [{"image": "geerlingguy/docker-ubuntu2004-ansible", "name": "ubuntu-20.04", "pre_build_image": true}, {"image": "geerlingguy/docker-centos8-ansible", "name": "centos-8", "pre_build_image": true}], "provisioner": {"log": true, "name": "ansible"}, "test_sequence": ["dependency", "lint", "cleanup", "destroy", "syntax", "create", "prepare", "converge", "idempotence", "verify", "cleanup", "destroy"], "verifier": {"name": "ansible"}}, "test_cases": {"add": [{"description": "Add an A record", "domain": "test.local", "expected_result": true, "hostname": "server02", "ip_address": "************", "name": "add_a_record", "operation": "add", "record_type": "a"}, {"description": "Add a duplicate A record", "domain": "test.local", "expected_result": false, "hostname": "server01", "ip_address": "************", "name": "add_duplicate_a_record", "operation": "add", "record_type": "a"}, {"cname_target": "server01.test.local", "description": "Add a CNAME record", "domain": "test.local", "expected_result": true, "hostname": "alias", "name": "add_cname_record", "operation": "add", "record_type": "cname"}, {"description": "Add a PTR record", "domain": "test.local", "expected_result": true, "hostname": "server03", "ip_address": "************", "name": "add_ptr_record", "operation": "add", "record_type": "ptr"}], "remove": [{"description": "Remove an A record", "domain": "test.local", "expected_result": true, "hostname": "server01", "name": "remove_a_record", "operation": "remove", "record_type": "a"}, {"description": "Remove a non-existent A record", "domain": "test.local", "expected_result": true, "hostname": "nonexistent", "name": "remove_nonexistent_a_record", "operation": "remove", "record_type": "a"}, {"description": "Remove a CNAME record", "domain": "test.local", "expected_result": true, "hostname": "www", "name": "remove_cname_record", "operation": "remove", "record_type": "cname"}, {"description": "Remove a PTR record", "domain": "test.local", "expected_result": true, "hostname": "server01", "ip_address": "************", "name": "remove_ptr_record", "operation": "remove", "record_type": "ptr"}], "update": [{"description": "Update an A record", "domain": "test.local", "expected_result": true, "hostname": "server01", "ip_address": "************", "name": "update_a_record", "operation": "update", "record_type": "a"}, {"description": "Update a non-existent A record", "domain": "test.local", "expected_result": false, "hostname": "nonexistent", "ip_address": "************", "name": "update_nonexistent_a_record", "operation": "update", "record_type": "a"}, {"cname_target": "server02.test.local", "description": "Update a CNAME record", "domain": "test.local", "expected_result": true, "hostname": "www", "name": "update_cname_record", "operation": "update", "record_type": "cname"}, {"description": "Update a PTR record", "domain": "test.local", "expected_result": true, "hostname": "server01", "ip_address": "************", "name": "update_ptr_record", "operation": "update", "record_type": "ptr"}], "verify": [{"description": "Verify an existing A record", "domain": "test.local", "expected_result": true, "hostname": "server01", "name": "verify_existing_a_record", "operation": "verify", "record_type": "a"}, {"description": "Verify a non-existent A record", "domain": "test.local", "expected_result": false, "hostname": "nonexistent", "name": "verify_nonexistent_a_record", "operation": "verify", "record_type": "a"}, {"description": "Verify an existing CNAME record", "domain": "test.local", "expected_result": true, "hostname": "www", "name": "verify_existing_cname_record", "operation": "verify", "record_type": "cname"}, {"description": "Verify an existing PTR record", "domain": "test.local", "expected_result": true, "hostname": "server01", "ip_address": "************", "name": "verify_existing_ptr_record", "operation": "verify", "record_type": "ptr"}]}, "test_environments": {"local": {"admt_server": "localhost", "credentials": {"password": "testpassword", "username": "testuser"}, "description": "Local testing environment", "dns_server": "localhost", "domains": ["test.local", "example.test"]}}}, "ansible_included_var_files": ["/runner/project/vmlc-services-dns-v3/dev/vars/testing_config.yml"], "changed": false}

TASK [common : Set developer mode flag] ****************************************
task path: /runner/project/vmlc-services-dns-v3/roles/common/tasks/setup_credentials.yml:19
ok: [localhost] => {"ansible_facts": {"developer_mode": false}, "changed": false}

TASK [common : Load vault file with credentials] *******************************
task path: /runner/project/vmlc-services-dns-v3/roles/common/tasks/setup_credentials.yml:24
ok: [localhost] => {"ansible_facts": {"var_dns_aic_password": {"__ansible_vault": "$ANSIBLE_VAULT;1.1;AES256\\n35383266326533646664303235626139636365313063623534343361356538386663353165386161\\n3331373439656565303663326236616163333732346634610a343932353532343364663730303030\\n31613436393036363231643933306237616137346465316261636565373166356661373134303534\\n3061616637373633610a323733636661396337633162336365373335313332393334303736316237\\n64303136646531653634663339373436653732653534366534346137326461663339\\n"}, "var_dns_aic_username": "<EMAIL>", "var_dns_devhealthgrp_password": {"__ansible_vault": "$ANSIBLE_VAULT;1.1;AES256\\n39333835316566303565626562383565336461636565393039303937653133333062383765623036\\n3962333337666331633739336632623035373838663035390a306465646333613837393431643134\\n35306239656637363239356562306366653765323937373237366363613466366161653732653961\\n6636323962303261650a376165623965343132386561643464303638643965323935323163373161\\n64343363323061383838623566626138316562306638316464333333316231336438\\n"}, "var_dns_devhealthgrp_username": "<EMAIL>", "var_dns_exthealthgrp_password": {"__ansible_vault": "$ANSIBLE_VAULT;1.1;AES256\\n30666566383632323639633537363238616531626263323536386631333664353934396530636561\\n6463666332353136376264643338636336656433623064660a643536353033343134336164613632\\n31326362623336663535356636343165383637316237383665393231306532613433373733626263\\n6633343064313734390a386331386663393661353063303639643064636630616638633832303634\\n37393462333761393631326461626336326233343764633061643962356363633761\\n"}, "var_dns_exthealthgrp_username": "<EMAIL>", "var_dns_hcloud_password": {"__ansible_vault": "$ANSIBLE_VAULT;1.1;AES256\\n30616130343838333964663939363035633635393034346332343431653833653237633338623665\\n3537656535373134666339396365386237353039636338360a386362326334656131663564646637\\n62653036366164363534363935336663653136626436393566363734636134393131303465313831\\n6132663662653032330a313864333136643630366238383236396166386234333231366130623461\\n63313464613033303464353564386633623238306538303362363637666533363835\\n"}, "var_dns_hcloud_username": "<EMAIL>", "var_dns_healthgrp_password": {"__ansible_vault": "$ANSIBLE_VAULT;1.1;AES256\\n35626566653732666430353135643763373538663437623231656565313631643835346430343833\\n3137323834313431366635333163326536313232616162390a616339313132343063323031313564\\n30323161346233313236386434303737656532643636306435623935663033656535353161376538\\n3366646631313261390a636433616563616638313637333635353963356431396539373963343633\\n34623534346537653439366637653662336538653231333562303430623939326562\\n"}, "var_dns_healthgrp_username": "<EMAIL>", "var_dns_healthgrpextp_password": {"__ansible_vault": "$ANSIBLE_VAULT;1.1;AES256\\n64663330343238363463303232393133313834613130323237383530383038396633626362336631\\n3961396430333632376138666134363438393439346461300a663431336234336330373931393937\\n66373230636230343137346232343838343237313730393564613162663934616566663165393838\\n6231623533323161610a323137326430633133353635393434316233376134633564353430323061\\n37663365623762626235316533656364373763326130636434393032393936316433\\n"}, "var_dns_healthgrpextp_username": "<EMAIL>", "var_dns_healthgrpexts_password": {"__ansible_vault": "$ANSIBLE_VAULT;1.1;AES256\\n62623037653335363761396362393066626238613438653661323733366536386164336234366164\\n6165363237366337363231633964626365633131623236390a613561626239336463653934343964\\n66396364313762656364346530646532323333386637373238393064663364353839346433303738\\n3830366463663461620a306138356466616130663639363438356237326362383162653566343431\\n39356134326565613238366136306430386435666263646138306264653861623137\\n"}, "var_dns_healthgrpexts_username": "<EMAIL>", "var_dns_iltc_password": {"__ansible_vault": "$ANSIBLE_VAULT;1.1;AES256\\n30616238353261333834386263366537653037633831393332346261646439633331643138373164\\n3434333461303564623261313965376566346338376234370a636561396233313334653864663233\\n62656438376565396639336639373032326531383933353930303131636332343737633134373932\\n3338353237646636360a336133366238373438383530313832653762643432663333663630663132\\n35326530633337653565343039383165653964616263653434333164383732663863\\n"}, "var_dns_iltc_username": "<EMAIL>", "var_dns_nhg_password": {"__ansible_vault": "$ANSIBLE_VAULT;1.1;AES256\\n30353361663035343038316638373663343632346530376464633465623762633237613037373935\\n3033373434373438306539646139623031663364393135350a323931643862383836303161646263\\n36333536316436653762326632663638613037393136613437633261653064303039653864353335\\n3337386430663566320a663365333634383262346563663362643437396237333466626163663061\\n61363532616666643532623438393965626633633762383866623837393031396265\\n"}, "var_dns_nhg_username": "<EMAIL>", "var_dns_nnstg_password": {"__ansible_vault": "$ANSIBLE_VAULT;1.1;AES256\\n33323431636430366639666531663130313838316536623664646634356633636364653536656332\\n3261636435613436613130323761626264376530313234370a326330383630376430356638386536\\n34613832626536663837363131313133646165346436313562303162653935343863323735303261\\n3631383931303064650a383462353536626432643763646539623464643932323837376535656462\\n37653564663332383166306233363332343133666532613234323165356337323564\\n"}, "var_dns_nnstg_username": "<EMAIL>", "var_dns_ses_password": {"__ansible_vault": "$ANSIBLE_VAULT;1.1;AES256\\n32306632363763313964626433633930323038303462626631626364633938623336633334373032\\n3638336334306561333239383464666465333336373963620a346138633463346266646132663064\\n64316631373063316339353566653766366465323862663064326433373361616463323235356461\\n6662346634356231320a353639613563613434613561313133326662386238343661356536623338\\n33313465323231353531623937376234353166653663343264383033353365363532\\n"}, "var_dns_ses_username": "<EMAIL>", "var_dns_shses_password": {"__ansible_vault": "$ANSIBLE_VAULT;1.1;AES256\\n34323538316462613765396634333663323637303165376634303331303066383661306537346665\\n3031353866326233316264356138653335386135373764640a343766363530643132613532373761\\n34343731613132356634323839623733623630636135623034626539663566616661643335643032\\n3835623363363239340a383835636261343461616634383432306131623961363034666163343338\\n34653662616630386166666635633236643330383765343838613633343464323066\\n"}, "var_dns_shses_username": "<EMAIL>"}, "ansible_included_var_files": ["/runner/project/vmlc-services-dns-v3/vars/vault.yml"], "changed": false}

TASK [common : Display developer mode information] *****************************
task path: /runner/project/vmlc-services-dns-v3/roles/common/tasks/setup_credentials.yml:30
skipping: [localhost] => {"false_condition": "developer_mode | bool"}

TASK [common : Set connection parameters for ADMT server] **********************
task path: /runner/project/vmlc-services-dns-v3/roles/common/tasks/setup_credentials.yml:36
ok: [localhost] => {"censored": "the output has been hidden due to the fact that 'no_log: true' was specified for this result", "changed": false}

TASK [common : Add ADMT server to inventory with connection parameters] ********
task path: /runner/project/vmlc-services-dns-v3/roles/common/tasks/setup_credentials.yml:46
creating host via 'add_host': hostname=HISADMTVDSEC01.devhealthgrp.com.sg
ok: [localhost] => {"censored": "the output has been hidden due to the fact that 'no_log: true' was specified for this result", "changed": false}

TASK [common : Reset connection parameters for localhost operations] ***********
task path: /runner/project/vmlc-services-dns-v3/roles/common/tasks/setup_credentials.yml:62
ok: [localhost] => {"ansible_facts": {"ansible_connection": "local", "ansible_password": null, "ansible_user": null, "ansible_winrm_server_cert_validation": null}, "changed": false}

TASK [common : Set ADMT server, DNS server, and credentials] *******************
task path: /runner/project/vmlc-services-dns-v3/roles/common/tasks/setup_credentials.yml:71
ok: [localhost] => {"censored": "the output has been hidden due to the fact that 'no_log: true' was specified for this result", "changed": false}

TASK [common : Set PTR DNS server based on domain] *****************************
task path: /runner/project/vmlc-services-dns-v3/roles/common/tasks/setup_credentials.yml:82
ok: [localhost] => {"ansible_facts": {"ptr_dns_server": "hisaddcvdutl01.devhealthgrp.com.sg"}, "changed": false}

TASK [common : Create domain to credential mapping for multi-domain operations] ***
task path: /runner/project/vmlc-services-dns-v3/roles/common/tasks/setup_credentials.yml:88
skipping: [localhost] => {"censored": "the output has been hidden due to the fact that 'no_log: true' was specified for this result", "changed": false}

TASK [common : Display credential information] *********************************
task path: /runner/project/vmlc-services-dns-v3/roles/common/tasks/setup_credentials.yml:96
skipping: [localhost] => {"false_condition": "current_log_level | upper == log_settings.levels.debug"}

TASK [/runner/project/vmlc-services-dns-v3/playbooks/../roles/dns_operations : Log verify operation start] ***
task path: /runner/project/vmlc-services-dns-v3/roles/dns_operations/tasks/verify.yml:7
skipping: [localhost] => {"false_condition": "current_log_level | upper == log_settings.levels.debug and verify_only is not defined"}

TASK [/runner/project/vmlc-services-dns-v3/playbooks/../roles/dns_operations : Ensure Temp directory exists on Windows server] ***
task path: /runner/project/vmlc-services-dns-v3/roles/dns_operations/tasks/verify.yml:13
ok: [localhost -> HISADMTVDSEC01.devhealthgrp.com.sg] => {"changed": false}

TASK [/runner/project/vmlc-services-dns-v3/playbooks/../roles/dns_operations : Copy PowerShell script to Windows server] ***
task path: /runner/project/vmlc-services-dns-v3/roles/dns_operations/tasks/verify.yml:25
ok: [localhost -> HISADMTVDSEC01.devhealthgrp.com.sg] => {"changed": false, "checksum": "30862ceb278d662185a014d959407c04dc0135f6", "dest": "C:\\\\Temp\\\\set-dns.ps1", "operation": "file_copy", "original_basename": "set-dns.ps1", "size": 22703, "src": "/runner/project/vmlc-services-dns-v3/playbooks/../scripts/set-dns.ps1"}

TASK [/runner/project/vmlc-services-dns-v3/playbooks/../roles/dns_operations : Ensure log directory exists on Windows server] ***
task path: /runner/project/vmlc-services-dns-v3/roles/dns_operations/tasks/verify.yml:38
changed: [localhost -> HISADMTVDSEC01.devhealthgrp.com.sg] => {"changed": true}

TASK [/runner/project/vmlc-services-dns-v3/playbooks/../roles/dns_operations : Execute PowerShell script to verify DNS record] ***
task path: /runner/project/vmlc-services-dns-v3/roles/dns_operations/tasks/verify.yml:50
changed: [localhost -> HISADMTVDSEC01.devhealthgrp.com.sg] => {"changed": true, "cmd": "C:\\\\\\\\Temp\\\\\\\\set-dns.ps1 `\\n  -Operation Verify `\\n  -RecordType A `\\n  -Hostname ANSORTEST `\\n  -Domain devhealthgrp.com.sg `\\n    -IPAddress \\"none\\" `\\n      -DNSServer hisaddcvdutl01.devhealthgrp.com.sg `\\n  -PTRDNSServer hisaddcvdutl01.devhealthgrp.com.sg `\\n  -AsJson $true `\\n  -Force $false `\\n  -ManagePTR $true `\\n  -LogPath \\"C:\\\\OE_AAP_LOGS\\\\20250425_INC-123456_ANSORTEST_devhealthgrp.com.sg_A_VERIFY_POWERSHELL.log\\" `\\n  -LogLevel INFO", "delta": "0:00:08.012001", "end": "2025-04-25 02:46:13.353669", "failed_when_result": false, "rc": 0, "start": "2025-04-25 02:46:05.341668", "stderr": "", "stderr_lines": [], "stdout": "[2025-04-25 10:46:07] [Info] [Verify] [A] [ANSORTEST.devhealthgrp.com.sg] Starting Verify operation for A record ANSORTEST.devhealthgrp.com.sg\\n[2025-04-25 10:46:13] [Error] [Verify] [A] [ANSORTEST.devhealthgrp.com.sg] Error checking DNS record: Failed to get the zone information for devhealthgrp.com.sg on server hisaddcvdutl01.devhealthgrp.com.sg.\\n[2025-04-25 10:46:13] [Error] [Verify] [A] [ANSORTEST.devhealthgrp.com.sg] Error: Failed to get the zone information for devhealthgrp.com.sg on server hisaddcvdutl01.devhealthgrp.com.sg.\\n{\\r\\n    \\"message\\":  \\"Error: Failed to get the zone information for devhealthgrp.com.sg on server hisaddcvdutl01.devhealthgrp.com.sg.\\",\\r\\n    \\"changed\\":  false,\\r\\n    \\"timestamp\\":  \\"2025-04-25T10:46:07\\",\\r\\n    \\"record\\":  {\\r\\n                   \\"domain\\":  \\"devhealthgrp.com.sg\\",\\r\\n                   \\"hostname\\":  \\"ANSORTEST\\",\\r\\n                   \\"type\\":  \\"A\\",\\r\\n                   \\"fqdn\\":  \\"ANSORTEST.devhealthgrp.com.sg\\"\\r\\n               },\\r\\n    \\"success\\":  false,\\r\\n    \\"operation\\":  \\"Verify\\"\\r\\n}\\r\\n", "stdout_lines": ["[2025-04-25 10:46:07] [Info] [Verify] [A] [ANSORTEST.devhealthgrp.com.sg] Starting Verify operation for A record ANSORTEST.devhealthgrp.com.sg", "[2025-04-25 10:46:13] [Error] [Verify] [A] [ANSORTEST.devhealthgrp.com.sg] Error checking DNS record: Failed to get the zone information for devhealthgrp.com.sg on server hisaddcvdutl01.devhealthgrp.com.sg.", "[2025-04-25 10:46:13] [Error] [Verify] [A] [ANSORTEST.devhealthgrp.com.sg] Error: Failed to get the zone information for devhealthgrp.com.sg on server hisaddcvdutl01.devhealthgrp.com.sg.", "{", "    \\"message\\":  \\"Error: Failed to get the zone information for devhealthgrp.com.sg on server hisaddcvdutl01.devhealthgrp.com.sg.\\",", "    \\"changed\\":  false,", "    \\"timestamp\\":  \\"2025-04-25T10:46:07\\",", "    \\"record\\":  {", "                   \\"domain\\":  \\"devhealthgrp.com.sg\\",", "                   \\"hostname\\":  \\"ANSORTEST\\",", "                   \\"type\\":  \\"A\\",", "                   \\"fqdn\\":  \\"ANSORTEST.devhealthgrp.com.sg\\"", "               },", "    \\"success\\":  false,", "    \\"operation\\":  \\"Verify\\"", "}"]}

TASK [/runner/project/vmlc-services-dns-v3/playbooks/../roles/dns_operations : Log PowerShell output for debugging] ***
task path: /runner/project/vmlc-services-dns-v3/roles/dns_operations/tasks/verify.yml:82
ok: [localhost] => {
    "msg": [
        "PowerShell stdout: [2025-04-25 10:46:07] [Info] [Verify] [A] [ANSORTEST.devhealthgrp.com.sg] Starting Verify operation for A record ANSORTEST.devhealthgrp.com.sg\\n[2025-04-25 10:46:13] [Error] [Verify] [A] [ANSORTEST.devhealthgrp.com.sg] Error checking DNS record: Failed to get the zone information for devhealthgrp.com.sg on server hisaddcvdutl01.devhealthgrp.com.sg.\\n[2025-04-25 10:46:13] [Error] [Verify] [A] [ANSORTEST.devhealthgrp.com.sg] Error: Failed to get the zone information for devhealthgrp.com.sg on server hisaddcvdutl01.devhealthgrp.com.sg.\\n{\\r\\n    \\"message\\":  \\"Error: Failed to get the zone information for devhealthgrp.com.sg on server hisaddcvdutl01.devhealthgrp.com.sg.\\",\\r\\n    \\"changed\\":  false,\\r\\n    \\"timestamp\\":  \\"2025-04-25T10:46:07\\",\\r\\n    \\"record\\":  {\\r\\n                   \\"domain\\":  \\"devhealthgrp.com.sg\\",\\r\\n                   \\"hostname\\":  \\"ANSORTEST\\",\\r\\n                   \\"type\\":  \\"A\\",\\r\\n                   \\"fqdn\\":  \\"ANSORTEST.devhealthgrp.com.sg\\"\\r\\n               },\\r\\n    \\"success\\":  false,\\r\\n    \\"operation\\":  \\"Verify\\"\\r\\n}\\r\\n",
        "PowerShell stderr: "
    ]
}

TASK [/runner/project/vmlc-services-dns-v3/playbooks/../roles/dns_operations : Check for PowerShell execution errors] ***
task path: /runner/project/vmlc-services-dns-v3/roles/dns_operations/tasks/verify.yml:90
ok: [localhost] => {"ansible_facts": {"ps_execution_failed": false}, "changed": false}

TASK [/runner/project/vmlc-services-dns-v3/playbooks/../roles/dns_operations : Extract JSON from PowerShell output] ***
task path: /runner/project/vmlc-services-dns-v3/roles/dns_operations/tasks/verify.yml:95
ok: [localhost] => {"ansible_facts": {"ps_json_output": ""}, "changed": false}

TASK [/runner/project/vmlc-services-dns-v3/playbooks/../roles/dns_operations : Parse PowerShell result] ***
task path: /runner/project/vmlc-services-dns-v3/roles/dns_operations/tasks/verify.yml:101
fatal: [localhost]: FAILED! => {"msg": "the field 'args' has an invalid value ({'dns_operation_result': '{{ ps_json_output | from_json }}'}), and could not be converted to an dict.The error was: Expecting value: line 1 column 1 (char 0)\\n\\nThe error appears to be in '/runner/project/vmlc-services-dns-v3/roles/dns_operations/tasks/verify.yml': line 101, column 3, but may\\nbe elsewhere in the file depending on the exact syntax problem.\\n\\nThe offending line appears to be:\\n\\n# Parse PowerShell result\\n- name: Parse PowerShell result\\n  ^ here\\n"}
...ignoring

TASK [/runner/project/vmlc-services-dns-v3/playbooks/../roles/dns_operations : Set error result if PowerShell execution failed] ***
task path: /runner/project/vmlc-services-dns-v3/roles/dns_operations/tasks/verify.yml:108
skipping: [localhost] => {"changed": false, "false_condition": "ps_execution_failed | bool", "skip_reason": "Conditional result was False"}

TASK [/runner/project/vmlc-services-dns-v3/playbooks/../roles/dns_operations : Set default operation result if parsing fails] ***
task path: /runner/project/vmlc-services-dns-v3/roles/dns_operations/tasks/verify.yml:118
skipping: [localhost] => {"changed": false, "false_condition": "dns_operation_result is not defined or dns_operation_result == {}", "skip_reason": "Conditional result was False"}

TASK [/runner/project/vmlc-services-dns-v3/playbooks/../roles/dns_operations : Display operation result] ***
task path: /runner/project/vmlc-services-dns-v3/roles/dns_operations/tasks/verify.yml:127
ok: [localhost] => {
    "msg": "Operation not yet executed"
}

TASK [Execute multi-domain operation] ******************************************
task path: /runner/project/vmlc-services-dns-v3/playbooks/manage_dns.yml:97
skipping: [localhost] => {"changed": false, "false_condition": "is_multi_domain", "skip_reason": "Conditional result was False"}

TASK [Process operation results] ***********************************************
task path: /runner/project/vmlc-services-dns-v3/playbooks/manage_dns.yml:106
included: /runner/project/vmlc-services-dns-v3/roles/common/tasks/process_results.yml for localhost

TASK [Check async job status] **************************************************
task path: /runner/project/vmlc-services-dns-v3/roles/common/tasks/process_results.yml:7
skipping: [localhost] => {"changed": false, "false_condition": "is_multi_domain and async_results is defined and async_results | length > 0", "skip_reason": "Conditional result was False"}

TASK [Extract results from async jobs] *****************************************
task path: /runner/project/vmlc-services-dns-v3/roles/common/tasks/process_results.yml:18
skipping: [localhost] => {"changed": false, "false_condition": "is_multi_domain and job_result is defined and job_result.results is defined", "skip_reason": "Conditional result was False"}

TASK [Calculate success rate for multi-domain operations] **********************
task path: /runner/project/vmlc-services-dns-v3/roles/common/tasks/process_results.yml:25
skipping: [localhost] => {"changed": false, "false_condition": "is_multi_domain and consolidated_results is defined and consolidated_results | length > 0", "skip_reason": "Conditional result was False"}

TASK [Display operation results for single domain] *****************************
task path: /runner/project/vmlc-services-dns-v3/roles/common/tasks/process_results.yml:32
ok: [localhost] => {
    "msg": [
        "Verify operation for A record ANSORTEST.devhealthgrp.com.sg failed",
        "Message: Operation not yet executed"
    ]
}

TASK [Display operation results for multi-domain] ******************************
task path: /runner/project/vmlc-services-dns-v3/roles/common/tasks/process_results.yml:40
skipping: [localhost] => {"false_condition": "is_multi_domain and consolidated_results is defined and consolidated_results | length > 0"}

TASK [Write results to log file] ***********************************************
task path: /runner/project/vmlc-services-dns-v3/roles/common/tasks/process_results.yml:50
changed: [localhost] => {"changed": true, "checksum": "a0402cb9ba917f65e5c4955e15e2595779f5a15a", "dest": "/runner/project/vmlc-services-dns-v3/playbooks/../logs/ansible/20250425_INC-123456_ANSORTEST_devhealthgrp.com.sg_A_VERIFY_ANSIBLE.log", "gid": 0, "group": "root", "md5sum": "76ccf3ffbd3130a7a3bbf3fde098f2ce", "mode": "0644", "owner": "root", "size": 311, "src": "/home/<USER>/.ansible/tmp/ansible-tmp-**********.9587657-734-235028654362723/source", "state": "file", "uid": 0}

TASK [Generate standard report] ************************************************
task path: /runner/project/vmlc-services-dns-v3/playbooks/manage_dns.yml:113
skipping: [localhost] => {"changed": false, "false_condition": "generate_report | bool", "skip_reason": "Conditional result was False"}

TASK [Generate consolidated report] ********************************************
task path: /runner/project/vmlc-services-dns-v3/playbooks/manage_dns.yml:120
skipping: [localhost] => {"changed": false, "false_condition": "generate_report | bool", "skip_reason": "Conditional result was False"}

TASK [Email report] ************************************************************
task path: /runner/project/vmlc-services-dns-v3/playbooks/manage_dns.yml:131
skipping: [localhost] => {"changed": false, "false_condition": "generate_report | bool and email_report | bool", "skip_reason": "Conditional result was False"}

TASK [Email logs] **************************************************************
task path: /runner/project/vmlc-services-dns-v3/playbooks/manage_dns.yml:138

TASK [/runner/project/vmlc-services-dns-v3/playbooks/../roles/reporting : Include email configuration] ***
task path: /runner/project/vmlc-services-dns-v3/roles/reporting/tasks/email_logs.yml:7
ok: [localhost] => {"ansible_facts": {"email_flags": {"email_logs": false, "email_report": false, "generate_report": true, "include_bcc": true, "send_to_test_only": false, "testing_mode": false}, "email_settings": {"attachments": {"max_size": 10, "types": ["pdf", "log", "html"]}, "recipients": {"bcc": "<EMAIL>", "domain_specific": {"default": "<EMAIL>", "ses.shsu.com.sg": "<EMAIL>", "shses.shs.com.sg": "<EMAIL>"}, "testing": "<EMAIL>"}, "smtp": {"dc_servers": {"hdc1": "asmtp.hcloud.healthgrp.com.sg", "hdc2": "fsmtp.hcloud.healthgrp.com.sg"}, "default_dc": "hdc1", "from": "<EMAIL>", "port": 25}, "templates": {"bodies": {"consolidated_report": "consolidated_report_email_body.j2", "logs": "logs_email_body.j2", "report": "report_email_body.j2"}, "directory": "{{ playbook_dir }}/../templates/emails", "subjects": {"consolidated_report": "DNS Management Consolidated Report - {operation} {record_type} Records", "logs": "DNS Management Logs - {operation} {record_type} Record for {hostname}.{domain}", "report": "DNS Management Report - {operation} {record_type} Record for {hostname}.{domain}"}}}}, "ansible_included_var_files": ["/runner/project/vmlc-services-dns-v3/vars/email_config.yml"], "changed": false}

TASK [/runner/project/vmlc-services-dns-v3/playbooks/../roles/reporting : Include dynamic email recipient selection] ***
task path: /runner/project/vmlc-services-dns-v3/roles/reporting/tasks/email_logs.yml:12
included: /runner/project/vmlc-services-dns-v3/roles/reporting/tasks/set_email_recipient.yml for localhost

TASK [/runner/project/vmlc-services-dns-v3/playbooks/../roles/reporting : Set domain for email recipient selection] ***
task path: /runner/project/vmlc-services-dns-v3/roles/reporting/tasks/set_email_recipient.yml:6
ok: [localhost] => {"ansible_facts": {"email_domain": "devhealthgrp.com.sg"}, "changed": false}

TASK [/runner/project/vmlc-services-dns-v3/playbooks/../roles/reporting : Set email recipient based on domain (for specific domains)] ***
task path: /runner/project/vmlc-services-dns-v3/roles/reporting/tasks/set_email_recipient.yml:11
skipping: [localhost] => {"changed": false, "false_condition": "email_domain in email_settings.recipients.domain_specific", "skip_reason": "Conditional result was False"}

TASK [/runner/project/vmlc-services-dns-v3/playbooks/../roles/reporting : Set default email recipient (for other domains)] ***
task path: /runner/project/vmlc-services-dns-v3/roles/reporting/tasks/set_email_recipient.yml:21
skipping: [localhost] => {"changed": false, "false_condition": "not send_to_test_only | default(email_flags.send_to_test_only) | bool", "skip_reason": "Conditional result was False"}

TASK [/runner/project/vmlc-services-dns-v3/playbooks/../roles/reporting : Set testing email recipient (when testing mode or send_to_test_only is enabled)] ***
task path: /runner/project/vmlc-services-dns-v3/roles/reporting/tasks/set_email_recipient.yml:31
ok: [localhost] => {"ansible_facts": {"dynamic_email_recipient": "<EMAIL>", "email_bcc": "<EMAIL>"}, "changed": false}

TASK [/runner/project/vmlc-services-dns-v3/playbooks/../roles/reporting : Override email_recipient with dynamic recipient] ***
task path: /runner/project/vmlc-services-dns-v3/roles/reporting/tasks/set_email_recipient.yml:37
ok: [localhost] => {"ansible_facts": {"email_recipient": "<EMAIL>"}, "changed": false}

TASK [/runner/project/vmlc-services-dns-v3/playbooks/../roles/reporting : Display selected email recipient] ***
task path: /runner/project/vmlc-services-dns-v3/roles/reporting/tasks/set_email_recipient.yml:42
ok: [localhost] => {
    "msg": "Email will be sent to: <EMAIL> (BCC: <EMAIL>)  (SENDING TO TEST RECIPIENT ONLY) "
}

TASK [/runner/project/vmlc-services-dns-v3/playbooks/../roles/reporting : Validate email recipient is provided] ***
task path: /runner/project/vmlc-services-dns-v3/roles/reporting/tasks/email_logs.yml:17
ok: [localhost] => {
    "changed": false,
    "msg": "All assertions passed"
}

TASK [/runner/project/vmlc-services-dns-v3/playbooks/../roles/reporting : Include dynamic SMTP server selection] ***
task path: /runner/project/vmlc-services-dns-v3/roles/reporting/tasks/email_logs.yml:26
included: /runner/project/vmlc-services-dns-v3/roles/reporting/tasks/set_smtp_server.yml for localhost

TASK [/runner/project/vmlc-services-dns-v3/playbooks/../roles/reporting : Determine DC location from domain configuration] ***
task path: /runner/project/vmlc-services-dns-v3/roles/reporting/tasks/set_smtp_server.yml:6
ok: [localhost] => {"ansible_facts": {"dc_location": "hdc1"}, "changed": false}

TASK [/runner/project/vmlc-services-dns-v3/playbooks/../roles/reporting : Set SMTP server based on DC location] ***
task path: /runner/project/vmlc-services-dns-v3/roles/reporting/tasks/set_smtp_server.yml:11
ok: [localhost] => {"ansible_facts": {"smtp_from": "<EMAIL>", "smtp_port": "25", "smtp_server": "asmtp.hcloud.healthgrp.com.sg"}, "changed": false}

TASK [/runner/project/vmlc-services-dns-v3/playbooks/../roles/reporting : Set default SMTP server if no DC location is found] ***
task path: /runner/project/vmlc-services-dns-v3/roles/reporting/tasks/set_smtp_server.yml:18
skipping: [localhost] => {"changed": false, "false_condition": "smtp_server is not defined", "skip_reason": "Conditional result was False"}

TASK [/runner/project/vmlc-services-dns-v3/playbooks/../roles/reporting : Display selected SMTP server] ***
task path: /runner/project/vmlc-services-dns-v3/roles/reporting/tasks/set_smtp_server.yml:25
ok: [localhost] => {
    "msg": "Using SMTP server: asmtp.hcloud.healthgrp.com.sg for DC location: hdc1 \\n"
}

TASK [/runner/project/vmlc-services-dns-v3/playbooks/../roles/reporting : Set initial email subject] ***
task path: /runner/project/vmlc-services-dns-v3/roles/reporting/tasks/email_logs.yml:31
ok: [localhost] => {"ansible_facts": {"email_subject": "DNS Management Logs - {operation} {record_type} Record for {hostname}.{domain}"}, "changed": false}

TASK [/runner/project/vmlc-services-dns-v3/playbooks/../roles/reporting : Format email subject with operation] ***
task path: /runner/project/vmlc-services-dns-v3/roles/reporting/tasks/email_logs.yml:37
ok: [localhost] => {"ansible_facts": {"email_subject": "DNS Management Logs - Verify {record_type} Record for {hostname}.{domain}"}, "changed": false}

TASK [/runner/project/vmlc-services-dns-v3/playbooks/../roles/reporting : Format email subject with record type] ***
task path: /runner/project/vmlc-services-dns-v3/roles/reporting/tasks/email_logs.yml:43
ok: [localhost] => {"ansible_facts": {"email_subject": "DNS Management Logs - Verify A Record for {hostname}.{domain}"}, "changed": false}

TASK [/runner/project/vmlc-services-dns-v3/playbooks/../roles/reporting : Format email subject with hostname] ***
task path: /runner/project/vmlc-services-dns-v3/roles/reporting/tasks/email_logs.yml:49
ok: [localhost] => {"ansible_facts": {"email_subject": "DNS Management Logs - Verify A Record for ANSORTEST.{domain}"}, "changed": false}

TASK [/runner/project/vmlc-services-dns-v3/playbooks/../roles/reporting : Format email subject with domain] ***
task path: /runner/project/vmlc-services-dns-v3/roles/reporting/tasks/email_logs.yml:55
ok: [localhost] => {"ansible_facts": {"email_subject": "DNS Management Logs - Verify A Record for ANSORTEST.devhealthgrp.com.sg"}, "changed": false}

TASK [/runner/project/vmlc-services-dns-v3/playbooks/../roles/reporting : Generate email body from template] ***
task path: /runner/project/vmlc-services-dns-v3/roles/reporting/tasks/email_logs.yml:61
fatal: [localhost]: FAILED! => {"msg": "An unhandled exception occurred while templating '{'directories': {'reports': '{{ playbook_dir }}/../reports', 'templates': '{{ playbook_dir }}/../templates/reports'}, 'format': {'date_format': '%Y%m%d', 'separator': '_', 'extension': '.pdf'}, 'types': {'standard': 'STANDARD', 'consolidated': 'CONSOLIDATED'}, 'templates': {'standard': 'dns_report.html.j2', 'consolidated': 'consolidated_report.html.j2'}, 'content': {'standard_sections': [{'title': 'Operation Details', 'fields': [{'name': 'Operation', 'value': \\"{{ operation | default('unknown') | capitalize }}\\"}, {'name': 'Record Type', 'value': \\"{{ record_type | default('unknown') | upper }}\\"}, {'name': 'Hostname', 'value': \\"{{ hostname | default('unknown') }}.{{ domain | default('unknown') }}\\"}, {'name': 'Ticket', 'value': \\"{{ ticket | default('INC-123456') }}\\"}]}, {'title': 'Result Summary', 'fields': [{'name': 'Status', 'value': \\"{{ 'Successful' if dns_operation_result is defined and dns_operation_result.success else 'Failed' }}\\"}, {'name': 'Message', 'value': \\"{{ dns_operation_result.message if dns_operation_result is defined else 'No operation result available' }}\\"}]}, {'title': 'Record Details', 'fields': [{'name': 'Domain', 'value': \\"{{ domain | default('unknown') }}\\"}, {'name': 'Hostname', 'value': \\"{{ hostname | default('unknown') }}\\"}, {'name': 'FQDN', 'value': \\"{{ hostname | default('unknown') }}.{{ domain | default('unknown') }}\\"}, {'name': 'Record Type', 'value': \\"{{ record_type | default('unknown') | upper }}\\"}, {'name': 'IP Address', 'value': 'N/A', 'condition': \\"{{ record_type | default('unknown') | lower in ['a', 'ptr'] }}\\"}, {'name': 'CNAME Target', 'value': 'N/A', 'condition': \\"{{ record_type | default('unknown') | lower == 'cname' }}\\"}, {'name': 'TTL', 'value': '3600'}]}, {'title': 'Execution Information', 'fields': [{'name': 'Execution Time', 'value': '{{ ansible_date_time.iso8601 }}'}, {'name': 'Executed By', 'value': '{{ ansible_user_id }}'}, {'name': 'Target Server', 'value': \\"{{ dns_server | default('unknown') }}\\"}]}], 'consolidated_sections': [{'title': 'Operation Summary', 'fields': [{'name': 'Operation', 'value': \\"{{ operation | default('unknown') | capitalize }}\\"}, {'name': 'Record Type', 'value': \\"{{ record_type | default('unknown') | upper }}\\"}, {'name': 'Ticket', 'value': \\"{{ ticket | default('INC-123456') }}\\"}, {'name': 'Total Domains', 'value': \\"{{ domains | default('') | split(',') | length if domains is defined and domains else 0 }}\\"}, {'name': 'Successful Operations', 'value': \\"{{ consolidated_results | default([]) | selectattr('success', 'equalto', true) | list | length if consolidated_results is defined else 0 }}\\"}, {'name': 'Failed Operations', 'value': \\"{{ consolidated_results | default([]) | selectattr('success', 'equalto', false) | list | length if consolidated_results is defined else 0 }}\\"}]}]}, 'pdf': {'page_size': 'A4', 'orientation': 'portrait', 'margins': {'top': '20mm', 'right': '20mm', 'bottom': '20mm', 'left': '20mm'}}}'. Error was a <class 'ansible.errors.AnsibleError'>, original message: Unexpected templating type error occurred on ({{ domains | default('') | split(',') | length if domains is defined and domains else 0 }}): descriptor 'split' for 'str' objects doesn't apply to a 'dict' object. descriptor 'split' for 'str' objects doesn't apply to a 'dict' object"}
...ignoring

TASK [/runner/project/vmlc-services-dns-v3/playbooks/../roles/reporting : Read email body content] ***
task path: /runner/project/vmlc-services-dns-v3/roles/reporting/tasks/email_logs.yml:72
skipping: [localhost] => {"changed": false, "false_condition": "email_logs | default(email_flags.email_logs) | bool and email_body_file is defined and email_body_file.dest is defined", "skip_reason": "Conditional result was False"}

TASK [/runner/project/vmlc-services-dns-v3/playbooks/../roles/reporting : Send email with logs] ***
task path: /runner/project/vmlc-services-dns-v3/roles/reporting/tasks/email_logs.yml:80
fatal: [localhost]: FAILED! => {"censored": "the output has been hidden due to the fact that 'no_log: true' was specified for this result", "changed": false}
...ignoring

TASK [/runner/project/vmlc-services-dns-v3/playbooks/../roles/reporting : Display email status] ***
task path: /runner/project/vmlc-services-dns-v3/roles/reporting/tasks/email_logs.yml:100
ok: [localhost] => {
    "msg": " Failed to send <NAME_EMAIL>. Error: Failed to send community.general.mail: can't attach file C:\\\\OE_AAP_LOGS\\\\20250425_INC-123456_ANSORTEST_devhealthgrp.com.sg_A_VERIFY_POWERSHELL.log: [Errno 2] No such file or directory: 'C:\\\\\\\\OE_AAP_LOGS\\\\\\\\20250425_INC-123456_ANSORTEST_devhealthgrp.com.sg_A_VERIFY_POWERSHELL.log' \\n"
}

TASK [/runner/project/vmlc-services-dns-v3/playbooks/../roles/reporting : Register email status for summary] ***
task path: /runner/project/vmlc-services-dns-v3/roles/reporting/tasks/email_logs.yml:113
ok: [localhost] => {"ansible_facts": {"email_status": "Failed to send community.general.mail: can't attach file C:\\\\OE_AAP_LOGS\\\\20250425_INC-123456_ANSORTEST_devhealthgrp.com.sg_A_VERIFY_POWERSHELL.log: [Errno 2] No such file or directory: 'C:\\\\\\\\OE_AAP_LOGS\\\\\\\\20250425_INC-123456_ANSORTEST_devhealthgrp.com.sg_A_VERIFY_POWERSHELL.log'"}, "changed": false}

TASK [Upload logs] *************************************************************
task path: /runner/project/vmlc-services-dns-v3/playbooks/manage_dns.yml:146
skipping: [localhost] => {"changed": false, "false_condition": "store_logs_target_server | bool", "skip_reason": "Conditional result was False"}

TASK [Clean up] ****************************************************************
task path: /runner/project/vmlc-services-dns-v3/playbooks/manage_dns.yml:154
redirecting (type: modules) ansible.builtin.archive to community.general.archive
included: /runner/project/vmlc-services-dns-v3/roles/common/tasks/cleanup.yml for localhost

TASK [Clear sensitive variables] ***********************************************
task path: /runner/project/vmlc-services-dns-v3/roles/common/tasks/cleanup.yml:7
ok: [localhost] => {"censored": "the output has been hidden due to the fact that 'no_log: true' was specified for this result", "changed": false}

TASK [Archive logs if configured] **********************************************
task path: /runner/project/vmlc-services-dns-v3/roles/common/tasks/cleanup.yml:18
redirecting (type: modules) ansible.builtin.archive to community.general.archive
redirecting (type: modules) ansible.builtin.archive to community.general.archive
changed: [localhost] => {"archived": ["/runner/project/vmlc-services-dns-v3/playbooks/../logs/ansible/20250425_INC-123456_ANSORTEST_devhealthgrp.com.sg_A_VERIFY_ANSIBLE.log"], "arcroot": "/runner/project/vmlc-services-dns-v3/playbooks/../logs/", "changed": true, "dest": "/runner/project/vmlc-services-dns-v3/playbooks/../logs/archive/20250425_INC-123456_ANSORTEST_devhealthgrp.com.sg_A_VERIFY_ARCHIVE.tar.gz", "dest_state": "incomplete", "expanded_exclude_paths": [], "expanded_paths": ["/runner/project/vmlc-services-dns-v3/playbooks/../logs/ansible/20250425_INC-123456_ANSORTEST_devhealthgrp.com.sg_A_VERIFY_ANSIBLE.log", "/runner/project/vmlc-services-dns-v3/playbooks/../logs/powershell/20250425_INC-123456_ANSORTEST_devhealthgrp.com.sg_A_VERIFY_POWERSHELL.log", "/runner/project/vmlc-services-dns-v3/playbooks/../logs/progress/20250425_INC-123456_ANSORTEST_devhealthgrp.com.sg_A_VERIFY_PROGRESS.log"], "gid": 0, "group": "root", "missing": ["/runner/project/vmlc-services-dns-v3/playbooks/../logs/powershell/20250425_INC-123456_ANSORTEST_devhealthgrp.com.sg_A_VERIFY_POWERSHELL.log", "/runner/project/vmlc-services-dns-v3/playbooks/../logs/progress/20250425_INC-123456_ANSORTEST_devhealthgrp.com.sg_A_VERIFY_PROGRESS.log"], "mode": "0644", "owner": "root", "size": 467, "state": "file", "uid": 0}

TASK [Display operation summary] ***********************************************
task path: /runner/project/vmlc-services-dns-v3/playbooks/manage_dns.yml:158
included: /runner/project/vmlc-services-dns-v3/roles/common/tasks/display_summary.yml for localhost

TASK [Display operation summary for single domain] *****************************
task path: /runner/project/vmlc-services-dns-v3/roles/common/tasks/display_summary.yml:7
ok: [localhost] => {
    "msg": [
        "Operation Summary:",
        "  Operation: Verify",
        "  Record Type: A",
        "  Hostname: ANSORTEST.devhealthgrp.com.sg",
        "  Result: Failure",
        "  Message: Operation not yet executed",
        "  Logs: /runner/project/vmlc-services-dns-v3/playbooks/../logs/ansible/20250425_INC-123456_ANSORTEST_devhealthgrp.com.sg_A_VERIFY_ANSIBLE.log",
        "  PowerShell Log: /runner/project/vmlc-services-dns-v3/playbooks/../logs/powershell/20250425_INC-123456_ANSORTEST_devhealthgrp.com.sg_A_VERIFY_POWERSHELL.log",
        "  Report: Not generated",
        "  ADMT Server: HISADMTVDSEC01.devhealthgrp.com.sg",
        "  DNS Server: hisaddcvdutl01.devhealthgrp.com.sg",
        "  PTR DNS Server: hisaddcvdutl01.devhealthgrp.com.sg"
    ]
}

TASK [Display operation summary for multi-domain] ******************************
task path: /runner/project/vmlc-services-dns-v3/roles/common/tasks/display_summary.yml:25
skipping: [localhost] => {"false_condition": "is_multi_domain and consolidated_results is defined and consolidated_results | length > 0"}

TASK [Display PowerShell execution information] ********************************
task path: /runner/project/vmlc-services-dns-v3/roles/common/tasks/display_summary.yml:39
ok: [localhost] => {
    "msg": [
        "PowerShell Execution Information:",
        "  Return Code: 0",
        "  Execution Status: Success",
        "  PowerShell Log: /runner/project/vmlc-services-dns-v3/playbooks/../logs/powershell/20250425_INC-123456_ANSORTEST_devhealthgrp.com.sg_A_VERIFY_POWERSHELL.log"
    ]
}

TASK [Display email information] ***********************************************
task path: /runner/project/vmlc-services-dns-v3/roles/common/tasks/display_summary.yml:49
ok: [localhost] => {
    "msg": [
        "Email Information:",
        "  Report Email: Failed to <NAME_EMAIL>",
        "  Logs Email: Failed to <NAME_EMAIL>",
        "  SMTP Server: asmtp.hcloud.healthgrp.com.sg",
        "  Email Status: Failed"
    ]
}

TASK [Display log storage information] *****************************************
task path: /runner/project/vmlc-services-dns-v3/roles/common/tasks/display_summary.yml:60
skipping: [localhost] => {"false_condition": "store_logs_target_server | bool"}

PLAY RECAP *********************************************************************
localhost                  : ok=125  changed=5    unreachable=0    failed=0    skipped=54   rescued=0    ignored=3   
