---
# Default converge playbook for DNS Management testing
# Author: CES Operational Excellence Team
# Contributors: <PERSON> Bin <PERSON> (7409)

- name: Converge
  hosts: all
  gather_facts: true

  vars_files:
    - "../../../../vars/defaults.yml"
    - "../../../../vars/domain_config.yml"
    - "../../../../vars/credentials_config.yml"
    - "../../../../vars/vault.yml"
    - "../../../../vars/email_config.yml"
    - "../../../../vars/log_config.yml"
    - "../../../../vars/operations_config.yml"
    - "../../../../vars/powershell_config.yml"
    - "../../../../vars/report_config.yml"
    - "../../../../dev/vars/testing_config.yml"

  vars:
    # Test variables
    test_environment: local
    test_domain: test.local
    test_hostname: server01
    test_ip_address: ************
    test_cname_target: server01.test.local
    test_ticket: TEST123

    # Override variables for testing
    dns_server: "{{ test_environments[test_environment].dns_server }}"
    admt_server: "{{ test_environments[test_environment].admt_server }}"
    domain: "{{ test_domain }}"
    hostname: "{{ test_hostname }}"
    ip_address: "{{ test_ip_address }}"
    cname_target: "{{ test_cname_target }}"
    ticket: "{{ test_ticket }}"

    # Disable email and reporting for tests
    email_report: false
    email_logs: false
    generate_report: false
    store_logs_target_server: false

  tasks:
    # Set up test environment
    - name: Set up test environment
      ansible.builtin.debug:
        msg: "Setting up test environment: {{ test_environment }}"

    # Create test directory structure
    - name: Create test directory structure
      ansible.builtin.file:
        path: "{{ item }}"
        state: directory
        mode: '0755'
      loop:
        - /tmp/dns_management_test/logs/ansible
        - /tmp/dns_management_test/logs/powershell
        - /tmp/dns_management_test/logs/progress
        - /tmp/dns_management_test/logs/archive
        - /tmp/dns_management_test/reports

    # Override log paths for testing
    - name: Override log paths for testing
      ansible.builtin.set_fact:
        log_settings:
          directories:
            ansible: "/tmp/dns_management_test/logs/ansible"
            powershell: "/tmp/dns_management_test/logs/powershell"
            progress: "/tmp/dns_management_test/logs/progress"
            archive: "/tmp/dns_management_test/logs/archive"
          format:
            date_format: "%Y%m%d"
            separator: "_"
            extension: ".log"
          types:
            ansible: "ANSIBLE"
            powershell: "POWERSHELL"
            progress: "PROGRESS"
            report: "REPORT"
          levels:
            error: "ERROR"
            warning: "WARNING"
            info: "INFO"
            debug: "DEBUG"
          target_server:
            base_path: "/tmp/dns_management_test/logs/"
          rotation:
            max_age: 30
            max_size: 10
            max_files: 10
          format_string:
            ansible: "[{timestamp}] [{level}] [{operation}] [{record_type}] [{hostname}.{domain}] {message}"
            powershell: "[{timestamp}] [{level}] [{operation}] [{record_type}] [{hostname}.{domain}] {message}"

        report_settings:
          directories:
            reports: "/tmp/dns_management_test/reports"
            templates: "../../../../templates/reports"
          format:
            date_format: "%Y%m%d"
            separator: "_"
            extension: ".pdf"
          types:
            standard: "STANDARD"
            consolidated: "CONSOLIDATED"
          templates:
            standard: "dns_report.html.j2"
            consolidated: "consolidated_report.html.j2"

    # Mock DNS server for testing
    - name: Mock DNS server for testing
      ansible.builtin.debug:
        msg: "Mocking DNS server: {{ dns_server }}"
