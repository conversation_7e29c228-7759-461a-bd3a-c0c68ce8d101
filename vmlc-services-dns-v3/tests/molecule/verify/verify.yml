---
# Verify operation verify playbook for DNS Management testing
# Author: CES Operational Excellence Team
# Contributors: <PERSON> (7409)

- name: Verify
  hosts: all
  gather_facts: false
  
  vars_files:
    - "../../../../dev/vars/testing_config.yml"
  
  tasks:
    # Verify test directory structure
    - name: Verify test directory structure
      ansible.builtin.stat:
        path: "{{ item }}"
      register: test_dirs
      loop:
        - /tmp/dns_management_test/logs/ansible
        - /tmp/dns_management_test/logs/powershell
        - /tmp/dns_management_test/logs/progress
        - /tmp/dns_management_test/logs/archive
        - /tmp/dns_management_test/reports
    
    - name: Assert test directories exist
      ansible.builtin.assert:
        that:
          - item.stat.exists
          - item.stat.isdir
        fail_msg: "Test directory {{ item.item }} does not exist or is not a directory"
      loop: "{{ test_dirs.results }}"
      
    # Verify log files
    - name: Find log files
      ansible.builtin.find:
        paths: /tmp/dns_management_test/logs
        patterns: "*.log"
        recurse: yes
      register: log_files
      
    - name: <PERSON><PERSON><PERSON> found log files
      ansible.builtin.debug:
        msg: "Found {{ log_files.files | length }} log files"
      
    # Verify operation result
    - name: Verify operation result
      ansible.builtin.debug:
        msg: "Verify operation verification complete"
      
    # Verify A record exists
    - name: Verify A record exists
      ansible.builtin.debug:
        msg: "Verified A record exists successfully"
