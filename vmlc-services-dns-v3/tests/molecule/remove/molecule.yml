---
# Remove operation Molecule configuration for DNS Management testing
# Author: CES Operational Excellence Team
# Contributors: <PERSON> (7409)

dependency:
  name: galaxy
driver:
  name: docker
platforms:
  - name: dns-management-remove-test
    image: geerlingguy/docker-ubuntu2004-ansible
    pre_build_image: true
    volumes:
      - /sys/fs/cgroup:/sys/fs/cgroup:ro
    privileged: true
    command: "/lib/systemd/systemd"
provisioner:
  name: ansible
  playbooks:
    converge: converge.yml
  inventory:
    group_vars:
      all:
        ansible_python_interpreter: /usr/bin/python3
        operation: remove
        record_type: a
        hostname: server01
        domain: test.local
        ip_address: ************
        ticket: TEST123
verifier:
  name: ansible
  playbooks:
    verify: verify.yml
