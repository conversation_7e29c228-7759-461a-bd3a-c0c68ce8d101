# VMLC Services - DNS Management - Usage Guide

**Author:** CES Operational Excellence Team
**Contributors:** <PERSON> (7409)

## Table of Contents

1. [Introduction](#1-introduction)
2. [Prerequisites](#2-prerequisites)
3. [Ansible Automation Platform (AAP) Usage](#3-ansible-automation-platform-aap-usage)
4. [Parameters Reference](#4-parameters-reference)
5. [Basic Operations](#5-basic-operations)
   - [Verify Operations](#51-verify-operations)
   - [Add Operations](#52-add-operations)
   - [Remove Operations](#53-remove-operations)
   - [Update Operations](#54-update-operations)
6. [Advanced Operations](#6-advanced-operations)
   - [Custom TTL](#61-custom-ttl)
   - [Custom Description](#62-custom-description)
   - [Force Remove](#63-force-remove)
   - [PTR Management](#64-ptr-management)
   - [Special PTR Handling](#65-special-ptr-handling)
7. [Multi-Domain Operations](#7-multi-domain-operations)
   - [Verify Across Domains](#71-verify-across-domains)
   - [Add Across Domains](#72-add-across-domains)
   - [Remove Across Domains](#73-remove-across-domains)
   - [Update Across Domains](#74-update-across-domains)
   - [Domain Groups](#75-domain-groups)
8. [Email Notifications](#8-email-notifications)
   - [Enable Email Reporting](#81-enable-email-reporting)
   - [Enable Email Logs](#82-enable-email-logs)
   - [Testing Mode](#83-testing-mode)
   - [Custom Email Recipient](#84-custom-email-recipient)
9. [Logging](#9-logging)
   - [Set Log Level](#91-set-log-level)
   - [Disable Target Server Log Storage](#92-disable-target-server-log-storage)
   - [Log File Locations](#93-log-file-locations)
   - [Log File Naming Convention](#94-log-file-naming-convention)
10. [Reporting](#10-reporting)
    - [Generate Report](#101-generate-report)
    - [Disable Report Generation](#102-disable-report-generation)
    - [Report File Locations](#103-report-file-locations)
11. [Troubleshooting](#11-troubleshooting)
    - [Common Issues in AAP](#111-common-issues-in-aap)
    - [Debugging in AAP](#112-debugging-in-aap)
    - [Viewing Job Output in AAP](#113-viewing-job-output-in-aap)
    - [Contacting Support](#114-contacting-support)
12. [Best Practices](#12-best-practices)
    - [Always Specify a Ticket Number](#121-always-specify-a-ticket-number)
    - [Verify Before Adding or Removing](#122-verify-before-adding-or-removing)
    - [Use Descriptive Hostnames](#123-use-descriptive-hostnames)
    - [Document Changes](#124-document-changes)
    - [Use Testing Mode for Email Testing](#125-use-testing-mode-for-email-testing)
    - [Use Workflow Templates for Complex Operations](#126-use-workflow-templates-for-complex-operations)
    - [Implement Approval Workflows](#127-implement-approval-workflows)
13. [Developer Testing](#13-developer-testing)
    - [Direct Ansible Playbook Execution](#131-direct-ansible-playbook-execution)
    - [Using the Developer Helper Script](#132-using-the-developer-helper-script)
    - [Running Molecule Tests](#133-running-molecule-tests)
    - [Testing Specific Operations with Molecule](#134-testing-specific-operations-with-molecule)
    - [Testing in Different Environments](#135-testing-in-different-environments)
    - [Creating Custom Test Cases](#136-creating-custom-test-cases)
    - [Testing Idempotency](#137-testing-idempotency)

## 1. Introduction

The DNS Management System provides a standardized approach to managing DNS records across multiple domains. This system is primarily designed to be used through Ansible Automation Platform (AAP), providing a user-friendly interface for DNS operations with proper access controls and audit trails. This guide explains how to use the system effectively through AAP.

## 2. Prerequisites

Before using the DNS Management System, ensure you have:

- Access to Ansible Automation Platform (AAP)
- Appropriate permissions to run DNS Management job templates
- Valid ticket number for tracking and auditing purposes
- Knowledge of the DNS records you need to manage

For administrators and developers, the following are also required:

- PowerShell 5.1 or higher on target servers
- DnsServer PowerShell module installed on target servers
- WinRM configured for remote management
- Appropriate credentials for target domains

## 3. Ansible Automation Platform (AAP) Usage

The DNS Management System is primarily used through Ansible Automation Platform (AAP) for centralized management, scheduling, and access control. This section explains how to use Job Templates in AAP for DNS operations.

### 3.1. Accessing DNS Management in AAP

1. Log in to your AAP instance
2. Navigate to **Templates** in the left sidebar
3. Find and select the **DNS Management** job template

### 3.2. Using the Survey Form

The DNS Management job template includes a survey form with the following fields:

| Field | Description | Example Value |
|-------|-------------|---------------|
| Operation | The operation to perform (verify, add, remove, update) | add |
| Record Type | The type of DNS record (a, cname, ptr) | a |
| Hostname | The hostname part of the DNS record | server01 |
| Domain | The domain part of the DNS record | example.com |
| IP Address | The IP address (for A or PTR records) | ************ |
| CNAME Target | The target hostname (for CNAME records) | server01.example.com |
| TTL | Time to live in seconds | 3600 |
| Description | Description for the DNS record | Web server |
| Manage PTR | Whether to manage PTR records for A records | true |
| Force Remove | Whether to force removal of records | false |
| Ticket Number | Ticket number for tracking | INC123456 |
| Email Report | Whether to send an email report | true |
| Email Recipient | Email recipient for the report | <EMAIL> |

### 3.3. Launching a Job

1. Click on the **DNS Management** job template
2. Click the **Launch** button
3. Fill in the survey form with the required information
4. Click **Next** to review the inputs
5. Click **Launch** to start the job

### 3.4. Using Extra Variables

Advanced users can also launch jobs with extra variables in JSON format. This can be done by:

1. Click on the **DNS Management** job template
2. Click the **Launch** button
3. Instead of filling out the survey, click on the **Extra Variables** tab
4. Enter your variables in JSON format (see examples in section 4)
5. Click **Launch** to start the job

## 4. Parameters Reference

This section provides a comprehensive reference of all parameters available for DNS operations.

### 4.1. Required Parameters

| Parameter | Description | Required For | Example Value |
|-----------|-------------|--------------|---------------|
| `operation` | The operation to perform | All operations | `verify`, `add`, `remove`, `update` |
| `record_type` | The type of DNS record | All operations | `a`, `cname`, `ptr` |
| `hostname` | The hostname part of the DNS record | Single domain operations | `server01` |
| `domain` | The domain part of the DNS record | Single domain operations | `example.com` |
| `hostnames` | Comma-separated list of hostnames | Multi-domain operations | `server01,server02` |
| `domains` | Comma-separated list of domains | Multi-domain operations | `example.com,example.org` |
| `ip_address` | The IP address for the record | A and PTR records | `************` |
| `ip_addresses` | Comma-separated list of IP addresses | Multi-domain A and PTR records | `************,************` |
| `cname_target` | The target hostname for CNAME records | CNAME records | `server01.example.com` |
| `cname_targets` | Comma-separated list of CNAME targets | Multi-domain CNAME records | `server01.example.com,server02.example.org` |
| `ticket` | Ticket number for tracking | All operations | `INC123456` |

### 4.2. Optional Parameters

| Parameter | Description | Default Value | Example Value |
|-----------|-------------|---------------|---------------|
| `ttl` | Time to live in seconds | 3600 | `7200` |
| `description` | Description for the DNS record | "Managed by Ansible DNS Management" | `Web server` |
| `manage_ptr` | Whether to manage PTR records for A records | `true` | `false` |
| `force_remove` | Whether to force removal of records | `false` | `true` |
| `domain_group` | Domain group to operate on | None | `production` |
| `email_report` | Whether to send an email report | `false` | `true` |
| `email_logs` | Whether to email logs | `false` | `true` |
| `email_recipient` | Email recipient for the report | Domain-specific default | `<EMAIL>` |
| `testing_mode` | Whether to use testing mode for emails | `false` | `true` |
| `send_to_test_only` | Whether to send emails only to test recipient | `false` | `true` |
| `generate_report` | Whether to generate a report | `true` | `false` |
| `log_level` | Log level for operations | `Info` | `Debug` |
| `store_logs_target_server` | Whether to store logs on target server | `true` | `false` |
| `test_environment` | Environment to use for testing | None | `local` |

### 4.3. Special Parameters

| Parameter | Description | Default Value | Example Value |
|-----------|-------------|---------------|---------------|
| `special_ptr_handling` | Enable special PTR handling for specific domains | `false` | `true` |
| `skip_connectivity_check` | Skip connectivity check before operations | `false` | `true` |
| `verify_only` | Only verify, don't make changes | `false` | `true` |
| `async_operations` | Run multi-domain operations asynchronously | `false` | `true` |
| `max_async_time` | Maximum time to wait for async operations | `600` | `300` |
| `max_async_poll` | Polling interval for async operations | `5` | `10` |

## 5. Basic Operations

This section provides examples of basic DNS operations using AAP extra variables format.

### 5.1. Verify Operations

#### 5.1.1. Verify A Record

```json
{
  "operation": "verify",
  "record_type": "a",
  "hostname": "server01",
  "domain": "example.com",
  "ticket": "INC123456"
}
```

#### 5.1.2. Verify CNAME Record

```json
{
  "operation": "verify",
  "record_type": "cname",
  "hostname": "www",
  "domain": "example.com",
  "ticket": "INC123456"
}
```

#### 5.1.3. Verify PTR Record

```json
{
  "operation": "verify",
  "record_type": "ptr",
  "hostname": "server01",
  "domain": "example.com",
  "ip_address": "************",
  "ticket": "INC123456"
}
```

#### 5.1.4. Verify with Detailed Output

```json
{
  "operation": "verify",
  "record_type": "a",
  "hostname": "server01",
  "domain": "example.com",
  "ticket": "INC123456",
  "log_level": "Debug"
}
```

### 5.2. Add Operations

#### 5.2.1. Add A Record

```json
{
  "operation": "add",
  "record_type": "a",
  "hostname": "server01",
  "domain": "example.com",
  "ip_address": "************",
  "ticket": "INC123456"
}
```

#### 5.2.2. Add CNAME Record

```json
{
  "operation": "add",
  "record_type": "cname",
  "hostname": "www",
  "domain": "example.com",
  "cname_target": "server01.example.com",
  "ticket": "INC123456"
}
```

#### 5.2.3. Add PTR Record

```json
{
  "operation": "add",
  "record_type": "ptr",
  "hostname": "server01",
  "domain": "example.com",
  "ip_address": "************",
  "ticket": "INC123456"
}
```

#### 5.2.4. Add A Record with Custom TTL and Description

```json
{
  "operation": "add",
  "record_type": "a",
  "hostname": "server01",
  "domain": "example.com",
  "ip_address": "************",
  "ttl": 7200,
  "description": "Web server for marketing department",
  "ticket": "INC123456"
}
```

#### 5.2.5. Add A Record without PTR Management

```json
{
  "operation": "add",
  "record_type": "a",
  "hostname": "server01",
  "domain": "example.com",
  "ip_address": "************",
  "manage_ptr": false,
  "ticket": "INC123456"
}
```

### 5.3. Remove Operations

#### 5.3.1. Remove A Record

```json
{
  "operation": "remove",
  "record_type": "a",
  "hostname": "server01",
  "domain": "example.com",
  "ticket": "INC123456"
}
```

#### 5.3.2. Remove CNAME Record

```json
{
  "operation": "remove",
  "record_type": "cname",
  "hostname": "www",
  "domain": "example.com",
  "ticket": "INC123456"
}
```

#### 5.3.3. Remove PTR Record

```json
{
  "operation": "remove",
  "record_type": "ptr",
  "hostname": "server01",
  "domain": "example.com",
  "ip_address": "************",
  "ticket": "INC123456"
}
```

#### 5.3.4. Force Remove A Record

```json
{
  "operation": "remove",
  "record_type": "a",
  "hostname": "server01",
  "domain": "example.com",
  "force_remove": true,
  "ticket": "INC123456"
}
```

### 5.4. Update Operations

#### 5.4.1. Update A Record

```json
{
  "operation": "update",
  "record_type": "a",
  "hostname": "server01",
  "domain": "example.com",
  "ip_address": "************",
  "ticket": "INC123456"
}
```

#### 5.4.2. Update CNAME Record

```json
{
  "operation": "update",
  "record_type": "cname",
  "hostname": "www",
  "domain": "example.com",
  "cname_target": "server02.example.com",
  "ticket": "INC123456"
}
```

#### 5.4.3. Update PTR Record

```json
{
  "operation": "update",
  "record_type": "ptr",
  "hostname": "server01",
  "domain": "example.com",
  "ip_address": "************",
  "ticket": "INC123456"
}
```

#### 5.4.4. Update A Record with New TTL and Description

```json
{
  "operation": "update",
  "record_type": "a",
  "hostname": "server01",
  "domain": "example.com",
  "ip_address": "************",
  "ttl": 3600,
  "description": "Updated web server",
  "ticket": "INC123456"
}
```

## 6. Advanced Operations

This section provides examples of advanced DNS operations using AAP extra variables format.

### 6.1. Custom TTL

You can specify a custom TTL (Time To Live) value for DNS records. If not specified, the default TTL from the configuration will be used.

```json
{
  "operation": "add",
  "record_type": "a",
  "hostname": "server01",
  "domain": "example.com",
  "ip_address": "************",
  "ttl": 7200,
  "ticket": "INC123456"
}
```

### 6.2. Custom Description

You can add a custom description to DNS records for documentation purposes.

```json
{
  "operation": "add",
  "record_type": "a",
  "hostname": "server01",
  "domain": "example.com",
  "ip_address": "************",
  "description": "Web server for marketing department",
  "ticket": "INC123456"
}
```

### 6.3. Force Remove

You can force the removal of DNS records even if they are referenced by other records or if connectivity checks fail.

```json
{
  "operation": "remove",
  "record_type": "a",
  "hostname": "server01",
  "domain": "example.com",
  "force_remove": true,
  "ticket": "INC123456"
}
```

### 6.4. PTR Management

#### 6.4.1. Disable PTR Management

By default, when adding or updating A records, corresponding PTR records are automatically managed. You can disable this behavior:

```json
{
  "operation": "add",
  "record_type": "a",
  "hostname": "server01",
  "domain": "example.com",
  "ip_address": "************",
  "manage_ptr": false,
  "ticket": "INC123456"
}
```

#### 6.4.2. Explicit PTR Management

You can explicitly manage PTR records independently of A records:

```json
{
  "operation": "add",
  "record_type": "ptr",
  "hostname": "server01",
  "domain": "example.com",
  "ip_address": "************",
  "ticket": "INC123456"
}
```

### 6.5. Email Configuration

#### 6.5.1. Send Emails Only to Test Recipient

You can configure the system to send emails only to the test recipient without enabling full testing mode:

```json
{
  "operation": "add",
  "record_type": "a",
  "hostname": "server01",
  "domain": "example.com",
  "ip_address": "************",
  "email_report": true,
  "send_to_test_only": true,
  "ticket": "INC123456"
}
```

### 6.6. Special PTR Handling

The system supports special PTR handling for specific domains. This is particularly useful for domains with non-standard PTR record requirements.

#### 6.6.1. Special PTR Handling for SES Domain

```json
{
  "operation": "add",
  "record_type": "a",
  "hostname": "ses",
  "domain": "shsu.com.sg",
  "ip_address": "************",
  "special_ptr_handling": true,
  "ticket": "INC123456"
}
```

This will create a PTR record pointing to `shdcvsys22h1.shsu.com.sg` instead of the default `ses.shsu.com.sg`.

#### 6.6.2. Special PTR Handling for SHSES Domain

```json
{
  "operation": "add",
  "record_type": "a",
  "hostname": "shses",
  "domain": "shs.com.sg",
  "ip_address": "************",
  "special_ptr_handling": true,
  "ticket": "INC123456"
}
```

This will create a PTR record pointing to `sesdcvpsys11.shs.com.sg` instead of the default `shses.shs.com.sg`.

#### 6.6.3. Skip Connectivity Check

You can skip the connectivity check before removing records:

```json
{
  "operation": "remove",
  "record_type": "a",
  "hostname": "server01",
  "domain": "example.com",
  "skip_connectivity_check": true,
  "ticket": "INC123456"
}
```

#### 6.6.4. Verify Only Mode

You can run operations in verify-only mode, which will check what changes would be made without actually making them:

```json
{
  "operation": "add",
  "record_type": "a",
  "hostname": "server01",
  "domain": "example.com",
  "ip_address": "************",
  "verify_only": true,
  "ticket": "INC123456"
}
```

## 7. Multi-Domain Operations

This section provides examples of multi-domain DNS operations using AAP extra variables format. Multi-domain operations allow you to perform the same operation across multiple domains in a single job.

### 7.1. Verify Across Domains

#### 7.1.1. Verify A Records Across Multiple Domains

```json
{
  "operation": "verify",
  "record_type": "a",
  "hostnames": "server01,server02",
  "domains": "example.com,example.org",
  "ticket": "INC123456"
}
```

#### 7.1.2. Verify CNAME Records Across Multiple Domains

```json
{
  "operation": "verify",
  "record_type": "cname",
  "hostnames": "www,mail",
  "domains": "example.com,example.org",
  "ticket": "INC123456"
}
```

#### 7.1.3. Verify PTR Records Across Multiple Domains

```json
{
  "operation": "verify",
  "record_type": "ptr",
  "hostnames": "server01,server02",
  "domains": "example.com,example.org",
  "ip_addresses": "************,************",
  "ticket": "INC123456"
}
```

### 7.2. Add Across Domains

#### 7.2.1. Add A Records Across Multiple Domains

```json
{
  "operation": "add",
  "record_type": "a",
  "hostnames": "server01,server02",
  "domains": "example.com,example.org",
  "ip_addresses": "************,************",
  "ticket": "INC123456"
}
```

#### 7.2.2. Add CNAME Records Across Multiple Domains

```json
{
  "operation": "add",
  "record_type": "cname",
  "hostnames": "www,mail",
  "domains": "example.com,example.org",
  "cname_targets": "server01.example.com,server02.example.org",
  "ticket": "INC123456"
}
```

#### 7.2.3. Add A Records with Custom TTL Across Multiple Domains

```json
{
  "operation": "add",
  "record_type": "a",
  "hostnames": "server01,server02",
  "domains": "example.com,example.org",
  "ip_addresses": "************,************",
  "ttl": 7200,
  "ticket": "INC123456"
}
```

### 7.3. Remove Across Domains

#### 7.3.1. Remove A Records Across Multiple Domains

```json
{
  "operation": "remove",
  "record_type": "a",
  "hostnames": "server01,server02",
  "domains": "example.com,example.org",
  "ticket": "INC123456"
}
```

#### 7.3.2. Remove CNAME Records Across Multiple Domains

```json
{
  "operation": "remove",
  "record_type": "cname",
  "hostnames": "www,mail",
  "domains": "example.com,example.org",
  "ticket": "INC123456"
}
```

#### 7.3.3. Force Remove Records Across Multiple Domains

```json
{
  "operation": "remove",
  "record_type": "a",
  "hostnames": "server01,server02",
  "domains": "example.com,example.org",
  "force_remove": true,
  "ticket": "INC123456"
}
```

### 7.4. Update Across Domains

#### 7.4.1. Update A Records Across Multiple Domains

```json
{
  "operation": "update",
  "record_type": "a",
  "hostnames": "server01,server02",
  "domains": "example.com,example.org",
  "ip_addresses": "************,************",
  "ticket": "INC123456"
}
```

#### 7.4.2. Update CNAME Records Across Multiple Domains

```json
{
  "operation": "update",
  "record_type": "cname",
  "hostnames": "www,mail",
  "domains": "example.com,example.org",
  "cname_targets": "server03.example.com,server04.example.org",
  "ticket": "INC123456"
}
```

#### 7.4.3. Asynchronous Multi-Domain Operations

For large multi-domain operations, you can run them asynchronously for better performance:

```json
{
  "operation": "add",
  "record_type": "a",
  "hostnames": "server01,server02,server03,server04,server05",
  "domains": "example.com,example.org,example.net,example.edu,example.io",
  "ip_addresses": "************,************,************,************,************",
  "async_operations": true,
  "max_async_time": 300,
  "ticket": "INC123456"
}
```

### 7.5. Domain Groups

You can perform operations on predefined domain groups instead of listing individual domains.

#### 7.5.1. Verify Records Across a Domain Group

```json
{
  "operation": "verify",
  "record_type": "a",
  "hostname": "server01",
  "domain_group": "production",
  "ticket": "INC123456"
}
```

#### 7.5.2. Add Records Across a Domain Group

```json
{
  "operation": "add",
  "record_type": "a",
  "hostname": "server01",
  "domain_group": "production",
  "ip_address": "************",
  "ticket": "INC123456"
}
```

#### 7.5.3. Remove Records Across a Domain Group

```json
{
  "operation": "remove",
  "record_type": "a",
  "hostname": "server01",
  "domain_group": "production",
  "ticket": "INC123456"
}
```

#### 7.5.4. Update Records Across a Domain Group

```json
{
  "operation": "update",
  "record_type": "a",
  "hostname": "server01",
  "domain_group": "production",
  "ip_address": "************",
  "ticket": "INC123456"
}
```

## 8. Email Notifications

This section provides examples of email notification options using AAP extra variables format.

### 8.1. Enable Email Reporting

```json
{
  "operation": "verify",
  "record_type": "a",
  "hostname": "server01",
  "domain": "example.com",
  "ticket": "INC123456",
  "email_report": true
}
```

### 8.2. Enable Email Logs

```json
{
  "operation": "verify",
  "record_type": "a",
  "hostname": "server01",
  "domain": "example.com",
  "ticket": "INC123456",
  "email_logs": true
}
```

### 8.3. Testing Mode

```json
{
  "operation": "verify",
  "record_type": "a",
  "hostname": "server01",
  "domain": "example.com",
  "ticket": "INC123456",
  "email_report": true,
  "testing_mode": true
}
```

### 8.4. Custom Email Recipient

```json
{
  "operation": "verify",
  "record_type": "a",
  "hostname": "server01",
  "domain": "example.com",
  "ticket": "INC123456",
  "email_report": true,
  "email_recipient": "<EMAIL>"
}
```

## 9. Logging

This section provides information about logging options and conventions.

### 9.1. Set Log Level

You can set the log level in the AAP extra variables:

```json
{
  "operation": "verify",
  "record_type": "a",
  "hostname": "server01",
  "domain": "example.com",
  "ticket": "INC123456",
  "log_level": "Debug"
}
```

### 9.2. Disable Target Server Log Storage

You can disable storing logs on the target server:

```json
{
  "operation": "verify",
  "record_type": "a",
  "hostname": "server01",
  "domain": "example.com",
  "ticket": "INC123456",
  "store_logs_target_server": false
}
```

### 9.3. Log File Locations

Logs are stored in the following locations:

- Ansible logs: `logs/ansible/`
- PowerShell logs: `logs/powershell/`
- Progress logs: `logs/progress/`
- Archive logs: `logs/archive/`

In AAP, you can view logs directly in the job output or download them from the job details page.

### 9.4. Log File Naming Convention

```
<YYYYMMDD>_<TICKET>_<HOSTNAME>_<DOMAIN>_<RECORDTYPE>_<OPERATION>_<LOGTYPE>.log
```

Example:
```
20230501_INC123456_server01_example.com_A_ADD_ANSIBLE.log
```

## 10. Reporting

This section provides information about report generation options.

### 10.1. Generate Report

You can enable report generation in the AAP extra variables:

```json
{
  "operation": "verify",
  "record_type": "a",
  "hostname": "server01",
  "domain": "example.com",
  "ticket": "INC123456",
  "generate_report": true
}
```

### 10.2. Disable Report Generation

You can disable report generation:

```json
{
  "operation": "verify",
  "record_type": "a",
  "hostname": "server01",
  "domain": "example.com",
  "ticket": "INC123456",
  "generate_report": false
}
```

### 10.3. Report File Locations

Reports are stored in the `reports/` directory with the following naming convention:

```
<YYYYMMDD>_<TICKET>_<HOSTNAME>_<DOMAIN>_<RECORDTYPE>_<OPERATION>_STANDARD.html
```

For consolidated reports (multi-domain operations):

```
<YYYYMMDD>_<TICKET>_MULTI_DOMAIN_<RECORDTYPE>_<OPERATION>_CONSOLIDATED.html
```

In AAP, reports can be accessed through the job artifacts or sent via email if email reporting is enabled.

### 10.4. Report Format

Currently, reports are generated in HTML format only. PDF generation is disabled in the current version but may be implemented in a future release.

To view the HTML reports:
1. Download the report from the job artifacts in AAP
2. Open the HTML file in a web browser

### 10.5. Email Reports

You can have reports automatically emailed to specified recipients:

```json
{
  "operation": "verify",
  "record_type": "a",
  "hostname": "server01",
  "domain": "example.com",
  "ticket": "INC123456",
  "generate_report": true,
  "email_report": true,
  "email_recipient": "<EMAIL>"
}
```

## 11. Troubleshooting

This section provides information about common issues and troubleshooting steps when using AAP.

### 11.1. Common Issues in AAP

#### Connection Issues

If you encounter connection issues in AAP, check:
- WinRM configuration on the target server
- Network connectivity between AAP and target servers
- Firewall settings allowing WinRM traffic
- Credentials configured in AAP

#### Permission Issues

If you encounter permission issues, check:
- DNS server permissions for the account used in AAP
- User account permissions in Active Directory
- Domain controller permissions

#### Script Execution Issues

If you encounter script execution issues, check:
- PowerShell execution policy on target servers
- PowerShell module availability on target servers
- Script path configuration in AAP

### 11.2. Debugging in AAP

For detailed debugging information, set the log level to Debug in the extra variables:

```json
{
  "operation": "verify",
  "record_type": "a",
  "hostname": "server01",
  "domain": "example.com",
  "ticket": "INC123456",
  "log_level": "Debug"
}
```

### 11.3. Viewing Job Output in AAP

1. Navigate to the **Jobs** section in AAP
2. Find and click on your job
3. Click on the **Output** tab to view detailed job output
4. Use the search function to find specific error messages

### 11.4. Contacting Support

If you encounter issues that you cannot resolve, contact the CES Operational Excellence Team with the following information:

1. Job ID from AAP
2. Error message
3. Operation details (operation, record type, hostname, domain)
4. Ticket number

## 12. Best Practices

This section provides best practices for using the DNS Management System in AAP.

### 12.1. Always Specify a Ticket Number

Always include a ticket number for auditing and tracking purposes:

```json
{
  "ticket": "INC123456",
  ...
}
```

### 12.2. Verify Before Adding or Removing

Always verify a record before adding or removing it by running a verify operation first:

```json
{
  "operation": "verify",
  ...
}
```

### 12.3. Use Descriptive Hostnames

Use descriptive hostnames that follow your organization's naming convention.

### 12.4. Document Changes

Document all DNS changes in your ticketing system and reference the AAP job ID.

### 12.5. Use Testing Mode for Email Testing

Use testing mode when testing email functionality:

```json
{
  "testing_mode": true,
  ...
}
```

### 12.6. Use Workflow Templates for Complex Operations

For complex operations that involve multiple steps, create workflow templates in AAP instead of running individual jobs.

### 12.7. Implement Approval Workflows

For sensitive DNS changes, implement approval workflows in AAP to require approval before execution.

## 13. Developer Testing

This section is for developers and administrators who need to test the DNS Management System outside of AAP.

### 13.1. Direct Ansible Playbook Execution

Developers can run the playbook directly using the ansible-playbook command:

```bash
ansible-playbook playbooks/manage_dns.yml -e "operation=verify record_type=a hostname=server01 domain=example.com ticket=TEST123"
```

Common operations examples:

```bash
# Verify A record
ansible-playbook playbooks/manage_dns.yml -e "operation=verify record_type=a hostname=server01 domain=example.com ticket=TEST123"

# Add A record
ansible-playbook playbooks/manage_dns.yml -e "operation=add record_type=a hostname=server01 domain=example.com ip_address=************ ticket=TEST123"

# Remove A record
ansible-playbook playbooks/manage_dns.yml -e "operation=remove record_type=a hostname=server01 domain=example.com ticket=TEST123"

# Update A record
ansible-playbook playbooks/manage_dns.yml -e "operation=update record_type=a hostname=server01 domain=example.com ip_address=************ ticket=TEST123"
```

### 13.2. Using the Developer Helper Script

Developers can use the provided helper script for testing:

```bash
./dev/run_dns_management.sh --verify -a hostname=server01 -a domain=example.com -a ticket=TEST123
```

#### 13.2.1. With Different Vault Password Methods

```bash
# Using prompt method
./dev/run_dns_management.sh --verify --vault-method=prompt -a hostname=server01 -a domain=example.com -a ticket=TEST123

# Using file method
./dev/run_dns_management.sh --verify --vault-method=file --vault-file=~/.vault_pass.txt -a hostname=server01 -a domain=example.com -a ticket=TEST123

# Using environment method
./dev/run_dns_management.sh --verify --vault-method=environment -a hostname=server01 -a domain=example.com -a ticket=TEST123
```

#### 13.2.2. Different Operations

```bash
# Add operation
./dev/run_dns_management.sh --add -a hostname=server01 -a domain=example.com -a ip_address=************ -a ticket=TEST123

# Remove operation
./dev/run_dns_management.sh --remove -a hostname=server01 -a domain=example.com -a ticket=TEST123

# Update operation
./dev/run_dns_management.sh --update -a hostname=server01 -a domain=example.com -a ip_address=************ -a ticket=TEST123
```

### 13.3. Running Molecule Tests

The DNS Management System includes Molecule tests for validating functionality. To run these tests:

```bash
cd vmlc-services-dns
molecule test -s default  # Run default scenario
```

### 13.4. Testing Specific Operations with Molecule

You can test specific operations using dedicated Molecule scenarios:

```bash
molecule test -s add      # Test add operation
molecule test -s remove   # Test remove operation
molecule test -s update   # Test update operation
molecule test -s verify   # Test verify operation
```

### 13.5. Testing in Different Environments

You can test in different environments by setting the test_environment variable:

```bash
ansible-playbook playbooks/manage_dns.yml -e "operation=verify record_type=a hostname=server01 domain=test.local ticket=TEST123 test_environment=local"
```

### 13.6. Creating Custom Test Cases

You can create custom test cases by adding them to the dev/vars/testing_config.yml file:

```yaml
test_cases:
  verify:
    - name: "custom_test"
      description: "Custom test case"
      operation: "verify"
      record_type: "a"
      hostname: "custom"
      domain: "example.com"
      expected_result: true
```

### 13.7. Testing Idempotency

To test idempotency, run the same operation twice and verify that the second run reports no changes:

```bash
# First run - should show changed: true
ansible-playbook playbooks/manage_dns.yml -e "operation=add record_type=a hostname=server01 domain=example.com ip_address=************ ticket=TEST123"

# Second run - should show changed: false
ansible-playbook playbooks/manage_dns.yml -e "operation=add record_type=a hostname=server01 domain=example.com ip_address=************ ticket=TEST123"
```

### 13.8. Testing Special PTR Handling

You can test special PTR handling for specific domains:

#### 13.8.1. Special PTR Handling for SES Domain

```bash
# Using ansible-playbook
ansible-playbook playbooks/manage_dns.yml -e "operation=add record_type=a hostname=ses domain=shsu.com.sg ip_address=************ special_ptr_handling=true ticket=TEST123"

# Using developer helper script
./dev/run_dns_management.sh --add -a hostname=ses -a domain=shsu.com.sg -a ip_address=************ -a special_ptr_handling=true -a ticket=TEST123
```

#### 13.8.2. Special PTR Handling for SHSES Domain

```bash
# Using ansible-playbook
ansible-playbook playbooks/manage_dns.yml -e "operation=add record_type=a hostname=shses domain=shs.com.sg ip_address=************ special_ptr_handling=true ticket=TEST123"

# Using developer helper script
./dev/run_dns_management.sh --add -a hostname=shses -a domain=shs.com.sg -a ip_address=************ -a special_ptr_handling=true -a ticket=TEST123
```

