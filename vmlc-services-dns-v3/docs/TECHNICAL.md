# VMLC Services - DNS Management - Technical Documentation

**Author:** CES Operational Excellence Team
**Contributors:** <PERSON> Bin <PERSON> (7409)

## Table of Contents

1. [Architecture](#1-architecture)
2. [Configuration Files](#2-configuration-files)
3. [Playbooks](#3-playbooks)
4. [Roles](#4-roles)
5. [Scripts](#5-scripts)
6. [Templates](#6-templates)
7. [Logging](#7-logging)
8. [Reporting](#8-reporting)
9. [Email Notifications](#9-email-notifications)
10. [<PERSON>rro<PERSON> Handling](#10-error-handling)
11. [Security](#11-security)
12. [Performance](#12-performance)
13. [Extensibility](#13-extensibility)
14. [Testing](#14-testing)

## 1. Architecture

The DNS Management System follows a modular architecture with clear separation of concerns:

### 1.1. Component Diagram

```
+----------------+     +----------------+     +----------------+
|                |     |                |     |                |
|   Playbooks    |---->|     Roles     |---->|    Scripts     |
|                |     |                |     |                |
+----------------+     +----------------+     +----------------+
        |                     |                      |
        v                     v                      v
+----------------+     +----------------+     +----------------+
|                |     |                |     |                |
| Configuration  |     |   Templates    |     |     Logs       |
|                |     |                |     |                |
+----------------+     +----------------+     +----------------+
```

### 1.2. Flow Diagram

```
+----------------+     +----------------+     +----------------+
|                |     |                |     |                |
| Load Config    |---->| Validate Input |---->| Setup Logging  |
|                |     |                |     |                |
+----------------+     +----------------+     +----------------+
        |
        v
+----------------+     +----------------+     +----------------+
|                |     |                |     |                |
| Setup Creds    |---->| Execute Op     |---->| Process Results|
|                |     |                |     |                |
+----------------+     +----------------+     +----------------+
        |
        v
+----------------+     +----------------+     +----------------+
|                |     |                |     |                |
| Generate Report|---->| Send Email     |---->| Upload Logs    |
|                |     |                |     |                |
+----------------+     +----------------+     +----------------+
        |
        v
+----------------+     +----------------+
|                |     |                |
| Cleanup        |---->| Display Summary|
|                |     |                |
+----------------+     +----------------+
```

## 2. Configuration Files

### 2.1. Unified Domain Configuration (`domains.yml`)

This file contains all non-sensitive domain-related configuration, including:

- Domain metadata (environment, network zone, data center)
- ADMT server information
- DNS server information
- PTR DNS server information
- Domain relationships
- Domain groups
- Domain keys for validation
- Default DNS settings

#### Structure

```yaml
# Domain groups
domain_groups:
  production:
    - domain1.com
    - domain2.com
  staging:
    - dev.domain1.com
    - dev.domain2.com

# Centralized DNS server configuration
dns_servers:
  domain1.com: dns.domain1.com
  domain2.com: dns.domain2.com

# Centralized PTR DNS server configuration
ptr_dns_servers:
  domain1.com: dns.domain1.com
  domain2.com: dns.domain2.com

# Domain configuration
domains:
  domain1.com:
    description: "Domain 1"
    environment: prd
    network_zone: mgt
    dc: hdc1
    admt_server: admt.domain1.com
    related_domains:
      - domain2.com
```

### 2.2. Domain Credentials (`domain_credentials.yml`)

This file contains all sensitive domain credential information, including:

- DNS username variables for each domain
- DNS password variables for each domain

#### Structure

```yaml
# Domain credentials
domain_credentials:
  domain1.com:
    dns_username: "{{ var_dns_domain1_username }}"
    dns_password: "{{ var_dns_domain1_password }}"
  domain2.com:
    dns_username: "{{ var_dns_domain2_username }}"
    dns_password: "{{ var_dns_domain2_password }}"
```

### 2.3. Credentials Configuration (`credentials_config.yml`)

This file contains all credential-related configuration, including:

- Credential selection strategy
- Credential storage method
- Credential security settings

#### Structure

```yaml
# Credential selection mechanism
credential_selection:
  strategy: "domain"  # Options: domain, environment, manual

  # No default credentials - explicit credentials required for each domain

# Credential storage
credential_storage:
  method: "ansible_vault"
  vault_file: "{{ playbook_dir }}/../vars/vault.yml"

# Credential security
credential_security:
  mask_credentials: true
  use_no_log: true
  clear_after_use: true
```

### 2.4. Email Configuration (`email_config.yml`)

This file contains all email-related configuration, including:

- SMTP server configuration
- Email recipients
- Email templates
- Attachment settings
- Email flags

#### Structure

```yaml
# Email settings
email_settings:
  smtp:
    port: 25
    from: "<EMAIL>"
    dc_servers:
      hdc1: "asmtp.hcloud.healthgrp.com.sg"
      hdc2: "fsmtp.hcloud.healthgrp.com.sg"
    default_dc: "hdc1"  # Default DC to use if no matching DC is found

  recipients:
    domain_specific:
      domain1.com: "<EMAIL>"
      domain2.com: "<EMAIL>"
      default: "<EMAIL>"
    testing: "<EMAIL>"
    bcc: "<EMAIL>"

  templates:
    directory: "{{ playbook_dir }}/../templates/emails"
    subjects:
      report: "DNS Report - {operation} {record_type} Record"
```

### 2.5. Log Configuration (`log_config.yml`)

This file contains all log-related configuration, including:

- Log directories
- Log file naming format
- Log types
- Log levels
- Target server log paths
- Log rotation settings
- Log format strings
- Log flags

#### Structure

```yaml
# Log settings
log_settings:
  directories:
    ansible: "{{ playbook_dir }}/../logs/ansible"
    powershell: "{{ playbook_dir }}/../logs/powershell"
    progress: "{{ playbook_dir }}/../logs/progress"
    archive: "{{ playbook_dir }}/../logs/archive"

  format:
    date_format: "%Y%m%d"
    separator: "_"
    extension: ".log"

  types:
    ansible: "ANSIBLE"
    powershell: "POWERSHELL"
    progress: "PROGRESS"
    report: "REPORT"
```

### 2.6. Operations Configuration (`operations_config.yml`)

This file contains all operation-related configuration, including:

- Operation definitions
- Required parameters
- Validation rules
- Record type definitions
- Multi-domain operation settings
- Operation defaults

#### Structure

```yaml
# Operation definitions
operations:
  add:
    description: "Add a DNS record"
    required_params:
      all: ["hostname", "domain", "ticket"]
      a: ["ip_address"]
      ptr: ["ip_address"]
      cname: ["cname_target"]
    validation:
      check_exists: true
      error_if_exists: true
```

### 2.7. PowerShell Configuration (`powershell_config.yml`)

This file contains all PowerShell-related configuration, including:

- Script paths
- Default parameters
- Log paths
- Error handling settings
- Timeouts
- Special configurations
- PowerShell flags

#### Structure

```yaml
# PowerShell settings
powershell_settings:
  scripts:
    dns_management: "{{ playbook_dir }}/../scripts/set-dns.ps1"
    connectivity_test: "{{ playbook_dir }}/../scripts/test-connectivity.ps1"

  defaults:
    ttl: 3600
    description: "Managed by Ansible DNS Management"
    log_level: "Info"
```

### 2.8. Report Configuration (`report_config.yml`)

This file contains all report-related configuration, including:

- Report directories
- Report file naming format
- Report types
- Report templates
- Report content
- Report flags
- Future PDF generation options (currently disabled)

#### Structure

```yaml
# Report settings
report_settings:
  directories:
    reports: "{{ playbook_dir }}/../reports"
    templates: "{{ playbook_dir }}/../templates/reports"

  format:
    date_format: "%Y%m%d"
    separator: "_"
    extension: ".html"  # Currently only HTML reports are generated

  types:
    standard: "STANDARD"
    consolidated: "CONSOLIDATED"

# Report configuration flags
report_flags:
  # Whether to generate reports
  generate_report: true

  # Whether to generate PDF reports (currently disabled)
  generate_pdf: false

  # Future PDF generation options (not currently used)
  pdf_generation:
    # Method to use for PDF generation (options will be: weasyprint, libreoffice, chrome, api)
    method: "none"
```

### 2.9. Testing Configuration (`dev/vars/testing_config.yml`)

This file contains all testing-related configuration, including:

- Test environments
- Test cases for different operations
- Molecule configuration
- Test platforms
- Test sequence

#### Structure

```yaml
# Test environments
test_environments:
  local:
    description: "Local testing environment"
    dns_server: "localhost"
    admt_server: "localhost"
    domains:
      - test.local
      - example.test
    credentials:
      username: "testuser"
      password: "testpassword"

# Test cases
test_cases:
  verify:
    - name: "verify_existing_a_record"
      description: "Verify an existing A record"
      operation: "verify"
      record_type: "a"
      hostname: "server01"
      domain: "test.local"
      expected_result: true

# Molecule configuration
molecule:
  driver: docker
  platforms:
    - name: ubuntu-20.04
      image: geerlingguy/docker-ubuntu2004-ansible
      pre_build_image: true
```

## 3. Playbooks

### 3.1. Main Playbook (`manage_dns.yml`)

The main playbook orchestrates the entire DNS management process:

1. Loads configuration
2. Validates domain and operation parameters
3. Sets up logging
4. Sets up credentials
5. Executes DNS operations
6. Processes results
7. Generates reports
8. Sends emails
9. Uploads logs
10. Cleans up
11. Displays summary

#### Structure

```yaml
- name: DNS Management Operations
  hosts: localhost
  gather_facts: true

  vars_files:
    - "../vars/defaults.yml"
    - "../vars/domains.yml"
    - "../vars/domain_credentials.yml"
    # Other configuration files

  pre_tasks:
    - name: Load configuration
      ansible.builtin.include_tasks: "{{ roles_path }}/common/tasks/load_configuration.yml"

    # Other pre-tasks

  tasks:
    - name: Execute DNS operations
      block:
        - name: Execute single domain operation
          ansible.builtin.include_role:
            name: "{{ roles_path }}/dns_operations"
            tasks_from: "{{ operation }}.yml"
          when: not is_multi_domain

        # Multi-domain operation
      rescue:
        - name: Handle operation errors
          ansible.builtin.include_tasks: "{{ roles_path }}/common/tasks/handle_errors.yml"

    # Other tasks

  post_tasks:
    # Clean up and display summary
```

## 4. Roles

### 4.1. Common Role

The common role contains shared tasks used across the system:

- `load_configuration.yml`: Loads and validates configuration
- `validate_domain.yml`: Validates domain configuration
- `setup_logging.yml`: Sets up logging
- `validate_operation.yml`: Validates operation parameters
- `setup_credentials.yml`: Sets up credentials
- `initialize_results.yml`: Initializes result variables
- `handle_errors.yml`: Handles operation errors
- `process_results.yml`: Processes operation results
- `cleanup.yml`: Cleans up after operations
- `display_summary.yml`: Displays operation summary

### 4.2. DNS Operations Role

The DNS operations role contains tasks for performing DNS operations:

- `add.yml`: Adds DNS records
- `remove.yml`: Removes DNS records
- `update.yml`: Updates DNS records
- `verify.yml`: Verifies DNS records
- `main.yml`: Main task file for multi-domain operations
- `process_domain_async.yml`: Processes domains asynchronously

### 4.3. Reporting Role

The reporting role contains tasks for generating reports and sending emails:

- `generate_report.yml`: Generates HTML operation reports
- `generate_consolidated_report.yml`: Generates HTML consolidated reports
- `generate_pdf.yml`: Placeholder for future PDF generation implementation
- `email_report.yml`: Sends report emails with HTML attachments
- `email_logs.yml`: Sends log emails
- `upload_logs_to_target_server.yml`: Uploads logs to target server

Note: PDF generation is currently disabled. Only HTML reports are generated. The system is designed to allow for easy implementation of PDF generation in the future.

## 5. Scripts

### 5.1. DNS Management Script (`set-dns.ps1`)

The main PowerShell script for DNS operations:

- Supports A, PTR, and CNAME records
- Handles add, remove, update, and verify operations
- Manages corresponding PTR records
- Provides detailed logging
- Returns results in JSON format

### 5.2. Connectivity Test Script (`test-connectivity.ps1`)

A PowerShell script for testing connectivity:

- Tests network connectivity to hosts
- Verifies DNS resolution
- Checks port availability
- Returns results in JSON format

## 6. Templates

### 6.1. Report Templates

- `dns_report.html.j2`: Template for standard DNS operation reports
- `consolidated_report.html.j2`: Template for consolidated reports

### 6.2. Email Templates

- `report_email_body.j2`: Template for report email bodies
- `consolidated_report_email_body.j2`: Template for consolidated report email bodies
- `logs_email_body.j2`: Template for logs email bodies

### 6.3. Role Templates

#### 6.3.1. DNS Operations Templates

- `dns_operation_summary.j2`: Template for DNS operation summary
- `powershell_params.j2`: Template for PowerShell script parameters

#### 6.3.2. Reporting Templates

- `email_params.j2`: Template for email parameters
- `report_summary.j2`: Template for report summary

## 7. Logging

### 7.1. Log Types

- **Ansible Logs**: Logs from Ansible tasks
- **PowerShell Logs**: Logs from PowerShell scripts
- **Progress Logs**: Logs tracking operation progress
- **Report Logs**: Logs related to report generation

### 7.2. Log Levels

- **Error**: Critical errors that prevent operation completion
- **Warning**: Non-critical issues that may affect operation
- **Info**: General information about operation progress
- **Debug**: Detailed information for troubleshooting

### 7.3. Log File Naming Convention

```
<YYYYMMDD>_<TICKET>_<HOSTNAME>_<DOMAIN>_<RECORDTYPE>_<OPERATION>_<LOGTYPE>.log
```

### 7.4. Log Rotation

Logs are rotated based on:

- Age: Logs older than `max_age` days are archived
- Size: Logs larger than `max_size` MB are archived
- Count: Only `max_files` log files are kept

## 8. Reporting

### 8.1. Report Types

- **Standard Reports**: Reports for single-domain operations
- **Consolidated Reports**: Reports for multi-domain operations

### 8.2. Report Sections

- **Operation Details**: Information about the operation
- **Result Summary**: Summary of operation results
- **Record Details**: Details of the DNS record
- **Execution Information**: Information about execution environment

### 8.3. Report Generation

Reports are generated using:

1. Jinja2 templates for HTML generation
2. WeasyPrint for PDF conversion

## 9. Email Notifications

### 9.1. Email Types

- **Report Emails**: Emails with operation reports
- **Log Emails**: Emails with operation logs

### 9.2. Dynamic Recipient Selection

Recipients are selected based on:

- Domain: Domain-specific recipients (e.g., shses.shs.com.<NAME_EMAIL>)
- Default: Default recipient for domains without specific configuration
- Testing Mode: Testing recipient if testing mode is enabled

### 9.3. SMTP Server Selection

SMTP servers are selected based on:

- Data Center: DC-specific SMTP servers (e.g., hdc1 uses asmtp.hcloud.healthgrp.com.sg)
- Default DC: If no matching DC is found, the system uses the SMTP server from the default DC (configured as hdc1)

## 10. Error Handling

### 10.1. Error Types

- **Validation Errors**: Errors in input validation
- **Connection Errors**: Errors connecting to servers
- **Operation Errors**: Errors during DNS operations
- **Script Errors**: Errors in PowerShell scripts

### 10.2. Error Recovery

The system attempts to recover from errors by:

- Retrying operations with exponential backoff
- Falling back to alternative servers
- Providing detailed error information for manual recovery

### 10.3. Error Reporting

Errors are reported through:

- Console output
- Log files
- Email notifications
- Operation reports

## 11. Security

### 11.1. Credential Management

Credentials are managed securely through:

- Ansible Vault for encrypted storage
- No-log for sensitive tasks
- Clearing credentials after use

### 11.2. Authentication

Authentication is handled through:

- WinRM for Windows servers
- Domain-specific credentials
- Least privilege principle

### 11.3. Authorization

Authorization is enforced through:

- Role-based access control
- Domain-specific permissions
- Operation-specific permissions

## 12. Performance

### 12.1. Parallel Execution

Multi-domain operations are executed in parallel:

- Asynchronous task execution
- Configurable maximum parallel operations
- Timeout handling

### 12.2. Caching

The system uses caching to improve performance:

- DNS record caching
- Server information caching
- Configuration caching

### 12.3. Optimization

Performance is optimized through:

- Minimizing network calls
- Efficient PowerShell scripts
- Selective logging

## 13. Extensibility

### 13.1. Adding New Operations

To add a new operation:

1. Add operation definition to `operations_config.yml`
2. Create operation task file in `dns_operations/tasks/`
3. Update PowerShell script to support the operation
4. Update documentation

### 13.2. Adding New Record Types

To add a new record type:

1. Add record type definition to `operations_config.yml`
2. Update PowerShell script to support the record type
3. Update operation task files
4. Update documentation

### 13.3. Adding New Domains

To add a new domain:

1. Add domain configuration to `domain_config.yml`
2. Add domain credentials to `credentials_config.yml`
3. Update domain groups if needed
4. Test domain configuration

## 14. Testing

### 14.1. Test Environments

The system supports multiple test environments:

- **Local**: For local development and testing
- **Development**: For development environment testing
- **Staging**: For staging environment testing

Each environment has its own configuration, including DNS servers, ADMT servers, domains, and credentials.

### 14.2. Test Cases

Test cases are defined for each operation type:

- **Verify**: Test cases for verifying DNS records
- **Add**: Test cases for adding DNS records
- **Remove**: Test cases for removing DNS records
- **Update**: Test cases for updating DNS records

Each test case includes operation parameters and expected results.

### 14.3. Molecule Testing

Molecule is used for testing the Ansible roles:

- **Default Scenario**: Tests the basic functionality
- **Operation-Specific Scenarios**: Tests specific operations (add, remove, update, verify)

### 14.4. Running Tests

To run tests using Molecule:

```bash
cd vmlc-services-dns
molecule test -s default  # Run default scenario
molecule test -s add      # Run add operation scenario
molecule test -s remove   # Run remove operation scenario
molecule test -s update   # Run update operation scenario
molecule test -s verify   # Run verify operation scenario
```

### 14.5. Test Reports

Test results are reported in the console output and can be configured to generate HTML reports using the Molecule verifier.
