# DNS Management System

**Author:** CES Operational Excellence Team
**Contributors:** <PERSON> (7409)

## Overview

The DNS Management System is an enterprise-grade automation framework for managing DNS records across multiple domains. It provides a standardized approach to DNS operations with proper access controls, audit trails, and integration with other systems.

## Features

- Support for A, CNAME, and PTR records
- Multi-domain operations
- Automatic PTR record management
- Special PTR handling for specific domains
- Comprehensive logging and reporting
- Email notifications
- Integration with Jira and Bitbucket
- Rollback capabilities
- Idempotent operations

## Six-Phase Lifecycle

The DNS Management System follows a six-phase lifecycle for all operations:

1. **Configuration**: Merge survey-driven overrides with secrets (Vault/CyberArk) → single run_config.json
2. **Loading**: Select static vs. dynamic inventory (with cache TTL)
3. **Execution**: Invoke playbooks via Job Templates (site, canary, full, rollback)
4. **Error Handling**: Callback plugin captures, enriches, retries or triggers rollback
5. **Reporting**: Emit JSON events → AAP Artifacts → auto-attach to Jira + Bitbucket logs
6. **Cleanup**: Teardown hooks + scheduled cleanup jobs for leftovers

## Project Structure

```
ansible-project/
├─ ansible.cfg, requirements.yml, bitbucket-pipelines.yml
├─ inventories/{dev,prod,dynamic}/
├─ group_vars/, host_vars/
├─ playbooks/ (site.yml, canary.yml, full.yml, rollback.yml)
├─ roles/ (common, dns_operations, reporting, monitoring)
├─ collections/synapxe.dns/plugins/callback/
├─ scripts/bootstrap.py, inventory.py
├─ tests/ (schema/, molecule/)
└─ docs/ (runbooks/, architecture.md)
```

## Getting Started

### Prerequisites

- Ansible Automation Platform (AAP) 2.0 or higher
- Python 3.6 or higher
- PowerShell 5.1 or higher on target servers
- DnsServer PowerShell module installed on target servers
- WinRM configured on target servers

### Installation

1. Clone this repository to your Ansible control node
2. Install required dependencies:
   ```bash
   ansible-galaxy install -r requirements.yml
   ```
3. Configure your inventory
4. Configure your credentials
5. Run the playbook:
   ```bash
   ansible-playbook playbooks/site.yml
   ```

### Using AAP

1. Import the project into AAP
2. Create credential objects for DNS servers
3. Create job templates for different operations
4. Configure surveys for parameter input
5. Set up notification templates for alerts
6. Create workflow templates for complex operations

## Documentation

- [Architecture](docs/architecture.md)
- [Usage Guide](docs/USAGE.md)
- [Technical Documentation](docs/TECHNICAL.md)
- [Process Flows](docs/PROCESS_FLOWS.md)
- [Runbooks](docs/runbooks/)

## Development

### Setting Up Development Environment

1. Clone this repository
2. Install development dependencies:
   ```bash
   pip install -r dev-requirements.txt
   ```
3. Run linting:
   ```bash
   ansible-lint
   yamllint .
   ```
4. Run tests:
   ```bash
   cd tests/molecule
   molecule test -s default
   ```

### Contribution Guidelines

1. Create a feature branch from `develop` with the Jira ticket ID (e.g. `feature/PROJ-1234-add-new-feature`)
2. Make your changes
3. Run linting and tests
4. Submit a pull request to `develop`
5. Ensure the pull request passes all CI checks
6. Get approval from at least one reviewer
7. Merge the pull request

## License

This project is licensed under the GNU General Public License v3.0 - see the LICENSE file for details.

## Support

For support, please contact the CES Operational Excellence Team.
