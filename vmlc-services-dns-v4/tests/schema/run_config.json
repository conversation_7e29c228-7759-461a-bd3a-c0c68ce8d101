{"$schema": "http://json-schema.org/draft-07/schema#", "title": "DNS Management Run Configuration", "description": "Schema for DNS Management run_config.json", "type": "object", "required": ["operation", "record_type", "ticket"], "properties": {"generated_at": {"type": "string", "format": "date-time", "description": "Timestamp when the configuration was generated"}, "job_id": {"type": "string", "description": "AAP job ID or 'local' for local execution"}, "operation": {"type": "string", "enum": ["verify", "add", "remove", "update"], "description": "DNS operation to perform"}, "record_type": {"type": "string", "enum": ["a", "cname", "ptr"], "description": "DNS record type"}, "hostname": {"type": "string", "description": "Hostname for the DNS record"}, "domain": {"type": "string", "description": "Domain for the DNS record"}, "domains": {"type": "string", "description": "Comma-separated list of domains for multi-domain operations"}, "ip_address": {"type": "string", "format": "ipv4", "description": "IP address for A or PTR records"}, "cname_target": {"type": "string", "description": "Target hostname for CNAME records"}, "ticket": {"type": "string", "description": "Ticket number for tracking"}, "ttl": {"type": "integer", "minimum": 0, "description": "Time to live in seconds"}, "description": {"type": "string", "description": "Description for the DNS record"}, "manage_ptr": {"type": "boolean", "description": "Whether to manage PTR records for A records"}, "force_remove": {"type": "boolean", "description": "Whether to force removal of records"}, "email_report": {"type": "boolean", "description": "Whether to send an email report"}, "email_logs": {"type": "boolean", "description": "Whether to email logs"}, "email_recipient": {"type": "string", "description": "Email recipient for the report"}, "testing_mode": {"type": "boolean", "description": "Whether to use testing mode for emails"}, "log_level": {"type": "string", "enum": ["Debug", "Info", "Warning", "Error"], "description": "Log level for operations"}, "store_logs_target_server": {"type": "boolean", "description": "Whether to store logs on target server"}, "generate_report": {"type": "boolean", "description": "Whether to generate a report"}, "is_multi_domain": {"type": "boolean", "description": "Whether this is a multi-domain operation"}, "async_operations": {"type": "boolean", "description": "Run multi-domain operations asynchronously"}, "credentials": {"type": "object", "description": "Credentials for DNS servers", "additionalProperties": true}}, "allOf": [{"if": {"properties": {"operation": {"enum": ["verify"]}}}, "then": {"anyOf": [{"required": ["hostname", "domain"]}, {"required": ["domains"]}]}}, {"if": {"properties": {"operation": {"enum": ["add", "update"]}, "record_type": {"enum": ["a", "ptr"]}}}, "then": {"anyOf": [{"required": ["hostname", "domain", "ip_address"]}, {"required": ["domains", "ip_addresses"]}]}}, {"if": {"properties": {"operation": {"enum": ["add", "update"]}, "record_type": {"enum": ["cname"]}}}, "then": {"anyOf": [{"required": ["hostname", "domain", "cname_target"]}, {"required": ["domains", "cname_targets"]}]}}, {"if": {"properties": {"operation": {"enum": ["remove"]}}}, "then": {"anyOf": [{"required": ["hostname", "domain"]}, {"required": ["domains"]}]}}]}