# Molecule Tests for DNS Management System

This directory contains Molecule tests for the DNS Management System. We use a hybrid approach to testing:

1. **Role-Specific Tests**: Located in each role's `molecule/` directory, these tests focus on the functionality of individual roles in isolation.
2. **Centralized Integration Tests**: Located in this directory, these tests focus on the integration between roles and end-to-end workflows.

## Role-Specific Tests

### Common Role Tests
- **Default**: Tests the basic functionality of the common role, including validation, credential setup, and logging.

### DNS Operations Role Tests
- **Default**: Tests the basic functionality of the dns_operations role.
- **Verify**: Tests the verify operation specifically.
- **Add**: Tests the add operation specifically.
- **Remove**: Tests the remove operation specifically.
- **Update**: Tests the update operation specifically.

### Reporting Role Tests
- **Default**: Tests the basic functionality of the reporting role, including report generation and email notifications.

## Centralized Integration Tests

- **Default**: Tests the basic functionality of the entire system.
- **Integration**: Tests the integration between all roles in a complete workflow.
- **Multi-Domain**: Tests operations across multiple domains.

## Running the Tests

### Running Role-Specific Tests

```bash
# Run tests for the common role
cd roles/common
molecule test -s default

# Run tests for the dns_operations role
cd roles/dns_operations
molecule test -s default
molecule test -s verify
molecule test -s add

# Run tests for the reporting role
cd roles/reporting
molecule test -s default
```

### Running Centralized Integration Tests

```bash
# Run the default integration test
cd tests/molecule
molecule test -s default

# Run the multi-domain test
cd tests/molecule
molecule test -s multi_domain
```

## Test Coverage

Our tests cover:

1. **Functionality**: Ensuring that each operation (verify, add, remove, update) works correctly.
2. **Integration**: Ensuring that all components work together properly.
3. **Edge Cases**: Testing special scenarios like multi-domain operations.
4. **Idempotency**: Ensuring that operations are idempotent (running the same operation twice produces the same result).

## Adding New Tests

When adding new functionality:

1. Add role-specific tests to test the functionality in isolation.
2. Update or add centralized tests to ensure proper integration.
3. Ensure all tests pass before submitting a pull request.

## CI/CD Integration

These tests are automatically run in our CI/CD pipeline:

- Role-specific tests are run when changes are made to that specific role.
- Centralized integration tests are run for all changes.
- All tests must pass before a pull request can be merged.
