---
- name: Converge
  hosts: all
  gather_facts: true
  
  vars:
    operation: verify
    record_type: a
    hostname: test
    domain: test.local
    ticket: TEST123
  
  tasks:
    - name: Generate run_config.json
      ansible.builtin.copy:
        content: |
          {
            "generated_at": "{{ ansible_date_time.iso8601 }}",
            "job_id": "molecule-integration",
            "operation": "{{ operation }}",
            "record_type": "{{ record_type }}",
            "hostname": "{{ hostname }}",
            "domain": "{{ domain }}",
            "ticket": "{{ ticket }}",
            "ttl": 3600,
            "description": "Test record",
            "manage_ptr": true,
            "force_remove": false,
            "email_report": false,
            "email_logs": false,
            "testing_mode": true,
            "log_level": "Debug",
            "store_logs_target_server": false,
            "generate_report": true,
            "is_multi_domain": false,
            "async_operations": false,
            "credentials": {
              "domains": {
                "test.local": {
                  "dns_username": "administrator",
                  "dns_password": "Password123!"
                }
              }
            }
          }
        dest: "{{ playbook_dir }}/../../../run_config.json"
      delegate_to: localhost
    
    - name: Create necessary directories
      ansible.builtin.file:
        path: "{{ playbook_dir }}/../../../{{ item }}"
        state: directory
        mode: '0755'
      loop:
        - logs/ansible
        - logs/powershell
        - logs/progress
        - logs/archive
        - reports
        - templates/reports
        - templates/emails
      delegate_to: localhost
    
    - name: Create mock PowerShell script
      ansible.builtin.copy:
        content: |
          #!/bin/bash
          # Mock PowerShell script for DNS operations
          
          # Extract parameters
          OPERATION=$1
          RECORD_TYPE=$2
          HOSTNAME=$3
          DOMAIN=$4
          IP_ADDRESS=$5
          
          # Create JSON result
          echo '{
            "success": true,
            "message": "A record test.test.local exists with IP address ************",
            "changed": false,
            "timestamp": "2023-05-01T12:00:00",
            "record": {
              "domain": "test.local",
              "hostname": "test",
              "type": "a",
              "fqdn": "test.test.local",
              "ip_address": "************",
              "ttl": 3600,
              "description": "Test record"
            },
            "operation": "verify"
          }'
        dest: "{{ playbook_dir }}/../../../scripts/set-dns.ps1"
        mode: '0755'
      delegate_to: localhost
    
    - name: Create mock connectivity test script
      ansible.builtin.copy:
        content: |
          #!/bin/bash
          # Mock connectivity test script
          
          # Extract parameters
          IP_ADDRESS=$1
          
          # Create JSON result
          echo '{
            "success": true,
            "message": "Host is reachable",
            "ip_address": "************",
            "response_time_ms": 10
          }'
        dest: "{{ playbook_dir }}/../../../scripts/test-connectivity.ps1"
        mode: '0755'
      delegate_to: localhost
    
    - name: Create mock report template
      ansible.builtin.copy:
        content: |
          <!DOCTYPE html>
          <html>
          <head>
            <title>DNS Operation Report</title>
          </head>
          <body>
            <h1>DNS Operation Report</h1>
            <p>Operation: {{ operation }}</p>
            <p>Record Type: {{ record_type }}</p>
            <p>Hostname: {{ hostname }}</p>
            <p>Domain: {{ domain }}</p>
            <p>Result: {{ dns_operation_result.success }}</p>
            <p>Message: {{ dns_operation_result.message }}</p>
          </body>
          </html>
        dest: "{{ playbook_dir }}/../../../templates/reports/dns_report.html.j2"
        mode: '0644'
      delegate_to: localhost
    
    - name: Create mock email templates
      ansible.builtin.copy:
        content: |
          DNS Operation Report
          
          Operation: {{ operation }}
          Record Type: {{ record_type }}
          Hostname: {{ hostname }}
          Domain: {{ domain }}
          Result: {{ dns_operation_result.success }}
          Message: {{ dns_operation_result.message }}
        dest: "{{ playbook_dir }}/../../../templates/emails/{{ item }}"
        mode: '0644'
      loop:
        - report_email_body.j2
        - logs_email_body.j2
        - consolidated_report_email_body.j2
      delegate_to: localhost
    
    - name: Include site playbook
      ansible.builtin.include_playbook: "{{ playbook_dir }}/../../../playbooks/site.yml"
