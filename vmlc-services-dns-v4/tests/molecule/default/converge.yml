---
- name: Converge
  hosts: all
  gather_facts: true
  
  vars:
    operation: verify
    record_type: a
    hostname: test
    domain: test.local
    ticket: TEST123
  
  tasks:
    - name: Generate run_config.json
      ansible.builtin.copy:
        content: |
          {
            "generated_at": "{{ ansible_date_time.iso8601 }}",
            "job_id": "molecule",
            "operation": "{{ operation }}",
            "record_type": "{{ record_type }}",
            "hostname": "{{ hostname }}",
            "domain": "{{ domain }}",
            "ticket": "{{ ticket }}",
            "ttl": 3600,
            "description": "Test record",
            "manage_ptr": true,
            "force_remove": false,
            "email_report": false,
            "email_logs": false,
            "testing_mode": true,
            "log_level": "Debug",
            "store_logs_target_server": false,
            "generate_report": true,
            "is_multi_domain": false,
            "async_operations": false,
            "credentials": {
              "domains": {
                "test.local": {
                  "dns_username": "administrator",
                  "dns_password": "Password123!"
                }
              }
            }
          }
        dest: "{{ playbook_dir }}/../../../run_config.json"
      delegate_to: localhost
    
    - name: Create mock DNS server script
      ansible.builtin.copy:
        content: |
          #!/bin/bash
          echo "Mock DNS server script"
          echo "Operation: $1"
          echo "Record Type: $2"
          echo "Hostname: $3"
          echo "Domain: $4"
          echo "IP Address: $5"
          echo "TTL: $6"
          echo "Description: $7"
          echo "Success: true"
        dest: /tmp/mock-dns-server.sh
        mode: '0755'
    
    - name: Include site playbook
      ansible.builtin.include_playbook: "{{ playbook_dir }}/../../../playbooks/site.yml"
