---
- name: Verify
  hosts: all
  gather_facts: false
  
  tasks:
    - name: Check if run_config.json exists
      ansible.builtin.stat:
        path: "{{ playbook_dir }}/../../../run_config.json"
      register: run_config_stat
      delegate_to: localhost
    
    - name: Verify run_config.json exists
      ansible.builtin.assert:
        that:
          - run_config_stat.stat.exists
        fail_msg: "run_config.json does not exist"
        success_msg: "run_config.json exists"
    
    - name: Check if logs directory exists
      ansible.builtin.stat:
        path: "{{ playbook_dir }}/../../../logs"
      register: logs_stat
      delegate_to: localhost
    
    - name: Verify logs directory exists
      ansible.builtin.assert:
        that:
          - logs_stat.stat.exists
          - logs_stat.stat.isdir
        fail_msg: "logs directory does not exist"
        success_msg: "logs directory exists"
    
    - name: Check if reports directory exists
      ansible.builtin.stat:
        path: "{{ playbook_dir }}/../../../reports"
      register: reports_stat
      delegate_to: localhost
    
    - name: Verify reports directory exists
      ansible.builtin.assert:
        that:
          - reports_stat.stat.exists
          - reports_stat.stat.isdir
        fail_msg: "reports directory does not exist"
        success_msg: "reports directory exists"
