---
dependency:
  name: galaxy
driver:
  name: docker
platforms:
  - name: instance
    image: geerlingguy/docker-ubuntu2004-ansible
    pre_build_image: true
    privileged: true
    volumes:
      - /sys/fs/cgroup:/sys/fs/cgroup:ro
    command: "/lib/systemd/systemd"
provisioner:
  name: ansible
  env:
    ANSIBLE_ROLES_PATH: ../../roles
  playbooks:
    converge: converge.yml
    verify: verify.yml
verifier:
  name: ansible
scenario:
  name: default
  test_sequence:
    - dependency
    - lint
    - cleanup
    - destroy
    - syntax
    - create
    - prepare
    - converge
    - idempotence
    - verify
    - cleanup
    - destroy
