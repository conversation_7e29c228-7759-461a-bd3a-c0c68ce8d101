---
- name: Verify
  hosts: all
  gather_facts: false
  
  tasks:
    - name: Check for consolidated report file
      ansible.builtin.find:
        paths: "{{ playbook_dir }}/../../../reports"
        patterns: "*CONSOLIDATED.html"
      register: consolidated_reports
      delegate_to: localhost
    
    - name: Verify consolidated report exists
      ansible.builtin.assert:
        that:
          - consolidated_reports.files | length > 0
        fail_msg: "No consolidated report found"
        success_msg: "Consolidated report found"
    
    - name: Check consolidated report content
      ansible.builtin.command:
        cmd: "cat {{ consolidated_reports.files[0].path }}"
      register: report_content
      changed_when: false
      delegate_to: localhost
    
    - name: Verify consolidated report content
      ansible.builtin.assert:
        that:
          - "'DNS Consolidated Report' in report_content.stdout"
          - "'Operation: verify' in report_content.stdout"
          - "'Record Type: a' in report_content.stdout"
          - "'test.local' in report_content.stdout"
          - "'example.com' in report_content.stdout"
        fail_msg: "Consolidated report content is incorrect"
        success_msg: "Consolidated report content is correct"
