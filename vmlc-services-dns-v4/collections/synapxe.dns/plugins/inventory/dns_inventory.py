#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
Ansible inventory plugin for DNS Management.
Author: CES Operational Excellence Team
Contributors: <PERSON> (7409)

This plugin generates a dynamic inventory for DNS Management operations.
It reads domain configuration and creates inventory groups for DNS servers,
ADMT servers, and domain groups.
"""

from __future__ import (absolute_import, division, print_function)
__metaclass__ = type

import os
import yaml
from datetime import datetime, timedelta

from ansible.errors import AnsibleParserError
from ansible.plugins.inventory import BaseInventoryPlugin, Cacheable, Constructable

DOCUMENTATION = '''
    name: dns_inventory
    plugin_type: inventory
    short_description: DNS Management inventory source
    description:
        - Generates inventory for DNS Management operations
        - Uses domain configuration to create inventory groups
    options:
        plugin:
            description: token that ensures this is a source file for the plugin.
            required: True
            choices: ['synapxe.dns.dns_inventory']
        domain_config_file:
            description: Path to domain configuration file
            default: group_vars/all/domains.yml
        cache:
            description: Toggle to enable/disable the caching of the inventory's source data
            type: boolean
            default: True
        cache_plugin:
            description: Cache plugin to use for the inventory's source data
            type: string
            default: jsonfile
        cache_timeout:
            description: Cache duration in seconds
            type: integer
            default: 3600
        cache_connection:
            description: Path to the cache file
            type: string
            default: inventories/dynamic/cache.json
'''

class InventoryModule(BaseInventoryPlugin, Cacheable, Constructable):
    """
    Inventory plugin for DNS Management.
    """
    
    NAME = 'synapxe.dns.dns_inventory'
    
    def verify_file(self, path):
        """
        Verify that the source file is valid.
        """
        if super(InventoryModule, self).verify_file(path):
            if path.endswith(('dns_inventory.yml', 'dns_inventory.yaml')):
                return True
        return False
    
    def parse(self, inventory, loader, path, cache=True):
        """
        Parse the inventory source file.
        """
        super(InventoryModule, self).parse(inventory, loader, path, cache)
        
        # Load configuration
        self._read_config_data(path)
        
        # Get domain configuration file path
        domain_config_file = self.get_option('domain_config_file')
        if not os.path.isabs(domain_config_file):
            domain_config_file = os.path.join(os.path.dirname(os.path.dirname(os.path.dirname(path))), domain_config_file)
        
        # Check if cache is valid
        cache_key = self.get_cache_key(path)
        user_cache_setting = self.get_option('cache')
        attempt_to_read_cache = user_cache_setting and cache
        cache_needs_update = True
        
        # Read from cache if possible
        if attempt_to_read_cache:
            cache = self.get_option('cache_plugin')
            cache_timeout = self.get_option('cache_timeout')
            
            # Load from cache
            cache_content = self._cache.get(cache_key)
            if cache_content:
                try:
                    # Check if cache is valid
                    cache_time = datetime.fromtimestamp(cache_content['_meta']['cache_time'])
                    now = datetime.now()
                    
                    if (now - cache_time) < timedelta(seconds=cache_timeout):
                        # Cache is valid, use it
                        self._populate_from_cache(cache_content)
                        cache_needs_update = False
                except Exception as e:
                    self.display.warning(f"Error reading cache: {e}")
        
        # Generate inventory if cache is not valid
        if cache_needs_update:
            # Load domain configuration
            try:
                with open(domain_config_file, 'r') as f:
                    domain_config = yaml.safe_load(f)
            except Exception as e:
                raise AnsibleParserError(f"Error loading domain configuration: {e}")
            
            # Generate inventory
            self._generate_inventory(domain_config)
            
            # Update cache
            if attempt_to_read_cache:
                cache_content = self._get_cache_content()
                self._cache.set(cache_key, cache_content)
    
    def _populate_from_cache(self, cache_content):
        """
        Populate inventory from cache.
        """
        # Add groups
        for group_name in cache_content:
            if group_name != '_meta':
                self.inventory.add_group(group_name)
                
                # Add hosts to group
                if 'hosts' in cache_content[group_name]:
                    for host_name in cache_content[group_name]['hosts']:
                        self.inventory.add_host(host_name, group_name)
                
                # Add children to group
                if 'children' in cache_content[group_name]:
                    for child_name in cache_content[group_name]['children']:
                        self.inventory.add_child(group_name, child_name)
        
        # Add host variables
        for host_name, host_vars in cache_content['_meta']['hostvars'].items():
            for var_name, var_value in host_vars.items():
                self.inventory.set_variable(host_name, var_name, var_value)
    
    def _generate_inventory(self, domain_config):
        """
        Generate inventory from domain configuration.
        """
        # Add groups
        self.inventory.add_group('all')
        self.inventory.add_group('dns_servers')
        self.inventory.add_group('admt_servers')
        self.inventory.add_group('domain_groups')
        
        # Add domain groups
        for group_name, domains in domain_config.get("domain_groups", {}).items():
            self.inventory.add_group(group_name)
            self.inventory.add_child('domain_groups', group_name)
        
        # Add DNS and ADMT servers
        for domain, config in domain_config.get("domains", {}).items():
            dns_server = config.get("dns_server")
            admt_server = config.get("admt_server")
            
            if dns_server:
                # Add DNS server to inventory
                if dns_server not in self.inventory.hosts:
                    self.inventory.add_host(dns_server, 'dns_servers')
                    self.inventory.set_variable(dns_server, 'ansible_connection', 'winrm')
                    self.inventory.set_variable(dns_server, 'ansible_winrm_server_cert_validation', 'ignore')
                    self.inventory.set_variable(dns_server, 'domain', domain)
                    self.inventory.set_variable(dns_server, 'environment', config.get('environment', 'prd'))
                    self.inventory.set_variable(dns_server, 'network_zone', config.get('network_zone', 'mgt'))
                    self.inventory.set_variable(dns_server, 'dc', config.get('dc', 'hdc1'))
            
            if admt_server:
                # Add ADMT server to inventory
                if admt_server not in self.inventory.hosts:
                    self.inventory.add_host(admt_server, 'admt_servers')
                    self.inventory.set_variable(admt_server, 'ansible_connection', 'winrm')
                    self.inventory.set_variable(admt_server, 'ansible_winrm_server_cert_validation', 'ignore')
                    self.inventory.set_variable(admt_server, 'domain', domain)
                    self.inventory.set_variable(admt_server, 'environment', config.get('environment', 'prd'))
                    self.inventory.set_variable(admt_server, 'network_zone', config.get('network_zone', 'mgt'))
                    self.inventory.set_variable(admt_server, 'dc', config.get('dc', 'hdc1'))
            
            # Add servers to domain groups
            for group_name, domains_list in domain_config.get("domain_groups", {}).items():
                if domain in domains_list:
                    if dns_server:
                        self.inventory.add_host(dns_server, group_name)
                    if admt_server:
                        self.inventory.add_host(admt_server, group_name)
    
    def _get_cache_content(self):
        """
        Get inventory content for cache.
        """
        cache_content = {
            '_meta': {
                'hostvars': {},
                'cache_time': datetime.now().timestamp()
            }
        }
        
        # Add groups
        for group_name in self.inventory.groups:
            cache_content[group_name] = {
                'hosts': [],
                'children': []
            }
            
            # Add hosts to group
            for host_name in self.inventory.groups[group_name].hosts:
                cache_content[group_name]['hosts'].append(host_name)
            
            # Add children to group
            for child_name in self.inventory.groups[group_name].child_groups:
                cache_content[group_name]['children'].append(child_name)
        
        # Add host variables
        for host_name in self.inventory.hosts:
            cache_content['_meta']['hostvars'][host_name] = {}
            for var_name, var_value in self.inventory.hosts[host_name].vars.items():
                cache_content['_meta']['hostvars'][host_name][var_name] = var_value
        
        return cache_content
