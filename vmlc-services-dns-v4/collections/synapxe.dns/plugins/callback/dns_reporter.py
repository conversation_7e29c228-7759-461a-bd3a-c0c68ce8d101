#!/usr/bin/env python3
# -*- coding: utf-8 -*-

# Copyright: (c) 2023, CES Operational Excellence Team
# GNU General Public License v3.0+ (see COPYING or https://www.gnu.org/licenses/gpl-3.0.txt)

from __future__ import (absolute_import, division, print_function)
__metaclass__ = type

DOCUMENTATION = '''
    callback: dns_reporter
    type: notification
    short_description: Captures DNS operation results and generates reports
    version_added: "1.0"
    description:
        - This callback plugin captures DNS operation results and generates structured reports
        - It can send reports to Jira and create Bitbucket logs
        - It also handles error enrichment and can trigger rollbacks
    options:
        jira_integration:
            description: Enable Jira integration
            default: False
            type: bool
            env:
                - name: ANSIBLE_CALLBACK_DNS_REPORTER_JIRA
        bitbucket_integration:
            description: Enable Bitbucket integration
            default: False
            type: bool
            env:
                - name: ANSIBLE_CALLBACK_DNS_REPORTER_BITBUCKET
        retry_count:
            description: Number of retries for failed operations
            default: 3
            type: int
            env:
                - name: ANSIBLE_CALLBACK_DNS_REPORTER_RETRY_COUNT
        auto_rollback:
            description: Enable automatic rollback for failed operations
            default: False
            type: bool
            env:
                - name: ANSIBLE_CALLBACK_DNS_REPORTER_AUTO_ROLLBACK
    requirements:
        - python >= 3.6
        - requests (for API integrations)
'''

import os
import json
import time
import datetime
import uuid
import socket
import requests
from ansible.plugins.callback import CallbackBase
from ansible.utils.display import Display

display = Display()

class CallbackModule(CallbackBase):
    """
    Ansible callback plugin for DNS operations reporting and error handling.
    """
    CALLBACK_VERSION = 2.0
    CALLBACK_TYPE = 'notification'
    CALLBACK_NAME = 'dns_reporter'
    CALLBACK_NEEDS_WHITELIST = True

    def __init__(self, display=None):
        super(CallbackModule, self).__init__(display)
        self.hostname = socket.gethostname()
        self.session_id = str(uuid.uuid4())
        self.start_time = datetime.datetime.now()
        self.results = {
            'session_id': self.session_id,
            'hostname': self.hostname,
            'start_time': self.start_time.isoformat(),
            'end_time': None,
            'duration': None,
            'status': 'running',
            'operation': None,
            'record_type': None,
            'domain': None,
            'hostname': None,
            'tasks': [],
            'errors': [],
            'warnings': [],
            'changes': [],
            'summary': {}
        }

        # Load configuration
        self.jira_integration = self._get_option('jira_integration', False)
        self.bitbucket_integration = self._get_option('bitbucket_integration', False)
        self.retry_count = self._get_option('retry_count', 3)
        self.auto_rollback = self._get_option('auto_rollback', False)

        # Initialize counters
        self.task_count = 0
        self.failed_count = 0
        self.changed_count = 0
        self.ok_count = 0
        self.skipped_count = 0

        # Initialize retry tracking
        self.retries = {}

        # Initialize artifacts directory
        self.artifacts_dir = os.environ.get('ANSIBLE_CALLBACK_DNS_REPORTER_ARTIFACTS_DIR', '/tmp/ansible_artifacts')
        if not os.path.exists(self.artifacts_dir):
            try:
                os.makedirs(self.artifacts_dir)
            except Exception as e:
                display.warning(f"Could not create artifacts directory: {e}")

        display.v("DNS Reporter callback plugin initialized")

    def _get_option(self, option_name, default=None):
        """Get option from environment or use default"""
        env_name = f'ANSIBLE_CALLBACK_DNS_REPORTER_{option_name.upper()}'
        env_value = os.environ.get(env_name)

        if env_value is None:
            return default

        if isinstance(default, bool):
            return env_value.lower() in ('true', 'yes', '1')
        elif isinstance(default, int):
            try:
                return int(env_value)
            except ValueError:
                return default
        else:
            return env_value

    def _extract_dns_info(self, result):
        """Extract DNS operation information from task result"""
        facts = result.get('ansible_facts', {})

        # Try to extract operation details
        if 'operation' in facts and self.results['operation'] is None:
            self.results['operation'] = facts['operation']

        if 'record_type' in facts and self.results['record_type'] is None:
            self.results['record_type'] = facts['record_type']

        if 'domain' in facts and self.results['domain'] is None:
            self.results['domain'] = facts['domain']

        if 'hostname' in facts and self.results['hostname'] is None:
            self.results['hostname'] = facts['hostname']

        # Extract DNS operation result if available
        if 'dns_operation_result' in facts:
            dns_result = facts['dns_operation_result']
            self.results['summary'] = dns_result

            # Record changes
            if dns_result.get('changed', False):
                self.results['changes'].append({
                    'operation': self.results['operation'],
                    'record_type': self.results['record_type'],
                    'domain': self.results['domain'],
                    'hostname': self.results['hostname'],
                    'timestamp': datetime.datetime.now().isoformat(),
                    'details': dns_result
                })

    def _should_retry(self, result):
        """Determine if a failed task should be retried"""
        task_name = result.get('task_name', '')

        # Initialize retry counter for this task if not exists
        if task_name not in self.retries:
            self.retries[task_name] = 0

        # Check if we've reached the retry limit
        if self.retries[task_name] >= self.retry_count:
            return False

        # Increment retry counter
        self.retries[task_name] += 1

        # Check for transient errors that should be retried
        error_msg = result.get('msg', '').lower()
        retryable_errors = [
            'timeout', 'connection refused', 'temporary failure',
            'network error', 'connection reset', 'service unavailable'
        ]

        return any(err in error_msg for err in retryable_errors)

    def _trigger_rollback(self, result):
        """Trigger rollback for failed operations"""
        if not self.auto_rollback:
            return

        # Only trigger rollback for DNS operations
        if self.results['operation'] not in ['add', 'remove', 'update']:
            return

        display.warning(f"Triggering rollback for failed operation: {self.results['operation']}")

        # In a real implementation, this would call the rollback API or
        # trigger a rollback job in AAP

    def _send_to_jira(self):
        """Send report to Jira"""
        if not self.jira_integration:
            return

        # Extract ticket number from results
        ticket = self.results.get('summary', {}).get('ticket', '')
        if not ticket:
            display.warning("No ticket number found, skipping Jira integration")
            return

        display.v(f"Sending report to Jira for ticket {ticket}")

        # In a real implementation, this would use the Jira API to
        # attach the report to the ticket

    def _send_to_bitbucket(self):
        """Create operation log in Bitbucket"""
        if not self.bitbucket_integration:
            return

        display.v("Creating operation log in Bitbucket")

        # In a real implementation, this would create a Markdown log file in Bitbucket

    def _save_artifact(self):
        """Save report as artifact"""
        try:
            timestamp = datetime.datetime.now().strftime('%Y%m%d_%H%M%S')
            operation = self.results.get('operation', 'unknown')
            domain = self.results.get('domain', 'unknown')
            hostname = self.results.get('hostname', 'unknown')
            record_type = self.results.get('record_type', 'unknown')

            filename = f"{timestamp}_{operation}_{record_type}_{hostname}_{domain}.json"
            filepath = os.path.join(self.artifacts_dir, filename)

            with open(filepath, 'w') as f:
                json.dump(self.results, f, indent=2)

            display.v(f"Report saved as artifact: {filepath}")

            # Set artifact path in environment variable for AAP to pick up
            os.environ['ANSIBLE_CALLBACK_DNS_REPORTER_ARTIFACT'] = filepath

        except Exception as e:
            display.warning(f"Failed to save artifact: {e}")

    def v2_playbook_on_start(self, playbook):
        """Called when playbook starts"""
        display.v("Playbook started")
        self.results['playbook'] = playbook._file_name

    def v2_playbook_on_task_start(self, task, is_conditional):
        """Called when task starts"""
        self.task_count += 1
        task_name = task.get_name()
        display.v(f"Task started: {task_name}")

        self.results['tasks'].append({
            'name': task_name,
            'start_time': datetime.datetime.now().isoformat(),
            'end_time': None,
            'duration': None,
            'status': 'running'
        })

    def v2_runner_on_failed(self, result, ignore_errors=False):
        """Called when task fails"""
        self.failed_count += 1
        task_name = result._task.get_name()
        display.warning(f"Task failed: {task_name}")

        # Update task status
        for task in self.results['tasks']:
            if task['name'] == task_name and task['status'] == 'running':
                task['status'] = 'failed'
                task['end_time'] = datetime.datetime.now().isoformat()
                task['duration'] = (datetime.datetime.now() - datetime.datetime.fromisoformat(task['start_time'])).total_seconds()
                break

        # Extract result details
        result_dict = result._result.copy()

        # Record error
        error = {
            'task': task_name,
            'timestamp': datetime.datetime.now().isoformat(),
            'msg': result_dict.get('msg', 'Unknown error'),
            'details': result_dict
        }
        self.results['errors'].append(error)

        # Check if we should retry
        if self._should_retry(result_dict):
            display.warning(f"Retrying task: {task_name} (attempt {self.retries[task_name]})")
            # In a real implementation, this would requeue the task
        elif not ignore_errors:
            # Trigger rollback if needed
            self._trigger_rollback(result_dict)

    def v2_runner_on_ok(self, result):
        """Called when task succeeds"""
        self.ok_count += 1
        task_name = result._task.get_name()

        # Update task status
        for task in self.results['tasks']:
            if task['name'] == task_name and task['status'] == 'running':
                task['status'] = 'ok'
                task['end_time'] = datetime.datetime.now().isoformat()
                task['duration'] = (datetime.datetime.now() - datetime.datetime.fromisoformat(task['start_time'])).total_seconds()
                break

        # Check if task changed something
        if result._result.get('changed', False):
            self.changed_count += 1

        # Extract DNS information
        self._extract_dns_info(result._result)

    def v2_runner_on_skipped(self, result):
        """Called when task is skipped"""
        self.skipped_count += 1
        task_name = result._task.get_name()

        # Update task status
        for task in self.results['tasks']:
            if task['name'] == task_name and task['status'] == 'running':
                task['status'] = 'skipped'
                task['end_time'] = datetime.datetime.now().isoformat()
                task['duration'] = (datetime.datetime.now() - datetime.datetime.fromisoformat(task['start_time'])).total_seconds()
                break

    def v2_playbook_on_stats(self, stats):
        """Called when playbook is finished"""
        end_time = datetime.datetime.now()
        duration = (end_time - self.start_time).total_seconds()

        # Update results
        self.results['end_time'] = end_time.isoformat()
        self.results['duration'] = duration
        self.results['status'] = 'failed' if self.failed_count > 0 else 'success'

        # Add statistics
        self.results['stats'] = {
            'tasks': self.task_count,
            'failed': self.failed_count,
            'changed': self.changed_count,
            'ok': self.ok_count,
            'skipped': self.skipped_count
        }

        display.v(f"Playbook finished: {self.results['status']}")

        # Save artifact
        self._save_artifact()

        # Send reports to external systems
        self._send_to_jira()
        self._send_to_bitbucket()
