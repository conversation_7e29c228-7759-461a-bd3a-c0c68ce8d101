#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
Ansible callback plugin for DNS Management operations.
Author: CES Operational Excellence Team
Contributors: <PERSON> (7409)

This plugin captures DNS operation events and:
1. Enriches error messages
2. Triggers rollbacks for failed operations
3. Sends events to Jira and Bitbucket
4. Attaches reports to Jira tickets
"""

from __future__ import (absolute_import, division, print_function)
__metaclass__ = type

import json
import os
import time
from datetime import datetime

from ansible.plugins.callback import CallbackBase
from ansible.utils.display import Display

display = Display()

class CallbackModule(CallbackBase):
    """
    Callback plugin for DNS Management operations.
    """

    CALLBACK_VERSION = 2.0
    CALLBACK_TYPE = 'notification'
    CALLBACK_NAME = 'dns_operations'
    CALLBACK_NEEDS_WHITELIST = True

    def __init__(self):
        super(CallbackModule, self).__init__()
        self.playbook_start_time = time.time()
        self.operation_results = {}
        self.current_task = None
        self.current_host = None
        self.errors = []
        self.jira_ticket = None
        self.operation = None
        self.record_type = None
        self.hostname = None
        self.domain = None
        self.is_multi_domain = False
        self.domains = None
        self.project_root = None

    def v2_playbook_on_start(self, playbook):
        """
        Called when the playbook starts.
        """
        self.playbook_start_time = time.time()
        self.project_root = os.path.dirname(os.path.dirname(os.path.dirname(os.path.dirname(os.path.dirname(playbook._file_name)))))
        display.display("DNS Operations callback plugin initialized", color='green')

    def v2_playbook_on_play_start(self, play):
        """
        Called when a play starts.
        """
        # Extract variables from play
        vars_dict = play.get_vars()

        # Set operation parameters
        self.operation = vars_dict.get('operation', 'unknown')
        self.record_type = vars_dict.get('record_type', 'unknown')
        self.hostname = vars_dict.get('hostname', 'unknown')
        self.domain = vars_dict.get('domain', 'unknown')
        self.is_multi_domain = vars_dict.get('is_multi_domain', False)
        self.domains = vars_dict.get('domains', 'unknown')
        self.jira_ticket = vars_dict.get('ticket', 'unknown')

        display.display(f"DNS Operations: {self.operation} {self.record_type} record for {'multiple domains' if self.is_multi_domain else self.hostname + '.' + self.domain}", color='blue')

    def v2_runner_on_failed(self, result, ignore_errors=False):
        """
        Called when a task fails.
        """
        if not ignore_errors:
            host = result._host.get_name()
            task = result._task.get_name()

            # Enrich error message
            error_message = self._get_error_message(result._result)
            enriched_message = self._enrich_error_message(error_message)

            # Log error
            self.errors.append({
                'host': host,
                'task': task,
                'message': enriched_message,
                'result': result._result
            })

            # Display error
            display.error(f"Task '{task}' failed on {host}: {enriched_message}")

            # Check if rollback is needed
            if self._should_trigger_rollback(result._result):
                self._trigger_rollback()

    def v2_playbook_on_stats(self, stats):
        """
        Called when the playbook is finished.
        """
        # Calculate duration
        duration = time.time() - self.playbook_start_time

        # Prepare summary
        summary = {
            'operation': self.operation,
            'record_type': self.record_type,
            'hostname': self.hostname if not self.is_multi_domain else 'multiple',
            'domain': self.domain if not self.is_multi_domain else self.domains,
            'is_multi_domain': self.is_multi_domain,
            'ticket': self.jira_ticket,
            'duration': duration,
            'success': len(self.errors) == 0,
            'errors': self.errors
        }

        # Display summary
        display.display("DNS Operations Summary:", color='blue')
        display.display(f"  Operation: {summary['operation']}", color='blue')
        display.display(f"  Record Type: {summary['record_type']}", color='blue')
        display.display(f"  {'Domains' if self.is_multi_domain else 'Hostname'}: {summary['domain'] if self.is_multi_domain else summary['hostname'] + '.' + summary['domain']}", color='blue')
        display.display(f"  Duration: {duration:.2f} seconds", color='blue')
        display.display(f"  Success: {summary['success']}", color='green' if summary['success'] else 'red')

        # Send summary to monitoring systems
        self._send_to_monitoring(summary)

        # Attach reports to Jira
        self._attach_to_jira(summary)

    def _get_error_message(self, result):
        """
        Extract error message from result.
        """
        if 'msg' in result:
            return result['msg']
        elif 'stderr' in result:
            return result['stderr']
        elif 'exception' in result:
            return result['exception']
        else:
            return "Unknown error"

    def _enrich_error_message(self, message):
        """
        Enrich error message with context.
        """
        # Add operation context
        enriched = f"[{self.operation.upper()} {self.record_type.upper()}] {message}"

        # Add common solutions
        if "already exists" in message:
            enriched += " - Try using 'verify' operation first or use 'update' operation instead."
        elif "does not exist" in message:
            enriched += " - Try using 'verify' operation first or check hostname and domain spelling."
        elif "permission denied" in message:
            enriched += " - Check credentials and permissions on the DNS server."
        elif "connection refused" in message:
            enriched += " - Check network connectivity to the DNS server."

        return enriched

    def _should_trigger_rollback(self, result):
        """
        Determine if rollback should be triggered.
        """
        # Only trigger rollback for add and remove operations
        if self.operation not in ['add', 'remove']:
            return False

        # Check if the operation was partially successful
        if 'changed' in result and result['changed']:
            return True

        return False

    def _trigger_rollback(self):
        """
        Trigger rollback for failed operation.
        """
        display.display("Triggering rollback for failed operation", color='yellow')

        # In a real implementation, this would call the rollback playbook
        # For now, just log the rollback
        timestamp = datetime.now().strftime("%Y-%m-%d %H:%M:%S")
        log_path = os.path.join(self.project_root, "logs/ansible/rollback.log")

        try:
            with open(log_path, 'a') as f:
                f.write(f"{timestamp} - Rollback triggered for {self.operation} {self.record_type} record for {'multiple domains' if self.is_multi_domain else self.hostname + '.' + self.domain}\n")
        except Exception as e:
            display.error(f"Failed to log rollback: {e}")

    def _send_to_monitoring(self, summary):
        """
        Send summary to monitoring systems.
        """
        # In a real implementation, this would send data to Jira and Bitbucket
        # For now, just log the monitoring data
        timestamp = datetime.now().strftime("%Y-%m-%d %H:%M:%S")
        log_path = os.path.join(self.project_root, "logs/ansible/monitoring.log")

        try:
            with open(log_path, 'a') as f:
                f.write(f"{timestamp} - Monitoring data: {json.dumps(summary, default=str)}\n")
        except Exception as e:
            display.error(f"Failed to log monitoring data: {e}")

    def _attach_to_jira(self, summary):
        """
        Attach reports to Jira ticket.
        """
        # In a real implementation, this would attach reports to Jira
        # For now, just log the Jira attachment
        timestamp = datetime.now().strftime("%Y-%m-%d %H:%M:%S")
        log_path = os.path.join(self.project_root, "logs/ansible/jira.log")

        try:
            with open(log_path, 'a') as f:
                f.write(f"{timestamp} - Jira attachment for ticket {self.jira_ticket}: {json.dumps(summary, default=str)}\n")
        except Exception as e:
            display.error(f"Failed to log Jira attachment: {e}")
