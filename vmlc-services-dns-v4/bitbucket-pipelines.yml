---
image: python:3.9

definitions:
  steps:
    - step: &lint
        name: <PERSON><PERSON>
        script:
          - pip install ansible ansible-lint yamllint
          - ansible-lint
          - yamllint .
    - step: &syntax-check
        name: Syntax Check
        script:
          - pip install ansible
          - ansible-galaxy install -r requirements.yml
          - ansible-playbook playbooks/site.yml --syntax-check
    - step: &molecule-test
        name: Molecule Tests
        script:
          - pip install ansible molecule molecule-docker pytest-testinfra
          - ansible-galaxy install -r requirements.yml
          - cd tests/molecule && molecule test -s default
    - step: &security-scan
        name: Security Scan
        script:
          - pip install ansible-lint bandit
          - ansible-lint --profile security
          - bandit -r .

pipelines:
  default:
    - step: *lint
    - step: *syntax-check
    - step: *molecule-test
    - step: *security-scan
  
  branches:
    feature/*:
      - step: *lint
      - step: *syntax-check
    
    release/*:
      - step: *lint
      - step: *syntax-check
      - step: *molecule-test
      - step: *security-scan
  
  custom:
    lint:
      - step: *lint
    syntax-check:
      - step: *syntax-check
    molecule-test:
      - step: *molecule-test
    security-scan:
      - step: *security-scan
