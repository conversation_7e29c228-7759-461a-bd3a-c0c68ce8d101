#!/usr/bin/env python3
"""
Dynamic Inventory Script for DNS Management
Author: CES Operational Excellence Team
Contributors: <PERSON> (7409)

This script is a symlink to ../../scripts/inventory.py
"""

import os
import sys

# Get the directory of this script
script_dir = os.path.dirname(os.path.abspath(__file__))

# Get the path to the inventory.py script
inventory_script = os.path.join(script_dir, '..', '..', 'scripts', 'inventory.py')

# Execute the inventory script with the same arguments
os.execv(inventory_script, [inventory_script] + sys.argv[1:])
