---
# Rollback DNS Management Playbook
# Author: CES Operational Excellence Team
# Contributors: <PERSON> (7409)

- name: DNS Management Rollback Operations
  hosts: localhost
  gather_facts: true
  
  vars:
    # Load run configuration from JSON
    run_config: "{{ lookup('file', playbook_dir + '/../run_config.json') | from_json }}"
    
    # Operation parameters
    operation: "{{ run_config.operation | default('verify') }}"
    record_type: "{{ run_config.record_type | default('a') }}"
    hostname: "{{ run_config.hostname | default('') }}"
    domain: "{{ run_config.domain | default('') }}"
    ticket: "{{ run_config.ticket | default('') }}"
    
    # Rollback parameters
    is_canary: false
    is_rollback: true
    rollback_job_id: "{{ run_config.rollback_job_id | default('') }}"
    
  pre_tasks:
    # Phase 1: Configuration
    - name: Configuration Phase
      block:
        - name: Load configuration
          ansible.builtin.include_role:
            name: common
            tasks_from: load_configuration
        
        - name: Load rollback data
          ansible.builtin.include_role:
            name: common
            tasks_from: load_rollback_data
          when: rollback_job_id != ''
        
        - name: Validate operation parameters
          ansible.builtin.include_role:
            name: common
            tasks_from: validate_operation
        
        - name: Validate domain
          ansible.builtin.include_role:
            name: common
            tasks_from: validate_domain
      tags:
        - always
        - configuration
    
    # Phase 2: Loading
    - name: Loading Phase
      block:
        - name: Setup credentials
          ansible.builtin.include_role:
            name: common
            tasks_from: setup_credentials
        
        - name: Setup logging
          ansible.builtin.include_role:
            name: common
            tasks_from: setup_logging
        
        - name: Initialize results
          ansible.builtin.include_role:
            name: common
            tasks_from: initialize_results
      tags:
        - always
        - loading
  
  tasks:
    # Phase 3: Execution
    - name: Execution Phase
      block:
        - name: Determine rollback operation
          ansible.builtin.set_fact:
            rollback_operation: >-
              {% if operation == 'add' %}remove
              {% elif operation == 'remove' %}add
              {% elif operation == 'update' %}update
              {% else %}verify{% endif %}
        
        - name: Execute rollback operation
          ansible.builtin.include_role:
            name: dns_operations
            tasks_from: "{{ rollback_operation }}.yml"
          vars:
            operation: "{{ rollback_operation }}"
      rescue:
        # Phase 4: Error Handling
        - name: Error Handling Phase
          ansible.builtin.include_role:
            name: common
            tasks_from: handle_errors
      tags:
        - always
        - execution
  
  post_tasks:
    # Phase 5: Reporting
    - name: Reporting Phase
      block:
        - name: Process operation results
          ansible.builtin.include_role:
            name: common
            tasks_from: process_results
        
        - name: Generate reports and send notifications
          ansible.builtin.include_role:
            name: reporting
      tags:
        - always
        - reporting
    
    # Phase 6: Cleanup
    - name: Cleanup Phase
      block:
        - name: Perform cleanup
          ansible.builtin.include_role:
            name: common
            tasks_from: cleanup
        
        - name: Display operation summary
          ansible.builtin.include_role:
            name: common
            tasks_from: display_summary
      tags:
        - always
        - cleanup
