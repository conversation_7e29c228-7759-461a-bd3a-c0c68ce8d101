---
# Main DNS Management Playbook
# Author: CES Operational Excellence Team
# Contributors: <PERSON> (7409)

- name: DNS Management Operations
  hosts: localhost
  gather_facts: true

  vars:
    # Load run configuration from JSON
    run_config: "{{ lookup('file', playbook_dir + '/../run_config.json') | from_json }}"

    # Operation parameters
    operation: "{{ run_config.operation | default('verify') }}"
    record_type: "{{ run_config.record_type | default('a') }}"
    hostname: "{{ run_config.hostname | default('') }}"
    domain: "{{ run_config.domain | default('') }}"
    ticket: "{{ run_config.ticket | default('') }}"

    # Execution parameters
    is_canary: false
    is_rollback: false

  pre_tasks:
    # CyberArk Integration
    - name: Retrieve Password from CyberArk
      ansible.builtin.include_role:
        name: cloud_cpe.cyberark_ccp.retrieve_from_cyberark
      vars:
        cyberark_app_id: "{{ cyberark_app_id | default('DNS_Management') }}"
        cyberark_safe: "{{ cyberark_safe | default('DNS_CREDS') }}"
        cyberark_object: "{{ cyberark_object | default('dns_admin_account') }}"
        cyberark_output_format: json
        cyberark_output_var: dns_credentials
      when: use_cyberark | default(false) | bool
      tags:
        - always
        - credentials
        - cyberark

    # Phase 1: Configuration
    - name: Configuration Phase
      block:
        - name: Load configuration
          ansible.builtin.include_role:
            name: common
            tasks_from: load_configuration

        - name: Validate operation parameters
          ansible.builtin.include_role:
            name: common
            tasks_from: validate_operation

        - name: Validate domain
          ansible.builtin.include_role:
            name: common
            tasks_from: validate_domain
      tags:
        - always
        - configuration

    # Phase 2: Loading
    - name: Loading Phase
      block:
        - name: Setup credentials
          ansible.builtin.include_role:
            name: common
            tasks_from: setup_credentials

        - name: Setup logging
          ansible.builtin.include_role:
            name: common
            tasks_from: setup_logging

        - name: Initialize results
          ansible.builtin.include_role:
            name: common
            tasks_from: initialize_results
      tags:
        - always
        - loading

  tasks:
    # Phase 3: Execution
    - name: Execution Phase
      block:
        - name: Execute DNS operation
          ansible.builtin.include_role:
            name: dns_operations
            tasks_from: "{{ operation }}.yml"
          when: not is_multi_domain

        - name: Execute multi-domain DNS operation
          ansible.builtin.include_role:
            name: dns_operations
            tasks_from: main
          when: is_multi_domain
      rescue:
        # Phase 4: Error Handling
        - name: Error Handling Phase
          ansible.builtin.include_role:
            name: common
            tasks_from: handle_errors
      tags:
        - always
        - execution

  post_tasks:
    # Phase 5: Reporting
    - name: Reporting Phase
      block:
        - name: Process operation results
          ansible.builtin.include_role:
            name: common
            tasks_from: process_results

        - name: Generate reports and send notifications
          ansible.builtin.include_role:
            name: reporting
      tags:
        - always
        - reporting

    # Phase 6: Cleanup
    - name: Cleanup Phase
      block:
        - name: Perform cleanup
          ansible.builtin.include_role:
            name: common
            tasks_from: cleanup

        - name: Display operation summary
          ansible.builtin.include_role:
            name: common
            tasks_from: display_summary
      tags:
        - always
        - cleanup
