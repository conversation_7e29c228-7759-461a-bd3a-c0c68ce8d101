---
# Send Jira Update Tasks
# Author: CES Operational Excellence Team
# Contributors: <PERSON> (7409)

- name: Load monitoring configuration
  ansible.builtin.include_vars:
    file: "{{ playbook_dir }}/../group_vars/all/defaults.yml"
    name: monitoring_config
    
- name: Set Jira data for single domain operation
  ansible.builtin.set_fact:
    jira_data:
      timestamp: "{{ '%Y-%m-%dT%H:%M:%S' | strftime }}"
      operation: "{{ operation }}"
      record_type: "{{ record_type }}"
      hostname: "{{ hostname }}"
      domain: "{{ domain }}"
      success: "{{ dns_operation_result.success | bool }}"
      message: "{{ dns_operation_result.message }}"
      ticket: "{{ ticket }}"
  when: not is_multi_domain
    
- name: Set Jira data for multi-domain operation
  ansible.builtin.set_fact:
    jira_data:
      timestamp: "{{ '%Y-%m-%dT%H:%M:%S' | strftime }}"
      operation: "{{ operation }}"
      record_type: "{{ record_type }}"
      domains: "{{ domains }}"
      success_count: "{{ multi_domain_results | selectattr('success', 'equalto', true) | list | length }}"
      failure_count: "{{ multi_domain_results | selectattr('success', 'equalto', false) | list | length }}"
      total_count: "{{ multi_domain_results | length }}"
      success_rate: "{{ (multi_domain_results | selectattr('success', 'equalto', true) | list | length / multi_domain_results | length * 100) | round(2) }}"
      ticket: "{{ ticket }}"
  when: is_multi_domain
    
- name: Check if Jira ticket exists
  uri:
    url: "{{ monitoring_config.monitoring_settings.jira.url }}/{{ ticket }}"
    method: GET
    user: "{{ monitoring_config.monitoring_settings.jira.username }}"
    password: "{{ monitoring_config.monitoring_settings.jira.password }}"
    force_basic_auth: yes
    status_code: [200, 404]
    return_content: yes
  register: jira_ticket_check
  delegate_to: localhost
  when: monitoring_config.monitoring_settings.jira.enabled | default(false) | bool
  ignore_errors: true
    
- name: Create Jira ticket if it doesn't exist
  uri:
    url: "{{ monitoring_config.monitoring_settings.jira.url }}"
    method: POST
    body_format: json
    body:
      fields:
        project:
          key: "{{ monitoring_config.monitoring_settings.jira.project_key }}"
        summary: "DNS {{ operation | upper }} {{ record_type | upper }} Record: {{ hostname if not is_multi_domain else 'Multiple Domains' }}"
        description: |
          DNS Operation Details:
          - Operation: {{ operation | capitalize }}
          - Record Type: {{ record_type | upper }}
          - {{ 'Hostname: ' + hostname if not is_multi_domain else 'Domains: ' + domains }}
          - {{ 'Domain: ' + domain if not is_multi_domain else 'Total Domains: ' + (multi_domain_results | length | string) }}
          - Ticket: {{ ticket }}
          - Timestamp: {{ jira_data.timestamp }}
          
          {{ 'Result: ' + dns_operation_result.message if not is_multi_domain else 'Results: ' + jira_data.success_count + ' successful, ' + jira_data.failure_count + ' failed' }}
        issuetype:
          name: "{{ monitoring_config.monitoring_settings.jira.issue_type }}"
    user: "{{ monitoring_config.monitoring_settings.jira.username }}"
    password: "{{ monitoring_config.monitoring_settings.jira.password }}"
    force_basic_auth: yes
    status_code: 201
  register: jira_create_result
  delegate_to: localhost
  when: 
    - monitoring_config.monitoring_settings.jira.enabled | default(false) | bool
    - jira_ticket_check.status == 404
  ignore_errors: true
    
- name: Add comment to existing Jira ticket
  uri:
    url: "{{ monitoring_config.monitoring_settings.jira.url }}/{{ ticket }}/comment"
    method: POST
    body_format: json
    body:
      body: |
        DNS Operation Update:
        - Operation: {{ operation | capitalize }}
        - Record Type: {{ record_type | upper }}
        - {{ 'Hostname: ' + hostname if not is_multi_domain else 'Domains: ' + domains }}
        - {{ 'Domain: ' + domain if not is_multi_domain else 'Total Domains: ' + (multi_domain_results | length | string) }}
        - Timestamp: {{ jira_data.timestamp }}
        
        {{ 'Result: ' + dns_operation_result.message if not is_multi_domain else 'Results: ' + jira_data.success_count + ' successful, ' + jira_data.failure_count + ' failed' }}
    user: "{{ monitoring_config.monitoring_settings.jira.username }}"
    password: "{{ monitoring_config.monitoring_settings.jira.password }}"
    force_basic_auth: yes
    status_code: 201
  register: jira_comment_result
  delegate_to: localhost
  when: 
    - monitoring_config.monitoring_settings.jira.enabled | default(false) | bool
    - jira_ticket_check.status == 200
  ignore_errors: true
    
- name: Log Jira update
  ansible.builtin.lineinfile:
    path: "{{ ansible_log_path }}"
    line: "{{ '%Y-%m-%d %H:%M:%S' | strftime }} - Updated Jira ticket {{ ticket }}"
  when: 
    - monitoring_config.monitoring_settings.jira.enabled | default(false) | bool
    - (jira_create_result is defined and jira_create_result.status == 201) or 
      (jira_comment_result is defined and jira_comment_result.status == 201)
