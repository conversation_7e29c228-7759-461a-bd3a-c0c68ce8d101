---
# Send Bitbucket Update Tasks
# Author: CES Operational Excellence Team
# Contributors: <PERSON> (7409)

- name: Load monitoring configuration
  ansible.builtin.include_vars:
    file: "{{ playbook_dir }}/../group_vars/all/defaults.yml"
    name: monitoring_config
    
- name: Set Bitbucket data for single domain operation
  ansible.builtin.set_fact:
    bitbucket_data:
      timestamp: "{{ '%Y-%m-%dT%H:%M:%S' | strftime }}"
      operation: "{{ operation }}"
      record_type: "{{ record_type }}"
      hostname: "{{ hostname }}"
      domain: "{{ domain }}"
      success: "{{ dns_operation_result.success | bool }}"
      message: "{{ dns_operation_result.message }}"
      ticket: "{{ ticket }}"
  when: not is_multi_domain
    
- name: Set Bitbucket data for multi-domain operation
  ansible.builtin.set_fact:
    bitbucket_data:
      timestamp: "{{ '%Y-%m-%dT%H:%M:%S' | strftime }}"
      operation: "{{ operation }}"
      record_type: "{{ record_type }}"
      domains: "{{ domains }}"
      success_count: "{{ multi_domain_results | selectattr('success', 'equalto', true) | list | length }}"
      failure_count: "{{ multi_domain_results | selectattr('success', 'equalto', false) | list | length }}"
      total_count: "{{ multi_domain_results | length }}"
      success_rate: "{{ (multi_domain_results | selectattr('success', 'equalto', true) | list | length / multi_domain_results | length * 100) | round(2) }}"
      ticket: "{{ ticket }}"
  when: is_multi_domain
    
- name: Create DNS operation log file for Bitbucket
  ansible.builtin.copy:
    content: |
      # DNS Operation Log - {{ ticket }}
      
      ## Operation Details
      - **Operation:** {{ operation | capitalize }}
      - **Record Type:** {{ record_type | upper }}
      - **{{ 'Hostname:' if not is_multi_domain else 'Domains:' }}** {{ hostname if not is_multi_domain else domains }}
      - **{{ 'Domain:' if not is_multi_domain else 'Total Domains:' }}** {{ domain if not is_multi_domain else bitbucket_data.total_count }}
      - **Ticket:** {{ ticket }}
      - **Timestamp:** {{ bitbucket_data.timestamp }}
      
      ## Results
      {{ dns_operation_result.message if not is_multi_domain else bitbucket_data.success_count + ' successful, ' + bitbucket_data.failure_count + ' failed' }}
      
      {% if is_multi_domain %}
      ### Domain Details
      {% for result in multi_domain_results %}
      - **{{ result.record.hostname }}.{{ result.record.domain }}:** {{ 'Success' if result.success else 'Failure' }} - {{ result.message }}
      {% endfor %}
      {% endif %}
    dest: "{{ playbook_dir }}/../logs/bitbucket/{{ ticket }}_{{ '%Y%m%d%H%M%S' | strftime }}.md"
    mode: '0644'
  register: bitbucket_log_file
  when: monitoring_config.monitoring_settings.bitbucket.enabled | default(false) | bool
    
- name: Create directory for Bitbucket logs
  ansible.builtin.file:
    path: "{{ playbook_dir }}/../logs/bitbucket"
    state: directory
    mode: '0755'
  when: monitoring_config.monitoring_settings.bitbucket.enabled | default(false) | bool
    
- name: Log Bitbucket update
  ansible.builtin.lineinfile:
    path: "{{ ansible_log_path }}"
    line: "{{ '%Y-%m-%d %H:%M:%S' | strftime }} - Created Bitbucket log file for ticket {{ ticket }}"
  when: 
    - monitoring_config.monitoring_settings.bitbucket.enabled | default(false) | bool
    - bitbucket_log_file is defined and bitbucket_log_file.changed
