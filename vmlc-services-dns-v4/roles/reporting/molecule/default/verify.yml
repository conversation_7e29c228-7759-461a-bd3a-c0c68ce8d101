---
- name: Verify
  hosts: all
  gather_facts: false
  
  tasks:
    - name: Check if report file exists
      ansible.builtin.stat:
        path: "/tmp/reports/test_TEST123_test_test.local_a_verify_STANDARD.html"
      register: report_file
    
    - name: Verify report file exists
      ansible.builtin.assert:
        that:
          - report_file.stat.exists
        fail_msg: "Report file does not exist"
        success_msg: "Report file exists"
    
    - name: Check report file content
      ansible.builtin.command:
        cmd: "cat /tmp/reports/test_TEST123_test_test.local_a_verify_STANDARD.html"
      register: report_content
      changed_when: false
    
    - name: Verify report content
      ansible.builtin.assert:
        that:
          - "'DNS Operation Report' in report_content.stdout"
          - "'Operation: verify' in report_content.stdout"
          - "'Record Type: a' in report_content.stdout"
          - "'Hostname: test' in report_content.stdout"
          - "'Domain: test.local' in report_content.stdout"
        fail_msg: "Report content is incorrect"
        success_msg: "Report content is correct"
