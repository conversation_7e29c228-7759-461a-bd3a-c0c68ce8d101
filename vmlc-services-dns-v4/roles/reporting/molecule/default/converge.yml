---
- name: Converge
  hosts: all
  gather_facts: true
  
  vars:
    operation: verify
    record_type: a
    hostname: test
    domain: test.local
    ticket: TEST123
    
    # Mock DNS operation result
    dns_operation_result:
      success: true
      message: "A record test.test.local exists with IP address ************"
      changed: false
      timestamp: "2023-05-01T12:00:00"
      record:
        domain: test.local
        hostname: test
        type: a
        fqdn: test.test.local
        ip_address: ************
      operation: verify
    
    # Mock log paths
    ansible_log_path: "/tmp/logs/ansible/test.log"
    powershell_log_path: "/tmp/logs/powershell/test.log"
    
    # Mock report settings
    report_settings:
      directories:
        reports: "/tmp/reports"
        templates: "/tmp/templates"
      format:
        date_format: "%Y%m%d"
        separator: "_"
        extension: ".html"
      types:
        standard: "STANDARD"
        consolidated: "CONSOLIDATED"
      templates:
        standard: "dns_report.html.j2"
        consolidated: "consolidated_report.html.j2"
    
    # Mock email settings
    email_settings:
      smtp:
        port: 25
        from: "<EMAIL>"
        dc_servers:
          hdc1: "smtp.example.com"
        default_dc: "hdc1"
      recipients:
        domain_specific:
          default: "<EMAIL>"
        testing: "<EMAIL>"
      templates:
        directory: "/tmp/templates/emails"
        subjects:
          report: "DNS Report - {operation} {record_type} Record"
          logs: "DNS Logs - {operation} {record_type} Record"
        bodies:
          report: "report_email_body.j2"
          logs: "logs_email_body.j2"
          consolidated: "consolidated_report_email_body.j2"
    
    # Report flags
    generate_report: true
    email_report: false
    email_logs: false
    testing_mode: true
  
  tasks:
    - name: Create test directories
      ansible.builtin.file:
        path: "{{ item }}"
        state: directory
        mode: '0755'
      loop:
        - /tmp/logs/ansible
        - /tmp/logs/powershell
        - /tmp/reports
        - /tmp/templates
        - /tmp/templates/emails
    
    - name: Create mock report template
      ansible.builtin.copy:
        content: |
          <!DOCTYPE html>
          <html>
          <head>
            <title>DNS Operation Report</title>
          </head>
          <body>
            <h1>DNS Operation Report</h1>
            <p>Operation: {{ operation }}</p>
            <p>Record Type: {{ record_type }}</p>
            <p>Hostname: {{ hostname }}</p>
            <p>Domain: {{ domain }}</p>
            <p>Result: {{ dns_operation_result.success }}</p>
            <p>Message: {{ dns_operation_result.message }}</p>
          </body>
          </html>
        dest: /tmp/templates/dns_report.html.j2
        mode: '0644'
    
    - name: Create mock consolidated report template
      ansible.builtin.copy:
        content: |
          <!DOCTYPE html>
          <html>
          <head>
            <title>DNS Consolidated Report</title>
          </head>
          <body>
            <h1>DNS Consolidated Report</h1>
            <p>Operation: {{ operation }}</p>
            <p>Record Type: {{ record_type }}</p>
            <p>Domains: {{ domains | join(', ') }}</p>
            <p>Results:</p>
            <ul>
              {% for result in results %}
              <li>{{ result.domain }}: {{ result.message }}</li>
              {% endfor %}
            </ul>
          </body>
          </html>
        dest: /tmp/templates/consolidated_report.html.j2
        mode: '0644'
    
    - name: Create mock email templates
      ansible.builtin.copy:
        content: |
          DNS Operation Report
          
          Operation: {{ operation }}
          Record Type: {{ record_type }}
          Hostname: {{ hostname }}
          Domain: {{ domain }}
          Result: {{ dns_operation_result.success }}
          Message: {{ dns_operation_result.message }}
        dest: "/tmp/templates/emails/{{ item }}"
        mode: '0644'
      loop:
        - report_email_body.j2
        - logs_email_body.j2
        - consolidated_report_email_body.j2
    
    - name: Include reporting role tasks
      ansible.builtin.include_role:
        name: reporting
        tasks_from: "{{ item }}"
      loop:
        - generate_report
        - email_report
