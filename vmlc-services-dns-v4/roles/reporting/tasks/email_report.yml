---
# Email Report Tasks
# Author: CES Operational Excellence Team
# Contributors: <PERSON> (7409)

- name: Load email configuration
  ansible.builtin.include_vars:
    file: "{{ playbook_dir }}/../group_vars/all/defaults.yml"
    name: email_config

- name: Set email variables
  ansible.builtin.set_fact:
    smtp_server: "{{ email_config.email_settings.smtp.dc_servers[domain_config.domains[domain].dc] | default(email_config.email_settings.smtp.dc_servers[email_config.email_settings.smtp.default_dc]) }}"
    smtp_port: "{{ email_config.email_settings.smtp.port }}"
    email_from: "{{ email_config.email_settings.smtp.from }}"
    email_subject: "DNS Report - {{ operation | capitalize }} {{ record_type | upper }} Record"

- name: Set email recipient based on testing mode
  ansible.builtin.set_fact:
    email_to: "{{ email_config.email_settings.recipients.testing }}"
  when: testing_mode | bool

- name: Set email recipient based on admin team
  ansible.builtin.set_fact:
    email_to: "{{ email_recipient | default(email_config.email_settings.recipients.admin_teams[domain_config.domains[domain].admin_team] | default(email_config.email_settings.recipients.admin_teams.default)) }}"
  when: not testing_mode | bool and not is_multi_domain and domain_config.domains[domain].admin_team is defined

- name: Set email recipient based on domain (fallback)
  ansible.builtin.set_fact:
    email_to: "{{ email_recipient | default(email_config.email_settings.recipients.domain_specific[domain] | default(email_config.email_settings.recipients.domain_specific.default)) }}"
  when: not testing_mode | bool and not is_multi_domain and domain_config.domains[domain].admin_team is not defined

- name: Determine primary domain admin team for multi-domain operation
  ansible.builtin.set_fact:
    primary_domain: "{{ domains_list[0] if domains_list is defined else domains.split(',')[0] }}"
  when: not testing_mode | bool and is_multi_domain

- name: Set email recipient for multi-domain operation based on admin team
  ansible.builtin.set_fact:
    email_to: "{{ email_recipient | default(email_config.email_settings.recipients.admin_teams[domain_config.domains[primary_domain].admin_team] | default(email_config.email_settings.recipients.admin_teams.default)) }}"
  when: not testing_mode | bool and is_multi_domain and domain_config.domains[primary_domain].admin_team is defined

- name: Set email recipient for multi-domain operation (fallback)
  ansible.builtin.set_fact:
    email_to: "{{ email_recipient | default(email_config.email_settings.recipients.domain_specific.default) }}"
  when: not testing_mode | bool and is_multi_domain and (primary_domain is not defined or domain_config.domains[primary_domain].admin_team is not defined)

- name: Create email templates directory
  ansible.builtin.file:
    path: "{{ playbook_dir }}/../templates/emails"
    state: directory
    mode: '0755'

- name: Create email template
  ansible.builtin.copy:
    content: |
      DNS Operation Report

      Operation: {{ operation | capitalize }}
      Record Type: {{ record_type | upper }}
      {% if is_multi_domain %}
      Domains: {{ domains }}
      {% else %}
      Hostname: {{ hostname }}
      Domain: {{ domain }}
      FQDN: {{ hostname }}.{{ domain }}
      {% endif %}
      Ticket: {{ ticket }}
      Timestamp: {{ '%Y-%m-%d %H:%M:%S' | strftime }}
      Status: {{ 'Success' if dns_operation_result.success else 'Failure' if not is_multi_domain else 'Success' if multi_domain_results | selectattr('success', 'equalto', true) | list | length == multi_domain_results | length else 'Partial Success' if multi_domain_results | selectattr('success', 'equalto', true) | list | length > 0 else 'Failure' }}

      {% if is_multi_domain %}
      Summary:
      {% for result in multi_domain_results %}
      - {{ result.record.hostname }}.{{ result.record.domain }}: {{ 'Success' if result.success else 'Failure' }} - {{ result.message }}
      {% endfor %}
      {% else %}
      Message: {{ dns_operation_result.message }}
      {% endif %}

      Please see the attached report for details.

      This is an automated message from the DNS Management System.
      CES Operational Excellence Team
    dest: "{{ playbook_dir }}/../templates/emails/report_email_body.j2"
    mode: '0644'
    force: no

- name: Create consolidated email template
  ansible.builtin.copy:
    content: |
      DNS Consolidated Operation Report

      Operation: {{ operation | capitalize }}
      Record Type: {{ record_type | upper }}
      Domains: {{ domains }}
      Ticket: {{ ticket }}
      Timestamp: {{ '%Y-%m-%d %H:%M:%S' | strftime }}
      Status: {{ 'Success' if multi_domain_results | selectattr('success', 'equalto', true) | list | length == multi_domain_results | length else 'Partial Success' if multi_domain_results | selectattr('success', 'equalto', true) | list | length > 0 else 'Failure' }}

      Summary:
      {% for result in multi_domain_results %}
      - {{ result.record.hostname }}.{{ result.record.domain }}: {{ 'Success' if result.success else 'Failure' }} - {{ result.message }}
      {% endfor %}

      Please see the attached report for details.

      This is an automated message from the DNS Management System.
      CES Operational Excellence Team
    dest: "{{ playbook_dir }}/../templates/emails/consolidated_report_email_body.j2"
    mode: '0644'
    force: no

- name: Generate email body
  ansible.builtin.template:
    src: "{{ playbook_dir }}/../templates/emails/{{ 'consolidated_report_email_body.j2' if is_multi_domain else 'report_email_body.j2' }}"
    dest: "{{ playbook_dir }}/../reports/email_body.txt"
    mode: '0644'

- name: Send email with report
  community.general.mail:
    host: "{{ smtp_server }}"
    port: "{{ smtp_port }}"
    from: "{{ email_from }}"
    to: "{{ email_to }}"
    bcc: "{{ email_config.email_settings.recipients.bcc }}"
    subject: "{{ email_subject }} - {{ ticket }}"
    body: "{{ lookup('file', playbook_dir + '/../reports/email_body.txt') }}"
    attach: "{{ report_file_path }}"
  delegate_to: localhost
  ignore_errors: true

- name: Log email sent
  ansible.builtin.lineinfile:
    path: "{{ ansible_log_path }}"
    line: "{{ '%Y-%m-%d %H:%M:%S' | strftime }} - Sent email report to {{ email_to }}"
