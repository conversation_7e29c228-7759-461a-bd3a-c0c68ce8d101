---
# Upload Logs to Target Server Tasks
# Author: CES Operational Excellence Team
# Contributors: <PERSON> (7409)

- name: Set target server log path
  ansible.builtin.set_fact:
    target_log_path: "C:\\OE_AAP_LOGS\\{{ timestamp }}_{{ ticket }}_{{ 'MULTI_DOMAIN' if is_multi_domain else hostname + '_' + domain }}_{{ record_type | upper }}_{{ operation | upper }}"
    
- name: Create target log directory on ADMT server
  ansible.windows.win_file:
    path: "{{ target_log_path }}"
    state: directory
  delegate_to: "{{ domain_config.domains[domain].admt_server }}"
  when: not is_multi_domain
  ignore_errors: true
    
- name: Upload logs to ADMT server
  ansible.windows.win_copy:
    src: "{{ item }}"
    dest: "{{ target_log_path }}\\{{ item | basename }}"
  loop:
    - "{{ ansible_log_path }}"
    - "{{ powershell_log_path }}"
    - "{{ progress_log_path }}"
  delegate_to: "{{ domain_config.domains[domain].admt_server }}"
  when: not is_multi_domain
  ignore_errors: true
    
- name: Upload report to ADMT server
  ansible.windows.win_copy:
    src: "{{ report_file_path }}"
    dest: "{{ target_log_path }}\\{{ report_file_path | basename }}"
  delegate_to: "{{ domain_config.domains[domain].admt_server }}"
  when: not is_multi_domain and generate_report | bool
  ignore_errors: true
    
- name: Log upload to target server
  ansible.builtin.lineinfile:
    path: "{{ ansible_log_path }}"
    line: "{{ '%Y-%m-%d %H:%M:%S' | strftime }} - Uploaded logs to target server at {{ target_log_path }}"
  when: not is_multi_domain
    
- name: Upload logs to multiple ADMT servers
  block:
    - name: Create target log directory on ADMT server
      ansible.windows.win_file:
        path: "{{ target_log_path }}"
        state: directory
      delegate_to: "{{ domain_config.domains[item].admt_server }}"
      loop: "{{ domains_list }}"
      ignore_errors: true
      
    - name: Upload logs to ADMT server
      ansible.windows.win_copy:
        src: "{{ item[1] }}"
        dest: "{{ target_log_path }}\\{{ item[1] | basename }}"
      loop: "{{ domains_list | product([ansible_log_path, powershell_log_path, progress_log_path]) | list }}"
      loop_control:
        loop_var: item
      delegate_to: "{{ domain_config.domains[item[0]].admt_server }}"
      ignore_errors: true
      
    - name: Upload report to ADMT server
      ansible.windows.win_copy:
        src: "{{ report_file_path }}"
        dest: "{{ target_log_path }}\\{{ report_file_path | basename }}"
      loop: "{{ domains_list }}"
      delegate_to: "{{ domain_config.domains[item].admt_server }}"
      ignore_errors: true
      when: generate_report | bool
      
    - name: Log upload to target servers
      ansible.builtin.lineinfile:
        path: "{{ ansible_log_path }}"
        line: "{{ '%Y-%m-%d %H:%M:%S' | strftime }} - Uploaded logs to multiple target servers"
  when: is_multi_domain
