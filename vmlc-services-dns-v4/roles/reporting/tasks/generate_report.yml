---
# Generate Report Tasks
# Author: CES Operational Excellence Team
# Contributors: <PERSON> (7409)

- name: Set report file path
  ansible.builtin.set_fact:
    report_file_path: "{{ playbook_dir }}/../reports/{{ timestamp }}_{{ ticket }}_{{ hostname }}_{{ domain }}_{{ record_type | upper }}_{{ operation | upper }}_STANDARD.html"
    
- name: Create reports directory
  ansible.builtin.file:
    path: "{{ playbook_dir }}/../reports"
    state: directory
    mode: '0755'
    
- name: Create templates directory
  ansible.builtin.file:
    path: "{{ playbook_dir }}/../templates/reports"
    state: directory
    mode: '0755'
    
- name: Create report template
  ansible.builtin.copy:
    content: |
      <!DOCTYPE html>
      <html>
      <head>
        <title>DNS Operation Report</title>
        <style>
          body {
            font-family: Arial, sans-serif;
            margin: 20px;
            line-height: 1.6;
          }
          h1 {
            color: #2c3e50;
            border-bottom: 2px solid #3498db;
            padding-bottom: 10px;
          }
          .report-header {
            background-color: #f8f9fa;
            padding: 15px;
            border-radius: 5px;
            margin-bottom: 20px;
          }
          .report-section {
            margin-bottom: 20px;
          }
          .report-section h2 {
            color: #3498db;
            border-bottom: 1px solid #ddd;
            padding-bottom: 5px;
          }
          .success {
            color: #27ae60;
            font-weight: bold;
          }
          .failure {
            color: #e74c3c;
            font-weight: bold;
          }
          table {
            width: 100%;
            border-collapse: collapse;
            margin-bottom: 20px;
          }
          th, td {
            padding: 12px 15px;
            text-align: left;
            border-bottom: 1px solid #ddd;
          }
          th {
            background-color: #f2f2f2;
          }
          tr:hover {
            background-color: #f5f5f5;
          }
          .footer {
            margin-top: 30px;
            text-align: center;
            font-size: 0.8em;
            color: #7f8c8d;
          }
        </style>
      </head>
      <body>
        <h1>DNS Operation Report</h1>
        
        <div class="report-header">
          <p><strong>Operation:</strong> {{ operation | capitalize }}</p>
          <p><strong>Record Type:</strong> {{ record_type | upper }}</p>
          <p><strong>Hostname:</strong> {{ hostname }}</p>
          <p><strong>Domain:</strong> {{ domain }}</p>
          <p><strong>FQDN:</strong> {{ hostname }}.{{ domain }}</p>
          <p><strong>Ticket:</strong> {{ ticket }}</p>
          <p><strong>Timestamp:</strong> {{ '%Y-%m-%d %H:%M:%S' | strftime }}</p>
          <p><strong>Status:</strong> <span class="{{ 'success' if dns_operation_result.success else 'failure' }}">{{ 'Success' if dns_operation_result.success else 'Failure' }}</span></p>
        </div>
        
        <div class="report-section">
          <h2>Operation Details</h2>
          <p><strong>Message:</strong> {{ dns_operation_result.message }}</p>
          <p><strong>Changed:</strong> {{ 'Yes' if dns_operation_result.changed else 'No' }}</p>
          <p><strong>Timestamp:</strong> {{ dns_operation_result.timestamp }}</p>
        </div>
        
        {% if dns_operation_result.record %}
        <div class="report-section">
          <h2>Record Details</h2>
          <table>
            <tr>
              <th>Property</th>
              <th>Value</th>
            </tr>
            {% for key, value in dns_operation_result.record.items() %}
            <tr>
              <td>{{ key }}</td>
              <td>{{ value }}</td>
            </tr>
            {% endfor %}
          </table>
        </div>
        {% endif %}
        
        <div class="footer">
          <p>Generated by DNS Management System</p>
          <p>CES Operational Excellence Team</p>
        </div>
      </body>
      </html>
    dest: "{{ playbook_dir }}/../templates/reports/dns_report.html.j2"
    mode: '0644'
    force: no
    
- name: Generate HTML report
  ansible.builtin.template:
    src: "{{ playbook_dir }}/../templates/reports/dns_report.html.j2"
    dest: "{{ report_file_path }}"
    mode: '0644'
    
- name: Log report generation
  ansible.builtin.lineinfile:
    path: "{{ ansible_log_path }}"
    line: "{{ '%Y-%m-%d %H:%M:%S' | strftime }} - Generated report at {{ report_file_path }}"
