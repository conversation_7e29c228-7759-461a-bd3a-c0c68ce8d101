---
# Reporting Role Main Tasks
# Author: CES Operational Excellence Team
# Contributors: <PERSON> (7409)

- name: Include generate_report tasks
  ansible.builtin.import_tasks: generate_report.yml
  when: 
    - generate_report | bool
    - not is_multi_domain
  tags:
    - always
    - reporting

- name: Include generate_consolidated_report tasks
  ansible.builtin.import_tasks: generate_consolidated_report.yml
  when: 
    - generate_report | bool
    - is_multi_domain
  tags:
    - always
    - reporting

- name: Include email_report tasks
  ansible.builtin.import_tasks: email_report.yml
  when: 
    - generate_report | bool
    - email_report | bool
  tags:
    - always
    - reporting

- name: Include email_logs tasks
  ansible.builtin.import_tasks: email_logs.yml
  when: email_logs | bool
  tags:
    - always
    - reporting

- name: Include upload_logs_to_target_server tasks
  ansible.builtin.import_tasks: upload_logs_to_target_server.yml
  when: store_logs_target_server | bool
  tags:
    - always
    - reporting
