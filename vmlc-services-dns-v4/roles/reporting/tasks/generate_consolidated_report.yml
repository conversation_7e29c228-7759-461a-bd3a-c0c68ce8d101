---
# Generate Consolidated Report Tasks
# Author: CES Operational Excellence Team
# Contributors: <PERSON> (7409)

- name: Set consolidated report file path
  ansible.builtin.set_fact:
    report_file_path: "{{ playbook_dir }}/../reports/{{ timestamp }}_{{ ticket }}_MULTI_DOMAIN_{{ record_type | upper }}_{{ operation | upper }}_CONSOLIDATED.html"
    
- name: Create reports directory
  ansible.builtin.file:
    path: "{{ playbook_dir }}/../reports"
    state: directory
    mode: '0755'
    
- name: Create templates directory
  ansible.builtin.file:
    path: "{{ playbook_dir }}/../templates/reports"
    state: directory
    mode: '0755'
    
- name: Create consolidated report template
  ansible.builtin.copy:
    content: |
      <!DOCTYPE html>
      <html>
      <head>
        <title>DNS Consolidated Operation Report</title>
        <style>
          body {
            font-family: Arial, sans-serif;
            margin: 20px;
            line-height: 1.6;
          }
          h1 {
            color: #2c3e50;
            border-bottom: 2px solid #3498db;
            padding-bottom: 10px;
          }
          .report-header {
            background-color: #f8f9fa;
            padding: 15px;
            border-radius: 5px;
            margin-bottom: 20px;
          }
          .report-section {
            margin-bottom: 20px;
          }
          .report-section h2 {
            color: #3498db;
            border-bottom: 1px solid #ddd;
            padding-bottom: 5px;
          }
          .success {
            color: #27ae60;
            font-weight: bold;
          }
          .failure {
            color: #e74c3c;
            font-weight: bold;
          }
          table {
            width: 100%;
            border-collapse: collapse;
            margin-bottom: 20px;
          }
          th, td {
            padding: 12px 15px;
            text-align: left;
            border-bottom: 1px solid #ddd;
          }
          th {
            background-color: #f2f2f2;
          }
          tr:hover {
            background-color: #f5f5f5;
          }
          .domain-section {
            margin-bottom: 30px;
            padding: 15px;
            border: 1px solid #ddd;
            border-radius: 5px;
          }
          .domain-section h3 {
            margin-top: 0;
            color: #2c3e50;
          }
          .footer {
            margin-top: 30px;
            text-align: center;
            font-size: 0.8em;
            color: #7f8c8d;
          }
        </style>
      </head>
      <body>
        <h1>DNS Consolidated Operation Report</h1>
        
        <div class="report-header">
          <p><strong>Operation:</strong> {{ operation | capitalize }}</p>
          <p><strong>Record Type:</strong> {{ record_type | upper }}</p>
          <p><strong>Domains:</strong> {{ domains }}</p>
          <p><strong>Ticket:</strong> {{ ticket }}</p>
          <p><strong>Timestamp:</strong> {{ '%Y-%m-%d %H:%M:%S' | strftime }}</p>
          <p><strong>Overall Status:</strong> 
            <span class="{{ 'success' if multi_domain_results | selectattr('success', 'equalto', true) | list | length == multi_domain_results | length else 'failure' }}">
              {{ 'Success' if multi_domain_results | selectattr('success', 'equalto', true) | list | length == multi_domain_results | length else 'Partial Success' if multi_domain_results | selectattr('success', 'equalto', true) | list | length > 0 else 'Failure' }}
            </span>
          </p>
        </div>
        
        <div class="report-section">
          <h2>Summary</h2>
          <table>
            <tr>
              <th>Domain</th>
              <th>Hostname</th>
              <th>Status</th>
              <th>Message</th>
            </tr>
            {% for result in multi_domain_results %}
            <tr>
              <td>{{ result.record.domain }}</td>
              <td>{{ result.record.hostname }}</td>
              <td class="{{ 'success' if result.success else 'failure' }}">{{ 'Success' if result.success else 'Failure' }}</td>
              <td>{{ result.message }}</td>
            </tr>
            {% endfor %}
          </table>
        </div>
        
        <div class="report-section">
          <h2>Detailed Results</h2>
          
          {% for result in multi_domain_results %}
          <div class="domain-section">
            <h3>{{ result.record.hostname }}.{{ result.record.domain }}</h3>
            <p><strong>Status:</strong> <span class="{{ 'success' if result.success else 'failure' }}">{{ 'Success' if result.success else 'Failure' }}</span></p>
            <p><strong>Message:</strong> {{ result.message }}</p>
            <p><strong>Changed:</strong> {{ 'Yes' if result.changed else 'No' }}</p>
            <p><strong>Timestamp:</strong> {{ result.timestamp }}</p>
            
            {% if result.record %}
            <h4>Record Details</h4>
            <table>
              <tr>
                <th>Property</th>
                <th>Value</th>
              </tr>
              {% for key, value in result.record.items() %}
              <tr>
                <td>{{ key }}</td>
                <td>{{ value }}</td>
              </tr>
              {% endfor %}
            </table>
            {% endif %}
          </div>
          {% endfor %}
        </div>
        
        <div class="footer">
          <p>Generated by DNS Management System</p>
          <p>CES Operational Excellence Team</p>
        </div>
      </body>
      </html>
    dest: "{{ playbook_dir }}/../templates/reports/consolidated_report.html.j2"
    mode: '0644'
    force: no
    
- name: Generate consolidated HTML report
  ansible.builtin.template:
    src: "{{ playbook_dir }}/../templates/reports/consolidated_report.html.j2"
    dest: "{{ report_file_path }}"
    mode: '0644'
    
- name: Log consolidated report generation
  ansible.builtin.lineinfile:
    path: "{{ ansible_log_path }}"
    line: "{{ '%Y-%m-%d %H:%M:%S' | strftime }} - Generated consolidated report at {{ report_file_path }}"
