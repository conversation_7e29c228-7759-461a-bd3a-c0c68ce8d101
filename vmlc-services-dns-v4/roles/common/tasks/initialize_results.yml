---
# Initialize Results Tasks
# Author: CES Operational Excellence Team
# Contributors: <PERSON> (7409)

- name: Initialize DNS operation result
  ansible.builtin.set_fact:
    dns_operation_result:
      success: false
      message: "Operation not yet executed"
      changed: false
      timestamp: "{{ '%Y-%m-%dT%H:%M:%S' | strftime }}"
      record: {}
      operation: "{{ operation }}"
    
- name: Initialize multi-domain results
  ansible.builtin.set_fact:
    multi_domain_results: []
  when: is_multi_domain
    
- name: Display results initialization
  ansible.builtin.debug:
    msg: "Results initialized for {{ operation }} operation"
  when: log_level == "Debug"
