---
# Validate Domain Tasks
# Author: CES Operational Excellence Team
# Contributors: <PERSON> (7409)

- name: Validate domain exists in configuration
  ansible.builtin.assert:
    that:
      - domain in domain_config.domains
    fail_msg: "Domain {{ domain }} is not configured in domain configuration"
    success_msg: "Domain {{ domain }} is valid and configured"
  when: not is_multi_domain
    
- name: Parse domains list for multi-domain operation
  ansible.builtin.set_fact:
    domains_list: "{{ domains.split(',') }}"
  when: is_multi_domain
    
- name: Parse hostnames list for multi-domain operation
  ansible.builtin.set_fact:
    hostnames_list: "{{ hostnames.split(',') }}"
  when: is_multi_domain
    
- name: Validate domains list length matches hostnames list length
  ansible.builtin.assert:
    that:
      - domains_list | length == hostnames_list | length
    fail_msg: "Number of domains ({{ domains_list | length }}) does not match number of hostnames ({{ hostnames_list | length }})"
    success_msg: "Number of domains and hostnames match ({{ domains_list | length }})"
  when: is_multi_domain
    
- name: Validate each domain in domains list exists in configuration
  ansible.builtin.assert:
    that:
      - item in domain_config.domains
    fail_msg: "Domain {{ item }} is not configured in domain configuration"
    success_msg: "Domain {{ item }} is valid and configured"
  loop: "{{ domains_list }}"
  when: is_multi_domain
    
- name: Parse IP addresses list for multi-domain A record add/update operation
  ansible.builtin.set_fact:
    ip_addresses_list: "{{ ip_addresses.split(',') }}"
  when: 
    - is_multi_domain
    - record_type == 'a'
    - operation in ['add', 'update']
    - ip_addresses is defined and ip_addresses != ''
    
- name: Validate IP addresses list length matches domains list length
  ansible.builtin.assert:
    that:
      - ip_addresses_list | length == domains_list | length
    fail_msg: "Number of IP addresses ({{ ip_addresses_list | length }}) does not match number of domains ({{ domains_list | length }})"
    success_msg: "Number of IP addresses and domains match ({{ ip_addresses_list | length }})"
  when: 
    - is_multi_domain
    - record_type == 'a'
    - operation in ['add', 'update']
    - ip_addresses is defined and ip_addresses != ''
    
- name: Parse CNAME targets list for multi-domain CNAME record add/update operation
  ansible.builtin.set_fact:
    cname_targets_list: "{{ cname_targets.split(',') }}"
  when: 
    - is_multi_domain
    - record_type == 'cname'
    - operation in ['add', 'update']
    - cname_targets is defined and cname_targets != ''
    
- name: Validate CNAME targets list length matches domains list length
  ansible.builtin.assert:
    that:
      - cname_targets_list | length == domains_list | length
    fail_msg: "Number of CNAME targets ({{ cname_targets_list | length }}) does not match number of domains ({{ domains_list | length }})"
    success_msg: "Number of CNAME targets and domains match ({{ cname_targets_list | length }})"
  when: 
    - is_multi_domain
    - record_type == 'cname'
    - operation in ['add', 'update']
    - cname_targets is defined and cname_targets != ''
