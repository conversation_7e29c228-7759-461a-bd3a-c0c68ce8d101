---
# Cleanup Tasks
# Author: CES Operational Excellence Team
# Contributors: <PERSON> (7409)

- name: Log cleanup start
  ansible.builtin.lineinfile:
    path: "{{ ansible_log_path }}"
    line: "{{ '%Y-%m-%d %H:%M:%S' | strftime }} - Starting cleanup phase"
    
- name: Find old log files
  ansible.builtin.find:
    paths:
      - "{{ playbook_dir }}/../logs/ansible"
      - "{{ playbook_dir }}/../logs/powershell"
      - "{{ playbook_dir }}/../logs/progress"
    age: 30d
    recurse: no
  register: old_logs
    
- name: Archive old log files
  ansible.builtin.archive:
    path: "{{ item.path }}"
    dest: "{{ playbook_dir }}/../logs/archive/{{ item.path | basename }}.zip"
    format: zip
    remove: true
  loop: "{{ old_logs.files }}"
  when: old_logs.matched > 0
    
- name: Find old report files
  ansible.builtin.find:
    paths:
      - "{{ playbook_dir }}/../reports"
    age: 30d
    recurse: no
  register: old_reports
    
- name: Remove old report files
  ansible.builtin.file:
    path: "{{ item.path }}"
    state: absent
  loop: "{{ old_reports.files }}"
  when: old_reports.matched > 0
    
- name: Find old archive files
  ansible.builtin.find:
    paths:
      - "{{ playbook_dir }}/../logs/archive"
    age: 90d
    recurse: no
  register: old_archives
    
- name: Remove old archive files
  ansible.builtin.file:
    path: "{{ item.path }}"
    state: absent
  loop: "{{ old_archives.files }}"
  when: old_archives.matched > 0
    
- name: Log cleanup completion
  ansible.builtin.lineinfile:
    path: "{{ ansible_log_path }}"
    line: "{{ '%Y-%m-%d %H:%M:%S' | strftime }} - Cleanup phase completed"
