---
# Process Results Tasks
# Author: CES Operational Excellence Team
# Contributors: <PERSON> (7409)

- name: Log operation result
  ansible.builtin.lineinfile:
    path: "{{ ansible_log_path }}"
    line: "{{ '%Y-%m-%d %H:%M:%S' | strftime }} - Operation result: {{ dns_operation_result.message }}"
  when: not is_multi_domain
    
- name: Log multi-domain operation results
  ansible.builtin.lineinfile:
    path: "{{ ansible_log_path }}"
    line: "{{ '%Y-%m-%d %H:%M:%S' | strftime }} - Multi-domain operation results: {{ multi_domain_results | length }} domains processed"
  when: is_multi_domain
    
- name: Log multi-domain operation details
  ansible.builtin.lineinfile:
    path: "{{ ansible_log_path }}"
    line: "{{ '%Y-%m-%d %H:%M:%S' | strftime }} - Domain {{ item.record.domain }}: {{ item.message }}"
  loop: "{{ multi_domain_results }}"
  when: is_multi_domain
