---
# Display Summary Tasks
# Author: CES Operational Excellence Team
# Contributors: <PERSON> (7409)

- name: Display operation summary for single domain
  ansible.builtin.debug:
    msg: |
      Operation Summary:
        Operation: {{ operation | capitalize }}
        Record Type: {{ record_type | upper }}
        Hostname: {{ hostname }}.{{ domain }}
        Result: {{ 'Success' if dns_operation_result.success else 'Failure' }}
        Message: {{ dns_operation_result.message }}
        Logs: {{ ansible_log_path }}
        Report: {{ report_file_path if generate_report | bool else 'Not generated' }}
  when: not is_multi_domain
    
- name: Display operation summary for multi-domain
  ansible.builtin.debug:
    msg: |
      Operation Summary:
        Operation: {{ operation | capitalize }}
        Record Type: {{ record_type | upper }}
        Domains: {{ domains }}
        Results:
          {% for result in multi_domain_results %}
          - {{ result.record.domain }}: {{ result.message }}
          {% endfor %}
        Logs: {{ ansible_log_path }}
        Report: {{ report_file_path if generate_report | bool else 'Not generated' }}
  when: is_multi_domain
