---
# Common Role Main Tasks
# Author: CES Operational Excellence Team
# Contributors: <PERSON> (7409)

- name: Include load_configuration tasks
  ansible.builtin.import_tasks: load_configuration.yml
  tags:
    - always
    - configuration

- name: Include validate_operation tasks
  ansible.builtin.import_tasks: validate_operation.yml
  tags:
    - always
    - configuration

- name: Include validate_domain tasks
  ansible.builtin.import_tasks: validate_domain.yml
  tags:
    - always
    - configuration

- name: Include setup_credentials tasks
  ansible.builtin.import_tasks: setup_credentials.yml
  tags:
    - always
    - loading

- name: Include setup_logging tasks
  ansible.builtin.import_tasks: setup_logging.yml
  tags:
    - always
    - loading

- name: Include initialize_results tasks
  ansible.builtin.import_tasks: initialize_results.yml
  tags:
    - always
    - loading
