---
# Handle Errors Tasks
# Author: CES Operational Excellence Team
# Contributors: <PERSON> (7409)

- name: Log error
  ansible.builtin.lineinfile:
    path: "{{ ansible_log_path }}"
    line: "{{ '%Y-%m-%d %H:%M:%S' | strftime }} - Error occurred: {{ ansible_failed_result | default('Unknown error') }}"
    
- name: Set error result
  ansible.builtin.set_fact:
    dns_operation_result:
      success: false
      message: "Error: {{ ansible_failed_result | default('Unknown error') }}"
      changed: false
      timestamp: "{{ '%Y-%m-%dT%H:%M:%S' | strftime }}"
      record: {}
      operation: "{{ operation }}"
  when: not is_multi_domain
    
- name: Add error result to multi-domain results
  ansible.builtin.set_fact:
    multi_domain_results: "{{ multi_domain_results + [{'success': false, 'message': 'Error: ' + (ansible_failed_result | default('Unknown error')), 'changed': false, 'timestamp': '%Y-%m-%dT%H:%M:%S' | strftime, 'record': {'domain': current_domain | default('unknown'), 'hostname': current_hostname | default('unknown'), 'type': record_type}, 'operation': operation}] }}"
  when: is_multi_domain and current_domain is defined
