---
# Setup Logging Tasks
# Author: CES Operational Excellence Team
# Contributors: <PERSON> (7409)

- name: Set timestamp
  ansible.builtin.set_fact:
    timestamp: "{{ '%Y%m%d' | strftime }}"
    
- name: Set log file paths for single domain operation
  ansible.builtin.set_fact:
    ansible_log_path: "{{ playbook_dir }}/../logs/ansible/{{ timestamp }}_{{ ticket }}_{{ hostname }}_{{ domain }}_{{ record_type | upper }}_{{ operation | upper }}_ANSIBLE.log"
    powershell_log_path: "{{ playbook_dir }}/../logs/powershell/{{ timestamp }}_{{ ticket }}_{{ hostname }}_{{ domain }}_{{ record_type | upper }}_{{ operation | upper }}_POWERSHELL.log"
    progress_log_path: "{{ playbook_dir }}/../logs/progress/{{ timestamp }}_{{ ticket }}_{{ hostname }}_{{ domain }}_{{ record_type | upper }}_{{ operation | upper }}_PROGRESS.log"
  when: not is_multi_domain
    
- name: Set log file paths for multi-domain operation
  ansible.builtin.set_fact:
    ansible_log_path: "{{ playbook_dir }}/../logs/ansible/{{ timestamp }}_{{ ticket }}_MULTI_DOMAIN_{{ record_type | upper }}_{{ operation | upper }}_ANSIBLE.log"
    powershell_log_path: "{{ playbook_dir }}/../logs/powershell/{{ timestamp }}_{{ ticket }}_MULTI_DOMAIN_{{ record_type | upper }}_{{ operation | upper }}_POWERSHELL.log"
    progress_log_path: "{{ playbook_dir }}/../logs/progress/{{ timestamp }}_{{ ticket }}_MULTI_DOMAIN_{{ record_type | upper }}_{{ operation | upper }}_PROGRESS.log"
  when: is_multi_domain
    
- name: Create log directories
  ansible.builtin.file:
    path: "{{ item }}"
    state: directory
    mode: '0755'
  loop:
    - "{{ playbook_dir }}/../logs/ansible"
    - "{{ playbook_dir }}/../logs/powershell"
    - "{{ playbook_dir }}/../logs/progress"
    - "{{ playbook_dir }}/../logs/archive"
    
- name: Create ansible log file
  ansible.builtin.copy:
    content: "--- Ansible Log for {{ operation | upper }} {{ record_type | upper }} Record Operation ---\nTimestamp: {{ '%Y-%m-%d %H:%M:%S' | strftime }}\nTicket: {{ ticket }}\n"
    dest: "{{ ansible_log_path }}"
    mode: '0644'
    
- name: Create powershell log file
  ansible.builtin.copy:
    content: "--- PowerShell Log for {{ operation | upper }} {{ record_type | upper }} Record Operation ---\nTimestamp: {{ '%Y-%m-%d %H:%M:%S' | strftime }}\nTicket: {{ ticket }}\n"
    dest: "{{ powershell_log_path }}"
    mode: '0644'
    
- name: Create progress log file
  ansible.builtin.copy:
    content: "--- Progress Log for {{ operation | upper }} {{ record_type | upper }} Record Operation ---\nTimestamp: {{ '%Y-%m-%d %H:%M:%S' | strftime }}\nTicket: {{ ticket }}\n"
    dest: "{{ progress_log_path }}"
    mode: '0644'
    
- name: Display log setup
  ansible.builtin.debug:
    msg: "Logs set up at {{ ansible_log_path }}"
  when: log_level == "Debug"
