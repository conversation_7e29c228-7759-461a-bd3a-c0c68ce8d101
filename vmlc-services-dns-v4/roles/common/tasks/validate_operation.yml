---
# Validate Operation Tasks
# Author: CES Operational Excellence Team
# Contributors: <PERSON> (7409)

- name: Validate operation
  ansible.builtin.assert:
    that:
      - operation in ['verify', 'add', 'remove', 'update']
    fail_msg: "Invalid operation: {{ operation }}. Valid operations are: verify, add, remove, update"
    success_msg: "Valid operation: {{ operation }}"
    
- name: Validate record type
  ansible.builtin.assert:
    that:
      - record_type in ['a', 'cname', 'ptr']
    fail_msg: "Invalid record type: {{ record_type }}. Valid record types are: a, cname, ptr"
    success_msg: "Valid record type: {{ record_type }}"
    
- name: Validate ticket
  ansible.builtin.assert:
    that:
      - ticket is defined and ticket != ''
    fail_msg: "Ticket number is required"
    success_msg: "Valid ticket number: {{ ticket }}"
    
- name: Validate hostname for single domain operation
  ansible.builtin.assert:
    that:
      - hostname is defined and hostname != ''
    fail_msg: "Hostname is required for single domain operations"
    success_msg: "Valid hostname: {{ hostname }}"
  when: not is_multi_domain
    
- name: Validate domain for single domain operation
  ansible.builtin.assert:
    that:
      - domain is defined and domain != ''
    fail_msg: "Domain is required for single domain operations"
    success_msg: "Valid domain: {{ domain }}"
  when: not is_multi_domain
    
- name: Validate domains for multi-domain operation
  ansible.builtin.assert:
    that:
      - domains is defined and domains != ''
    fail_msg: "Domains list is required for multi-domain operations"
    success_msg: "Valid domains list: {{ domains }}"
  when: is_multi_domain
    
- name: Validate hostnames for multi-domain operation
  ansible.builtin.assert:
    that:
      - hostnames is defined and hostnames != ''
    fail_msg: "Hostnames list is required for multi-domain operations"
    success_msg: "Valid hostnames list: {{ hostnames }}"
  when: is_multi_domain
    
- name: Validate IP address for A record add/update operation
  ansible.builtin.assert:
    that:
      - ip_address is defined and ip_address != ''
    fail_msg: "IP address is required for A record add/update operations"
    success_msg: "Valid IP address: {{ ip_address }}"
  when: 
    - not is_multi_domain
    - record_type == 'a'
    - operation in ['add', 'update']
    
- name: Validate IP addresses for multi-domain A record add/update operation
  ansible.builtin.assert:
    that:
      - ip_addresses is defined and ip_addresses != ''
    fail_msg: "IP addresses list is required for multi-domain A record add/update operations"
    success_msg: "Valid IP addresses list: {{ ip_addresses }}"
  when: 
    - is_multi_domain
    - record_type == 'a'
    - operation in ['add', 'update']
    
- name: Validate CNAME target for CNAME record add/update operation
  ansible.builtin.assert:
    that:
      - cname_target is defined and cname_target != ''
    fail_msg: "CNAME target is required for CNAME record add/update operations"
    success_msg: "Valid CNAME target: {{ cname_target }}"
  when: 
    - not is_multi_domain
    - record_type == 'cname'
    - operation in ['add', 'update']
    
- name: Validate CNAME targets for multi-domain CNAME record add/update operation
  ansible.builtin.assert:
    that:
      - cname_targets is defined and cname_targets != ''
    fail_msg: "CNAME targets list is required for multi-domain CNAME record add/update operations"
    success_msg: "Valid CNAME targets list: {{ cname_targets }}"
  when: 
    - is_multi_domain
    - record_type == 'cname'
    - operation in ['add', 'update']
