---
# Load Configuration Tasks
# Author: CES Operational Excellence Team
# Contributors: <PERSON> (7409)

- name: Load run_config.json
  ansible.builtin.set_fact:
    run_config: "{{ lookup('file', playbook_dir + '/../run_config.json') | from_json }}"
  
- name: Set operation parameters from run_config
  ansible.builtin.set_fact:
    operation: "{{ run_config.operation | default('verify') }}"
    record_type: "{{ run_config.record_type | default('a') }}"
    hostname: "{{ run_config.hostname | default('') }}"
    domain: "{{ run_config.domain | default('') }}"
    domains: "{{ run_config.domains | default('') }}"
    hostnames: "{{ run_config.hostnames | default('') }}"
    ip_address: "{{ run_config.ip_address | default('') }}"
    ip_addresses: "{{ run_config.ip_addresses | default('') }}"
    cname_target: "{{ run_config.cname_target | default('') }}"
    cname_targets: "{{ run_config.cname_targets | default('') }}"
    ticket: "{{ run_config.ticket | default('') }}"
    ttl: "{{ run_config.ttl | default(3600) }}"
    description: "{{ run_config.description | default('Managed by Ansible DNS Management') }}"
    manage_ptr: "{{ run_config.manage_ptr | default(true) | bool }}"
    force_remove: "{{ run_config.force_remove | default(false) | bool }}"
    email_report: "{{ run_config.email_report | default(false) | bool }}"
    email_logs: "{{ run_config.email_logs | default(false) | bool }}"
    email_recipient: "{{ run_config.email_recipient | default('') }}"
    testing_mode: "{{ run_config.testing_mode | default(false) | bool }}"
    log_level: "{{ run_config.log_level | default('Info') }}"
    store_logs_target_server: "{{ run_config.store_logs_target_server | default(true) | bool }}"
    generate_report: "{{ run_config.generate_report | default(true) | bool }}"
    is_multi_domain: "{{ run_config.is_multi_domain | default(false) | bool }}"
    async_operations: "{{ run_config.async_operations | default(false) | bool }}"
    domain_group: "{{ run_config.domain_group | default('') }}"
    
- name: Load domain configuration
  ansible.builtin.include_vars:
    file: "{{ playbook_dir }}/../group_vars/all/domains.yml"
    name: domain_config
    
- name: Set is_multi_domain based on domains parameter
  ansible.builtin.set_fact:
    is_multi_domain: true
  when: domains is defined and domains != ''
    
- name: Display loaded configuration
  ansible.builtin.debug:
    msg: "Loaded configuration for {{ operation }} operation on {{ 'multiple domains' if is_multi_domain else domain }}"
  when: log_level == "Debug"
