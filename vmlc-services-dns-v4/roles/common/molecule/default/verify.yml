---
- name: Verify
  hosts: all
  gather_facts: false
  
  tasks:
    - name: Check if log directories exist
      ansible.builtin.stat:
        path: "/tmp/logs/{{ item }}"
      register: log_dirs
      loop:
        - ansible
        - powershell
    
    - name: Verify log directories exist
      ansible.builtin.assert:
        that:
          - log_dirs.results[0].stat.exists
          - log_dirs.results[0].stat.isdir
          - log_dirs.results[1].stat.exists
          - log_dirs.results[1].stat.isdir
        fail_msg: "Log directories do not exist"
        success_msg: "Log directories exist"
    
    - name: Verify ansible_facts are set
      ansible.builtin.assert:
        that:
          - ansible_facts.dns_operation_result is defined
        fail_msg: "DNS operation result facts not set"
        success_msg: "DNS operation result facts set correctly"
