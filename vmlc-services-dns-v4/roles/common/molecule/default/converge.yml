---
- name: Converge
  hosts: all
  gather_facts: true
  
  vars:
    operation: verify
    record_type: a
    hostname: test
    domain: test.local
    ticket: TEST123
    
    # Mock domain configuration
    domains:
      test.local:
        description: "Test Domain"
        environment: test
        network_zone: test
        dc: local
        dns_server: localhost
        ptr_dns_server: localhost
        admt_server: localhost
    
    # Mock domain credentials
    domain_credentials:
      test.local:
        dns_username: "administrator"
        dns_password: "Password123!"
  
  tasks:
    - name: Create test directories
      ansible.builtin.file:
        path: "{{ item }}"
        state: directory
        mode: '0755'
      loop:
        - /tmp/logs/ansible
        - /tmp/logs/powershell
    
    - name: Include common role tasks
      ansible.builtin.include_role:
        name: common
        tasks_from: "{{ item }}"
      loop:
        - validate_operation
        - validate_domain
        - setup_credentials
        - setup_logging
        - initialize_results
