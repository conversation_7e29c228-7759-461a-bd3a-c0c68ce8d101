---
# Remove DNS Record Tasks
# Author: CES Operational Excellence Team
# Contributors: <PERSON> (7409)

- name: Get DNS server for domain
  ansible.builtin.set_fact:
    dns_server: "{{ domain_config.domains[domain].dns_server }}"
    ptr_dns_server: "{{ domain_config.domains[domain].ptr_dns_server }}"
  when: not is_multi_domain

- name: Log remove operation start
  ansible.builtin.lineinfile:
    path: "{{ ansible_log_path }}"
    line: "{{ '%Y-%m-%d %H:%M:%S' | strftime }} - Starting remove operation for {{ record_type | upper }} record {{ hostname }}.{{ domain }}"
  when: not is_multi_domain

- name: Execute PowerShell script to remove DNS record
  ansible.windows.win_shell: |
    $LogPath = "{{ powershell_log_path }}"
    $ScriptPath = "{{ playbook_dir }}/../scripts/set-dns.ps1"
    
    & $ScriptPath -Operation "remove" -RecordType "{{ record_type }}" -Hostname "{{ hostname }}" -Domain "{{ domain }}" -DNSServer "{{ dns_server }}" -PTRDNSServer "{{ ptr_dns_server }}" -LogPath $LogPath -LogLevel "{{ log_level }}" -AsJson $true -ManagePTR ${{ manage_ptr | lower }} -Force ${{ force_remove | lower }}
  args:
    executable: powershell.exe
  register: remove_result
  delegate_to: "{{ domain_config.domains[domain].admt_server }}"
  when: not is_multi_domain
  
- name: Parse remove result
  ansible.builtin.set_fact:
    dns_operation_result: "{{ remove_result.stdout | from_json }}"
  when: not is_multi_domain and remove_result.stdout is defined and remove_result.stdout != ""
  
- name: Log remove operation result
  ansible.builtin.lineinfile:
    path: "{{ ansible_log_path }}"
    line: "{{ '%Y-%m-%d %H:%M:%S' | strftime }} - Remove operation result: {{ dns_operation_result.message }}"
  when: not is_multi_domain
  
- name: Process multi-domain remove operation
  ansible.builtin.include_tasks: process_domain.yml
  loop: "{{ range(0, domains_list | length) | list }}"
  loop_control:
    loop_var: domain_index
  when: is_multi_domain
