---
# Add DNS Record Tasks
# Author: CES Operational Excellence Team
# Contributors: <PERSON> (7409)

- name: Get DNS server for domain
  ansible.builtin.set_fact:
    dns_server: "{{ domain_config.domains[domain].dns_server }}"
    ptr_dns_server: "{{ domain_config.domains[domain].ptr_dns_server }}"
  when: not is_multi_domain

- name: Log add operation start
  ansible.builtin.lineinfile:
    path: "{{ ansible_log_path }}"
    line: "{{ '%Y-%m-%d %H:%M:%S' | strftime }} - Starting add operation for {{ record_type | upper }} record {{ hostname }}.{{ domain }}"
  when: not is_multi_domain

- name: Execute PowerShell script to add A or PTR record
  ansible.windows.win_shell: |
    $LogPath = "{{ powershell_log_path }}"
    $ScriptPath = "{{ playbook_dir }}/../scripts/set-dns.ps1"
    
    & $ScriptPath -Operation "add" -RecordType "{{ record_type }}" -Hostname "{{ hostname }}" -Domain "{{ domain }}" -IPAddress "{{ ip_address }}" -DNSServer "{{ dns_server }}" -PTRDNSServer "{{ ptr_dns_server }}" -LogPath $LogPath -LogLevel "{{ log_level }}" -AsJson $true -ManagePTR ${{ manage_ptr | lower }} -Force $false
  args:
    executable: powershell.exe
  register: add_result
  delegate_to: "{{ domain_config.domains[domain].admt_server }}"
  when: not is_multi_domain and (record_type == 'a' or record_type == 'ptr')
  
- name: Execute PowerShell script to add CNAME record
  ansible.windows.win_shell: |
    $LogPath = "{{ powershell_log_path }}"
    $ScriptPath = "{{ playbook_dir }}/../scripts/set-dns.ps1"
    
    & $ScriptPath -Operation "add" -RecordType "{{ record_type }}" -Hostname "{{ hostname }}" -Domain "{{ domain }}" -Target "{{ cname_target }}" -DNSServer "{{ dns_server }}" -LogPath $LogPath -LogLevel "{{ log_level }}" -AsJson $true
  args:
    executable: powershell.exe
  register: add_result
  delegate_to: "{{ domain_config.domains[domain].admt_server }}"
  when: not is_multi_domain and record_type == 'cname'
  
- name: Parse add result
  ansible.builtin.set_fact:
    dns_operation_result: "{{ add_result.stdout | from_json }}"
  when: not is_multi_domain and add_result.stdout is defined and add_result.stdout != ""
  
- name: Log add operation result
  ansible.builtin.lineinfile:
    path: "{{ ansible_log_path }}"
    line: "{{ '%Y-%m-%d %H:%M:%S' | strftime }} - Add operation result: {{ dns_operation_result.message }}"
  when: not is_multi_domain
  
- name: Process multi-domain add operation
  ansible.builtin.include_tasks: process_domain.yml
  loop: "{{ range(0, domains_list | length) | list }}"
  loop_control:
    loop_var: domain_index
  when: is_multi_domain
