---
# Process Domain Tasks
# Author: CES Operational Excellence Team
# Contributors: <PERSON> (7409)

- name: Set current domain and hostname
  ansible.builtin.set_fact:
    current_domain: "{{ domains_list[domain_index] }}"
    current_hostname: "{{ hostnames_list[domain_index] }}"
    
- name: Set current IP address for A or PTR records
  ansible.builtin.set_fact:
    current_ip_address: "{{ ip_addresses_list[domain_index] }}"
  when: 
    - record_type == 'a' or record_type == 'ptr'
    - operation in ['add', 'update']
    - ip_addresses is defined and ip_addresses != ''
    
- name: Set current CNAME target for CNAME records
  ansible.builtin.set_fact:
    current_cname_target: "{{ cname_targets_list[domain_index] }}"
  when: 
    - record_type == 'cname'
    - operation in ['add', 'update']
    - cname_targets is defined and cname_targets != ''
    
- name: Get DNS server for current domain
  ansible.builtin.set_fact:
    current_dns_server: "{{ domain_config.domains[current_domain].dns_server }}"
    
- name: Log operation start for current domain
  ansible.builtin.lineinfile:
    path: "{{ ansible_log_path }}"
    line: "{{ '%Y-%m-%d %H:%M:%S' | strftime }} - Starting {{ operation }} operation for {{ record_type | upper }} record {{ current_hostname }}.{{ current_domain }}"
    
- name: Execute PowerShell script for verify operation
  ansible.windows.win_shell: |
    $LogPath = "{{ powershell_log_path }}"
    $ScriptPath = "{{ playbook_dir }}/../scripts/set-dns.ps1"
    
    & $ScriptPath -Operation "{{ operation }}" -RecordType "{{ record_type }}" -Hostname "{{ current_hostname }}" -Domain "{{ current_domain }}" -DNSServer "{{ current_dns_server }}" -LogPath $LogPath -LogLevel "{{ log_level }}" -AsJson $true
  args:
    executable: powershell.exe
  register: domain_result
  delegate_to: "{{ domain_config.domains[current_domain].admt_server }}"
  when: operation == 'verify'
    
- name: Execute PowerShell script for add operation
  ansible.windows.win_shell: |
    $LogPath = "{{ powershell_log_path }}"
    $ScriptPath = "{{ playbook_dir }}/../scripts/set-dns.ps1"
    
    & $ScriptPath -Operation "{{ operation }}" -RecordType "{{ record_type }}" -Hostname "{{ current_hostname }}" -Domain "{{ current_domain }}" -IPAddress "{{ current_ip_address }}" -DNSServer "{{ current_dns_server }}" -PTRDNSServer "{{ domain_config.domains[current_domain].ptr_dns_server }}" -LogPath $LogPath -LogLevel "{{ log_level }}" -AsJson $true -ManagePTR ${{ manage_ptr | lower }} -Force $false
  args:
    executable: powershell.exe
  register: domain_result
  delegate_to: "{{ domain_config.domains[current_domain].admt_server }}"
  when: 
    - operation == 'add'
    - record_type == 'a' or record_type == 'ptr'
    
- name: Execute PowerShell script for add CNAME operation
  ansible.windows.win_shell: |
    $LogPath = "{{ powershell_log_path }}"
    $ScriptPath = "{{ playbook_dir }}/../scripts/set-dns.ps1"
    
    & $ScriptPath -Operation "{{ operation }}" -RecordType "{{ record_type }}" -Hostname "{{ current_hostname }}" -Domain "{{ current_domain }}" -Target "{{ current_cname_target }}" -DNSServer "{{ current_dns_server }}" -LogPath $LogPath -LogLevel "{{ log_level }}" -AsJson $true
  args:
    executable: powershell.exe
  register: domain_result
  delegate_to: "{{ domain_config.domains[current_domain].admt_server }}"
  when: 
    - operation == 'add'
    - record_type == 'cname'
    
- name: Execute PowerShell script for remove operation
  ansible.windows.win_shell: |
    $LogPath = "{{ powershell_log_path }}"
    $ScriptPath = "{{ playbook_dir }}/../scripts/set-dns.ps1"
    
    & $ScriptPath -Operation "{{ operation }}" -RecordType "{{ record_type }}" -Hostname "{{ current_hostname }}" -Domain "{{ current_domain }}" -DNSServer "{{ current_dns_server }}" -PTRDNSServer "{{ domain_config.domains[current_domain].ptr_dns_server }}" -LogPath $LogPath -LogLevel "{{ log_level }}" -AsJson $true -ManagePTR ${{ manage_ptr | lower }} -Force ${{ force_remove | lower }}
  args:
    executable: powershell.exe
  register: domain_result
  delegate_to: "{{ domain_config.domains[current_domain].admt_server }}"
  when: operation == 'remove'
    
- name: Execute PowerShell script for update operation
  ansible.windows.win_shell: |
    $LogPath = "{{ powershell_log_path }}"
    $ScriptPath = "{{ playbook_dir }}/../scripts/set-dns.ps1"
    
    & $ScriptPath -Operation "{{ operation }}" -RecordType "{{ record_type }}" -Hostname "{{ current_hostname }}" -Domain "{{ current_domain }}" -IPAddress "{{ current_ip_address }}" -DNSServer "{{ current_dns_server }}" -PTRDNSServer "{{ domain_config.domains[current_domain].ptr_dns_server }}" -LogPath $LogPath -LogLevel "{{ log_level }}" -AsJson $true -ManagePTR ${{ manage_ptr | lower }}
  args:
    executable: powershell.exe
  register: domain_result
  delegate_to: "{{ domain_config.domains[current_domain].admt_server }}"
  when: 
    - operation == 'update'
    - record_type == 'a' or record_type == 'ptr'
    
- name: Execute PowerShell script for update CNAME operation
  ansible.windows.win_shell: |
    $LogPath = "{{ powershell_log_path }}"
    $ScriptPath = "{{ playbook_dir }}/../scripts/set-dns.ps1"
    
    & $ScriptPath -Operation "{{ operation }}" -RecordType "{{ record_type }}" -Hostname "{{ current_hostname }}" -Domain "{{ current_domain }}" -Target "{{ current_cname_target }}" -DNSServer "{{ current_dns_server }}" -LogPath $LogPath -LogLevel "{{ log_level }}" -AsJson $true
  args:
    executable: powershell.exe
  register: domain_result
  delegate_to: "{{ domain_config.domains[current_domain].admt_server }}"
  when: 
    - operation == 'update'
    - record_type == 'cname'
    
- name: Parse domain result
  ansible.builtin.set_fact:
    current_result: "{{ domain_result.stdout | from_json }}"
  when: domain_result.stdout is defined and domain_result.stdout != ""
    
- name: Add current result to multi-domain results
  ansible.builtin.set_fact:
    multi_domain_results: "{{ multi_domain_results + [current_result] }}"
  when: current_result is defined
    
- name: Log operation result for current domain
  ansible.builtin.lineinfile:
    path: "{{ ansible_log_path }}"
    line: "{{ '%Y-%m-%d %H:%M:%S' | strftime }} - {{ operation | capitalize }} operation result for {{ current_domain }}: {{ current_result.message }}"
  when: current_result is defined
