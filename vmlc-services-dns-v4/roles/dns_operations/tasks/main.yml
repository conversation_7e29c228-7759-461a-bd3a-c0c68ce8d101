---
# DNS Operations Role Main Tasks
# Author: CES Operational Excellence Team
# Contributors: <PERSON> (7409)

- name: Include verify tasks
  ansible.builtin.import_tasks: verify.yml
  when: operation == 'verify'
  tags:
    - verify
    - execution

- name: Include add tasks
  ansible.builtin.import_tasks: add.yml
  when: operation == 'add'
  tags:
    - add
    - execution

- name: Include remove tasks
  ansible.builtin.import_tasks: remove.yml
  when: operation == 'remove'
  tags:
    - remove
    - execution

- name: Include update tasks
  ansible.builtin.import_tasks: update.yml
  when: operation == 'update'
  tags:
    - update
    - execution
