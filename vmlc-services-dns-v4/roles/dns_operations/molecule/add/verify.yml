---
- name: Verify
  hosts: all
  gather_facts: false
  
  tasks:
    - name: Verify dns_operation_result is updated correctly for add operation
      ansible.builtin.assert:
        that:
          - ansible_facts.dns_operation_result is defined
          - ansible_facts.dns_operation_result.success | bool
          - ansible_facts.dns_operation_result.message is search("added with IP address")
          - ansible_facts.dns_operation_result.changed == true
          - ansible_facts.dns_operation_result.operation == "add"
          - ansible_facts.dns_operation_result.record.ip_address == "************"
        fail_msg: "DNS operation result not updated correctly for add operation"
        success_msg: "DNS operation result updated correctly for add operation"
