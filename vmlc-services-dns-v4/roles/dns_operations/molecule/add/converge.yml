---
- name: Converge
  hosts: all
  gather_facts: true
  
  vars:
    operation: add
    record_type: a
    hostname: test
    domain: test.local
    ip_address: ************
    ticket: TEST123
    
    # Mock domain configuration
    domains:
      test.local:
        description: "Test Domain"
        environment: test
        network_zone: test
        dc: local
        dns_server: localhost
        ptr_dns_server: localhost
        admt_server: localhost
    
    # Mock domain credentials
    domain_credentials:
      test.local:
        dns_username: "administrator"
        dns_password: "Password123!"
    
    # Mock DNS operation result
    dns_operation_result:
      success: false
      message: "Operation not yet executed"
      changed: false
    
    # Mock log paths
    ansible_log_path: "/tmp/logs/ansible/test.log"
    powershell_log_path: "/tmp/logs/powershell/test.log"
    
    # Mock PowerShell settings
    powershell_settings:
      scripts:
        dns_management: "/tmp/set-dns.ps1"
        connectivity_test: "/tmp/test-connectivity.ps1"
      timeouts:
        ping: 1000
        dns: 5000
        script: 300
  
  tasks:
    - name: Create test directories
      ansible.builtin.file:
        path: "{{ item }}"
        state: directory
        mode: '0755'
      loop:
        - /tmp/logs/ansible
        - /tmp/logs/powershell
    
    - name: Create mock PowerShell script for add operation
      ansible.builtin.copy:
        content: |
          param (
            [string]$Operation,
            [string]$RecordType,
            [string]$Hostname,
            [string]$Domain,
            [string]$IPAddress = "none",
            [string]$Target = "none",
            [string]$DNSServer,
            [string]$PTRDNSServer,
            [bool]$AsJson = $true,
            [bool]$Force = $false,
            [bool]$ManagePTR = $true,
            [string]$LogPath,
            [string]$LogLevel = "INFO"
          )
          
          # Mock add operation
          $result = @{
            success = $true
            message = "A record $Hostname.$Domain added with IP address $IPAddress"
            changed = $true
            timestamp = (Get-Date).ToString("yyyy-MM-ddTHH:mm:ss")
            record = @{
              domain = $Domain
              hostname = $Hostname
              type = $RecordType
              fqdn = "$Hostname.$Domain"
              ip_address = $IPAddress
              ttl = 3600
              description = "Test record"
            }
            operation = $Operation
          }
          
          # Output as JSON
          if ($AsJson) {
            $result | ConvertTo-Json
          }
          else {
            $result
          }
        dest: /tmp/set-dns.ps1
        mode: '0755'
    
    - name: Include dns_operations role add task
      ansible.builtin.include_role:
        name: dns_operations
        tasks_from: add.yml
