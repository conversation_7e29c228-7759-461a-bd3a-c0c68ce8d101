---
- name: Verify
  hosts: all
  gather_facts: false
  
  tasks:
    - name: Verify dns_operation_result is updated correctly for verify operation
      ansible.builtin.assert:
        that:
          - ansible_facts.dns_operation_result is defined
          - ansible_facts.dns_operation_result.success | bool
          - ansible_facts.dns_operation_result.message is search("exists with IP address")
          - ansible_facts.dns_operation_result.changed == false
          - ansible_facts.dns_operation_result.operation == "verify"
          - ansible_facts.dns_operation_result.record.ip_address == "************"
        fail_msg: "DNS operation result not updated correctly for verify operation"
        success_msg: "DNS operation result updated correctly for verify operation"
