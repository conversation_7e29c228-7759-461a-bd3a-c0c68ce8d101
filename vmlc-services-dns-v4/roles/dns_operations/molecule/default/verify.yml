---
- name: Verify
  hosts: all
  gather_facts: false
  
  tasks:
    - name: Check if log files exist
      ansible.builtin.stat:
        path: "/tmp/logs/{{ item }}/test.log"
      register: log_files
      loop:
        - ansible
        - powershell
    
    - name: Verify log files exist
      ansible.builtin.assert:
        that:
          - log_files.results[0].stat.exists
          - log_files.results[1].stat.exists
        fail_msg: "Log files do not exist"
        success_msg: "Log files exist"
    
    - name: Verify dns_operation_result is updated
      ansible.builtin.assert:
        that:
          - ansible_facts.dns_operation_result is defined
          - ansible_facts.dns_operation_result.success | bool
          - ansible_facts.dns_operation_result.message != "Operation not yet executed"
        fail_msg: "DNS operation result not updated correctly"
        success_msg: "DNS operation result updated correctly"
