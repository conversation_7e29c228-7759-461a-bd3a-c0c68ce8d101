[defaults]
inventory = inventories
roles_path = roles
collections_paths = collections
retry_files_enabled = False
host_key_checking = False
callback_whitelist = timer, profile_tasks
stdout_callback = yaml
bin_ansible_callbacks = True
force_color = 1
deprecation_warnings = False
command_warnings = False
interpreter_python = auto_silent
jinja2_extensions = jinja2.ext.do,jinja2.ext.loopcontrols

[ssh_connection]
pipelining = True
ssh_args = -o ControlMaster=auto -o ControlPersist=60s -o StrictHostKeyChecking=no

[inventory]
enable_plugins = host_list, script, auto, yaml, ini, toml

[callback_profile_tasks]
task_output_limit = 100
