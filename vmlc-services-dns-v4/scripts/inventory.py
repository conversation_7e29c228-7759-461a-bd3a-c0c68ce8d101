#!/usr/bin/env python3
"""
Dynamic Inventory Script for DNS Management
Author: CES Operational Excellence Team
Contributors: <PERSON> (7409)

This script generates a dynamic inventory for DNS Management operations.
It reads domain configuration and creates inventory groups for:
- DNS servers
- ADMT servers
- Domain groups
"""

import argparse
import json
import os
import sys
import yaml
import redis
from datetime import datetime, timedelta

# Configuration paths
DOMAINS_CONFIG_PATH = os.path.join(os.path.dirname(os.path.dirname(os.path.abspath(__file__))), "group_vars", "all", "domains.yml")
CACHE_TTL = 3600  # 1 hour cache TTL

# Redis configuration (if available)
REDIS_HOST = os.environ.get("REDIS_HOST", "localhost")
REDIS_PORT = int(os.environ.get("REDIS_PORT", 6379))
REDIS_DB = int(os.environ.get("REDIS_DB", 0))
REDIS_KEY_PREFIX = "dns_inventory:"
USE_REDIS = os.environ.get("USE_REDIS", "false").lower() == "true"

def load_yaml_file(file_path):
    """Load YAML file and return as dictionary"""
    try:
        with open(file_path, 'r') as file:
            return yaml.safe_load(file)
    except Exception as e:
        print(f"Error loading {file_path}: {e}", file=sys.stderr)
        return {}

def get_redis_client():
    """Get Redis client if available"""
    if USE_REDIS:
        try:
            return redis.Redis(host=REDIS_HOST, port=REDIS_PORT, db=REDIS_DB)
        except Exception as e:
            print(f"Error connecting to Redis: {e}", file=sys.stderr)
    return None

def get_cached_inventory(redis_client, cache_key):
    """Get cached inventory from Redis"""
    if redis_client:
        try:
            cached = redis_client.get(cache_key)
            if cached:
                return json.loads(cached)
        except Exception as e:
            print(f"Error getting cached inventory: {e}", file=sys.stderr)
    return None

def set_cached_inventory(redis_client, cache_key, inventory):
    """Set cached inventory in Redis"""
    if redis_client:
        try:
            redis_client.setex(cache_key, CACHE_TTL, json.dumps(inventory))
        except Exception as e:
            print(f"Error setting cached inventory: {e}", file=sys.stderr)

def build_inventory(domains_config):
    """Build inventory from domains configuration"""
    inventory = {
        "_meta": {
            "hostvars": {}
        },
        "all": {
            "children": ["dns_servers", "admt_servers", "domain_groups"]
        },
        "dns_servers": {
            "hosts": []
        },
        "admt_servers": {
            "hosts": []
        },
        "domain_groups": {
            "children": []
        }
    }
    
    # Process domains
    domains = domains_config.get("domains", {})
    for domain_name, domain_info in domains.items():
        # Add DNS server
        dns_server = domain_info.get("dns_server", "")
        if dns_server and dns_server not in inventory["dns_servers"]["hosts"]:
            inventory["dns_servers"]["hosts"].append(dns_server)
            inventory["_meta"]["hostvars"][dns_server] = {
                "ansible_connection": "winrm",
                "ansible_winrm_server_cert_validation": "ignore",
                "domain": domain_name,
                "environment": domain_info.get("environment", ""),
                "network_zone": domain_info.get("network_zone", ""),
                "dc": domain_info.get("dc", "")
            }
        
        # Add ADMT server
        admt_server = domain_info.get("admt_server", "")
        if admt_server and admt_server not in inventory["admt_servers"]["hosts"]:
            inventory["admt_servers"]["hosts"].append(admt_server)
            inventory["_meta"]["hostvars"][admt_server] = {
                "ansible_connection": "winrm",
                "ansible_winrm_server_cert_validation": "ignore",
                "domain": domain_name,
                "environment": domain_info.get("environment", ""),
                "network_zone": domain_info.get("network_zone", ""),
                "dc": domain_info.get("dc", "")
            }
    
    # Process domain groups
    domain_groups = domains_config.get("domain_groups", {})
    for group_name, domains_list in domain_groups.items():
        if group_name not in inventory["domain_groups"]["children"]:
            inventory["domain_groups"]["children"].append(group_name)
            inventory[group_name] = {"hosts": []}
            
            # Add DNS and ADMT servers for this domain group
            for domain_name in domains_list:
                if domain_name in domains:
                    dns_server = domains[domain_name].get("dns_server", "")
                    admt_server = domains[domain_name].get("admt_server", "")
                    
                    if dns_server and dns_server not in inventory[group_name]["hosts"]:
                        inventory[group_name]["hosts"].append(dns_server)
                    
                    if admt_server and admt_server not in inventory[group_name]["hosts"]:
                        inventory[group_name]["hosts"].append(admt_server)
    
    return inventory

def main():
    """Main function"""
    parser = argparse.ArgumentParser(description='Dynamic Inventory for DNS Management')
    parser.add_argument('--list', action='store_true', help='List all inventory')
    parser.add_argument('--host', help='Get variables for a specific host')
    parser.add_argument('--refresh-cache', action='store_true', help='Force refresh the cache')
    args = parser.parse_args()
    
    # Initialize Redis client if available
    redis_client = get_redis_client()
    cache_key = f"{REDIS_KEY_PREFIX}inventory"
    
    # Check if we should use cached inventory
    inventory = None
    if not args.refresh_cache and redis_client:
        inventory = get_cached_inventory(redis_client, cache_key)
    
    # Build inventory if not cached or refresh requested
    if inventory is None:
        domains_config = load_yaml_file(DOMAINS_CONFIG_PATH)
        inventory = build_inventory(domains_config)
        
        # Cache inventory
        if redis_client:
            set_cached_inventory(redis_client, cache_key, inventory)
    
    # Return requested data
    if args.host:
        # Return host variables
        host_vars = inventory.get("_meta", {}).get("hostvars", {}).get(args.host, {})
        print(json.dumps(host_vars, indent=2))
    else:
        # Return full inventory
        print(json.dumps(inventory, indent=2))
    
    return 0

if __name__ == "__main__":
    sys.exit(main())
