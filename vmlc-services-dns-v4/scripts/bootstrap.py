#!/usr/bin/env python3
"""
Bootstrap <PERSON>t for DNS Management
Author: CES Operational Excellence Team
Contributors: <PERSON> (7409)

This script bootstraps the DNS Management System by:
1. Creating necessary directories
2. Setting up initial configuration
3. Installing required dependencies
4. Configuring inventory
5. Setting up credentials
"""

import argparse
import json
import os
import subprocess
import sys
import yaml
from datetime import datetime

# Project root directory
PROJECT_ROOT = os.path.dirname(os.path.dirname(os.path.abspath(__file__)))

# Directory paths
DIRECTORIES = [
    "logs/ansible",
    "logs/powershell",
    "logs/progress",
    "logs/archive",
    "reports",
    "inventories/dev",
    "inventories/prod",
    "inventories/dynamic",
    "group_vars/all",
    "host_vars",
    "collections/synapxe.dns/plugins/callback",
    "collections/synapxe.dns/plugins/inventory",
    "tests/schema",
    "tests/molecule",
    "docs/runbooks"
]

def parse_args():
    """Parse command line arguments"""
    parser = argparse.ArgumentParser(description='Bootstrap DNS Management System')
    
    parser.add_argument('--env', choices=['dev', 'test', 'prod'], default='dev',
                        help='Environment to bootstrap')
    parser.add_argument('--install-deps', action='store_true',
                        help='Install dependencies')
    parser.add_argument('--setup-inventory', action='store_true',
                        help='Setup inventory')
    parser.add_argument('--setup-credentials', action='store_true',
                        help='Setup credentials')
    parser.add_argument('--all', action='store_true',
                        help='Perform all bootstrap actions')
    
    return parser.parse_args()

def create_directories():
    """Create necessary directories"""
    print("Creating directories...")
    
    for directory in DIRECTORIES:
        dir_path = os.path.join(PROJECT_ROOT, directory)
        if not os.path.exists(dir_path):
            try:
                os.makedirs(dir_path)
                print(f"  Created: {directory}")
            except Exception as e:
                print(f"  Error creating {directory}: {e}")
        else:
            print(f"  Already exists: {directory}")

def install_dependencies():
    """Install required dependencies"""
    print("Installing dependencies...")
    
    try:
        # Install Ansible Galaxy requirements
        subprocess.run(["ansible-galaxy", "install", "-r", os.path.join(PROJECT_ROOT, "requirements.yml")],
                      check=True)
        print("  Installed Ansible Galaxy requirements")
        
        # Install Python requirements
        subprocess.run(["pip", "install", "-r", os.path.join(PROJECT_ROOT, "requirements.txt")],
                      check=True)
        print("  Installed Python requirements")
    except subprocess.CalledProcessError as e:
        print(f"  Error installing dependencies: {e}")
    except FileNotFoundError as e:
        print(f"  Error: {e}")

def setup_inventory(env):
    """Setup inventory for the specified environment"""
    print(f"Setting up inventory for {env} environment...")
    
    # Create inventory file
    inventory_file = os.path.join(PROJECT_ROOT, f"inventories/{env}/hosts.yml")
    
    if not os.path.exists(inventory_file):
        inventory = {
            "all": {
                "children": {
                    "dns_servers": {
                        "hosts": {}
                    },
                    "admt_servers": {
                        "hosts": {}
                    }
                }
            }
        }
        
        # Add example hosts for dev environment
        if env == "dev":
            inventory["all"]["children"]["dns_servers"]["hosts"] = {
                "localhost": {
                    "ansible_connection": "local"
                }
            }
            inventory["all"]["children"]["admt_servers"]["hosts"] = {
                "localhost": {
                    "ansible_connection": "local"
                }
            }
        
        # Write inventory file
        with open(inventory_file, 'w') as f:
            yaml.dump(inventory, f, default_flow_style=False)
        
        print(f"  Created inventory file: inventories/{env}/hosts.yml")
    else:
        print(f"  Inventory file already exists: inventories/{env}/hosts.yml")

def setup_credentials(env):
    """Setup credentials for the specified environment"""
    print(f"Setting up credentials for {env} environment...")
    
    # Create vault file
    vault_file = os.path.join(PROJECT_ROOT, f"group_vars/{env}/vault.yml")
    vault_dir = os.path.dirname(vault_file)
    
    if not os.path.exists(vault_dir):
        os.makedirs(vault_dir)
    
    if not os.path.exists(vault_file):
        vault_content = """---
# Vault configuration for DNS Management (encrypted in production)
# Author: CES Operational Excellence Team
# Contributors: Muhammad Syazani Bin Mohamed Khairi (7409)

# Domain credentials
credentials:
  domains:
    test.local:
      dns_username: "{{ var_dns_test_username }}"
      dns_password: "{{ var_dns_test_password }}"

# Variables for local testing (these would be encrypted in production)
var_dns_test_username: "administrator"
var_dns_test_password: "Password123!"
"""
        
        with open(vault_file, 'w') as f:
            f.write(vault_content)
        
        print(f"  Created vault file: group_vars/{env}/vault.yml")
        
        # Create vault password file for dev environment
        if env == "dev":
            vault_password_file = os.path.join(PROJECT_ROOT, ".vault_password")
            
            if not os.path.exists(vault_password_file):
                with open(vault_password_file, 'w') as f:
                    f.write("password123")
                
                print(f"  Created vault password file: .vault_password")
                print(f"  WARNING: This is for development only. Do not use in production!")
    else:
        print(f"  Vault file already exists: group_vars/{env}/vault.yml")

def main():
    """Main function"""
    args = parse_args()
    
    print("Bootstrapping DNS Management System...")
    print(f"Project root: {PROJECT_ROOT}")
    
    # Create directories
    create_directories()
    
    # Install dependencies if requested
    if args.install_deps or args.all:
        install_dependencies()
    
    # Setup inventory if requested
    if args.setup_inventory or args.all:
        setup_inventory(args.env)
    
    # Setup credentials if requested
    if args.setup_credentials or args.all:
        setup_credentials(args.env)
    
    print("Bootstrap complete!")
    return 0

if __name__ == "__main__":
    sys.exit(main())
