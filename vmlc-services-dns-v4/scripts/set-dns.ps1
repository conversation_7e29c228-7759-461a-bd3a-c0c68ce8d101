# DNS Management PowerShell Script
# Author: CES Operational Excellence Team
# Contributors: <PERSON> (7409)

[CmdletBinding()]
param (
    [Parameter(Mandatory = $true)]
    [ValidateSet("Add", "Remove", "Update", "Verify")]
    [string]$Operation,

    [Parameter(Mandatory = $true)]
    [ValidateSet("A", "CNAME", "PTR")]
    [string]$RecordType,

    [Parameter(Mandatory = $true)]
    [string]$Hostname,

    [Parameter(Mandatory = $true)]
    [string]$Domain,

    [Parameter(Mandatory = $false)]
    [string]$IPAddress,

    [Parameter(Mandatory = $false)]
    [string]$Target,

    [Parameter(Mandatory = $false)]
    [int]$TTL = 3600,

    [Parameter(Mandatory = $false)]
    [string]$Description = "Managed by Ansible DNS Management",

    [Parameter(Mandatory = $true)]
    [string]$DNSServer,

    [Parameter(Mandatory = $false)]
    [string]$PTRDNSServer,

    [Parameter(Mandatory = $false)]
    [bool]$ManagePTR = $true,

    [Parameter(Mandatory = $false)]
    [bool]$Force = $false,

    [Parameter(Mandatory = $false)]
    [bool]$AsJson = $true,

    [Parameter(Mandatory = $false)]
    [string]$LogPath,

    [Parameter(Mandatory = $false)]
    [ValidateSet("Error", "Warning", "Info", "Debug")]
    [string]$LogLevel = "Info"
)

# Initialize result object
$result = @{
    success = $false
    message = ""
    record = @{
        type = $RecordType
        hostname = $Hostname
        domain = $Domain
        fqdn = "$Hostname.$Domain"
    }
    operation = $Operation
    timestamp = (Get-Date).ToString("yyyy-MM-ddTHH:mm:ss")
    changed = $false
}

# Function to write log
function Write-Log {
    param (
        [Parameter(Mandatory = $true)]
        [string]$Message,

        [Parameter(Mandatory = $false)]
        [ValidateSet("Error", "Warning", "Info", "Debug")]
        [string]$Level = "Info"
    )

    $levelMap = @{
        "Error" = 1
        "Warning" = 2
        "Info" = 3
        "Debug" = 4
    }

    $currentLevelValue = $levelMap[$LogLevel]
    $messageLevelValue = $levelMap[$Level]

    if ($messageLevelValue -le $currentLevelValue) {
        $timestamp = (Get-Date).ToString("yyyy-MM-dd HH:mm:ss")
        $logMessage = "[$timestamp] [$Level] [$Operation] [$RecordType] [$Hostname.$Domain] $Message"

        Write-Host $logMessage

        if ($LogPath) {
            Add-Content -Path $LogPath -Value $logMessage
        }
    }
}

# Function to convert IP address to PTR record
function ConvertTo-PTRRecord {
    param (
        [Parameter(Mandatory = $true)]
        [string]$IPAddress
    )

    try {
        $octets = $IPAddress.Split('.')
        if ($octets.Count -ne 4) {
            throw "Invalid IP address format"
        }

        $ptrRecord = "$($octets[3]).$($octets[2]).$($octets[1]).$($octets[0]).in-addr.arpa"
        return $ptrRecord
    }
    catch {
        Write-Log -Message "Error converting IP address to PTR record: $_" -Level "Error"
        throw
    }
}

# Function to check if DNS record exists
function Test-DNSRecord {
    param (
        [Parameter(Mandatory = $true)]
        [string]$RecordType,

        [Parameter(Mandatory = $true)]
        [string]$Hostname,

        [Parameter(Mandatory = $true)]
        [string]$Domain,

        [Parameter(Mandatory = $false)]
        [string]$IPAddress,

        [Parameter(Mandatory = $true)]
        [string]$DNSServer
    )

    try {
        Write-Log -Message "Checking if $RecordType record exists for $Hostname.$Domain on $DNSServer" -Level "Debug"

        switch ($RecordType) {
            "A" {
                $record = Get-DnsServerResourceRecord -ZoneName $Domain -Name $Hostname -RRType $RecordType -ComputerName $DNSServer -ErrorAction SilentlyContinue

                if ($record -and $IPAddress) {
                    $recordIP = $record.RecordData.IPv4Address.IPAddressToString
                    Write-Log -Message "Found A record with IP: $recordIP" -Level "Debug"
                    return @{
                        exists = $true
                        matches = ($recordIP -eq $IPAddress)
                        record = $record
                    }
                }
                elseif ($record) {
                    Write-Log -Message "Found A record" -Level "Debug"
                    return @{
                        exists = $true
                        matches = $true
                        record = $record
                    }
                }
                else {
                    Write-Log -Message "A record not found" -Level "Debug"
                    return @{
                        exists = $false
                        matches = $false
                        record = $null
                    }
                }
            }
            "CNAME" {
                $record = Get-DnsServerResourceRecord -ZoneName $Domain -Name $Hostname -RRType $RecordType -ComputerName $DNSServer -ErrorAction SilentlyContinue

                if ($record -and $Target) {
                    $recordTarget = $record.RecordData.HostNameAlias
                    Write-Log -Message "Found CNAME record with target: $recordTarget" -Level "Debug"
                    return @{
                        exists = $true
                        matches = ($recordTarget -eq $Target)
                        record = $record
                    }
                }
                elseif ($record) {
                    Write-Log -Message "Found CNAME record" -Level "Debug"
                    return @{
                        exists = $true
                        matches = $true
                        record = $record
                    }
                }
                else {
                    Write-Log -Message "CNAME record not found" -Level "Debug"
                    return @{
                        exists = $false
                        matches = $false
                        record = $null
                    }
                }
            }
            "PTR" {
                if (-not $IPAddress) {
                    throw "IP address is required for PTR record verification"
                }

                $ptrRecord = ConvertTo-PTRRecord -IPAddress $IPAddress
                $ptrZone = $ptrRecord.Substring($ptrRecord.IndexOf('.') + 1)
                $ptrName = $ptrRecord.Substring(0, $ptrRecord.IndexOf('.'))

                Write-Log -Message "Checking PTR record: $ptrName in zone $ptrZone on $DNSServer" -Level "Debug"

                $record = Get-DnsServerResourceRecord -ZoneName $ptrZone -Name $ptrName -RRType "PTR" -ComputerName $DNSServer -ErrorAction SilentlyContinue

                if ($record) {
                    $recordHostname = $record.RecordData.PtrDomainName
                    Write-Log -Message "Found PTR record with hostname: $recordHostname" -Level "Debug"
                    return @{
                        exists = $true
                        matches = ($recordHostname -eq "$Hostname.$Domain.")
                        record = $record
                    }
                }
                else {
                    Write-Log -Message "PTR record not found" -Level "Debug"
                    return @{
                        exists = $false
                        matches = $false
                        record = $null
                    }
                }
            }
            default {
                throw "Unsupported record type: $RecordType"
            }
        }
    }
    catch {
        Write-Log -Message "Error checking DNS record: $_" -Level "Error"
        throw
    }
}

# Main script logic
try {
    Write-Log -Message "Starting $Operation operation for $RecordType record $Hostname.$Domain" -Level "Info"

    # Validate parameters
    if ($RecordType -in @("A", "PTR") -and -not $IPAddress -and $Operation -in @("Add", "Update")) {
        throw "IP address is required for $RecordType records"
    }

    if ($RecordType -eq "CNAME" -and -not $Target -and $Operation -in @("Add", "Update")) {
        throw "Target is required for CNAME records"
    }

    # Set PTR DNS server to DNS server if not specified
    if (-not $PTRDNSServer) {
        $PTRDNSServer = $DNSServer
        Write-Log -Message "PTR DNS server not specified, using DNS server: $DNSServer" -Level "Debug"
    }

    # Check if record exists
    $recordCheck = Test-DNSRecord -RecordType $RecordType -Hostname $Hostname -Domain $Domain -IPAddress $IPAddress -DNSServer $DNSServer

    # Update result with record details
    switch ($RecordType) {
        "A" {
            $result.record["ip_address"] = $IPAddress
        }
        "CNAME" {
            $result.record["target"] = $Target
        }
        "PTR" {
            $result.record["ip_address"] = $IPAddress
            $result.record["ptr_record"] = (ConvertTo-PTRRecord -IPAddress $IPAddress)
        }
    }

    # Perform operation
    switch ($Operation) {
        "Verify" {
            if ($recordCheck.exists) {
                if ($recordCheck.matches) {
                    $result.success = $true
                    $result.message = "$RecordType record $Hostname.$Domain exists"

                    # Add additional details
                    if ($RecordType -eq "A") {
                        $result.message += " with IP address " + $recordCheck.record.RecordData.IPv4Address.IPAddressToString
                        $result.record["ip_address"] = $recordCheck.record.RecordData.IPv4Address.IPAddressToString
                    }
                    elseif ($RecordType -eq "CNAME") {
                        $result.message += " with target " + $recordCheck.record.RecordData.HostNameAlias
                        $result.record["target"] = $recordCheck.record.RecordData.HostNameAlias
                    }
                    elseif ($RecordType -eq "PTR") {
                        $result.message += " pointing to " + $recordCheck.record.RecordData.PtrDomainName
                        $result.record["ptr_domain_name"] = $recordCheck.record.RecordData.PtrDomainName
                    }
                }
                else {
                    $result.success = $false

                    if ($RecordType -eq "A") {
                        $result.message = "$RecordType record $Hostname.$Domain exists but has IP address " + $recordCheck.record.RecordData.IPv4Address.IPAddressToString + " instead of $IPAddress"
                        $result.record["ip_address"] = $recordCheck.record.RecordData.IPv4Address.IPAddressToString
                    }
                    elseif ($RecordType -eq "CNAME") {
                        $result.message = "$RecordType record $Hostname.$Domain exists but has target " + $recordCheck.record.RecordData.HostNameAlias + " instead of $Target"
                        $result.record["target"] = $recordCheck.record.RecordData.HostNameAlias
                    }
                    elseif ($RecordType -eq "PTR") {
                        $result.message = "$RecordType record for $IPAddress exists but points to " + $recordCheck.record.RecordData.PtrDomainName + " instead of $Hostname.$Domain."
                        $result.record["ptr_domain_name"] = $recordCheck.record.RecordData.PtrDomainName
                    }
                }
            }
            else {
                $result.success = $false
                $result.message = "$RecordType record $Hostname.$Domain does not exist"
            }
        }
        "Add" {
            if ($recordCheck.exists) {
                if ($recordCheck.matches) {
                    $result.success = $true
                    $result.message = "$RecordType record $Hostname.$Domain already exists with the correct values"
                }
                else {
                    $result.success = $false

                    if ($RecordType -eq "A") {
                        $result.message = "$RecordType record $Hostname.$Domain already exists but has IP address " + $recordCheck.record.RecordData.IPv4Address.IPAddressToString + " instead of $IPAddress"
                    }
                    elseif ($RecordType -eq "CNAME") {
                        $result.message = "$RecordType record $Hostname.$Domain already exists but has target " + $recordCheck.record.RecordData.HostNameAlias + " instead of $Target"
                    }
                    elseif ($RecordType -eq "PTR") {
                        $result.message = "$RecordType record for $IPAddress already exists but points to " + $recordCheck.record.RecordData.PtrDomainName + " instead of $Hostname.$Domain."
                    }
                }
            }
            else {
                # Add record
                Write-Log -Message "Adding $RecordType record $Hostname.$Domain" -Level "Info"

                switch ($RecordType) {
                    "A" {
                        Add-DnsServerResourceRecordA -ZoneName $Domain -Name $Hostname -IPv4Address $IPAddress -TimeToLive (New-TimeSpan -Seconds $TTL) -ComputerName $DNSServer
                        $result.changed = $true

                        # Add PTR record if requested
                        if ($ManagePTR) {
                            Write-Log -Message "Adding PTR record for $IPAddress" -Level "Info"

                            $ptrRecord = ConvertTo-PTRRecord -IPAddress $IPAddress
                            $ptrZone = $ptrRecord.Substring($ptrRecord.IndexOf('.') + 1)
                            $ptrName = $ptrRecord.Substring(0, $ptrRecord.IndexOf('.'))

                            try {
                                Add-DnsServerResourceRecordPtr -ZoneName $ptrZone -Name $ptrName -PtrDomainName "$Hostname.$Domain." -TimeToLive (New-TimeSpan -Seconds $TTL) -ComputerName $PTRDNSServer
                                Write-Log -Message "Added PTR record successfully" -Level "Info"
                            }
                            catch {
                                Write-Log -Message "Failed to add PTR record: $_" -Level "Warning"
                            }
                        }
                    }
                    "CNAME" {
                        Add-DnsServerResourceRecordCName -ZoneName $Domain -Name $Hostname -HostNameAlias $Target -TimeToLive (New-TimeSpan -Seconds $TTL) -ComputerName $DNSServer
                        $result.changed = $true
                    }
                    "PTR" {
                        $ptrRecord = ConvertTo-PTRRecord -IPAddress $IPAddress
                        $ptrZone = $ptrRecord.Substring($ptrRecord.IndexOf('.') + 1)
                        $ptrName = $ptrRecord.Substring(0, $ptrRecord.IndexOf('.'))

                        Add-DnsServerResourceRecordPtr -ZoneName $ptrZone -Name $ptrName -PtrDomainName "$Hostname.$Domain." -TimeToLive (New-TimeSpan -Seconds $TTL) -ComputerName $DNSServer
                        $result.changed = $true
                    }
                }

                $result.success = $true
                $result.message = "$RecordType record $Hostname.$Domain added successfully"
            }
        }
        "Remove" {
            if ($recordCheck.exists) {
                # Remove record
                Write-Log -Message "Removing $RecordType record $Hostname.$Domain" -Level "Info"

                switch ($RecordType) {
                    "A" {
                        Remove-DnsServerResourceRecord -ZoneName $Domain -Name $Hostname -RRType $RecordType -RecordData $recordCheck.record.RecordData -ComputerName $DNSServer -Force:$Force
                        $result.changed = $true

                        # Remove PTR record if requested
                        if ($ManagePTR) {
                            Write-Log -Message "Removing PTR record for $IPAddress" -Level "Info"

                            $ptrRecord = ConvertTo-PTRRecord -IPAddress $IPAddress
                            $ptrZone = $ptrRecord.Substring($ptrRecord.IndexOf('.') + 1)
                            $ptrName = $ptrRecord.Substring(0, $ptrRecord.IndexOf('.'))

                            try {
                                $ptrCheck = Test-DNSRecord -RecordType "PTR" -Hostname $Hostname -Domain $Domain -IPAddress $IPAddress -DNSServer $PTRDNSServer

                                if ($ptrCheck.exists) {
                                    Remove-DnsServerResourceRecord -ZoneName $ptrZone -Name $ptrName -RRType "PTR" -RecordData $ptrCheck.record.RecordData -ComputerName $PTRDNSServer -Force:$Force
                                    Write-Log -Message "Removed PTR record successfully" -Level "Info"
                                }
                                else {
                                    Write-Log -Message "PTR record not found, skipping removal" -Level "Warning"
                                }
                            }
                            catch {
                                Write-Log -Message "Failed to remove PTR record: $_" -Level "Warning"
                            }
                        }
                    }
                    "CNAME" {
                        Remove-DnsServerResourceRecord -ZoneName $Domain -Name $Hostname -RRType $RecordType -RecordData $recordCheck.record.RecordData -ComputerName $DNSServer -Force:$Force
                        $result.changed = $true
                    }
                    "PTR" {
                        $ptrRecord = ConvertTo-PTRRecord -IPAddress $IPAddress
                        $ptrZone = $ptrRecord.Substring($ptrRecord.IndexOf('.') + 1)
                        $ptrName = $ptrRecord.Substring(0, $ptrRecord.IndexOf('.'))

                        Remove-DnsServerResourceRecord -ZoneName $ptrZone -Name $ptrName -RRType "PTR" -RecordData $recordCheck.record.RecordData -ComputerName $DNSServer -Force:$Force
                        $result.changed = $true
                    }
                }

                $result.success = $true
                $result.message = "$RecordType record $Hostname.$Domain removed successfully"
            }
            else {
                $result.success = $true
                $result.message = "$RecordType record $Hostname.$Domain does not exist. No action needed."
            }
        }
        "Update" {
            if ($recordCheck.exists) {
                if ($recordCheck.matches) {
                    $result.success = $true
                    $result.message = "$RecordType record $Hostname.$Domain already has the correct values"
                }
                else {
                    # Update record (remove and add)
                    Write-Log -Message "Updating $RecordType record $Hostname.$Domain" -Level "Info"

                    switch ($RecordType) {
                        "A" {
                            Remove-DnsServerResourceRecord -ZoneName $Domain -Name $Hostname -RRType $RecordType -RecordData $recordCheck.record.RecordData -ComputerName $DNSServer -Force:$Force
                            Add-DnsServerResourceRecordA -ZoneName $Domain -Name $Hostname -IPv4Address $IPAddress -TimeToLive (New-TimeSpan -Seconds $TTL) -ComputerName $DNSServer
                            $result.changed = $true

                            # Update PTR record if requested
                            if ($ManagePTR) {
                                Write-Log -Message "Updating PTR record for $IPAddress" -Level "Info"

                                $ptrRecord = ConvertTo-PTRRecord -IPAddress $IPAddress
                                $ptrZone = $ptrRecord.Substring($ptrRecord.IndexOf('.') + 1)
                                $ptrName = $ptrRecord.Substring(0, $ptrRecord.IndexOf('.'))

                                try {
                                    $ptrCheck = Test-DNSRecord -RecordType "PTR" -Hostname $Hostname -Domain $Domain -IPAddress $IPAddress -DNSServer $PTRDNSServer

                                    if ($ptrCheck.exists) {
                                        Remove-DnsServerResourceRecord -ZoneName $ptrZone -Name $ptrName -RRType "PTR" -RecordData $ptrCheck.record.RecordData -ComputerName $PTRDNSServer -Force:$Force
                                    }

                                    Add-DnsServerResourceRecordPtr -ZoneName $ptrZone -Name $ptrName -PtrDomainName "$Hostname.$Domain." -TimeToLive (New-TimeSpan -Seconds $TTL) -ComputerName $PTRDNSServer
                                    Write-Log -Message "Updated PTR record successfully" -Level "Info"
                                }
                                catch {
                                    Write-Log -Message "Failed to update PTR record: $_" -Level "Warning"
                                }
                            }
                        }
                        "CNAME" {
                            Remove-DnsServerResourceRecord -ZoneName $Domain -Name $Hostname -RRType $RecordType -RecordData $recordCheck.record.RecordData -ComputerName $DNSServer -Force:$Force
                            Add-DnsServerResourceRecordCName -ZoneName $Domain -Name $Hostname -HostNameAlias $Target -TimeToLive (New-TimeSpan -Seconds $TTL) -ComputerName $DNSServer
                            $result.changed = $true
                        }
                        "PTR" {
                            $ptrRecord = ConvertTo-PTRRecord -IPAddress $IPAddress
                            $ptrZone = $ptrRecord.Substring($ptrRecord.IndexOf('.') + 1)
                            $ptrName = $ptrRecord.Substring(0, $ptrRecord.IndexOf('.'))

                            Remove-DnsServerResourceRecord -ZoneName $ptrZone -Name $ptrName -RRType "PTR" -RecordData $recordCheck.record.RecordData -ComputerName $DNSServer -Force:$Force
                            Add-DnsServerResourceRecordPtr -ZoneName $ptrZone -Name $ptrName -PtrDomainName "$Hostname.$Domain." -TimeToLive (New-TimeSpan -Seconds $TTL) -ComputerName $DNSServer
                            $result.changed = $true
                        }
                    }

                    $result.success = $true
                    $result.message = "$RecordType record $Hostname.$Domain updated successfully"
                }
            }
            else {
                $result.success = $false
                $result.message = "$RecordType record $Hostname.$Domain does not exist. Cannot update."
            }
        }
    }

    Write-Log -Message $result.message -Level "Info"
}
catch {
    $errorMessage = "Error: $_"
    $result.success = $false
    $result.message = $errorMessage
    Write-Log -Message $errorMessage -Level "Error"
}

# Output result
if ($AsJson) {
    $result | ConvertTo-Json -Depth 10
}
else {
    $result
}
