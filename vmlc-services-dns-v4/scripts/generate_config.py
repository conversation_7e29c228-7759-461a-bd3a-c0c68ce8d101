#!/usr/bin/env python3
"""
Configuration Generator for DNS Management
Author: CES Operational Excellence Team
Contributors: <PERSON> (7409)

This script generates a run_config.json file by merging:
1. Survey inputs (from AAP or CLI)
2. Vault secrets
3. Default configuration values
"""

import argparse
import json
import os
import sys
import yaml
from datetime import datetime

# Default configuration paths
DEFAULT_CONFIG_PATH = os.path.join(os.path.dirname(os.path.dirname(os.path.abspath(__file__))), "group_vars", "all", "defaults.yml")
VAULT_CONFIG_PATH = os.path.join(os.path.dirname(os.path.dirname(os.path.abspath(__file__))), "group_vars", "all", "vault.yml")
OUTPUT_PATH = os.path.join(os.path.dirname(os.path.dirname(os.path.abspath(__file__))), "run_config.json")

def load_yaml_file(file_path):
    """Load YAML file and return as dictionary"""
    try:
        with open(file_path, 'r') as file:
            return yaml.safe_load(file)
    except Exception as e:
        print(f"Error loading {file_path}: {e}")
        return {}

def parse_args():
    """Parse command line arguments"""
    parser = argparse.ArgumentParser(description='Generate run_config.json for DNS Management')
    
    # Operation parameters
    parser.add_argument('--operation', choices=['verify', 'add', 'remove', 'update'], default='verify',
                        help='DNS operation to perform')
    parser.add_argument('--record-type', choices=['a', 'cname', 'ptr'], default='a',
                        help='DNS record type')
    parser.add_argument('--hostname', help='Hostname for the DNS record')
    parser.add_argument('--domain', help='Domain for the DNS record')
    parser.add_argument('--domains', help='Comma-separated list of domains for multi-domain operations')
    parser.add_argument('--ip-address', help='IP address for A or PTR records')
    parser.add_argument('--cname-target', help='Target hostname for CNAME records')
    parser.add_argument('--ticket', help='Ticket number for tracking')
    
    # Optional parameters
    parser.add_argument('--ttl', type=int, help='Time to live in seconds')
    parser.add_argument('--description', help='Description for the DNS record')
    parser.add_argument('--manage-ptr', type=bool, help='Whether to manage PTR records for A records')
    parser.add_argument('--force-remove', type=bool, help='Whether to force removal of records')
    
    # Email parameters
    parser.add_argument('--email-report', type=bool, help='Whether to send an email report')
    parser.add_argument('--email-logs', type=bool, help='Whether to email logs')
    parser.add_argument('--email-recipient', help='Email recipient for the report')
    parser.add_argument('--testing-mode', type=bool, help='Whether to use testing mode for emails')
    
    # Logging parameters
    parser.add_argument('--log-level', choices=['Debug', 'Info', 'Warning', 'Error'], 
                        help='Log level for operations')
    parser.add_argument('--store-logs-target-server', type=bool, 
                        help='Whether to store logs on target server')
    
    # Report parameters
    parser.add_argument('--generate-report', type=bool, help='Whether to generate a report')
    
    # Execution parameters
    parser.add_argument('--is-multi-domain', type=bool, 
                        help='Whether this is a multi-domain operation')
    parser.add_argument('--async-operations', type=bool, 
                        help='Run multi-domain operations asynchronously')
    
    # Configuration parameters
    parser.add_argument('--config', help='Path to default configuration file')
    parser.add_argument('--vault', help='Path to vault configuration file')
    parser.add_argument('--output', help='Path to output run_config.json file')
    
    return parser.parse_args()

def generate_config():
    """Generate run_config.json by merging survey inputs, vault secrets, and defaults"""
    args = parse_args()
    
    # Load default configuration
    config_path = args.config if args.config else DEFAULT_CONFIG_PATH
    defaults = load_yaml_file(config_path)
    
    # Load vault configuration
    vault_path = args.vault if args.vault else VAULT_CONFIG_PATH
    vault = load_yaml_file(vault_path)
    
    # Create run configuration
    run_config = {
        # Metadata
        "generated_at": datetime.now().isoformat(),
        "job_id": os.environ.get("ANSIBLE_JOB_ID", "local"),
        
        # Operation parameters
        "operation": args.operation,
        "record_type": args.record_type,
        "hostname": args.hostname,
        "domain": args.domain,
        "domains": args.domains,
        "ip_address": args.ip_address,
        "cname_target": args.cname_target,
        "ticket": args.ticket,
        
        # Optional parameters
        "ttl": args.ttl if args.ttl else defaults.get("default_ttl", 3600),
        "description": args.description if args.description else defaults.get("default_description", "Managed by Ansible DNS Management"),
        "manage_ptr": args.manage_ptr if args.manage_ptr is not None else defaults.get("manage_ptr", True),
        "force_remove": args.force_remove if args.force_remove is not None else defaults.get("force_remove", False),
        
        # Email parameters
        "email_report": args.email_report if args.email_report is not None else defaults.get("email_report", False),
        "email_logs": args.email_logs if args.email_logs is not None else defaults.get("email_logs", False),
        "email_recipient": args.email_recipient if args.email_recipient else defaults.get("email_recipient", ""),
        "testing_mode": args.testing_mode if args.testing_mode is not None else defaults.get("testing_mode", False),
        
        # Logging parameters
        "log_level": args.log_level if args.log_level else defaults.get("log_level", "Info"),
        "store_logs_target_server": args.store_logs_target_server if args.store_logs_target_server is not None else defaults.get("store_logs_target_server", True),
        
        # Report parameters
        "generate_report": args.generate_report if args.generate_report is not None else defaults.get("generate_report", True),
        
        # Execution parameters
        "is_multi_domain": args.is_multi_domain if args.is_multi_domain is not None else (True if args.domains else False),
        "async_operations": args.async_operations if args.async_operations is not None else defaults.get("async_operations", False),
        
        # Credentials (from vault)
        "credentials": vault.get("credentials", {})
    }
    
    # Remove None values
    run_config = {k: v for k, v in run_config.items() if v is not None}
    
    # Write to output file
    output_path = args.output if args.output else OUTPUT_PATH
    with open(output_path, 'w') as file:
        json.dump(run_config, file, indent=2)
    
    print(f"Configuration generated at {output_path}")
    return 0

if __name__ == "__main__":
    sys.exit(generate_config())
