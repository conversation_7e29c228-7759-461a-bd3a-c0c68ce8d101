---
# Vault configuration for DNS Management (encrypted in production)
# Author: CES Operational Excellence Team
# Contributors: <PERSON> Bin <PERSON> (7409)

# Domain credentials
credentials:
  domains:
    shsu.com.sg:
      dns_username: "{{ var_dns_shsu_username }}"
      dns_password: "{{ var_dns_shsu_password }}"

    shs.com.sg:
      dns_username: "{{ var_dns_shs_username }}"
      dns_password: "{{ var_dns_shs_password }}"

    healthgrp.com.sg:
      dns_username: "{{ var_dns_healthgrp_username }}"
      dns_password: "{{ var_dns_healthgrp_password }}"

    devhealthgrp.com.sg:
      dns_username: "{{ var_dns_devhealthgrp_username }}"
      dns_password: "{{ var_dns_devhealthgrp_password }}"

    test.local:
      dns_username: "{{ var_dns_test_username }}"
      dns_password: "{{ var_dns_test_password }}"

# Variables for local testing (these would be encrypted in production)
var_dns_shsu_username: "administrator"
var_dns_shsu_password: "Password123!"
var_dns_shs_username: "administrator"
var_dns_shs_password: "Password123!"
var_dns_healthgrp_username: "administrator"
var_dns_healthgrp_password: "Password123!"
var_dns_devhealthgrp_username: "administrator"
var_dns_devhealthgrp_password: "Password123!"
var_dns_test_username: "administrator"
var_dns_test_password: "Password123!"
