---
# Default configuration for DNS Management
# Author: CES Operational Excellence Team
# Contributors: <PERSON> (7409)

# DNS record defaults
default_ttl: 3600
default_description: "Managed by Ansible DNS Management"
manage_ptr: true
force_remove: false

# Email settings
email_report: false
email_logs: false
email_recipient: ""
testing_mode: false

# Logging settings
log_level: "Info"
store_logs_target_server: true

# Report settings
generate_report: true

# Execution settings
async_operations: false
max_async_time: 600
max_async_poll: 5

# PowerShell settings
powershell_settings:
  scripts:
    dns_management: "{{ playbook_dir }}/../scripts/set-dns.ps1"
    connectivity_test: "{{ playbook_dir }}/../scripts/test-connectivity.ps1"

  defaults:
    ttl: 3600
    description: "Managed by Ansible DNS Management"
    log_level: "Info"

  timeouts:
    ping: 1000
    dns: 5000
    script: 300

# Log settings
log_settings:
  directories:
    ansible: "{{ playbook_dir }}/../logs/ansible"
    powershell: "{{ playbook_dir }}/../logs/powershell"
    progress: "{{ playbook_dir }}/../logs/progress"
    archive: "{{ playbook_dir }}/../logs/archive"

  format:
    date_format: "%Y%m%d"
    separator: "_"
    extension: ".log"

  types:
    ansible: "ANSIBLE"
    powershell: "POWERSHELL"
    progress: "PROGRESS"
    report: "REPORT"

  levels:
    debug: "Debug"
    info: "Info"
    warning: "Warning"
    error: "Error"

  target_server:
    base_path: "C:\\OE_AAP_LOGS"
    enabled: true

  rotation:
    enabled: true
    max_age_days: 30
    max_size_mb: 100

# Report settings
report_settings:
  directories:
    reports: "{{ playbook_dir }}/../reports"
    templates: "{{ playbook_dir }}/../templates/reports"

  format:
    date_format: "%Y%m%d"
    separator: "_"
    extension: ".html"

  types:
    standard: "STANDARD"
    consolidated: "CONSOLIDATED"

  templates:
    standard: "dns_report.html.j2"
    consolidated: "consolidated_report.html.j2"

# Email settings
email_settings:
  smtp:
    port: 25
    from: "<EMAIL>"
    dc_servers:
      hdc1: "asmtp.hcloud.healthgrp.com.sg"
      hdc2: "fsmtp.hcloud.healthgrp.com.sg"
    default_dc: "hdc1"

  recipients:
    domain_specific:
      "shses.shs.com.sg": "<EMAIL>"
      "ses.shsu.com.sg": "<EMAIL>"
      default: "<EMAIL>"
    admin_teams:
      "singhealth_team": "<EMAIL>"
      "healthgrp_team": "<EMAIL>"
      "test_team": "<EMAIL>"
      default: "<EMAIL>"
    testing: "<EMAIL>"
    bcc: "<EMAIL>"

  templates:
    directory: "{{ playbook_dir }}/../templates/emails"
    subjects:
      report: "DNS Report - {operation} {record_type} Record"
      logs: "DNS Logs - {operation} {record_type} Record"
    bodies:
      report: "report_email_body.j2"
      logs: "logs_email_body.j2"
      consolidated: "consolidated_report_email_body.j2"

# Operation settings
operations:
  verify:
    description: "Verify a DNS record"
    required_params:
      all: ["hostname", "domain", "ticket"]
      a: []
      ptr: []
      cname: []
    validation:
      check_exists: false
      error_if_not_exists: false
      check_connectivity: false
      error_if_exists: false

  add:
    description: "Add a DNS record"
    required_params:
      all: ["hostname", "domain", "ticket"]
      a: ["ip_address"]
      ptr: ["ip_address"]
      cname: ["cname_target"]
    validation:
      check_exists: true
      error_if_exists: true
      check_connectivity: false
      error_if_not_exists: false

  remove:
    description: "Remove a DNS record"
    required_params:
      all: ["hostname", "domain", "ticket"]
      a: []
      ptr: ["ip_address"]
      cname: []
    validation:
      check_exists: true
      error_if_not_exists: true
      check_connectivity: true
      error_if_exists: false

  update:
    description: "Update a DNS record"
    required_params:
      all: ["hostname", "domain", "ticket"]
      a: ["ip_address"]
      ptr: ["ip_address"]
      cname: ["cname_target"]
    validation:
      check_exists: true
      error_if_not_exists: true
      check_connectivity: false
      error_if_exists: false

# Record type settings
record_types:
  a:
    description: "A record"
    required_params: ["hostname", "domain", "ip_address"]
    optional_params: ["ttl", "description", "manage_ptr"]

  ptr:
    description: "PTR record"
    required_params: ["hostname", "domain", "ip_address"]
    optional_params: ["ttl", "description"]

  cname:
    description: "CNAME record"
    required_params: ["hostname", "domain", "cname_target"]
    optional_params: ["ttl", "description"]

# Credential security settings
credential_security:
  mask_credentials: true
  use_no_log: true
  clear_after_use: true

# CyberArk integration settings
cyberark_settings:
  enabled: false  # Set to true to enable CyberArk integration
  app_id: "DNS_Management"
  url: "https://cyberark.example.com"
  verify_ssl: true
  safes:
    dns_admin:
      name: "DNS_CREDS"
      object: "dns_admin_account"
    admt_admin:
      name: "ADMT_CREDS"
      object: "admt_admin_account"
    jira:
      name: "JIRA_CREDS"
      object: "jira_api_account"
    bitbucket:
      name: "BITBUCKET_CREDS"
      object: "bitbucket_api_account"

# Monitoring settings
monitoring_settings:
  jira:
    enabled: true
    url: "https://your-jira-instance/rest/api/2/issue"
    username: "{{ var_jira_username }}"
    password: "{{ var_jira_password }}"
    project_key: "DNS"
    issue_type: "Incident"

  bitbucket:
    enabled: true
    url: "https://your-bitbucket-instance/rest/api/1.0"
    username: "{{ var_bitbucket_username }}"
    password: "{{ var_bitbucket_password }}"
    repository: "vmlc-services-dns-v4"
