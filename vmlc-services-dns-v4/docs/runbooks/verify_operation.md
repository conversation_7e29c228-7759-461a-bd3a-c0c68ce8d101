# DNS Verify Operation Runbook

**Author:** CES Operational Excellence Team
**Contributors:** <PERSON> (7409)

## Table of Contents

1. [Overview](#1-overview)
2. [Prerequisites](#2-prerequisites)
3. [Procedure](#3-procedure)
4. [Troubleshooting](#4-troubleshooting)
5. [References](#5-references)

## 1. Overview

This runbook provides step-by-step instructions for verifying DNS records using the DNS Management System. The verify operation checks if a DNS record exists and returns its current configuration.

### 1.1. Use Cases

- Verify if a DNS record exists before adding it
- Check the current configuration of a DNS record
- Troubleshoot DNS resolution issues
- Audit DNS records

### 1.2. Expected Outcome

- Confirmation of whether the DNS record exists
- Details of the DNS record if it exists (IP address, TTL, etc.)
- Log files for the operation
- HTML report of the operation

## 2. Prerequisites

### 2.1. Access Requirements

- Access to Ansible Automation Platform (AAP)
- Permission to run the DNS Management job template
- Valid ticket number for tracking

### 2.2. Technical Requirements

- DNS server must be accessible from the ADMT server
- WinRM must be configured on the ADMT server
- PowerShell 5.1 or higher on the ADMT server
- DnsServer PowerShell module installed on the ADMT server

## 3. Procedure

### 3.1. Using AAP Job Template

1. Log in to Ansible Automation Platform
2. Navigate to **Templates**
3. Find and select the **DNS Management** job template
4. Click the **Launch** button
5. Fill in the survey form:
   - **Operation**: verify
   - **Record Type**: a, cname, or ptr
   - **Hostname**: The hostname part of the DNS record
   - **Domain**: The domain part of the DNS record
   - **Ticket Number**: Valid ticket number for tracking
6. Click **Next** to review the inputs
7. Click **Launch** to start the job
8. Monitor the job output for results

### 3.2. Using Extra Variables

Alternatively, you can launch the job with extra variables in JSON format:

1. Click on the **DNS Management** job template
2. Click the **Launch** button
3. Click on the **Extra Variables** tab
4. Enter the variables in JSON format:

```json
{
  "operation": "verify",
  "record_type": "a",
  "hostname": "server01",
  "domain": "example.com",
  "ticket": "INC123456"
}
```

5. Click **Launch** to start the job

### 3.3. Interpreting Results

The job output will show:

- Whether the DNS record exists
- Details of the DNS record if it exists
- Any errors encountered during the operation

Example success output:
```
Operation Summary:
  Operation: Verify
  Record Type: A
  Hostname: server01.example.com
  Result: Success
  Message: A record server01.example.com exists with IP address ************
  Logs: /logs/ansible/20230501_INC123456_server01_example.com_A_VERIFY_ANSIBLE.log
  Report: /reports/20230501_INC123456_server01_example.com_A_VERIFY_STANDARD.html
```

Example failure output:
```
Operation Summary:
  Operation: Verify
  Record Type: A
  Hostname: server01.example.com
  Result: Failure
  Message: A record server01.example.com does not exist
  Logs: /logs/ansible/20230501_INC123456_server01_example.com_A_VERIFY_ANSIBLE.log
  Report: /reports/20230501_INC123456_server01_example.com_A_VERIFY_STANDARD.html
```

## 4. Troubleshooting

### 4.1. Common Issues

#### 4.1.1. Connection Issues

**Symptom**: Job fails with connection errors to ADMT server

**Resolution**:
- Verify that the ADMT server is accessible
- Check WinRM configuration on the ADMT server
- Verify credentials for the ADMT server

#### 4.1.2. Permission Issues

**Symptom**: Job fails with permission errors

**Resolution**:
- Verify that the credentials have sufficient permissions to query DNS records
- Check if the DNS server is accessible from the ADMT server

#### 4.1.3. DNS Server Issues

**Symptom**: Job fails with DNS server errors

**Resolution**:
- Verify that the DNS server is accessible from the ADMT server
- Check if the DNS server is operational
- Verify that the DNS zone exists on the DNS server

### 4.2. Debugging

For detailed debugging information, set the log level to Debug:

```json
{
  "operation": "verify",
  "record_type": "a",
  "hostname": "server01",
  "domain": "example.com",
  "ticket": "INC123456",
  "log_level": "Debug"
}
```

### 4.3. Support

If you encounter issues that you cannot resolve, contact the CES Operational Excellence Team with the following information:

1. Job ID from AAP
2. Error message
3. Operation details (operation, record type, hostname, domain)
4. Ticket number

## 5. References

- [DNS Management System Architecture](../architecture.md)
- [DNS Management System Usage Guide](../USAGE.md)
- [DNS Management System Technical Documentation](../TECHNICAL.md)
- [PowerShell DnsServer Module Documentation](https://docs.microsoft.com/en-us/powershell/module/dnsserver/)
