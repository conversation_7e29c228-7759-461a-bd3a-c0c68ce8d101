# DNS Add Operation Runbook

**Author:** CES Operational Excellence Team
**Contributors:** <PERSON> (7409)

## Table of Contents

1. [Overview](#1-overview)
2. [Prerequisites](#2-prerequisites)
3. [Procedure](#3-procedure)
4. [Troubleshooting](#4-troubleshooting)
5. [References](#5-references)

## 1. Overview

This runbook provides step-by-step instructions for adding DNS records using the DNS Management System. The add operation creates new DNS records in the specified domain.

### 1.1. Use Cases

- Create new A records for servers
- Create new CNAME records for aliases
- Create new PTR records for reverse DNS lookups
- Create DNS records across multiple domains

### 1.2. Expected Outcome

- New DNS record created in the specified domain
- Corresponding PTR record created (for A records, if manage_ptr is enabled)
- Log files for the operation
- HTML report of the operation
- Email notification (if enabled)

## 2. Prerequisites

### 2.1. Access Requirements

- Access to Ansible Automation Platform (AAP)
- Permission to run the DNS Management job template
- Valid ticket number for tracking

### 2.2. Technical Requirements

- DNS server must be accessible from the ADMT server
- WinRM must be configured on the ADMT server
- PowerShell 5.1 or higher on the ADMT server
- DnsServer PowerShell module installed on the ADMT server
- Appropriate permissions to create DNS records

## 3. Procedure

### 3.1. Using AAP Job Template

1. Log in to Ansible Automation Platform
2. Navigate to **Templates**
3. Find and select the **DNS Management - Site** job template
4. Click the **Launch** button
5. Fill in the survey form:
   - **Operation**: add
   - **Record Type**: a, cname, or ptr
   - **Hostname**: The hostname part of the DNS record
   - **Domain**: The domain part of the DNS record
   - **IP Address**: The IP address (for A or PTR records)
   - **CNAME Target**: The target hostname (for CNAME records)
   - **TTL**: Time to live in seconds (optional)
   - **Description**: Description for the DNS record (optional)
   - **Manage PTR**: Whether to manage PTR records for A records (optional)
   - **Ticket Number**: Valid ticket number for tracking
   - **Email Report**: Whether to send an email report (optional)
   - **Email Recipient**: Email recipient for the report (optional)
6. Click **Next** to review the inputs
7. Click **Launch** to start the job
8. Monitor the job output for results

### 3.2. Using Extra Variables

Alternatively, you can launch the job with extra variables in JSON format:

1. Click on the **DNS Management - Site** job template
2. Click the **Launch** button
3. Click on the **Extra Variables** tab
4. Enter the variables in JSON format:

```json
{
  "operation": "add",
  "record_type": "a",
  "hostname": "server01",
  "domain": "example.com",
  "ip_address": "************",
  "ttl": 3600,
  "description": "Web server",
  "manage_ptr": true,
  "ticket": "INC123456",
  "email_report": true,
  "email_recipient": "<EMAIL>"
}
```

5. Click **Launch** to start the job

### 3.3. Multi-Domain Operations

To add DNS records across multiple domains:

1. Click on the **DNS Management - Full** job template
2. Click the **Launch** button
3. Click on the **Extra Variables** tab
4. Enter the variables in JSON format:

```json
{
  "operation": "add",
  "record_type": "a",
  "hostnames": "server01,server01",
  "domains": "example.com,example.org",
  "ip_addresses": "************,************",
  "ttl": 3600,
  "description": "Web server",
  "manage_ptr": true,
  "ticket": "INC123456",
  "email_report": true,
  "email_recipient": "<EMAIL>"
}
```

5. Click **Launch** to start the job

### 3.4. Canary Deployment

To test the operation on a non-critical domain first:

1. Click on the **DNS Management - Canary** job template
2. Follow the same steps as for the site job template
3. The operation will be performed on the canary domain first
4. If successful, you can then run the full job template

### 3.5. Interpreting Results

The job output will show:

- Whether the DNS record was added successfully
- Details of the DNS record that was added
- Any errors encountered during the operation

Example success output:
```
Operation Summary:
  Operation: Add
  Record Type: A
  Hostname: server01.example.com
  Result: Success
  Message: A record server01.example.com added with IP address ************
  Logs: /logs/ansible/20230501_INC123456_server01_example.com_A_ADD_ANSIBLE.log
  Report: /reports/20230501_INC123456_server01_example.com_A_ADD_STANDARD.html
```

Example failure output:
```
Operation Summary:
  Operation: Add
  Record Type: A
  Hostname: server01.example.com
  Result: Failure
  Message: A record server01.example.com already exists with IP address ************
  Logs: /logs/ansible/20230501_INC123456_server01_example.com_A_ADD_ANSIBLE.log
  Report: /reports/20230501_INC123456_server01_example.com_A_ADD_STANDARD.html
```

## 4. Troubleshooting

### 4.1. Common Issues

#### 4.1.1. Record Already Exists

**Symptom**: Job fails with "Record already exists" error

**Resolution**:
- Verify if the record already exists using the verify operation
- Update the record instead of adding it if it already exists
- Use the update operation to change the existing record

#### 4.1.2. Permission Issues

**Symptom**: Job fails with permission errors

**Resolution**:
- Verify that the credentials have sufficient permissions to create DNS records
- Check if the DNS server is accessible from the ADMT server
- Verify that the user has permissions to create records in the specified zone

#### 4.1.3. Invalid Parameters

**Symptom**: Job fails with parameter validation errors

**Resolution**:
- Check that all required parameters are provided
- Verify that the IP address is valid for A and PTR records
- Verify that the CNAME target is valid for CNAME records

### 4.2. Debugging

For detailed debugging information, set the log level to Debug:

```json
{
  "operation": "add",
  "record_type": "a",
  "hostname": "server01",
  "domain": "example.com",
  "ip_address": "************",
  "ticket": "INC123456",
  "log_level": "Debug"
}
```

### 4.3. Support

If you encounter issues that you cannot resolve, contact the CES Operational Excellence Team with the following information:

1. Job ID from AAP
2. Error message
3. Operation details (operation, record type, hostname, domain, IP address)
4. Ticket number

## 5. References

- [DNS Management System Architecture](../architecture.md)
- [DNS Management System Usage Guide](../USAGE.md)
- [DNS Management System Technical Documentation](../TECHNICAL.md)
- [PowerShell DnsServer Module Documentation](https://docs.microsoft.com/en-us/powershell/module/dnsserver/)
