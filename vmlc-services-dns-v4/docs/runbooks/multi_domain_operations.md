# DNS Multi-Domain Operations Runbook

**Author:** CES Operational Excellence Team
**Contributors:** <PERSON> (7409)

## Table of Contents

1. [Overview](#1-overview)
2. [Prerequisites](#2-prerequisites)
3. [Procedure](#3-procedure)
4. [Troubleshooting](#4-troubleshooting)
5. [References](#5-references)

## 1. Overview

This runbook provides step-by-step instructions for performing DNS operations across multiple domains using the DNS Management System. Multi-domain operations allow you to perform the same operation on multiple domains simultaneously.

### 1.1. Use Cases

- Verify DNS records across multiple domains
- Add DNS records across multiple domains
- Remove DNS records across multiple domains
- Update DNS records across multiple domains
- Perform operations on domain groups

### 1.2. Expected Outcome

- DNS operations performed on all specified domains
- Consolidated report of all operations
- Log files for each operation
- Email notification with consolidated results (if enabled)

## 2. Prerequisites

### 2.1. Access Requirements

- Access to Ansible Automation Platform (AAP)
- Permission to run the DNS Management job template
- Valid ticket number for tracking

### 2.2. Technical Requirements

- DNS servers must be accessible from the ADMT servers
- WinRM must be configured on the ADMT servers
- PowerShell 5.1 or higher on the ADMT servers
- DnsServer PowerShell module installed on the ADMT servers
- Appropriate permissions for all domains

## 3. Procedure

### 3.1. Using AAP Job Template

1. Log in to Ansible Automation Platform
2. Navigate to **Templates**
3. Find and select the **DNS Management - Full** job template
4. Click the **Launch** button
5. Fill in the survey form:
   - **Operation**: verify, add, remove, or update
   - **Record Type**: a, cname, or ptr
   - **Hostnames**: Comma-separated list of hostnames
   - **Domains**: Comma-separated list of domains
   - **IP Addresses**: Comma-separated list of IP addresses (for A or PTR records)
   - **CNAME Targets**: Comma-separated list of target hostnames (for CNAME records)
   - **TTL**: Time to live in seconds (optional)
   - **Description**: Description for the DNS records (optional)
   - **Manage PTR**: Whether to manage PTR records for A records (optional)
   - **Force Remove**: Whether to force removal of records (optional)
   - **Ticket Number**: Valid ticket number for tracking
   - **Email Report**: Whether to send an email report (optional)
   - **Email Recipient**: Email recipient for the report (optional)
   - **Async Operations**: Whether to run operations asynchronously (optional)
6. Click **Next** to review the inputs
7. Click **Launch** to start the job
8. Monitor the job output for results

### 3.2. Using Extra Variables

Alternatively, you can launch the job with extra variables in JSON format:

1. Click on the **DNS Management - Full** job template
2. Click the **Launch** button
3. Click on the **Extra Variables** tab
4. Enter the variables in JSON format:

```json
{
  "operation": "verify",
  "record_type": "a",
  "hostnames": "server01,server01",
  "domains": "example.com,example.org",
  "ticket": "INC123456",
  "email_report": true,
  "email_recipient": "<EMAIL>",
  "async_operations": true
}
```

5. Click **Launch** to start the job

### 3.3. Using Domain Groups

To perform operations on predefined domain groups:

1. Click on the **DNS Management - Full** job template
2. Click the **Launch** button
3. Click on the **Extra Variables** tab
4. Enter the variables in JSON format:

```json
{
  "operation": "verify",
  "record_type": "a",
  "hostname": "server01",
  "domain_group": "production",
  "ticket": "INC123456",
  "email_report": true,
  "email_recipient": "<EMAIL>"
}
```

5. Click **Launch** to start the job

### 3.4. Asynchronous Operations

To run operations asynchronously for better performance:

```json
{
  "operation": "verify",
  "record_type": "a",
  "hostnames": "server01,server01,server01",
  "domains": "example.com,example.org,example.net",
  "ticket": "INC123456",
  "async_operations": true,
  "max_async_time": 600,
  "max_async_poll": 5
}
```

### 3.5. Canary Deployment

To test the operation on a non-critical domain first:

1. Click on the **DNS Management - Canary** job template
2. Follow the same steps as for the full job template
3. The operation will be performed on the canary domain first
4. If successful, you can then run the full job template

### 3.6. Interpreting Results

The job output will show:

- Whether the DNS operations were successful on each domain
- Consolidated results of all operations
- Any errors encountered during the operations

Example success output:
```
Operation Summary:
  Operation: Verify
  Record Type: A
  Domains: example.com, example.org
  Results:
    - example.com: A record server01.example.com exists with IP address ************
    - example.org: A record server01.example.org exists with IP address ************
  Logs: /logs/ansible/20230501_INC123456_MULTI_DOMAIN_A_VERIFY_ANSIBLE.log
  Report: /reports/20230501_INC123456_MULTI_DOMAIN_A_VERIFY_CONSOLIDATED.html
```

Example mixed output:
```
Operation Summary:
  Operation: Verify
  Record Type: A
  Domains: example.com, example.org
  Results:
    - example.com: A record server01.example.com exists with IP address ************
    - example.org: A record server01.example.org does not exist
  Logs: /logs/ansible/20230501_INC123456_MULTI_DOMAIN_A_VERIFY_ANSIBLE.log
  Report: /reports/20230501_INC123456_MULTI_DOMAIN_A_VERIFY_CONSOLIDATED.html
```

## 4. Troubleshooting

### 4.1. Common Issues

#### 4.1.1. Mismatched Parameters

**Symptom**: Job fails with "Mismatched parameters" error

**Resolution**:
- Ensure that the number of hostnames matches the number of domains
- Ensure that the number of IP addresses matches the number of domains (for A or PTR records)
- Ensure that the number of CNAME targets matches the number of domains (for CNAME records)

#### 4.1.2. Domain Not Found

**Symptom**: Job fails with "Domain not found" error

**Resolution**:
- Verify that the domain is correctly spelled
- Check if the domain is configured in the domain configuration
- Verify that the DNS server for the domain is accessible

#### 4.1.3. Asynchronous Operation Timeout

**Symptom**: Job times out during asynchronous operations

**Resolution**:
- Increase the max_async_time parameter
- Reduce the number of domains being processed simultaneously
- Check if any of the DNS servers are slow to respond

### 4.2. Debugging

For detailed debugging information, set the log level to Debug:

```json
{
  "operation": "verify",
  "record_type": "a",
  "hostnames": "server01,server01",
  "domains": "example.com,example.org",
  "ticket": "INC123456",
  "log_level": "Debug"
}
```

### 4.3. Support

If you encounter issues that you cannot resolve, contact the CES Operational Excellence Team with the following information:

1. Job ID from AAP
2. Error message
3. Operation details (operation, record type, hostnames, domains)
4. Ticket number

## 5. References

- [DNS Management System Architecture](../architecture.md)
- [DNS Management System Usage Guide](../USAGE.md)
- [DNS Management System Technical Documentation](../TECHNICAL.md)
- [PowerShell DnsServer Module Documentation](https://docs.microsoft.com/en-us/powershell/module/dnsserver/)
