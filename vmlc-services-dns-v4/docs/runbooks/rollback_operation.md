# DNS Rollback Operation Runbook

**Author:** CES Operational Excellence Team
**Contributors:** <PERSON> (7409)

## Table of Contents

1. [Overview](#1-overview)
2. [Prerequisites](#2-prerequisites)
3. [Procedure](#3-procedure)
4. [Troubleshooting](#4-troubleshooting)
5. [References](#5-references)

## 1. Overview

This runbook provides step-by-step instructions for rolling back DNS operations using the DNS Management System. The rollback operation reverts previous DNS operations to their original state.

### 1.1. Use Cases

- Rollback an add operation that was performed incorrectly
- Rollback a remove operation to restore deleted records
- Rollback an update operation to restore previous values
- Rollback operations across multiple domains

### 1.2. Expected Outcome

- DNS records restored to their original state
- Log files for the rollback operation
- HTML report of the rollback operation
- Email notification (if enabled)

## 2. Prerequisites

### 2.1. Access Requirements

- Access to Ansible Automation Platform (AAP)
- Permission to run the DNS Management job template
- Valid ticket number for tracking
- Job ID of the operation to rollback

### 2.2. Technical Requirements

- DNS server must be accessible from the ADMT server
- WinRM must be configured on the ADMT server
- PowerShell 5.1 or higher on the ADMT server
- DnsServer PowerShell module installed on the ADMT server
- Appropriate permissions to modify DNS records

## 3. Procedure

### 3.1. Using AAP Job Template

1. Log in to Ansible Automation Platform
2. Navigate to **Templates**
3. Find and select the **DNS Management - Rollback** job template
4. Click the **Launch** button
5. Fill in the survey form:
   - **Rollback Job ID**: The job ID of the operation to rollback
   - **Ticket Number**: Valid ticket number for tracking
   - **Email Report**: Whether to send an email report (optional)
   - **Email Recipient**: Email recipient for the report (optional)
6. Click **Next** to review the inputs
7. Click **Launch** to start the job
8. Monitor the job output for results

### 3.2. Using Extra Variables

Alternatively, you can launch the job with extra variables in JSON format:

1. Click on the **DNS Management - Rollback** job template
2. Click the **Launch** button
3. Click on the **Extra Variables** tab
4. Enter the variables in JSON format:

```json
{
  "rollback_job_id": "12345",
  "ticket": "INC123456",
  "email_report": true,
  "email_recipient": "<EMAIL>"
}
```

5. Click **Launch** to start the job

### 3.3. Manual Rollback

If you need to rollback an operation manually without the job ID:

1. Click on the **DNS Management - Site** job template
2. Click the **Launch** button
3. Fill in the survey form with the opposite operation:
   - For add operations: use remove with the same parameters
   - For remove operations: use add with the same parameters
   - For update operations: use update with the original values
4. Click **Next** to review the inputs
5. Click **Launch** to start the job

### 3.4. Interpreting Results

The job output will show:

- Whether the rollback was successful
- Details of the DNS records before and after the rollback
- Any errors encountered during the rollback

Example success output:
```
Operation Summary:
  Operation: Rollback
  Original Operation: Add
  Record Type: A
  Hostname: server01.example.com
  Result: Success
  Message: A record server01.example.com removed (rollback of add operation)
  Logs: /logs/ansible/20230501_INC123456_server01_example.com_A_ROLLBACK_ANSIBLE.log
  Report: /reports/20230501_INC123456_server01_example.com_A_ROLLBACK_STANDARD.html
```

Example failure output:
```
Operation Summary:
  Operation: Rollback
  Original Operation: Add
  Record Type: A
  Hostname: server01.example.com
  Result: Failure
  Message: A record server01.example.com does not exist (cannot rollback add operation)
  Logs: /logs/ansible/20230501_INC123456_server01_example.com_A_ROLLBACK_ANSIBLE.log
  Report: /reports/20230501_INC123456_server01_example.com_A_ROLLBACK_STANDARD.html
```

## 4. Troubleshooting

### 4.1. Common Issues

#### 4.1.1. Job ID Not Found

**Symptom**: Job fails with "Job ID not found" error

**Resolution**:
- Verify that the job ID is correct
- Check if the job exists in AAP
- Verify that the job has not already been rolled back

#### 4.1.2. Record State Changed

**Symptom**: Job fails with "Record state has changed since the original operation" error

**Resolution**:
- Verify the current state of the record using the verify operation
- Perform a manual rollback with the current values
- Check if the record has been modified by another operation

#### 4.1.3. Unsupported Operation

**Symptom**: Job fails with "Unsupported operation for rollback" error

**Resolution**:
- Verify that the operation is add, remove, or update
- Verify operations cannot be rolled back
- Perform a manual operation to achieve the desired state

### 4.2. Debugging

For detailed debugging information, set the log level to Debug:

```json
{
  "rollback_job_id": "12345",
  "ticket": "INC123456",
  "log_level": "Debug"
}
```

### 4.3. Support

If you encounter issues that you cannot resolve, contact the CES Operational Excellence Team with the following information:

1. Job ID from AAP
2. Original job ID being rolled back
3. Error message
4. Ticket number

## 5. References

- [DNS Management System Architecture](../architecture.md)
- [DNS Management System Usage Guide](../USAGE.md)
- [DNS Management System Technical Documentation](../TECHNICAL.md)
- [PowerShell DnsServer Module Documentation](https://docs.microsoft.com/en-us/powershell/module/dnsserver/)
