# DNS Management System - Process Flows

**Author:** CES Operational Excellence Team
**Contributors:** <PERSON> (7409)

## Table of Contents

1. [Overview](#1-overview)
2. [Six-Phase Lifecycle Flow](#2-six-phase-lifecycle-flow)
3. [Configuration Phase Flow](#3-configuration-phase-flow)
4. [Loading Phase Flow](#4-loading-phase-flow)
5. [Execution Phase Flow](#5-execution-phase-flow)
6. [Error Handling Phase Flow](#6-error-handling-phase-flow)
7. [Reporting Phase Flow](#7-reporting-phase-flow)
8. [Cleanup Phase Flow](#8-cleanup-phase-flow)
9. [DNS Operation Flows](#9-dns-operation-flows)
   - [Verify Operation Flow](#91-verify-operation-flow)
   - [Add Operation Flow](#92-add-operation-flow)
   - [Remove Operation Flow](#93-remove-operation-flow)
   - [Update Operation Flow](#94-update-operation-flow)
10. [Multi-Domain Operation Flow](#10-multi-domain-operation-flow)
11. [Rollback Operation Flow](#11-rollback-operation-flow)
12. [Component Interaction Diagram](#12-component-interaction-diagram)

## 1. Overview

This document provides flowcharts and diagrams to visualize the processes and relationships in the DNS Management System. These diagrams help understand the flow of operations, the interactions between components, and the decision points in the system.

## 2. Six-Phase Lifecycle Flow

```mermaid
flowchart TD
    Start([Start]) --> Config[Configuration Phase]
    Config --> Loading[Loading Phase]
    Loading --> Execution[Execution Phase]

    Execution -->|Success| Reporting[Reporting Phase]
    Execution -->|Error| ErrorHandling[Error Handling Phase]
    ErrorHandling -->|Recoverable| Execution
    ErrorHandling -->|Non-recoverable| Reporting

    Reporting --> Cleanup[Cleanup Phase]
    Cleanup --> End([End])

    subgraph "Phase 1"
        Config
    end

    subgraph "Phase 2"
        Loading
    end

    subgraph "Phase 3"
        Execution
    end

    subgraph "Phase 4"
        ErrorHandling
    end

    subgraph "Phase 5"
        Reporting
    end

    subgraph "Phase 6"
        Cleanup
    end
```

## 3. Configuration Phase Flow

```mermaid
flowchart TD
    Start([Start Configuration Phase]) --> ReadSurvey[Read Survey Inputs or CLI Arguments]
    ReadSurvey --> ReadVault[Read Vault Secrets]
    ReadVault --> ReadDefaults[Read Default Configuration]
    ReadDefaults --> GenerateConfig[Generate run_config.json]

    GenerateConfig --> ValidateOperation[Validate Operation Parameters]
    ValidateOperation -->|Valid| ValidateDomain[Validate Domain Parameters]
    ValidateOperation -->|Invalid| ErrorOperation[Operation Validation Error]

    ValidateDomain -->|Valid| ConfigComplete[Configuration Complete]
    ValidateDomain -->|Invalid| ErrorDomain[Domain Validation Error]

    ErrorOperation --> End([End with Error])
    ErrorDomain --> End

    ConfigComplete --> NextPhase([Proceed to Loading Phase])
```

## 4. Loading Phase Flow

```mermaid
flowchart TD
    Start([Start Loading Phase]) --> CheckInventory{Check Inventory Type}

    CheckInventory -->|Static| LoadStatic[Load Static Inventory]
    CheckInventory -->|Dynamic| CheckCache{Check Cache}

    CheckCache -->|Cache Valid| LoadCache[Load Cached Inventory]
    CheckCache -->|Cache Invalid| GenerateDynamic[Generate Dynamic Inventory]
    GenerateDynamic --> UpdateCache[Update Cache]
    UpdateCache --> LoadDynamic[Load Dynamic Inventory]

    LoadStatic --> SetupCreds[Setup Credentials]
    LoadCache --> SetupCreds
    LoadDynamic --> SetupCreds

    SetupCreds --> SetupLogging[Setup Logging]
    SetupLogging --> InitResults[Initialize Results]

    InitResults --> LoadingComplete[Loading Complete]
    LoadingComplete --> NextPhase([Proceed to Execution Phase])
```

## 5. Execution Phase Flow

```mermaid
flowchart TD
    Start([Start Execution Phase]) --> CheckMultiDomain{Multi-Domain Operation?}

    CheckMultiDomain -->|Yes| CheckAsync{Async Operations?}
    CheckMultiDomain -->|No| ExecuteOperation[Execute DNS Operation]

    CheckAsync -->|Yes| SetupAsync[Setup Async Tasks]
    CheckAsync -->|No| ProcessDomains[Process Domains Sequentially]

    SetupAsync --> WaitAsync[Wait for Async Tasks]
    WaitAsync --> CollectResults[Collect Results]

    ProcessDomains --> CollectResults

    ExecuteOperation --> OperationComplete[Operation Complete]
    CollectResults --> OperationComplete

    OperationComplete --> NextPhase([Proceed to Reporting Phase])
```

## 6. Error Handling Phase Flow

```mermaid
flowchart TD
    Start([Start Error Handling Phase]) --> AnalyzeError[Analyze Error]

    AnalyzeError --> CheckRetry{Retry Possible?}

    CheckRetry -->|Yes| CheckRetryCount{Retry Count Exceeded?}
    CheckRetry -->|No| CheckRollback{Rollback Needed?}

    CheckRetryCount -->|No| IncrementRetry[Increment Retry Counter]
    CheckRetryCount -->|Yes| CheckRollback

    IncrementRetry --> RetryOperation[Retry Operation]
    RetryOperation --> Return([Return to Execution Phase])

    CheckRollback -->|Yes| TriggerRollback[Trigger Rollback]
    CheckRollback -->|No| LogError[Log Error Details]

    TriggerRollback --> LogError

    LogError --> EnrichError[Enrich Error with Context]
    EnrichError --> SetFailureStatus[Set Failure Status]

    SetFailureStatus --> NextPhase([Proceed to Reporting Phase])
```

## 7. Reporting Phase Flow

```mermaid
flowchart TD
    Start([Start Reporting Phase]) --> ProcessResults[Process Operation Results]

    ProcessResults --> CheckReportFlag{Generate Report?}

    CheckReportFlag -->|Yes| CheckMultiDomain{Multi-Domain Operation?}
    CheckReportFlag -->|No| SkipReport[Skip Report Generation]

    CheckMultiDomain -->|Yes| GenerateConsolidated[Generate Consolidated Report]
    CheckMultiDomain -->|No| GenerateStandard[Generate Standard Report]

    GenerateConsolidated --> CheckEmailFlag{Email Report?}
    GenerateStandard --> CheckEmailFlag
    SkipReport --> CheckEmailFlag

    CheckEmailFlag -->|Yes| CheckTestingMode{Testing Mode?}
    CheckEmailFlag -->|No| SkipEmail[Skip Email]

    CheckTestingMode -->|Yes| SendTestEmail[Send Email to Test Recipient]
    CheckTestingMode -->|No| SendEmail[Send Email to Recipients]

    SendTestEmail --> CheckLogEmailFlag{Email Logs?}
    SendEmail --> CheckLogEmailFlag
    SkipEmail --> CheckLogEmailFlag

    CheckLogEmailFlag -->|Yes| SendLogEmail[Send Log Email]
    CheckLogEmailFlag -->|No| SkipLogEmail[Skip Log Email]

    SendLogEmail --> CheckJiraFlag{Jira Integration?}
    SkipLogEmail --> CheckJiraFlag

    CheckJiraFlag -->|Yes| AttachToJira[Attach Reports to Jira Ticket]
    CheckJiraFlag -->|No| SkipJira[Skip Jira Integration]

    AttachToJira --> CheckBitbucketFlag{Bitbucket Integration?}
    SkipJira --> CheckBitbucketFlag

    CheckBitbucketFlag -->|Yes| SendToBitbucket[Create Bitbucket Log]
    CheckBitbucketFlag -->|No| SkipBitbucket[Skip Bitbucket Integration]

    SendToBitbucket --> ReportingComplete[Reporting Complete]
    SkipBitbucket --> ReportingComplete

    ReportingComplete --> NextPhase([Proceed to Cleanup Phase])
```

## 8. Cleanup Phase Flow

```mermaid
flowchart TD
    Start([Start Cleanup Phase]) --> RotateLogs[Rotate Logs]

    RotateLogs --> ArchiveOldReports[Archive Old Reports]
    ArchiveOldReports --> CleanupTempFiles[Cleanup Temporary Files]

    CleanupTempFiles --> CheckTargetServer{Store Logs on Target Server?}

    CheckTargetServer -->|Yes| UploadLogs[Upload Logs to Target Server]
    CheckTargetServer -->|No| SkipUpload[Skip Log Upload]

    UploadLogs --> DisplaySummary[Display Operation Summary]
    SkipUpload --> DisplaySummary

    DisplaySummary --> CleanupComplete[Cleanup Complete]
    CleanupComplete --> End([End])
```

## 9. DNS Operation Flows

### 9.1. Verify Operation Flow

```mermaid
flowchart TD
    Start([Start Verify Operation]) --> GetDNSServer[Get DNS Server for Domain]

    GetDNSServer --> ConnectDNSServer[Connect to DNS Server]
    ConnectDNSServer --> CheckRecordType{Record Type?}

    CheckRecordType -->|A| CheckARecord[Check A Record]
    CheckRecordType -->|CNAME| CheckCNAMERecord[Check CNAME Record]
    CheckRecordType -->|PTR| CheckPTRRecord[Check PTR Record]

    CheckARecord --> RecordExists{Record Exists?}
    CheckCNAMERecord --> RecordExists
    CheckPTRRecord --> RecordExists

    RecordExists -->|Yes| GetRecordDetails[Get Record Details]
    RecordExists -->|No| SetNotFoundResult[Set Not Found Result]

    GetRecordDetails --> SetSuccessResult[Set Success Result]

    SetSuccessResult --> End([End Verify Operation])
    SetNotFoundResult --> End
```

### 9.2. Add Operation Flow

```mermaid
flowchart TD
    Start([Start Add Operation]) --> GetDNSServer[Get DNS Server for Domain]

    GetDNSServer --> ConnectDNSServer[Connect to DNS Server]
    ConnectDNSServer --> CheckRecordExists{Record Exists?}

    CheckRecordExists -->|Yes| SetAlreadyExistsError[Set Already Exists Error]
    CheckRecordExists -->|No| CheckRecordType{Record Type?}

    CheckRecordType -->|A| AddARecord[Add A Record]
    CheckRecordType -->|CNAME| AddCNAMERecord[Add CNAME Record]
    CheckRecordType -->|PTR| AddPTRRecord[Add PTR Record]

    AddARecord --> CheckManagePTR{Manage PTR?}
    AddCNAMERecord --> SetSuccessResult[Set Success Result]
    AddPTRRecord --> SetSuccessResult

    CheckManagePTR -->|Yes| GetPTRDNSServer[Get PTR DNS Server]
    CheckManagePTR -->|No| SetSuccessResult

    GetPTRDNSServer --> ConnectPTRDNSServer[Connect to PTR DNS Server]
    ConnectPTRDNSServer --> AddPTRRecord2[Add PTR Record]

    AddPTRRecord2 --> SetSuccessResult

    SetSuccessResult --> End([End Add Operation])
    SetAlreadyExistsError --> End
```

### 9.3. Remove Operation Flow

```mermaid
flowchart TD
    Start([Start Remove Operation]) --> GetDNSServer[Get DNS Server for Domain]

    GetDNSServer --> ConnectDNSServer[Connect to DNS Server]
    ConnectDNSServer --> CheckRecordExists{Record Exists?}

    CheckRecordExists -->|No| SetNotFoundError[Set Not Found Error]
    CheckRecordExists -->|Yes| CheckRecordType{Record Type?}

    CheckRecordType -->|A| CheckConnectivity{Check Connectivity?}
    CheckRecordType -->|CNAME| RemoveCNAMERecord[Remove CNAME Record]
    CheckRecordType -->|PTR| RemovePTRRecord[Remove PTR Record]

    CheckConnectivity -->|Yes| TestConnectivity[Test Connectivity]
    CheckConnectivity -->|No| RemoveARecord[Remove A Record]

    TestConnectivity --> ConnectivityOK{Connectivity OK?}

    ConnectivityOK -->|Yes| SetConnectivityError[Set Connectivity Error]
    ConnectivityOK -->|No| CheckForceRemove{Force Remove?}

    CheckForceRemove -->|Yes| RemoveARecord
    CheckForceRemove -->|No| SetConnectivityError

    RemoveARecord --> CheckManagePTR{Manage PTR?}
    RemoveCNAMERecord --> SetSuccessResult[Set Success Result]
    RemovePTRRecord --> SetSuccessResult

    CheckManagePTR -->|Yes| GetPTRDNSServer[Get PTR DNS Server]
    CheckManagePTR -->|No| SetSuccessResult

    GetPTRDNSServer --> ConnectPTRDNSServer[Connect to PTR DNS Server]
    ConnectPTRDNSServer --> RemovePTRRecord2[Remove PTR Record]

    RemovePTRRecord2 --> SetSuccessResult

    SetSuccessResult --> End([End Remove Operation])
    SetNotFoundError --> End
    SetConnectivityError --> End
```

### 9.4. Update Operation Flow

```mermaid
flowchart TD
    Start([Start Update Operation]) --> GetDNSServer[Get DNS Server for Domain]

    GetDNSServer --> ConnectDNSServer[Connect to DNS Server]
    ConnectDNSServer --> CheckRecordExists{Record Exists?}

    CheckRecordExists -->|No| SetNotFoundError[Set Not Found Error]
    CheckRecordExists -->|Yes| CheckRecordType{Record Type?}

    CheckRecordType -->|A| UpdateARecord[Update A Record]
    CheckRecordType -->|CNAME| UpdateCNAMERecord[Update CNAME Record]
    CheckRecordType -->|PTR| UpdatePTRRecord[Update PTR Record]

    UpdateARecord --> CheckManagePTR{Manage PTR?}
    UpdateCNAMERecord --> SetSuccessResult[Set Success Result]
    UpdatePTRRecord --> SetSuccessResult

    CheckManagePTR -->|Yes| GetPTRDNSServer[Get PTR DNS Server]
    CheckManagePTR -->|No| SetSuccessResult

    GetPTRDNSServer --> ConnectPTRDNSServer[Connect to PTR DNS Server]
    ConnectPTRDNSServer --> UpdatePTRRecord2[Update PTR Record]

    UpdatePTRRecord2 --> SetSuccessResult

    SetSuccessResult --> End([End Update Operation])
    SetNotFoundError --> End
```

## 10. Multi-Domain Operation Flow

```mermaid
flowchart TD
    Start([Start Multi-Domain Operation]) --> ParseDomains[Parse Domains List]
    ParseDomains --> ParseHostnames[Parse Hostnames List]

    ParseHostnames --> CheckParamCount{Params Match?}

    CheckParamCount -->|No| SetMismatchError[Set Parameter Mismatch Error]
    CheckParamCount -->|Yes| CheckAsync{Async Operations?}

    CheckAsync -->|Yes| SetupAsyncTasks[Setup Async Tasks]
    CheckAsync -->|No| InitResults[Initialize Results]

    SetupAsyncTasks --> WaitForTasks[Wait for Tasks to Complete]
    WaitForTasks --> CollectResults[Collect Results]

    InitResults --> ProcessDomain[Process First Domain]

    ProcessDomain --> MoreDomains{More Domains?}
    MoreDomains -->|Yes| ProcessNextDomain[Process Next Domain]
    MoreDomains -->|No| GenerateConsolidatedReport[Generate Consolidated Report]

    ProcessNextDomain --> MoreDomains

    CollectResults --> GenerateConsolidatedReport

    GenerateConsolidatedReport --> End([End Multi-Domain Operation])
    SetMismatchError --> End
```

## 11. Rollback Operation Flow

```mermaid
flowchart TD
    Start([Start Rollback Operation]) --> GetJobID[Get Job ID to Rollback]

    GetJobID --> FetchJobDetails[Fetch Job Details]
    FetchJobDetails --> JobExists{Job Exists?}

    JobExists -->|No| SetJobNotFoundError[Set Job Not Found Error]
    JobExists -->|Yes| ExtractOperation[Extract Original Operation]

    ExtractOperation --> DetermineRollbackOp[Determine Rollback Operation]

    DetermineRollbackOp --> CheckOrigOp{Original Operation?}

    CheckOrigOp -->|Add| SetRemoveOp[Set Operation to Remove]
    CheckOrigOp -->|Remove| SetAddOp[Set Operation to Add]
    CheckOrigOp -->|Update| SetUpdateOp[Set Operation to Update with Original Values]

    SetRemoveOp --> ExecuteRollback[Execute Rollback Operation]
    SetAddOp --> ExecuteRollback
    SetUpdateOp --> ExecuteRollback

    ExecuteRollback --> SetRollbackResult[Set Rollback Result]

    SetRollbackResult --> End([End Rollback Operation])
    SetJobNotFoundError --> End
```

## 12. Component Interaction Diagram

```mermaid
sequenceDiagram
    participant User
    participant AAP as Ansible Automation Platform
    participant Playbook as DNS Management Playbook
    participant Config as Configuration Phase
    participant Loading as Loading Phase
    participant Execution as Execution Phase
    participant Error as Error Handling Phase
    participant Reporting as Reporting Phase
    participant Cleanup as Cleanup Phase
    participant DNS as DNS Server
    participant ADMT as ADMT Server
    participant Jira
    participant Bitbucket

    User->>AAP: Launch Job Template
    AAP->>Playbook: Execute with Parameters

    Playbook->>Config: Start Configuration Phase
    Config->>Config: Generate run_config.json
    Config->>Config: Validate Parameters
    Config-->>Playbook: Configuration Complete

    Playbook->>Loading: Start Loading Phase
    Loading->>Loading: Select Inventory
    Loading->>Loading: Setup Credentials
    Loading->>Loading: Setup Logging
    Loading-->>Playbook: Loading Complete

    Playbook->>Execution: Start Execution Phase

    alt Single Domain Operation
        Execution->>ADMT: Connect to ADMT Server
        ADMT->>DNS: Execute PowerShell Script
        DNS-->>ADMT: Return Result
        ADMT-->>Execution: Return Result
    else Multi-Domain Operation
        Execution->>Execution: Process Multiple Domains
        loop For Each Domain
            Execution->>ADMT: Connect to ADMT Server
            ADMT->>DNS: Execute PowerShell Script
            DNS-->>ADMT: Return Result
            ADMT-->>Execution: Return Result
        end
    end

    alt Error Occurs
        Execution->>Error: Handle Error
        Error->>Error: Analyze Error

        alt Retry Possible
            Error->>Execution: Retry Operation
        else Rollback Needed
            Error->>Execution: Trigger Rollback
        else Fatal Error
            Error-->>Playbook: Error Handling Complete
        end
    else Success
        Execution-->>Playbook: Execution Complete
    end

    Playbook->>Reporting: Start Reporting Phase

    alt Generate Report
        Reporting->>Reporting: Generate HTML Report
    end

    alt Email Report
        Reporting->>Reporting: Send Email
    end

    alt Jira Integration
        Reporting->>Jira: Attach Report to Ticket
    end

    alt Bitbucket Integration
        Reporting->>Bitbucket: Create Operation Log
    end

    Reporting-->>Playbook: Reporting Complete

    Playbook->>Cleanup: Start Cleanup Phase
    Cleanup->>Cleanup: Rotate Logs
    Cleanup->>Cleanup: Archive Reports

    alt Store Logs on Target
        Cleanup->>ADMT: Upload Logs
    end

    Cleanup->>Cleanup: Display Summary
    Cleanup-->>Playbook: Cleanup Complete

    Playbook-->>AAP: Job Complete
    AAP-->>User: Display Results
```

These flowcharts and diagrams provide a visual representation of the processes and relationships in the DNS Management System. They help understand the flow of operations, the interactions between components, and the decision points in the system.
