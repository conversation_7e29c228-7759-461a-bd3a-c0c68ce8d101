# Getting Started Guide: DNS Management System on AAP
**Author**: CES Operational Excellence Team
**Contributors**: <PERSON> (7409)

## Table of Contents

1. [Introduction](#1-introduction)
2. [Prerequisites](#2-prerequisites)
3. [Required Credential Types](#3-required-credential-types)
4. [Project Configuration](#4-project-configuration)
5. [Inventory Configuration](#5-inventory-configuration)
6. [Job Templates to Create](#6-job-templates-to-create)
   - [6.1. DNS Record Management](#61-dns-record-management)
   - [6.2. DNS Record Verification](#62-dns-record-verification)
   - [6.3. DNS Multi-Domain Operations](#63-dns-multi-domain-operations)
   - [6.4. DNS Rollback Operations](#64-dns-rollback-operations)
   - [6.5. DNS Canary Testing](#65-dns-canary-testing)
7. [Survey Configurations](#7-survey-configurations)
   - [7.1. DNS Record Management Survey](#71-dns-record-management-survey)
   - [7.2. DNS Record Verification Survey](#72-dns-record-verification-survey)
   - [7.3. DNS Multi-Domain Operations Survey](#73-dns-multi-domain-operations-survey)
   - [7.4. DNS Rollback Operations Survey](#74-dns-rollback-operations-survey)
8. [Using Extra Vars Instead of Surveys](#8-using-extra-vars-instead-of-surveys)
   - [8.1. Extra Vars Format](#81-extra-vars-format)
   - [8.2. Example Extra Vars](#82-example-extra-vars)
   - [8.3. Launching Jobs with Extra Vars](#83-launching-jobs-with-extra-vars)
9. [Workflow Templates](#9-workflow-templates)
   - [9.1. DNS Full Lifecycle Workflow](#91-dns-full-lifecycle-workflow)
   - [9.2. DNS Multi-Domain Workflow](#92-dns-multi-domain-workflow)
10. [Using the Job Templates](#10-using-the-job-templates)
    - [10.1. Single Domain Operations](#101-single-domain-operations)
    - [10.2. Multi-Domain Operations](#102-multi-domain-operations)
    - [10.3. Rollback Operations](#103-rollback-operations)
11. [Monitoring and Troubleshooting](#11-monitoring-and-troubleshooting)
    - [11.1. Monitoring Jobs](#111-monitoring-jobs)
    - [11.2. Troubleshooting](#112-troubleshooting)
12. [Best Practices](#12-best-practices)
13. [CyberArk Integration](#13-cyberark-integration)
    - [13.1. Prerequisites](#131-prerequisites)
    - [13.2. Configuration](#132-configuration)
    - [13.3. Using CyberArk in Job Templates](#133-using-cyberark-in-job-templates)
14. [Security Considerations](#14-security-considerations)

## 1. Introduction

This guide provides instructions for setting up and using the DNS Management System on Ansible Automation Platform (AAP). The DNS Management System automates the creation, verification, updating, and removal of DNS records across multiple domains, with features including:

- A, PTR, and CNAME record management
- Automatic PTR record management
- Multi-domain operations
- Rollback capabilities
- Integration with Jira and Bitbucket
- Email notifications
- Comprehensive logging and reporting

## 2. Prerequisites

Before setting up the job templates, ensure you have:

- Access to Ansible Automation Platform (AAP) with administrator privileges
- The vmlc-services-dns-v4 project synced to your AAP instance
- Appropriate credentials configured in AAP
- Network connectivity from AAP to your DNS and ADMT servers

## 3. Required Credential Types

Set up the following credential types in AAP:

1. **Windows Credentials**:
   - For connecting to ADMT and DNS servers
   - Requires domain username and password with appropriate permissions

2. **Source Control Credentials**:
   - For syncing the project from Bitbucket
   - Requires username and password or SSH key

3. **Jira Credentials**:
   - For integration with Jira
   - Requires username and API token

4. **Bitbucket Credentials**:
   - For integration with Bitbucket
   - Requires username and API token

5. **CyberArk Credentials**:
   - For retrieving credentials from CyberArk
   - Requires application ID and connection details

## 4. Project Configuration

1. **Create a Project in AAP**:
   - Name: `DNS Management System`
   - SCM Type: Git
   - SCM URL: Your Bitbucket repository URL
   - SCM Branch/Tag: main (or your preferred branch)
   - SCM Update Options:
     - ✓ Clean
     - ✓ Update on Launch
     - ✓ Delete on Update

## 5. Inventory Configuration

1. **Create an Inventory**:
   - Name: `DNS Management Inventory`
   - Add the following groups:
     - `admt_servers` - ADMT servers for each domain
     - `dns_servers` - DNS servers for each domain

2. **Configure Host Variables**:
   - For each ADMT server, set:
     ```yaml
     ansible_connection: winrm
     ansible_winrm_transport: ntlm
     ansible_winrm_server_cert_validation: ignore
     ```

## 6. Job Templates to Create

You'll need to create the following job templates:

### 6.1. DNS Record Management

1. **DNS Record Management**:
   - Name: `DNS - Record Management`
   - Inventory: `DNS Management Inventory`
   - Project: `DNS Management System`
   - Playbook: `playbooks/site.yml`
   - Credentials: Windows credentials
   - Variables:
     ```yaml
     monitoring_enabled: true
     email_report: true
     ```
   - Survey: See section 7.1

### 6.2. DNS Record Verification

2. **DNS Record Verification**:
   - Name: `DNS - Record Verification`
   - Inventory: `DNS Management Inventory`
   - Project: `DNS Management System`
   - Playbook: `playbooks/site.yml`
   - Credentials: Windows credentials
   - Variables:
     ```yaml
     operation: verify
     monitoring_enabled: true
     email_report: false
     ```
   - Survey: See section 7.2

### 6.3. DNS Multi-Domain Operations

3. **DNS - Multi-Domain Operations**:
   - Name: `DNS - Multi-Domain Operations`
   - Inventory: `DNS Management Inventory`
   - Project: `DNS Management System`
   - Playbook: `playbooks/site.yml`
   - Credentials: Windows credentials
   - Variables:
     ```yaml
     is_multi_domain: true
     monitoring_enabled: true
     email_report: true
     ```
   - Survey: See section 7.3

### 6.4. DNS Rollback Operations

4. **DNS - Rollback Operations**:
   - Name: `DNS - Rollback Operations`
   - Inventory: `DNS Management Inventory`
   - Project: `DNS Management System`
   - Playbook: `playbooks/rollback.yml`
   - Credentials: Windows credentials
   - Variables:
     ```yaml
     monitoring_enabled: true
     email_report: true
     ```
   - Survey: See section 7.4

### 6.5. DNS Canary Testing

5. **DNS - Canary Testing**:
   - Name: `DNS - Canary Testing`
   - Inventory: `DNS Management Inventory`
   - Project: `DNS Management System`
   - Playbook: `playbooks/canary.yml`
   - Credentials: Windows credentials
   - Variables:
     ```yaml
     is_canary: true
     monitoring_enabled: true
     email_report: false
     ```
   - Survey: Similar to section 7.1

## 7. Survey Configurations

### 7.1. DNS Record Management Survey

Create a survey with the following fields:

1. **Operation**:
   - Type: Multiple Choice (Single Select)
   - Options: add, remove, update
   - Default: add
   - Required: Yes

2. **Record Type**:
   - Type: Multiple Choice (Single Select)
   - Options: a, ptr, cname
   - Default: a
   - Required: Yes

3. **Hostname**:
   - Type: Text
   - Required: Yes
   - Description: Hostname without domain (e.g., server01)

4. **Domain**:
   - Type: Multiple Choice (Single Select)
   - Options: [List all domains from domains.yml]
   - Required: Yes

5. **IP Address**:
   - Type: Text
   - Required: Conditionally (for A and PTR records)
   - Description: IP address for A or PTR records

6. **CNAME Target**:
   - Type: Text
   - Required: Conditionally (for CNAME records)
   - Description: Target hostname for CNAME records

7. **TTL**:
   - Type: Integer
   - Default: 3600
   - Required: No
   - Description: Time to live in seconds

8. **Manage PTR**:
   - Type: Multiple Choice (Single Select)
   - Options: true, false
   - Default: true
   - Required: No
   - Description: Automatically manage corresponding PTR record

9. **Ticket Number**:
   - Type: Text
   - Required: Yes
   - Description: Jira ticket number (e.g., INC123456)

10. **Testing Mode**:
    - Type: Multiple Choice (Single Select)
    - Options: true, false
    - Default: false
    - Required: No
    - Description: Send emails only to test recipient

### 7.2. DNS Record Verification Survey

Create a survey with the following fields:

1. **Record Type**:
   - Type: Multiple Choice (Single Select)
   - Options: a, ptr, cname
   - Default: a
   - Required: Yes

2. **Hostname**:
   - Type: Text
   - Required: Yes
   - Description: Hostname without domain (e.g., server01)

3. **Domain**:
   - Type: Multiple Choice (Single Select)
   - Options: [List all domains from domains.yml]
   - Required: Yes

4. **Ticket Number**:
   - Type: Text
   - Required: Yes
   - Description: Jira ticket number (e.g., INC123456)

### 7.3. DNS Multi-Domain Operations Survey

Create a survey with the following fields:

1. **Operation**:
   - Type: Multiple Choice (Single Select)
   - Options: add, remove, update, verify
   - Default: add
   - Required: Yes

2. **Record Type**:
   - Type: Multiple Choice (Single Select)
   - Options: a, ptr, cname
   - Default: a
   - Required: Yes

3. **Hostnames**:
   - Type: Text
   - Required: Yes
   - Description: Comma-separated list of hostnames (e.g., server01,server02)

4. **Domains**:
   - Type: Text
   - Required: Yes
   - Description: Comma-separated list of domains (e.g., ses.shsu.com.sg,shses.shs.com.sg)

5. **IP Addresses**:
   - Type: Text
   - Required: Conditionally (for A and PTR records)
   - Description: Comma-separated list of IP addresses

6. **CNAME Targets**:
   - Type: Text
   - Required: Conditionally (for CNAME records)
   - Description: Comma-separated list of CNAME targets

7. **TTL**:
   - Type: Integer
   - Default: 3600
   - Required: No
   - Description: Time to live in seconds

8. **Manage PTR**:
   - Type: Multiple Choice (Single Select)
   - Options: true, false
   - Default: true
   - Required: No
   - Description: Automatically manage corresponding PTR records

9. **Ticket Number**:
   - Type: Text
   - Required: Yes
   - Description: Jira ticket number (e.g., INC123456)

10. **Testing Mode**:
    - Type: Multiple Choice (Single Select)
    - Options: true, false
    - Default: false
    - Required: No
    - Description: Send emails only to test recipient

11. **Async Operations**:
    - Type: Multiple Choice (Single Select)
    - Options: true, false
    - Default: false
    - Required: No
    - Description: Process domains in parallel

### 7.4. DNS Rollback Operations Survey

Create a survey with the following fields:

1. **Job ID to Rollback**:
   - Type: Text
   - Required: Yes
   - Description: ID of the job to rollback (e.g., 12345)

2. **Ticket Number**:
   - Type: Text
   - Required: Yes
   - Description: Jira ticket number (e.g., INC123456)

3. **Testing Mode**:
   - Type: Multiple Choice (Single Select)
   - Options: true, false
   - Default: false
   - Required: No
   - Description: Send emails only to test recipient

## 8. Using Extra Vars Instead of Surveys

While surveys provide a user-friendly interface for job template parameters, you can also use extra variables (extra_vars) for more advanced use cases, automation, or when launching jobs programmatically through the API.

### 8.1. Extra Vars Format

Extra vars are specified in JSON or YAML format and can be provided when launching a job template. They override any variables defined in the inventory, playbook, or job template.

The DNS Management System accepts the following extra vars:

```yaml
# Required parameters
operation: "add"           # Operation type: add, remove, update, verify
record_type: "a"           # Record type: a, ptr, cname
hostname: "server01"       # Hostname without domain
domain: "ses.shsu.com.sg"  # Domain name
ticket: "INC123456"        # Jira ticket number

# Conditional parameters (based on record type)
ip_address: "********"     # Required for A and PTR records
cname_target: "target.domain.com"  # Required for CNAME records

# Optional parameters
ttl: 3600                  # Time to live in seconds
description: "Web server"  # Description for the DNS record
manage_ptr: true           # Automatically manage PTR record
testing_mode: false        # Send emails only to test recipient

# Multi-domain parameters
is_multi_domain: false     # Enable multi-domain operations
hostnames: "server01,server02"  # Comma-separated list of hostnames
domains: "domain1.com,domain2.com"  # Comma-separated list of domains
ip_addresses: "********,********"  # Comma-separated list of IP addresses
async_operations: false    # Process domains in parallel
```

### 8.2. Example Extra Vars

Here are comprehensive examples of extra vars for all DNS operations:

#### 8.2.1. A Record Operations

**1. Adding an A Record:**

```json
{
  "operation": "add",
  "record_type": "a",
  "hostname": "webserver",
  "domain": "ses.shsu.com.sg",
  "ip_address": "********",
  "ttl": 3600,
  "manage_ptr": true,
  "description": "Web server for application X",
  "ticket": "INC123456",
  "testing_mode": false
}
```

**2. Verifying an A Record:**

```json
{
  "operation": "verify",
  "record_type": "a",
  "hostname": "webserver",
  "domain": "ses.shsu.com.sg",
  "ticket": "INC123456"
}
```

**3. Updating an A Record:**

```json
{
  "operation": "update",
  "record_type": "a",
  "hostname": "webserver",
  "domain": "ses.shsu.com.sg",
  "ip_address": "********",
  "ttl": 7200,
  "manage_ptr": true,
  "description": "Updated web server for application X",
  "ticket": "INC123456"
}
```

**4. Removing an A Record:**

```json
{
  "operation": "remove",
  "record_type": "a",
  "hostname": "webserver",
  "domain": "ses.shsu.com.sg",
  "ticket": "INC123456",
  "force_remove": false
}
```

#### 8.2.2. PTR Record Operations

**1. Adding a PTR Record:**

```json
{
  "operation": "add",
  "record_type": "ptr",
  "hostname": "webserver",
  "domain": "ses.shsu.com.sg",
  "ip_address": "********",
  "ttl": 3600,
  "description": "PTR record for web server",
  "ticket": "INC123456"
}
```

**2. Verifying a PTR Record:**

```json
{
  "operation": "verify",
  "record_type": "ptr",
  "ip_address": "********",
  "ticket": "INC123456"
}
```

**3. Updating a PTR Record:**

```json
{
  "operation": "update",
  "record_type": "ptr",
  "hostname": "webserver",
  "domain": "ses.shsu.com.sg",
  "ip_address": "********",
  "ttl": 7200,
  "description": "Updated PTR record for web server",
  "ticket": "INC123456"
}
```

**4. Removing a PTR Record:**

```json
{
  "operation": "remove",
  "record_type": "ptr",
  "ip_address": "********",
  "ticket": "INC123456"
}
```

#### 8.2.3. CNAME Record Operations

**1. Adding a CNAME Record:**

```json
{
  "operation": "add",
  "record_type": "cname",
  "hostname": "www",
  "domain": "shses.shs.com.sg",
  "cname_target": "webserver.shses.shs.com.sg",
  "ttl": 3600,
  "description": "CNAME for www",
  "ticket": "INC123456"
}
```

**2. Verifying a CNAME Record:**

```json
{
  "operation": "verify",
  "record_type": "cname",
  "hostname": "www",
  "domain": "shses.shs.com.sg",
  "ticket": "INC123456"
}
```

**3. Updating a CNAME Record:**

```json
{
  "operation": "update",
  "record_type": "cname",
  "hostname": "www",
  "domain": "shses.shs.com.sg",
  "cname_target": "new-webserver.shses.shs.com.sg",
  "ttl": 7200,
  "description": "Updated CNAME for www",
  "ticket": "INC123456"
}
```

**4. Removing a CNAME Record:**

```json
{
  "operation": "remove",
  "record_type": "cname",
  "hostname": "www",
  "domain": "shses.shs.com.sg",
  "ticket": "INC123456"
}
```

#### 8.2.4. Multi-Domain Operations

**1. Adding Multiple A Records:**

```json
{
  "operation": "add",
  "record_type": "a",
  "is_multi_domain": true,
  "hostnames": "web01,web02",
  "domains": "ses.shsu.com.sg,shses.shs.com.sg",
  "ip_addresses": "********,********",
  "ttl": 3600,
  "manage_ptr": true,
  "description": "Web servers for application X",
  "ticket": "INC123456",
  "async_operations": false,
  "testing_mode": false
}
```

**2. Verifying Multiple A Records:**

```json
{
  "operation": "verify",
  "record_type": "a",
  "is_multi_domain": true,
  "hostnames": "web01,web02",
  "domains": "ses.shsu.com.sg,shses.shs.com.sg",
  "ticket": "INC123456",
  "async_operations": false
}
```

**3. Updating Multiple A Records:**

```json
{
  "operation": "update",
  "record_type": "a",
  "is_multi_domain": true,
  "hostnames": "web01,web02",
  "domains": "ses.shsu.com.sg,shses.shs.com.sg",
  "ip_addresses": "********,********",
  "ttl": 7200,
  "manage_ptr": true,
  "description": "Updated web servers for application X",
  "ticket": "INC123456",
  "async_operations": false
}
```

**4. Removing Multiple A Records:**

```json
{
  "operation": "remove",
  "record_type": "a",
  "is_multi_domain": true,
  "hostnames": "web01,web02",
  "domains": "ses.shsu.com.sg,shses.shs.com.sg",
  "ticket": "INC123456",
  "async_operations": false,
  "force_remove": false
}
```

**5. Adding Multiple CNAME Records:**

```json
{
  "operation": "add",
  "record_type": "cname",
  "is_multi_domain": true,
  "hostnames": "www,mail",
  "domains": "ses.shsu.com.sg,shses.shs.com.sg",
  "cname_targets": "webserver.ses.shsu.com.sg,mailserver.shses.shs.com.sg",
  "ttl": 3600,
  "description": "CNAME records for services",
  "ticket": "INC123456",
  "async_operations": false
}
```

**6. Removing Multiple CNAME Records:**

```json
{
  "operation": "remove",
  "record_type": "cname",
  "is_multi_domain": true,
  "hostnames": "www,mail",
  "domains": "ses.shsu.com.sg,shses.shs.com.sg",
  "ticket": "INC123456",
  "async_operations": false
}
```

#### 8.2.5. Special Operations

**1. Rollback Operation:**

```json
{
  "job_id": "12345",
  "ticket": "INC123456",
  "testing_mode": true
}
```

**2. Canary Testing:**

```json
{
  "operation": "add",
  "record_type": "a",
  "hostname": "canary-test",
  "domain": "ses.shsu.com.sg",
  "ip_address": "********",
  "ttl": 60,
  "manage_ptr": true,
  "is_canary": true,
  "ticket": "INC123456",
  "testing_mode": true
}
```

**3. Operation with Email Notification:**

```json
{
  "operation": "add",
  "record_type": "a",
  "hostname": "webserver",
  "domain": "ses.shsu.com.sg",
  "ip_address": "********",
  "ticket": "INC123456",
  "email_report": true,
  "email_logs": true,
  "email_recipient": "<EMAIL>"
}
```

**4. Operation with Custom TTL and No PTR Management:**

```json
{
  "operation": "add",
  "record_type": "a",
  "hostname": "webserver",
  "domain": "ses.shsu.com.sg",
  "ip_address": "********",
  "ttl": 86400,
  "manage_ptr": false,
  "ticket": "INC123456"
}
```

**5. Force Remove Operation (Bypass Connectivity Check):**

```json
{
  "operation": "remove",
  "record_type": "a",
  "hostname": "webserver",
  "domain": "ses.shsu.com.sg",
  "force_remove": true,
  "ticket": "INC123456"
}
```

**6. Operation with CyberArk Integration:**

```json
{
  "operation": "add",
  "record_type": "a",
  "hostname": "webserver",
  "domain": "ses.shsu.com.sg",
  "ip_address": "********",
  "ticket": "INC123456",
  "use_cyberark": true,
  "cyberark_app_id": "DNS_Management",
  "cyberark_safe": "DNS_CREDS",
  "cyberark_object": "dns_admin_account"
}
```

### 8.3. Launching Jobs with Extra Vars

You can provide extra vars when launching a job template in several ways:

**1. Through the AAP Web Interface:**

1. Navigate to the Templates section in AAP
2. Select the job template you want to launch
3. Click "Launch with Parameters"
4. In the "Extra Variables" field, enter your variables in YAML or JSON format
5. Click "Launch"

**2. Through the AAP API:**

```bash
curl -X POST \
  https://your-aap-server/api/v2/job_templates/123/launch/ \
  -H 'Authorization: Bearer YOUR_TOKEN' \
  -H 'Content-Type: application/json' \
  -d '{
    "extra_vars": {
      "operation": "add",
      "record_type": "a",
      "hostname": "webserver",
      "domain": "ses.shsu.com.sg",
      "ip_address": "********",
      "ticket": "INC123456"
    }
  }'
```

**3. Using the AWX CLI Tool:**

```bash
awx job_template launch 123 \
  --extra-vars '{"operation":"add","record_type":"a","hostname":"webserver","domain":"ses.shsu.com.sg","ip_address":"********","ticket":"INC123456"}'
```

**4. Using Ansible Tower CLI Tool:**

```bash
tower-cli job launch --job-template=123 \
  --extra-vars='{"operation":"add","record_type":"a","hostname":"webserver","domain":"ses.shsu.com.sg","ip_address":"********","ticket":"INC123456"}'
```

## 9. Workflow Templates

For complex operations, create the following workflow templates:

### 9.1. DNS Full Lifecycle Workflow

1. **DNS - Full Lifecycle**:
   - Start with DNS Record Verification
   - If successful and record doesn't exist, proceed to DNS Record Management
   - If failed, stop and notify

### 9.2. DNS Multi-Domain Workflow

2. **DNS - Multi-Domain with Approval**:
   - Start with DNS Multi-Domain Operations (verify operation)
   - Add an approval node
   - If approved, proceed to DNS Multi-Domain Operations (actual operation)
   - If rejected, stop and notify

## 10. Using the Job Templates

### 10.1. Single Domain Operations

1. Navigate to the Templates section in AAP
2. Select `DNS - Record Management`
3. Click Launch
4. Fill in the survey:
   - Operation: add, remove, or update
   - Record Type: a, ptr, or cname
   - Hostname: The hostname without domain
   - Domain: Select from the dropdown
   - IP Address: For A or PTR records
   - CNAME Target: For CNAME records
   - TTL: Optional, defaults to 3600
   - Manage PTR: true or false
   - Ticket Number: Your Jira ticket
   - Testing Mode: true for testing, false for production
5. Click Launch

### 10.2. Multi-Domain Operations

1. Navigate to the Templates section in AAP
2. Select `DNS - Multi-Domain Operations`
3. Click Launch
4. Fill in the survey:
   - Operation: add, remove, update, or verify
   - Record Type: a, ptr, or cname
   - Hostnames: Comma-separated list of hostnames
   - Domains: Comma-separated list of domains
   - IP Addresses: Comma-separated list of IP addresses (for A or PTR)
   - CNAME Targets: Comma-separated list of targets (for CNAME)
   - TTL: Optional, defaults to 3600
   - Manage PTR: true or false
   - Ticket Number: Your Jira ticket
   - Testing Mode: true for testing, false for production
   - Async Operations: true for parallel processing, false for sequential
5. Click Launch

### 10.3. Rollback Operations

1. Navigate to the Templates section in AAP
2. Select `DNS - Rollback Operations`
3. Click Launch
4. Fill in the survey:
   - Job ID to Rollback: The ID of the job to rollback
   - Ticket Number: Your Jira ticket
   - Testing Mode: true for testing, false for production
5. Click Launch

## 11. Monitoring and Troubleshooting

### 11.1. Monitoring Jobs

- All jobs will create entries in the AAP job history
- Jobs will update Jira tickets with operation results
- Operation logs will be created in Bitbucket
- Email reports will be sent to the appropriate recipients

### 11.2. Troubleshooting

If a job fails:

1. Check the job output in AAP for error messages
2. Verify the Jira ticket for detailed error information
3. Check the logs in Bitbucket
4. If needed, use the `DNS - Rollback Operations` job to revert changes

## 12. Best Practices

1. **Always use a ticket number** - This ensures proper tracking and documentation
2. **Use testing mode first** - Especially for multi-domain operations
3. **Verify before adding or removing** - Use the verification job to check current state
4. **Use descriptive hostnames** - Follow your organization's naming conventions
5. **Document all changes** - Add comments to Jira tickets with operation details
6. **Review email reports** - Verify the operation completed as expected
7. **Use workflows for complex operations** - Especially when approval is required

## 13. CyberArk Integration

The DNS Management System can integrate with CyberArk Central Credential Provider (CCP) to securely retrieve credentials for DNS and ADMT servers, as well as for Jira and Bitbucket integrations.

### 13.1. Prerequisites

1. **CyberArk CCP Collection**:
   - The `cloud_cpe.cyberark_ccp` collection must be installed
   - This is specified in the `collections/requirements.yml` file

2. **CyberArk Configuration**:
   - CyberArk Central Credential Provider must be accessible from AAP
   - Appropriate safes and objects must be configured in CyberArk
   - Application ID must be set up with appropriate permissions

### 13.2. Configuration

1. **Enable CyberArk Integration**:
   - Set `cyberark_settings.enabled` to `true` in `defaults.yml` or via extra vars
   - Configure the CyberArk URL and application ID

2. **Configure Safes and Objects**:
   - Define the safes and objects for different credential types:
     - DNS admin credentials
     - ADMT admin credentials
     - Jira API credentials
     - Bitbucket API credentials

3. **Example Configuration**:

```yaml
cyberark_settings:
  enabled: true
  app_id: "DNS_Management"
  url: "https://cyberark.example.com"
  verify_ssl: true
  safes:
    dns_admin:
      name: "DNS_CREDS"
      object: "dns_admin_account"
    admt_admin:
      name: "ADMT_CREDS"
      object: "admt_admin_account"
```

### 13.3. Using CyberArk in Job Templates

When launching a job template, you can enable CyberArk integration by setting the following extra vars:

```json
{
  "use_cyberark": true,
  "cyberark_app_id": "DNS_Management",
  "cyberark_safe": "DNS_CREDS",
  "cyberark_object": "dns_admin_account"
}
```

The system will retrieve the credentials from CyberArk and use them for DNS operations. If CyberArk integration is disabled or fails, the system will fall back to using credentials from the vault.

## 14. Security Considerations

1. **Credential Management**:
   - Use CyberArk for credential management when possible
   - Use AAP's credential management system as a fallback
   - Rotate credentials regularly
   - Use the principle of least privilege

2. **Access Control**:
   - Limit who can run which job templates
   - Use RBAC in AAP to control access
   - Audit job template usage regularly

3. **Approval Workflows**:
   - Use approval nodes for sensitive operations
   - Implement dual control for critical changes
