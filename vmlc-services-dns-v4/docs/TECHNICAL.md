# DNS Management System - Technical Documentation

**Author:** CES Operational Excellence Team
**Contributors:** <PERSON> (7409)

## Table of Contents

1. [Overview](#1-overview)
2. [Configuration Files](#2-configuration-files)
3. [Project Structure](#3-project-structure)
4. [Roles and Playbooks](#4-roles-and-playbooks)
5. [Scripts](#5-scripts)
6. [Templates](#6-templates)
7. [Inventory](#7-inventory)
8. [Credentials](#8-credentials)
9. [Logging and Reporting](#9-logging-and-reporting)
10. [Error Handling](#10-error-handling)
11. [Integration Points](#11-integration-points)
12. [Testing](#12-testing)
13. [Deployment](#13-deployment)
14. [Maintenance](#14-maintenance)

## 1. Overview

The DNS Management System is an enterprise-grade automation framework for managing DNS records across multiple domains. It follows a six-phase lifecycle for all operations:

1. **Configuration**: Merge survey-driven overrides with secrets (Vault/CyberArk) → single run_config.json
2. **Loading**: Select static vs. dynamic inventory (with cache TTL)
3. **Execution**: Invoke playbooks via Job Templates (site, canary, full, rollback)
4. **Error Handling**: Callback plugin captures, enriches, retries or triggers rollback
5. **Reporting**: Emit JSON events → AAP Artifacts → auto-attach to Jira + Bitbucket logs
6. **Cleanup**: Teardown hooks + scheduled cleanup jobs for leftovers

## 2. Configuration Files

### 2.1. Ansible Configuration (`ansible.cfg`)

This file contains Ansible configuration settings, including:

- Inventory location
- Roles path
- Collections path
- Callback plugins
- SSH connection settings
- Python interpreter settings

### 2.2. Requirements (`requirements.yml`)

This file lists all required Ansible collections and roles:

```yaml
collections:
  - name: ansible.windows
    version: ">=1.10.0"
  - name: community.general
    version: ">=4.0.0"
  - name: community.dns
    version: ">=2.0.0"

roles: []
```

### 2.3. Run Configuration (`run_config.json`)

This file is generated at runtime and contains all parameters for the current operation:

```json
{
  "generated_at": "2023-05-01T12:00:00Z",
  "job_id": "12345",
  "operation": "verify",
  "record_type": "a",
  "hostname": "server01",
  "domain": "example.com",
  "ticket": "INC123456",
  "ttl": 3600,
  "description": "Web server",
  "manage_ptr": true,
  "force_remove": false,
  "email_report": true,
  "email_logs": false,
  "testing_mode": false,
  "log_level": "Info",
  "store_logs_target_server": true,
  "generate_report": true,
  "is_multi_domain": false,
  "async_operations": false,
  "credentials": {
    "domains": {
      "example.com": {
        "dns_username": "administrator",
        "dns_password": "********"
      }
    }
  }
}
```

### 2.4. Default Configuration (`group_vars/all/defaults.yml`)

This file contains default values for all parameters:

- DNS record defaults
- Email settings
- Logging settings
- Report settings
- PowerShell settings
- Operation settings
- Record type settings

### 2.5. Domain Configuration (`group_vars/all/domains.yml`)

This file contains domain-specific configuration:

- Domain groups
- Domain keys
- DNS server configuration
- PTR DNS server configuration
- Domain-specific settings
- Administrative team assignments

### 2.6. Vault Configuration (`group_vars/all/vault.yml`)

This file contains sensitive information encrypted with Ansible Vault:

- Domain credentials
- CyberArk integration settings
- API keys
- Other secrets

## 3. Project Structure

The project follows a standardized folder structure:

```
vmlc-services-dns-v4/
├─ ansible.cfg
├─ requirements.yml
├─ bitbucket-pipelines.yml
├─ run_config.json
├─ inventories/
│  ├─ dev/
│  ├─ prod/
│  └─ dynamic/
├─ group_vars/
│  └─ all/
│     ├─ defaults.yml
│     ├─ domains.yml
│     └─ vault.yml
├─ host_vars/
├─ playbooks/
│  ├─ site.yml
│  ├─ canary.yml
│  ├─ full.yml
│  └─ rollback.yml
├─ roles/
│  ├─ common/
│  ├─ dns_operations/
│  ├─ reporting/
│  └─ monitoring/
├─ collections/
│  └─ synapxe.dns/
│     └─ plugins/
│        ├─ callback/
│        └─ inventory/
├─ scripts/
│  ├─ bootstrap.py
│  ├─ generate_config.py
│  ├─ inventory.py
│  ├─ set-dns.ps1
│  └─ test-connectivity.ps1
├─ templates/
│  ├─ reports/
│  └─ emails/
├─ logs/
│  ├─ ansible/
│  ├─ powershell/
│  ├─ progress/
│  └─ archive/
├─ reports/
├─ tests/
│  ├─ schema/
│  └─ molecule/
└─ docs/
   ├─ architecture.md
   ├─ USAGE.md
   ├─ TECHNICAL.md
   └─ runbooks/
```

## 4. Roles and Playbooks

### 4.1. Playbooks

#### 4.1.1. Site Playbook (`site.yml`)

This is the main entry point for DNS operations. It includes all six phases of the lifecycle:

1. Configuration phase
2. Loading phase
3. Execution phase
4. Error handling phase
5. Reporting phase
6. Cleanup phase

#### 4.1.2. Canary Playbook (`canary.yml`)

This playbook runs operations on a subset of domains (canary domains) first to verify that everything works correctly before running on all domains.

#### 4.1.3. Full Playbook (`full.yml`)

This playbook runs operations on all specified domains, including multi-domain operations.

#### 4.1.4. Rollback Playbook (`rollback.yml`)

This playbook reverts previous operations based on the job ID.

### 4.2. Common Role

The common role contains tasks that are shared across all operations:

- `load_configuration.yml`: Loads and validates configuration
- `validate_operation.yml`: Validates operation parameters
- `validate_domain.yml`: Validates domain parameters
- `setup_credentials.yml`: Sets up credentials for target servers
- `setup_logging.yml`: Sets up logging
- `initialize_results.yml`: Initializes result variables
- `handle_errors.yml`: Handles errors and triggers rollbacks
- `process_results.yml`: Processes operation results
- `cleanup.yml`: Performs cleanup tasks
- `display_summary.yml`: Displays operation summary

### 4.3. DNS Operations Role

The DNS operations role contains tasks for specific DNS operations:

- `add.yml`: Adds DNS records
- `remove.yml`: Removes DNS records
- `update.yml`: Updates DNS records
- `verify.yml`: Verifies DNS records
- `main.yml`: Main task file for multi-domain operations
- `process_domain_async.yml`: Processes domains asynchronously

### 4.4. Reporting Role

The reporting role contains tasks for generating reports and sending emails:

- `generate_report.yml`: Generates HTML operation reports
- `generate_consolidated_report.yml`: Generates HTML consolidated reports
- `email_report.yml`: Sends report emails
- `email_logs.yml`: Sends log emails
- `upload_logs_to_target_server.yml`: Uploads logs to target server

### 4.5. Monitoring Role

The monitoring role contains tasks for monitoring and integration:

- `send_jira_update.yml`: Creates and updates Jira tickets
- `send_bitbucket_update.yml`: Creates operation logs in Bitbucket

## 5. Scripts

### 5.1. DNS Management Script (`set-dns.ps1`)

This PowerShell script performs DNS operations on Windows servers:

- Verify DNS records
- Add DNS records
- Remove DNS records
- Update DNS records
- Manage PTR records

### 5.2. Connectivity Test Script (`test-connectivity.ps1`)

This PowerShell script tests connectivity to hosts before removing DNS records.

### 5.3. Bootstrap Script (`bootstrap.py`)

This Python script bootstraps the DNS Management System:

- Creates necessary directories
- Sets up initial configuration
- Installs required dependencies
- Configures inventory
- Sets up credentials

### 5.4. Configuration Generator Script (`generate_config.py`)

This Python script generates the run_config.json file by merging:

- Survey inputs (from AAP or CLI)
- Vault secrets
- Default configuration values

### 5.5. Inventory Script (`inventory.py`)

This Python script generates a dynamic inventory for DNS Management operations:

- Reads domain configuration
- Creates inventory groups for DNS servers, ADMT servers, and domain groups
- Supports caching with configurable TTL

## 6. Templates

### 6.1. Report Templates

#### 6.1.1. Standard Report Template (`dns_report.html.j2`)

This Jinja2 template generates HTML reports for single-domain operations.

#### 6.1.2. Consolidated Report Template (`consolidated_report.html.j2`)

This Jinja2 template generates HTML reports for multi-domain operations.

### 6.2. Email Templates

#### 6.2.1. Report Email Template (`report_email_body.j2`)

This Jinja2 template generates email bodies for report emails.

#### 6.2.2. Logs Email Template (`logs_email_body.j2`)

This Jinja2 template generates email bodies for log emails.

#### 6.2.3. Consolidated Report Email Template (`consolidated_report_email_body.j2`)

This Jinja2 template generates email bodies for consolidated report emails.

## 7. Inventory

### 7.1. Static Inventory

#### 7.1.1. Development Inventory (`inventories/dev/hosts.yml`)

This YAML file contains the inventory for the development environment.

#### 7.1.2. Production Inventory (`inventories/prod/hosts.yml`)

This YAML file contains the inventory for the production environment.

### 7.2. Dynamic Inventory

#### 7.2.1. Dynamic Inventory Script (`inventories/dynamic/dns_inventory.py`)

This script generates a dynamic inventory based on the domain configuration.

## 8. Credentials

### 8.1. Credential Storage

Credentials are stored in the vault.yml file, which is encrypted with Ansible Vault.

### 8.2. Credential Access

Credentials are accessed through the setup_credentials.yml task in the common role.

### 8.3. Credential Security

Credentials are secured using the following methods:

- Ansible Vault encryption
- AAP Credential objects
- CyberArk integration (optional)
- no_log: true for sensitive tasks

## 9. Logging and Reporting

### 9.1. Logging

#### 9.1.1. Ansible Logs

Ansible logs are stored in the logs/ansible directory with the following naming convention:

```
<YYYYMMDD>_<TICKET>_<HOSTNAME>_<DOMAIN>_<RECORDTYPE>_<OPERATION>_ANSIBLE.log
```

#### 9.1.2. PowerShell Logs

PowerShell logs are stored in the logs/powershell directory with the following naming convention:

```
<YYYYMMDD>_<TICKET>_<HOSTNAME>_<DOMAIN>_<RECORDTYPE>_<OPERATION>_POWERSHELL.log
```

#### 9.1.3. Progress Logs

Progress logs are stored in the logs/progress directory with the following naming convention:

```
<YYYYMMDD>_<TICKET>_<HOSTNAME>_<DOMAIN>_<RECORDTYPE>_<OPERATION>_PROGRESS.log
```

#### 9.1.4. Archive Logs

Archive logs are stored in the logs/archive directory with the following naming convention:

```
<YYYYMMDD>_<TICKET>_<HOSTNAME>_<DOMAIN>_<RECORDTYPE>_<OPERATION>_ARCHIVE.tar.gz
```

### 9.2. Reporting

#### 9.2.1. HTML Reports

HTML reports are stored in the reports directory with the following naming convention:

```
<YYYYMMDD>_<TICKET>_<HOSTNAME>_<DOMAIN>_<RECORDTYPE>_<OPERATION>_STANDARD.html
```

For consolidated reports (multi-domain operations):

```
<YYYYMMDD>_<TICKET>_MULTI_DOMAIN_<RECORDTYPE>_<OPERATION>_CONSOLIDATED.html
```

#### 9.2.2. Email Reports

Email reports are sent to the specified recipients with the following subject:

```
DNS Report - <OPERATION> <RECORDTYPE> Record
```

#### 9.2.3. Email Routing

Email notifications are routed based on the administrative team assigned to each domain:

- Each domain in `domains.yml` has an `admin_team` field
- The `admin_teams` section in `defaults.yml` maps teams to email addresses
- Emails are sent to the appropriate team based on the domain's admin_team
- For multi-domain operations, emails are sent based on the primary domain's admin_team

## 10. Error Handling

### 10.1. Error Detection

Errors are detected through:

- PowerShell script exit codes
- PowerShell script output
- Ansible task results

### 10.2. Error Handling

Errors are handled through:

- Try/catch blocks in PowerShell scripts
- Rescue blocks in Ansible playbooks
- Custom callback plugin for error enrichment

### 10.3. Error Reporting

Errors are reported through:

- Ansible logs
- PowerShell logs
- HTML reports
- Email notifications
- Jira integration
- Bitbucket operation logs

### 10.4. Rollback

Rollback is triggered for critical errors through:

- Rollback playbook
- Rollback tasks in each operation
- Custom callback plugin for automatic rollback

## 11. Integration Points

### 11.1. Ansible Automation Platform (AAP)

The DNS Management System integrates with AAP through:

- Job Templates
- Surveys
- Credential objects
- Inventory
- Workflow Templates
- Notification Templates

### 11.2. Jira Integration

The DNS Management System integrates with Jira through:

- Automatic creation of tickets for DNS operations
- Adding comments to existing tickets with operation results
- Tracking ticket status based on operation results
- Linking DNS operations to relevant tickets

### 11.3. Bitbucket Integration

The DNS Management System integrates with Bitbucket through:

- Creating operation log files in Markdown format
- Storing operation logs in the repository
- Branch naming convention (feature/PROJ-1234)
- Version control of DNS configuration

### 11.4. Future Integration Possibilities

The DNS Management System could be extended in the future to support additional integrations such as:

- ELK/Splunk for advanced logging and monitoring
- Microsoft Teams for notifications
- Slack for alerts and notifications
- Prometheus for metrics collection

Note: These integrations would require additional development and configuration.

## 12. Testing

### 12.1. Molecule Tests

The DNS Management System includes Molecule tests for validating functionality:

#### 12.1.1. Role-Specific Tests

- Common role tests
- DNS operations role tests
- Reporting role tests

#### 12.1.2. Centralized Tests

- Default scenario
- Integration scenario
- Multi-domain scenario

### 12.2. Schema Validation

The DNS Management System includes JSON Schema validation for the run_config.json file.

### 12.3. Linting

The DNS Management System includes linting for:

- Ansible playbooks and roles
- YAML files
- Python scripts
- PowerShell scripts

### 12.4. Security Scanning

The DNS Management System includes security scanning for:

- Ansible playbooks and roles
- Python scripts
- PowerShell scripts

## 13. Deployment

### 13.1. Development Environment

The development environment is set up using:

- Local Ansible control node
- Mock credentials
- Test domains
- Molecule tests

### 13.2. Testing Environment

The testing environment is set up using:

- AAP test instance
- Test credentials
- Test domains
- Integration tests

### 13.3. Production Environment

The production environment is set up using:

- AAP production instance
- Production credentials
- Production domains
- Workflow Templates with approval steps

### 13.4. CI/CD Pipeline

The CI/CD pipeline is set up using:

- Bitbucket Pipelines
- Ansible Galaxy
- AAP API
- Automated testing
- Automated deployment

## 14. Maintenance

### 14.1. Log Rotation

Logs are rotated based on:

- Age (30 days)
- Size (100 MB)

### 14.2. Report Cleanup

Reports are cleaned up based on:

- Age (30 days)

### 14.3. Dependency Management

Dependencies are managed through:

- requirements.yml for Ansible collections and roles
- requirements.txt for Python dependencies

### 14.4. Version Control

The project is version controlled using:

- Git
- Bitbucket
- Feature branches
- Release branches
- Semantic versioning
