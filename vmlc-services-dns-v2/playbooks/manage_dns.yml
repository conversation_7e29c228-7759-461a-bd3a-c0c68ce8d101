---
# DNS Management Playbook
# This playbook orchestrates DNS operations across multiple domains
#
# Author: CES Operational Excellence Team
# Contributors: <PERSON> (7409)

- name: DNS Management Operations
  hosts: localhost
  gather_facts: true

  vars_files:
    - "../vars/unified_domain_config_clean.yml"
    - "../vars/credentials.yml"
    - "../vars/defaults.yml"
    - "../vars/email_config.yml"
    - "../vars/log_config.yml"

  vars:
    roles_path: "{{ playbook_dir }}/../roles"
    # Process input variables with proper type conversion
    operation: "{{ operation | default('verify') }}"
    record_type: "{{ record_type | default('a') | lower }}"
    hostname: "{{ hostname | default('') }}"
    hostnames: "{{ hostnames | default('') }}"
    domain: "{{ domain | default('') }}"
    domains: "{{ domains | default('') }}"
    ip_address: "{{ ip_address | default('') }}"
    ip_addresses: "{{ ip_addresses | default('') }}"
    cname_target: "{{ cname_target | default('') }}"
    cname_targets: "{{ cname_targets | default('') }}"
    ttl: "{{ ttl | default(default_dns_ttl) }}"
    description: "{{ description | default(default_dns_description) }}"
    manage_ptr: "{{ manage_ptr | default(true) | bool }}"
    force_remove: "{{ force_remove | default(false) | bool }}"
    ticket: "{{ ticket | default('') }}"
    generate_report: "{{ generate_report | default(email_flags.generate_report) | bool }}"
    email_report: "{{ email_report | default(email_flags.email_report) | bool }}"
    email_recipient: "{{ email_recipient | default('') }}"
    testing_mode: "{{ testing_mode | default(email_flags.testing_mode) | bool }}"
    email_logs: "{{ email_logs | default(email_flags.email_logs) | bool }}"
    log_level: "{{ log_level | default(log_flags.default_log_level) }}"
    store_logs_target_server: "{{ store_logs_target_server | default(log_flags.store_logs_target_server) | bool }}"
    # var_sr_number removed as CyberArk integration is no longer used

  pre_tasks:
    - name: Validate domain exists in domain configuration
      ansible.builtin.assert:
        that:
          - domain is defined
          - domain in domain_config
        fail_msg: "Domain '{{ domain }}' not found in unified_domain_config.yml configuration"
      when: domain is defined
      tags: always

    - name: Parse domains for multi-domain operations
      ansible.builtin.set_fact:
        domain_list: "{{ domains.split(',') | map('trim') | list }}"
      when: domains is defined and domains | length > 0
      tags: always

    - name: Validate all domains exist in domain configuration for multi-domain operations
      ansible.builtin.assert:
        that:
          - item in domain_config
        fail_msg: "Domain '{{ item }}' not found in unified_domain_config.yml configuration"
      loop: "{{ domain_list }}"
      when: domains is defined and domains | length > 0
      tags: always

    - name: Set domain information from unified configuration
      ansible.builtin.set_fact:
        domain_info: "{{ domain_config[domain] }}"
      when: domain is defined and domain in domain_config

    # CyberArk-related host variables removed as per request
    # Using direct credential selection instead

    # CyberArk integration removed as per request
    # Using hardcoded credentials from credential_selection role instead

    # ADMT servers are now defined in domain_mapping.yml

    - name: Set connection parameters for ADMT server
      ansible.builtin.set_fact:
        ansible_connection: "winrm"
        ansible_winrm_server_cert_validation: "ignore"
        ansible_user: "{{ domain_config[domain].dns_username }}"
        ansible_password: "{{ domain_config[domain].dns_password }}"
      when: domain is defined and domain in domain_config
      no_log: true

    - name: Add ADMT server to inventory with connection parameters
      ansible.builtin.add_host:
        name: "{{ domain_config[domain].admt_server }}"
        groups: dns_admt_servers
        ansible_connection: "winrm"
        ansible_winrm_server_cert_validation: "ignore"
        ansible_user: "{{ ansible_user }}"
        ansible_password: "{{ ansible_password }}"
      when: domain is defined and domain in domain_config
      no_log: true

    - name: Reset connection parameters for localhost operations
      ansible.builtin.set_fact:
        ansible_connection: "local"
        ansible_user: null
        ansible_password: null
        ansible_winrm_server_cert_validation: null
      when: domain is defined and domain in domain_config

    - name: Include log configuration
      ansible.builtin.include_vars:
        file: "{{ playbook_dir }}/../vars/log_config.yml"

    - name: Ensure log directories exist
      ansible.builtin.file:
        path: "{{ item }}"
        state: directory
        mode: '0755'
      loop:
        - "{{ log_settings.directories.ansible }}"
        - "{{ log_settings.directories.powershell }}"
        - "{{ log_settings.directories.progress }}"
        - "{{ log_settings.directories.archive }}"
      delegate_to: localhost
      run_once: true
      vars:
        ansible_connection: "local"

    - name: Validate required parameters
      ansible.builtin.assert:
        that:
          - domain != ""
          - hostname != ""
          - ticket != ""
        fail_msg: "Domain, hostname, and ticket parameters are required"

    - name: Validate operation-specific parameters for single domain
      ansible.builtin.assert:
        that:
          - (operation != 'add' and operation != 'remove' and operation != 'update') or (record_type != 'a' and record_type != 'ptr') or ip_address != ""
          - operation != 'add' or record_type != 'cname' or cname_target != ""
        fail_msg: "Missing required parameters for the specified operation and record type. IP address is required for A and PTR records when adding, removing, or updating."
      when: (domains is not defined or domains | length == 0) and (hostnames is not defined or hostnames | length == 0)

    - name: Validate operation-specific parameters for multi-domain
      ansible.builtin.assert:
        that:
          - (operation != 'add' and operation != 'remove' and operation != 'update') or (record_type != 'a' and record_type != 'ptr') or ip_addresses is defined
          - operation != 'add' or record_type != 'cname' or cname_targets is defined
          - ip_addresses is not defined or ip_addresses.split(',') | length == domains.split(',') | length
        fail_msg: "Missing required parameters for multi-domain operations. For A and PTR records, ip_addresses must be provided and match the number of domains."
      when: domains is defined and domains | length > 0 and hostnames is defined and hostnames | length > 0

    # Domain information is now retrieved from unified configuration

    # Multi-domain validation is now handled by domain mapping validation

    # Domain validation is now handled by domain mapping validation

    # ADMT server validation is now handled by domain mapping validation

    - name: Set ADMT server, DNS server, and credentials
      ansible.builtin.set_fact:
        admt_server: "{{ domain_config[domain].admt_server }}"
        dns_server: "{{ domain_config[domain].dns_server }}"
        dc_server: "{{ domain_config[domain].dns_server }}"  # Set dc_server to match dns_server for consistency
        dc_username: "{{ domain_config[domain].dns_username }}"
        dc_password: "{{ domain_config[domain].dns_password }}"
      when: domain is defined and domain in domain_config
      no_log: true

    - name: Set PTR DNS server based on domain
      ansible.builtin.set_fact:
        ptr_dns_server: "{{ domain_config[domain].ptr_dns_server }}"
      when: domain is defined and domain in domain_config

  tasks:
    - name: Initialize consolidated results variable
      ansible.builtin.set_fact:
        consolidated_results: []
      when: domains is defined and domains | length > 0 and hostnames is defined and hostnames | length > 0

    - name: Include DNS operations role
      ansible.builtin.include_role:
        name: "{{ roles_path }}/dns_operations"
        tasks_from: "{{ operation }}.yml"
      when: (domains is not defined or domains | length == 0) and (hostnames is not defined or hostnames | length == 0)

    - name: Include DNS operations role for multi-domain operations
      ansible.builtin.include_role:
        name: "{{ roles_path }}/dns_operations"
      when: domains is defined and domains | length > 0 and hostnames is defined and hostnames | length > 0

    - name: Check async job status
      ansible.builtin.async_status:
        jid: "{{ item.ansible_job_id }}"
      register: job_result
      until: job_result.finished
      retries: 300  # 30 minutes with 6-second intervals
      delay: 6
      loop: "{{ async_results }}"
      when: async_results is defined and async_results | length > 0

    - name: Process async results
      ansible.builtin.include_tasks: "{{ roles_path }}/dns_operations/tasks/process_async_results.yml"
      when: async_results is defined and async_results | length > 0

    - name: Generate operation report
      ansible.builtin.include_role:
        name: "{{ roles_path }}/reporting"
        tasks_from: generate_report.yml
      when: generate_report | bool and ((domains is not defined or domains | length == 0) or (hostnames is not defined or hostnames | length == 0))

    - name: Generate consolidated report for multi-domain operations
      ansible.builtin.include_role:
        name: "{{ roles_path }}/reporting"
        tasks_from: generate_consolidated_report.yml
      when: generate_report | bool and domains is defined and domains | length > 0 and hostnames is defined and hostnames | length > 0 and consolidated_results is defined and consolidated_results | length > 0

    - name: Email operation report
      ansible.builtin.include_role:
        name: "{{ roles_path }}/reporting"
        tasks_from: email_report.yml
      when: generate_report | bool and email_report | bool and email_recipient != ""

    - name: Email logs
      ansible.builtin.include_role:
        name: "{{ roles_path }}/reporting"
        tasks_from: email_logs.yml
      when: email_logs | bool and email_recipient != ""

    - name: Upload logs to target server
      ansible.builtin.include_role:
        name: "{{ roles_path }}/reporting"
        tasks_from: upload_logs_to_target_server.yml
      when: store_logs_target_server | bool
