---
# Default values for DNS Management
# Author: CES Operational Excellence Team
# Contributors: <PERSON> (7409)

# Progress tracking options
log_progress: true  # Whether to log progress to files
show_progress: true  # Whether to display progress in console

# Operation parameters
operation: "verify"
record_type: "a"
hostname: ""  # Single hostname for single domain operations
hostnames: ""  # Comma-separated list of hostnames for multi-domain operations (must match domains 1:1)
domain: ""  # Single domain for single domain operations
domains: ""  # Comma-separated list of domains for multi-domain operations
ip_address: ""  # IP address for single domain operations
ip_addresses: ""  # Comma-separated list of IP addresses for multi-domain operations (must match domains 1:1)
cname_target: ""  # CNAME target for single domain operations
cname_targets: ""  # Comma-separated list of CNAME targets for multi-domain operations (must match domains 1:1)
ttl: "{{ default_dns_ttl }}"
description: "Managed by Ansible"
manage_ptr: true
ticket: ""  # Service ticket number for auditing and tracking
generate_report: true
email_report: false
email_recipient: ""
log_level: "Info"

# Log and email configuration is now in log_config.yml and email_config.yml
