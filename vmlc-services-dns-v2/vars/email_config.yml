---
# Email configuration for DNS Management
# Author: CES Operational Excellence Team
# Contributors: <PERSON> (7409)

# Email settings
email_settings:
  # SMTP server configuration
  smtp:
    # Default SMTP settings
    default_server: "smtp.example.com"
    port: 25
    from: "<EMAIL>"

    # DC-specific SMTP servers
    hdc1_server: "asmtp.hcloud.healthgrp.com.sg"
    hdc2_server: "fsmtp.hcloud.healthgrp.com.sg"

  # Email recipients
  recipients:
    # Domain-specific email recipients
    domain_specific:
      # SHS domains
      shses.shs.com.sg: "<EMAIL>"
      ses.shsu.com.sg: "<EMAIL>"

      # Default recipient for all other domains
      default: "<EMAIL>"

    # Testing mode email recipient
    testing: "<EMAIL>"

    # BCC recipient for all emails
    bcc: "<EMAIL>"

  # Email templates
  templates:
    # Report email subject template
    report_subject: "DNS Management Report - {operation} {record_type} Record for {hostname}.{domain}"

    # Consolidated report email subject template
    consolidated_report_subject: "DNS Management Consolidated Report - {operation} {record_type} Records"

    # Logs email subject template
    logs_subject: "DNS Management Logs - {operation} {record_type} Record for {hostname}.{domain}"

# Email configuration flags
email_flags:
  # Whether to generate a report
  generate_report: true

  # Whether to email the report
  email_report: false

  # Whether to email logs
  email_logs: false

  # Whether to use testing mode
  testing_mode: false
