---
# Unified Domain Configuration
# This file serves as a single source of truth for all domain-related configuration
#
# Author: CES Operational Excellence Team
# Contributors: <PERSON> (7409)

domain_config:
  # Production domains
  healthgrp.com.sg:
    # ADMT server information (for first hop)
    admt_server: HISADMTVPSEC05.healthgrp.com.sg
    os: win
    environment: prd
    network_zone: mgt
    dc: hdc2

    # DNS server information (for second hop)
    dns_server: dc01.healthgrp.com.sg
    dns_username: "{{ healthgrp_dns_username }}"
    dns_password: "{{ healthgrp_dns_password }}"
    description: "Health Group Domain"

    # Special configurations
    ptr_dns_server: "dc01.healthgrp.com.sg"  # Same as DNS server

  hcloud.healthgrp.com.sg:
    # ADMT server information (for first hop)
    admt_server: HISADMTVPSEC06.hcloud.healthgrp.com.sg
    os: win
    environment: prd
    network_zone: mgt
    dc: hdc1

    # DNS server information (for second hop)
    dns_server: dc01.hcloud.healthgrp.com.sg
    dns_username: "{{ hcloud_dns_username }}"
    dns_password: "{{ hcloud_dns_password }}"
    description: "Health Group Cloud Domain"

    # Special configurations
    ptr_dns_server: "dc01.hcloud.healthgrp.com.sg"  # Same as DNS server

  iltc.healthgrp.com.sg:
    # ADMT server information (for first hop)
    admt_server: HISADMTVPSEC04.iltc.healthgrp.com.sg
    os: win
    environment: prd
    network_zone: mgt
    dc: hdc1

    # DNS server information (for second hop)
    dns_server: dc01.iltc.healthgrp.com.sg
    dns_username: "{{ iltc_dns_username }}"
    dns_password: "{{ iltc_dns_password }}"
    description: "ILTC Health Group Domain"

    # Special configurations
    ptr_dns_server: "dc01.iltc.healthgrp.com.sg"  # Same as DNS server

  healthgrpextp.com.sg:
    # ADMT server information (for first hop)
    admt_server: HISADMTVPSEC08.healthgrpextp.com.sg
    os: win
    environment: prd
    network_zone: mgt
    dc: hdc1

    # DNS server information (for second hop)
    dns_server: dc01.healthgrpextp.com.sg
    dns_username: "{{ healthgrpextp_dns_username }}"
    dns_password: "{{ healthgrpextp_dns_password }}"
    description: "Health Group External Production Domain"

    # Special configurations
    ptr_dns_server: "dc01.healthgrpextp.com.sg"  # Same as DNS server

  exthealthgrp.com.sg:
    # ADMT server information (for first hop)
    admt_server: HISADMTVPSEC07.exthealthgrp.com.sg
    os: win
    environment: prd
    network_zone: mgt
    dc: hdc1

    # DNS server information (for second hop)
    dns_server: dc01.exthealthgrp.com.sg
    dns_username: "{{ exthealthgrp_dns_username }}"
    dns_password: "{{ exthealthgrp_dns_password }}"
    description: "External Health Group Domain"

    # Special configurations
    ptr_dns_server: "dc01.exthealthgrp.com.sg"  # Same as DNS server

  nhg.local:
    # ADMT server information (for first hop)
    admt_server: HISADMTVPSEC11.nhg.local
    os: win
    environment: prd
    network_zone: mgt
    dc: hdc2

    # DNS server information (for second hop)
    dns_server: dc01.nhg.local
    dns_username: "{{ nhg_dns_username }}"
    dns_password: "{{ nhg_dns_password }}"
    description: "NHG Local Domain"

    # Special configurations
    ptr_dns_server: "dc01.nhg.local"  # Same as DNS server

  aic.local:
    # ADMT server information (for first hop)
    admt_server: HISADMTVPSEC02.aic.local
    os: win
    environment: prd
    network_zone: mgt
    dc: hdc1

    # DNS server information (for second hop)
    dns_server: dc01.aic.local
    dns_username: "{{ aic_dns_username }}"
    dns_password: "{{ aic_dns_password }}"
    description: "AIC Local Domain"

    # Special configurations
    ptr_dns_server: "dc01.aic.local"  # Same as DNS server

  shses.shs.com.sg:
    # ADMT server information (for first hop)
    admt_server: SHSADMTVPSEC12.shses.shs.com.sg
    os: win
    environment: prd
    network_zone: tsz
    dc: hdc1

    # DNS server information (for second hop)
    dns_server: dc01.shses.shs.com.sg
    dns_username: "{{ shses_dns_username }}"
    dns_password: "{{ shses_dns_password }}"
    description: "SHSES Domain"

    # Special configurations
    ptr_dns_server: "sesdcvpsys11.shs.com.sg"  # Special PTR DNS server

  # Staging domains
  devhealthgrp.com.sg:
    # ADMT server information (for first hop)
    admt_server: HISADMTVDSEC01.devhealthgrp.com.sg
    os: win
    environment: stg
    network_zone: mgt
    dc: hdc1

    # DNS server information (for second hop)
    dns_server: dc01.devhealthgrp.com.sg
    dns_username: "{{ devhealthgrp_dns_username }}"
    dns_password: "{{ devhealthgrp_dns_password }}"
    description: "Development Health Group Domain"

    # Special configurations
    ptr_dns_server: "dc01.devhealthgrp.com.sg"  # Same as DNS server

  healthgrpexts.com.sg:
    # ADMT server information (for first hop)
    admt_server: HISADMTVSSEC01.healthgrpexts.com.sg
    os: win
    environment: stg
    network_zone: mgt
    dc: hdc2

    # DNS server information (for second hop)
    dns_server: dc01.healthgrpexts.com.sg
    dns_username: "{{ healthgrpexts_dns_username }}"
    dns_password: "{{ healthgrpexts_dns_password }}"
    description: "Health Group External Staging Domain"

    # Special configurations
    ptr_dns_server: "dc01.healthgrpexts.com.sg"  # Same as DNS server

  nnstg.local:
    # ADMT server information (for first hop)
    admt_server: HISADMTVSSEC02.nnstg.local
    os: win
    environment: stg
    network_zone: mgt
    dc: hdc1

    # DNS server information (for second hop)
    dns_server: dc01.nnstg.local
    dns_username: "{{ nnstg_dns_username }}"
    dns_password: "{{ nnstg_dns_password }}"
    description: "NNSTG Local Domain"

    # Special configurations
    ptr_dns_server: "dc01.nnstg.local"  # Same as DNS server

  ses.shsu.com.sg:
    # ADMT server information (for first hop)
    admt_server: SHSADMTVDSEC02.ses.shsu.com.sg
    os: win
    environment: stg
    network_zone: mgt
    dc: hdc2

    # DNS server information (for second hop)
    dns_server: dc01.ses.shsu.com.sg
    dns_username: "{{ ses_dns_username }}"
    dns_password: "{{ ses_dns_password }}"
    description: "SES SHSU Domain"

    # Special configurations
    ptr_dns_server: "shdcvsys22h1.shsu.com.sg"  # Special PTR DNS server

# Default DNS settings
default_dns_ttl: 3600
default_dns_description: "Managed by Ansible DNS Management"
