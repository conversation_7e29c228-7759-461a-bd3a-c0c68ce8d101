#Requires -Version 5.1
#Requires -<PERSON><PERSON>les DnsServer

<#
.SYNOPSIS
    DNS Management Script for A, PTR, and CNAME records.

.DESCRIPTION
    This script manages DNS records (A, PTR, CNAME) across multiple domains.
    It supports adding, removing, updating, and verifying DNS records.
    The script automatically handles corresponding PTR records when managing A records.

.PARAMETER Operation
    The operation to perform: Add, Remove, Update, Verify.

.PARAMETER RecordType
    The type of DNS record: A, PTR, CNAME.

.PARAMETER Hostname
    The hostname part of the DNS record.

.PARAMETER Domain
    The domain part of the DNS record.

.PARAMETER IPAddress
    The IP address for A or PTR records.

.PARAMETER Target
    The target hostname for CNAME records.

.PARAMETER TTL
    Time to live value in seconds (default: 3600).

.PARAMETER Description
    Description for the DNS record.

# Credentials are now managed by Ansible using become method

.PARAMETER AsJson
    Return results as JSON.

.PARAMETER LogPath
    Path to the log file. If not specified, logs will be written to C:\Temp\dns_management.log

.PARAMETER LogLevel
    Log level: Error, Warning, Info, Debug. Default is Info.

.EXAMPLE
    .\set-dns.ps1 -Operation Add -RecordType A -Hostname server01 -Domain example.com -IPAddress ************ -AsJson

.EXAMPLE
    .\set-dns.ps1 -Operation Remove -RecordType CNAME -Hostname www -Domain example.com

.NOTES
    Author: CES Operational Excellence Team
    Contributors: Muhammad Syazani Bin Mohamed Khairi (7409)
    Version: 1.1
#>

[CmdletBinding()]
param (
    [Parameter(Mandatory = $true)]
    [ValidateSet('Add', 'Remove', 'Update', 'Verify')]
    [string]$Operation,

    [Parameter(Mandatory = $true)]
    [ValidateSet('A', 'PTR', 'CNAME')]
    [string]$RecordType,

    [Parameter(Mandatory = $true)]
    [string]$Hostname,

    [Parameter(Mandatory = $true)]
    [string]$Domain,

    [Parameter()]
    [string]$IPAddress,

    [Parameter()]
    [string]$Target,

    [Parameter()]
    [int]$TTL = 3600,

    [Parameter()]
    [string]$Description = "Managed by Ansible",

    [Parameter()]
    [string]$DNSServer,

    [Parameter()]
    [string]$PTRDNSServer,

    [Parameter()]
    [switch]$Force,

    # Credentials are now managed by Ansible using become method

    [Parameter()]
    [switch]$AsJson,

    [Parameter()]
    [bool]$ManagePTR = $true,

    [Parameter()]
    [string]$LogPath = "C:\Temp\dns_management.log",

    [Parameter()]
    [ValidateSet('Error', 'Warning', 'Info', 'Debug')]
    [string]$LogLevel = "Info"
)

# Logging functions
function Write-Log {
    [CmdletBinding()]
    param (
        [Parameter(Mandatory = $true)]
        [string]$Message,

        [Parameter()]
        [ValidateSet('Error', 'Warning', 'Info', 'Debug')]
        [string]$Level = "Info",

        [Parameter()]
        [string]$LogFile = $LogPath
    )

    # Create log directory if it doesn't exist
    $logDir = Split-Path -Path $LogFile -Parent
    if (-not (Test-Path -Path $logDir)) {
        try {
            New-Item -Path $logDir -ItemType Directory -Force | Out-Null
        } catch {
            Write-Error "Failed to create log directory: $_"
            return
        }
    }

    # Map log level to numeric value for filtering
    $levelMap = @{
        'Error' = 1
        'Warning' = 2
        'Info' = 3
        'Debug' = 4
    }

    # Only log if the message level is less than or equal to the configured log level
    if ($levelMap[$Level] -le $levelMap[$LogLevel]) {
        $timestamp = Get-Date -Format "yyyy-MM-dd HH:mm:ss"
        $logMessage = "[$timestamp] [$Level] [$Operation] [$RecordType] [$Hostname.$Domain] $Message"

        try {
            Add-Content -Path $LogFile -Value $logMessage -ErrorAction Stop
        } catch {
            Write-Error "Failed to write to log file: $_"
        }
    }
}

function Write-ErrorLog {
    [CmdletBinding()]
    param (
        [Parameter(Mandatory = $true)]
        [System.Management.Automation.ErrorRecord]$ErrorRecord,

        [Parameter()]
        [string]$CustomMessage = ""
    )

    $errorMessage = if ($CustomMessage) { "$CustomMessage - $($ErrorRecord.Exception.Message)" } else { $ErrorRecord.Exception.Message }
    $errorDetails = "Exception: $($ErrorRecord.Exception.GetType().FullName), StackTrace: $($ErrorRecord.ScriptStackTrace)"

    Write-Log -Message $errorMessage -Level Error
    Write-Log -Message $errorDetails -Level Debug
}

# Initialize result object
$result = @{
    success = $false
    message = ""
    record = @{
        type = $RecordType
        hostname = $Hostname
        domain = $Domain
        fqdn = "$Hostname.$Domain"
    }
    operation = $Operation
    timestamp = Get-Date -Format "yyyy-MM-dd HH:mm:ss"
    log_file = $LogPath
}

# Log script start
Write-Log -Message "Starting DNS operation" -Level Info

# Add operation-specific data to result
if ($RecordType -eq 'A' -or $RecordType -eq 'PTR') {
    $result.record.ip_address = $IPAddress
} elseif ($RecordType -eq 'CNAME') {
    $result.record.target = $Target
}

# Function to convert IP address to reverse lookup zone format
function ConvertTo-ReverseLookupZone {
    param (
        [string]$IPAddress
    )

    $octets = $IPAddress.Split('.')
    if ($octets.Count -ne 4) {
        throw "Invalid IP address format: $IPAddress"
    }

    return "$($octets[2]).$($octets[1]).$($octets[0]).in-addr.arpa"
}

# Function to get PTR record name from IP address
function Get-PTRRecordName {
    param (
        [string]$IPAddress
    )

    $octets = $IPAddress.Split('.')
    if ($octets.Count -ne 4) {
        throw "Invalid IP address format: $IPAddress"
    }

    return $octets[3]
}

# Helper function to test if a host is responding to ping
function Test-HostConnectivity {
    param (
        [Parameter(Mandatory=$true)]
        [string]$IPAddress,

        [Parameter()]
        [int]$TimeoutMilliseconds = 1000
    )

    try {
        Write-Log -Level Debug -Message "Testing connectivity to $IPAddress with timeout $TimeoutMilliseconds ms"
        $ping = New-Object System.Net.NetworkInformation.Ping
        $result = $ping.Send($IPAddress, $TimeoutMilliseconds)
        $isAlive = ($result.Status -eq 'Success')
        Write-Log -Level Debug -Message ("Ping result for {0}: {1}" -f $IPAddress, $result.Status)
        return $isAlive
    }
    catch {
        Write-Log -Level Debug -Message ("Error pinging {0}: {1}" -f $IPAddress, $_.Exception.Message)
        return $false
    }
}

# Function to validate parameters
function Test-Parameters {
    Write-Log -Message "Validating parameters" -Level Debug

    # Validate IP address for A and PTR records
    if (($RecordType -eq 'A' -or $RecordType -eq 'PTR') -and [string]::IsNullOrEmpty($IPAddress)) {
        $errorMsg = "IPAddress parameter is required for A and PTR records"
        Write-Log -Message $errorMsg -Level Error
        throw $errorMsg
    }

    # Validate target for CNAME records
    if ($RecordType -eq 'CNAME' -and [string]::IsNullOrEmpty($Target)) {
        $errorMsg = "Target parameter is required for CNAME records"
        Write-Log -Message $errorMsg -Level Error
        throw $errorMsg
    }

    # Validate hostname doesn't contain domain
    if ($Hostname.Contains('.')) {
        $errorMsg = "Hostname should not include domain. Use -Domain parameter instead."
        Write-Log -Message $errorMsg -Level Error
        throw $errorMsg
    }

    Write-Log -Message "Parameter validation successful" -Level Debug
}

# Function to handle A record operations
function Invoke-ARecordOperation {
    param (
        [string]$Operation,
        [string]$Hostname,
        [string]$Domain,
        [string]$IPAddress,
        [int]$TTL,
        [string]$Description,
        [bool]$ManagePTR
    )

    $fqdn = "$Hostname.$Domain"

    try {
        switch ($Operation) {
            'Add' {
                # Check if record already exists
                $existingRecord = Get-DnsServerResourceRecord -ZoneName $Domain -Name $Hostname -RRType A -ErrorAction SilentlyContinue

                if ($existingRecord) {
                    if ($existingRecord.RecordData.IPv4Address.IPAddressToString -eq $IPAddress) {
                        return @{
                            success = $true
                            message = "A record $fqdn already exists with IP $IPAddress. No changes made."
                            changed = $false
                        }
                    } else {
                        return @{
                            success = $false
                            message = "A record $fqdn already exists with different IP: $($existingRecord.RecordData.IPv4Address.IPAddressToString)"
                            changed = $false
                        }
                    }
                }

                # Add A record
                Add-DnsServerResourceRecordA -ZoneName $Domain -Name $Hostname -IPv4Address $IPAddress -TimeToLive (New-TimeSpan -Seconds $TTL) -ErrorAction Stop

                # If ManagePTR is true, add corresponding PTR record
                if ($ManagePTR) {
                    $ptrZone = ConvertTo-ReverseLookupZone -IPAddress $IPAddress
                    $ptrName = Get-PTRRecordName -IPAddress $IPAddress

                    try {
                        # Use PTRDNSServer if specified, otherwise use DNSServer
                        $targetDNSServer = if ($PTRDNSServer) { $PTRDNSServer } else { $DNSServer }

                        # If a target DNS server is specified, use ComputerName parameter
                        if ($targetDNSServer) {
                            Add-DnsServerResourceRecordPtr -ComputerName $targetDNSServer -ZoneName $ptrZone -Name $ptrName -PtrDomainName $fqdn -TimeToLive (New-TimeSpan -Seconds $TTL) -ErrorAction Stop
                        } else {
                            Add-DnsServerResourceRecordPtr -ZoneName $ptrZone -Name $ptrName -PtrDomainName $fqdn -TimeToLive (New-TimeSpan -Seconds $TTL) -ErrorAction Stop
                        }
                        return @{
                            success = $true
                            message = "Successfully added A record $fqdn with IP $IPAddress and corresponding PTR record"
                            changed = $true
                        }
                    } catch {
                        # If PTR creation fails, we still return success but with a warning
                        return @{
                            success = $true
                            message = ("Added A record {0} with IP {1}, but failed to add PTR record: {2}" -f $fqdn, $IPAddress, $_.Exception.Message)
                            changed = $true
                        }
                    }
                } else {
                    return @{
                        success = $true
                        message = "Successfully added A record $fqdn with IP $IPAddress (PTR management disabled)"
                        changed = $true
                    }
                }
            }
            'Remove' {
                # Check if record exists
                $existingRecord = Get-DnsServerResourceRecord -ZoneName $Domain -Name $Hostname -RRType A -ErrorAction SilentlyContinue

                if (-not $existingRecord) {
                    return @{
                        success = $true
                        message = "A record $fqdn does not exist. No changes made."
                        changed = $false
                    }
                }

                # Get IP address from existing record if not provided
                if ([string]::IsNullOrEmpty($IPAddress)) {
                    $IPAddress = $existingRecord.RecordData.IPv4Address.IPAddressToString
                }

                # Check if host is alive before removing DNS record
                if (-not $Force) {
                    $isHostAlive = Test-HostConnectivity -IPAddress $IPAddress
                    if ($isHostAlive) {
                        Write-Log -Level Warning -Message "Host $IPAddress is responding to ping. Use -Force to remove the DNS record for a live host."
                        return @{
                            success = $false
                            message = "Host $IPAddress is responding to ping. Use -Force to remove the DNS record for a live host."
                            changed = $false
                            details = "The DNS record removal was aborted because the host is still active. Use -Force parameter to override this safety check."
                        }
                    }
                }

                # Remove A record
                Remove-DnsServerResourceRecord -ZoneName $Domain -Name $Hostname -RRType A -Force -ErrorAction Stop

                # If ManagePTR is true, remove corresponding PTR record
                if ($ManagePTR) {
                    $ptrZone = ConvertTo-ReverseLookupZone -IPAddress $IPAddress
                    $ptrName = Get-PTRRecordName -IPAddress $IPAddress

                    try {
                        # Use PTRDNSServer if specified, otherwise use DNSServer
                        $targetDNSServer = if ($PTRDNSServer) { $PTRDNSServer } else { $DNSServer }

                        # If a target DNS server is specified, use ComputerName parameter
                        if ($targetDNSServer) {
                            Remove-DnsServerResourceRecord -ComputerName $targetDNSServer -ZoneName $ptrZone -Name $ptrName -RRType PTR -Force -ErrorAction Stop
                        } else {
                            Remove-DnsServerResourceRecord -ZoneName $ptrZone -Name $ptrName -RRType PTR -Force -ErrorAction Stop
                        }
                        return @{
                            success = $true
                            message = "Successfully removed A record $fqdn and corresponding PTR record"
                            changed = $true
                        }
                    } catch {
                        # If PTR removal fails, we still return success but with a warning
                        return @{
                            success = $true
                            message = ("Removed A record {0}, but failed to remove PTR record: {1}" -f $fqdn, $_.Exception.Message)
                            changed = $true
                        }
                    }
                } else {
                    return @{
                        success = $true
                        message = "Successfully removed A record $fqdn (PTR management disabled)"
                        changed = $true
                    }
                }
            }
            'Update' {
                # Check if record exists
                $existingRecord = Get-DnsServerResourceRecord -ZoneName $Domain -Name $Hostname -RRType A -ErrorAction SilentlyContinue

                if (-not $existingRecord) {
                    return @{
                        success = $false
                        message = "A record $fqdn does not exist. Cannot update."
                        changed = $false
                    }
                }

                $oldIPAddress = $existingRecord.RecordData.IPv4Address.IPAddressToString

                # If IP address is the same, just update TTL
                if ($oldIPAddress -eq $IPAddress) {
                    $newRecord = $existingRecord.Clone()
                    $newRecord.TimeToLive = New-TimeSpan -Seconds $TTL

                    Set-DnsServerResourceRecord -ZoneName $Domain -OldInputObject $existingRecord -NewInputObject $newRecord -ErrorAction Stop

                    return @{
                        success = $true
                        message = "Updated TTL for A record $fqdn"
                        changed = $true
                    }
                } else {
                    # Remove old record and add new one
                    Remove-DnsServerResourceRecord -ZoneName $Domain -Name $Hostname -RRType A -Force -ErrorAction Stop

                    # If ManagePTR is true, try to remove old PTR record
                    if ($ManagePTR) {
                        $oldPtrZone = ConvertTo-ReverseLookupZone -IPAddress $oldIPAddress
                        $oldPtrName = Get-PTRRecordName -IPAddress $oldIPAddress

                        try {
                            # Use PTRDNSServer if specified, otherwise use DNSServer
                            $targetDNSServer = if ($PTRDNSServer) { $PTRDNSServer } else { $DNSServer }

                            # If a target DNS server is specified, use ComputerName parameter
                            if ($targetDNSServer) {
                                Remove-DnsServerResourceRecord -ComputerName $targetDNSServer -ZoneName $oldPtrZone -Name $oldPtrName -RRType PTR -Force -ErrorAction SilentlyContinue
                            } else {
                                Remove-DnsServerResourceRecord -ZoneName $oldPtrZone -Name $oldPtrName -RRType PTR -Force -ErrorAction SilentlyContinue
                            }
                        } catch {
                            # Ignore errors when removing old PTR
                        }
                    }

                    # Add new A record
                    Add-DnsServerResourceRecordA -ZoneName $Domain -Name $Hostname -IPv4Address $IPAddress -TimeToLive (New-TimeSpan -Seconds $TTL) -ErrorAction Stop

                    # If ManagePTR is true, add new PTR record
                    if ($ManagePTR) {
                        $newPtrZone = ConvertTo-ReverseLookupZone -IPAddress $IPAddress
                        $newPtrName = Get-PTRRecordName -IPAddress $IPAddress

                        try {
                            # Use PTRDNSServer if specified, otherwise use DNSServer
                            $targetDNSServer = if ($PTRDNSServer) { $PTRDNSServer } else { $DNSServer }

                            # If a target DNS server is specified, use ComputerName parameter
                            if ($targetDNSServer) {
                                Add-DnsServerResourceRecordPtr -ComputerName $targetDNSServer -ZoneName $newPtrZone -Name $newPtrName -PtrDomainName $fqdn -TimeToLive (New-TimeSpan -Seconds $TTL) -ErrorAction Stop
                            } else {
                                Add-DnsServerResourceRecordPtr -ZoneName $newPtrZone -Name $newPtrName -PtrDomainName $fqdn -TimeToLive (New-TimeSpan -Seconds $TTL) -ErrorAction Stop
                            }
                            return @{
                                success = $true
                                message = "Updated A record $fqdn from $oldIPAddress to $IPAddress with corresponding PTR record"
                                changed = $true
                            }
                        } catch {
                            return @{
                                success = $true
                                message = ("Updated A record {0} from {1} to {2}, but failed to add new PTR record: {3}" -f $fqdn, $oldIPAddress, $IPAddress, $_.Exception.Message)
                                changed = $true
                            }
                        }
                    } else {
                        return @{
                            success = $true
                            message = "Updated A record $fqdn from $oldIPAddress to $IPAddress (PTR management disabled)"
                            changed = $true
                        }
                    }
                }
            }
            'Verify' {
                # Check if record exists
                $existingRecord = Get-DnsServerResourceRecord -ZoneName $Domain -Name $Hostname -RRType A -ErrorAction SilentlyContinue

                if (-not $existingRecord) {
                    return @{
                        success = $false
                        message = "A record $fqdn does not exist"
                        changed = $false
                    }
                }

                $recordIPAddress = $existingRecord.RecordData.IPv4Address.IPAddressToString

                # If IP address is provided, verify it matches
                if (-not [string]::IsNullOrEmpty($IPAddress) -and $recordIPAddress -ne $IPAddress) {
                    return @{
                        success = $false
                        message = "A record $fqdn exists but has IP $recordIPAddress instead of $IPAddress"
                        changed = $false
                    }
                }

                # If ManagePTR is true, check PTR record
                if ($ManagePTR) {
                    $ptrZone = ConvertTo-ReverseLookupZone -IPAddress $recordIPAddress
                    $ptrName = Get-PTRRecordName -IPAddress $recordIPAddress

                    try {
                        # Use PTRDNSServer if specified, otherwise use DNSServer
                        $targetDNSServer = if ($PTRDNSServer) { $PTRDNSServer } else { $DNSServer }

                        # If a target DNS server is specified, use ComputerName parameter
                        if ($targetDNSServer) {
                            $ptrRecord = Get-DnsServerResourceRecord -ComputerName $targetDNSServer -ZoneName $ptrZone -Name $ptrName -RRType PTR -ErrorAction SilentlyContinue
                        } else {
                            $ptrRecord = Get-DnsServerResourceRecord -ZoneName $ptrZone -Name $ptrName -RRType PTR -ErrorAction SilentlyContinue
                        }

                        if (-not $ptrRecord) {
                            return @{
                                success = $false
                                message = "A record $fqdn exists with IP $recordIPAddress, but corresponding PTR record is missing"
                                changed = $false
                            }
                        }

                        $ptrTarget = $ptrRecord.RecordData.PtrDomainName.TrimEnd('.')

                        if ($ptrTarget -ne $fqdn) {
                            return @{
                                success = $false
                                message = "A record $fqdn exists with IP $recordIPAddress, but PTR record points to $ptrTarget instead"
                                changed = $false
                            }
                        }

                        return @{
                            success = $true
                            message = "A record $fqdn exists with IP $recordIPAddress and correct PTR record"
                            changed = $false
                        }
                    } catch {
                        return @{
                            success = $false
                            message = ("A record {0} exists with IP {1}, but failed to verify PTR record: {2}" -f $fqdn, $recordIPAddress, $_.Exception.Message)
                            changed = $false
                        }
                    }
                } else {
                    return @{
                        success = $true
                        message = "A record $fqdn exists with IP $recordIPAddress (PTR verification disabled)"
                        changed = $false
                    }
                }
            }
        }
    } catch {
        return @{
            success = $false
            message = ("Error performing {0} operation on A record {1}: {2}" -f $Operation, $fqdn, $_.Exception.Message)
            changed = $false
        }
    }
}

# Function to handle PTR record operations
function Invoke-PTRRecordOperation {
    param (
        [string]$Operation,
        [string]$IPAddress,
        [string]$Hostname,
        [string]$Domain,
        [int]$TTL
    )

    $fqdn = "$Hostname.$Domain"
    $ptrZone = ConvertTo-ReverseLookupZone -IPAddress $IPAddress
    $ptrName = Get-PTRRecordName -IPAddress $IPAddress

    try {
        switch ($Operation) {
            'Add' {
                # Check if record already exists
                $existingRecord = Get-DnsServerResourceRecord -ZoneName $ptrZone -Name $ptrName -RRType PTR -ErrorAction SilentlyContinue

                if ($existingRecord) {
                    $existingTarget = $existingRecord.RecordData.PtrDomainName.TrimEnd('.')

                    if ($existingTarget -eq $fqdn) {
                        return @{
                            success = $true
                            message = "PTR record for $IPAddress already points to $fqdn. No changes made."
                            changed = $false
                        }
                    } else {
                        return @{
                            success = $false
                            message = "PTR record for $IPAddress already exists but points to $existingTarget"
                            changed = $false
                        }
                    }
                }

                # Add PTR record
                Add-DnsServerResourceRecordPtr -ZoneName $ptrZone -Name $ptrName -PtrDomainName $fqdn -TimeToLive (New-TimeSpan -Seconds $TTL) -ErrorAction Stop

                return @{
                    success = $true
                    message = "Successfully added PTR record for $IPAddress pointing to $fqdn"
                    changed = $true
                }
            }
            'Remove' {
                # Check if record exists
                $existingRecord = Get-DnsServerResourceRecord -ZoneName $ptrZone -Name $ptrName -RRType PTR -ErrorAction SilentlyContinue

                if (-not $existingRecord) {
                    return @{
                        success = $true
                        message = "PTR record for $IPAddress does not exist. No changes made."
                        changed = $false
                    }
                }

                # Check if host is alive before removing PTR record
                if (-not $Force) {
                    $isHostAlive = Test-HostConnectivity -IPAddress $IPAddress
                    if ($isHostAlive) {
                        Write-Log -Level Warning -Message "Host $IPAddress is responding to ping. Use -Force to remove the PTR record for a live host."
                        return @{
                            success = $false
                            message = "Host $IPAddress is responding to ping. Use -Force to remove the PTR record for a live host."
                            changed = $false
                            details = "The PTR record removal was aborted because the host is still active. Use -Force parameter to override this safety check."
                        }
                    }
                }

                # Remove PTR record
                Remove-DnsServerResourceRecord -ZoneName $ptrZone -Name $ptrName -RRType PTR -Force -ErrorAction Stop

                return @{
                    success = $true
                    message = "Successfully removed PTR record for $IPAddress"
                    changed = $true
                }
            }
            'Update' {
                # Check if record exists
                $existingRecord = Get-DnsServerResourceRecord -ZoneName $ptrZone -Name $ptrName -RRType PTR -ErrorAction SilentlyContinue

                if (-not $existingRecord) {
                    return @{
                        success = $false
                        message = "PTR record for $IPAddress does not exist. Cannot update."
                        changed = $false
                    }
                }

                $existingTarget = $existingRecord.RecordData.PtrDomainName.TrimEnd('.')

                # If target is the same, just update TTL
                if ($existingTarget -eq $fqdn) {
                    $newRecord = $existingRecord.Clone()
                    $newRecord.TimeToLive = New-TimeSpan -Seconds $TTL

                    Set-DnsServerResourceRecord -ZoneName $ptrZone -OldInputObject $existingRecord -NewInputObject $newRecord -ErrorAction Stop

                    return @{
                        success = $true
                        message = "Updated TTL for PTR record $IPAddress"
                        changed = $true
                    }
                } else {
                    # Remove old record and add new one
                    Remove-DnsServerResourceRecord -ZoneName $ptrZone -Name $ptrName -RRType PTR -Force -ErrorAction Stop

                    # Add new PTR record
                    Add-DnsServerResourceRecordPtr -ZoneName $ptrZone -Name $ptrName -PtrDomainName $fqdn -TimeToLive (New-TimeSpan -Seconds $TTL) -ErrorAction Stop

                    return @{
                        success = $true
                        message = "Updated PTR record for $IPAddress from $existingTarget to $fqdn"
                        changed = $true
                    }
                }
            }
            'Verify' {
                # Check if record exists
                $existingRecord = Get-DnsServerResourceRecord -ZoneName $ptrZone -Name $ptrName -RRType PTR -ErrorAction SilentlyContinue

                if (-not $existingRecord) {
                    return @{
                        success = $false
                        message = "PTR record for $IPAddress does not exist"
                        changed = $false
                    }
                }

                $existingTarget = $existingRecord.RecordData.PtrDomainName.TrimEnd('.')

                if ($existingTarget -ne $fqdn) {
                    return @{
                        success = $false
                        message = "PTR record for $IPAddress points to $existingTarget instead of $fqdn"
                        changed = $false
                    }
                }

                return @{
                    success = $true
                    message = "PTR record for $IPAddress correctly points to $fqdn"
                    changed = $false
                }
            }
        }
    } catch {
        return @{
            success = $false
            message = ("Error performing {0} operation on PTR record for {1}: {2}" -f $Operation, $IPAddress, $_.Exception.Message)
            changed = $false
        }
    }
}

# Function to handle CNAME record operations
function Invoke-CNAMERecordOperation {
    param (
        [string]$Operation,
        [string]$Hostname,
        [string]$Domain,
        [string]$Target,
        [int]$TTL
    )

    $fqdn = "$Hostname.$Domain"

    try {
        switch ($Operation) {
            'Add' {
                # Check if record already exists
                $existingRecord = Get-DnsServerResourceRecord -ZoneName $Domain -Name $Hostname -RRType CNAME -ErrorAction SilentlyContinue

                if ($existingRecord) {
                    $existingTarget = $existingRecord.RecordData.HostNameAlias.TrimEnd('.')

                    if ($existingTarget -eq $Target) {
                        return @{
                            success = $true
                            message = "CNAME record $fqdn already points to $Target. No changes made."
                            changed = $false
                        }
                    } else {
                        return @{
                            success = $false
                            message = "CNAME record $fqdn already exists but points to $existingTarget"
                            changed = $false
                        }
                    }
                }

                # Add CNAME record
                Add-DnsServerResourceRecordCName -ZoneName $Domain -Name $Hostname -HostNameAlias $Target -TimeToLive (New-TimeSpan -Seconds $TTL) -ErrorAction Stop

                return @{
                    success = $true
                    message = "Successfully added CNAME record $fqdn pointing to $Target"
                    changed = $true
                }
            }
            'Remove' {
                # Check if record exists
                $existingRecord = Get-DnsServerResourceRecord -ZoneName $Domain -Name $Hostname -RRType CNAME -ErrorAction SilentlyContinue

                if (-not $existingRecord) {
                    return @{
                        success = $true
                        message = "CNAME record $fqdn does not exist. No changes made."
                        changed = $false
                    }
                }

                # Remove CNAME record
                Remove-DnsServerResourceRecord -ZoneName $Domain -Name $Hostname -RRType CNAME -Force -ErrorAction Stop

                return @{
                    success = $true
                    message = "Successfully removed CNAME record $fqdn"
                    changed = $true
                }
            }
            'Update' {
                # Check if record exists
                $existingRecord = Get-DnsServerResourceRecord -ZoneName $Domain -Name $Hostname -RRType CNAME -ErrorAction SilentlyContinue

                if (-not $existingRecord) {
                    return @{
                        success = $false
                        message = "CNAME record $fqdn does not exist. Cannot update."
                        changed = $false
                    }
                }

                $existingTarget = $existingRecord.RecordData.HostNameAlias.TrimEnd('.')

                # If target is the same, just update TTL
                if ($existingTarget -eq $Target) {
                    $newRecord = $existingRecord.Clone()
                    $newRecord.TimeToLive = New-TimeSpan -Seconds $TTL

                    Set-DnsServerResourceRecord -ZoneName $Domain -OldInputObject $existingRecord -NewInputObject $newRecord -ErrorAction Stop

                    return @{
                        success = $true
                        message = "Updated TTL for CNAME record $fqdn"
                        changed = $true
                    }
                } else {
                    # Remove old record and add new one
                    Remove-DnsServerResourceRecord -ZoneName $Domain -Name $Hostname -RRType CNAME -Force -ErrorAction Stop

                    # Add new CNAME record
                    Add-DnsServerResourceRecordCName -ZoneName $Domain -Name $Hostname -HostNameAlias $Target -TimeToLive (New-TimeSpan -Seconds $TTL) -ErrorAction Stop

                    return @{
                        success = $true
                        message = "Updated CNAME record $fqdn from $existingTarget to $Target"
                        changed = $true
                    }
                }
            }
            'Verify' {
                # Check if record exists
                $existingRecord = Get-DnsServerResourceRecord -ZoneName $Domain -Name $Hostname -RRType CNAME -ErrorAction SilentlyContinue

                if (-not $existingRecord) {
                    return @{
                        success = $false
                        message = "CNAME record $fqdn does not exist"
                        changed = $false
                    }
                }

                $existingTarget = $existingRecord.RecordData.HostNameAlias.TrimEnd('.')

                if ($existingTarget -ne $Target) {
                    return @{
                        success = $false
                        message = "CNAME record $fqdn points to $existingTarget instead of $Target"
                        changed = $false
                    }
                }

                return @{
                    success = $true
                    message = "CNAME record $fqdn correctly points to $Target"
                    changed = $false
                }
            }
        }
    } catch {
        return @{
            success = $false
            message = ("Error performing {0} operation on CNAME record {1}: {2}" -f $Operation, $fqdn, $_.Exception.Message)
            changed = $false
        }
    }
}

# Main execution block
try {
    Write-Log -Message "Starting $Operation operation for $RecordType record $Hostname.$Domain" -Level Info

    # Validate parameters
    Test-Parameters

    # Set credentials and DNS server if provided
    $PSDefaultParameterValues = @{}

    # Credentials are now managed by Ansible using become method
    Write-Log -Message "Using current user context (credentials managed by Ansible)" -Level Info

    if ($DNSServer) {
        Write-Log -Message "Using DNS server: $DNSServer" -Level Debug
        $PSDefaultParameterValues.Add("Get-DnsServerResourceRecord:ComputerName", $DNSServer)
        $PSDefaultParameterValues.Add("Add-DnsServerResourceRecordA:ComputerName", $DNSServer)
        $PSDefaultParameterValues.Add("Add-DnsServerResourceRecordPtr:ComputerName", $DNSServer)
        $PSDefaultParameterValues.Add("Add-DnsServerResourceRecordCName:ComputerName", $DNSServer)
        $PSDefaultParameterValues.Add("Remove-DnsServerResourceRecord:ComputerName", $DNSServer)
        $PSDefaultParameterValues.Add("Set-DnsServerResourceRecord:ComputerName", $DNSServer)
    } else {
        Write-Log -Message "No DNS server specified, using local server" -Level Warning
    }

    # Execute operation based on record type
    $operationResult = $null
    Write-Log -Message "Executing $Operation operation for $RecordType record" -Level Info

    switch ($RecordType) {
        'A' {
            Write-Log -Message "Processing A record with IP $IPAddress" -Level Debug
            $operationResult = Invoke-ARecordOperation -Operation $Operation -Hostname $Hostname -Domain $Domain -IPAddress $IPAddress -TTL $TTL -Description $Description -ManagePTR $ManagePTR
        }
        'PTR' {
            Write-Log -Message "Processing PTR record for IP $IPAddress" -Level Debug
            $operationResult = Invoke-PTRRecordOperation -Operation $Operation -IPAddress $IPAddress -Hostname $Hostname -Domain $Domain -TTL $TTL
        }
        'CNAME' {
            Write-Log -Message "Processing CNAME record with target $Target" -Level Debug
            $operationResult = Invoke-CNAMERecordOperation -Operation $Operation -Hostname $Hostname -Domain $Domain -Target $Target -TTL $TTL
        }
    }

    # Update result with operation result
    $result.success = $operationResult.success
    $result.message = $operationResult.message
    $result.changed = $operationResult.changed

    # Log operation result
    if ($operationResult.success) {
        Write-Log -Message ("Operation successful: {0}" -f $operationResult.message) -Level Info
    } else {
        Write-Log -Message ("Operation failed: {0}" -f $operationResult.message) -Level Error
    }

} catch {
    Write-ErrorLog -ErrorRecord $_ -CustomMessage "Unhandled exception in main execution block"
    $result.success = $false
    $result.message = ("Error: {0}" -f $_.Exception.Message)
    $result.error_details = @{
        exception_type = $_.Exception.GetType().FullName
        exception_message = $_.Exception.Message
        script_stack_trace = $_.ScriptStackTrace
    }
}

# Log completion
Write-Log -Message ("DNS operation completed with status: {0}" -f $result.success) -Level Info

# Output result
if ($AsJson) {
    $result | ConvertTo-Json -Depth 5
} else {
    $result
}
