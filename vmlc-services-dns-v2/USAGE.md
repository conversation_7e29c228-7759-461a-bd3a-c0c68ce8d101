# DNS Management Usage Guide

This guide provides instructions on how to use the DNS Management system to manage DNS records

## Author

CES Operational Excellence Team

## Contributors

- <PERSON> (7409)
- <PERSON><PERSON> (9094)
- <PERSON><PERSON> (9413)

## Table of Contents

1. [Prerequisites](#1-prerequisites)
2. [Architecture](#2-architecture)
3. [Idempotency](#3-idempotency)
4. [Ansible Automation Platform Setup](#4-ansible-automation-platform-setup)
5. [Parameters](#5-parameters)
   5.1. [Required Parameters](#51-required-parameters)
   5.2. [Operation-Specific Parameters](#52-operation-specific-parameters)
   5.3. [Optional Parameters](#53-optional-parameters)
6. [Configuration](#6-configuration)
   6.1. [Domain-to-Server Mapping](#61-domain-to-server-mapping)
   6.2. [Default Settings](#62-default-settings)
   6.3. [Credentials](#63-credentials)
7. [CyberArk Integration](#7-cyberark-integration)
   7.1. [Configuration](#71-configuration)
   7.2. [Required Variables](#72-required-variables)
   7.3. [Usage](#73-usage)
8. [Single vs. Multi-Domain Operations](#8-single-vs-multi-domain-operations)
   8.1. [Single-Domain Operations](#81-single-domain-operations)
   8.2. [Multi-Domain Operations](#82-multi-domain-operations)
   8.3. [Example Job Template for Multi-Domain Operations](#83-example-job-template-for-multi-domain-operations)
   8.4. [Error Handling and Logging](#84-error-handling-and-logging)
9. [DNS Record Removal Safety Checks](#9-dns-record-removal-safety-checks)
   9.1. [Connectivity Check](#91-connectivity-check)
   9.2. [Force Removal](#92-force-removal)
   9.3. [When to Use Force Removal](#93-when-to-use-force-removal)
10. [Special PTR Record Management](#10-special-ptr-record-management)
    10.1. [Domain-Specific PTR DNS Servers](#101-domain-specific-ptr-dns-servers)
    10.2. [How It Works](#102-how-it-works)
11. [Report Generation](#11-report-generation)
12. [Email Notifications](#12-email-notifications)
    12.1. [Consolidated Report Emails](#121-consolidated-report-emails)
13. [Logging](#13-logging)
    13.1. [Log Locations](#131-log-locations)
    13.2. [Log Naming Convention](#132-log-naming-convention)
    13.3. [Log Levels](#133-log-levels)
    13.4. [Viewing Logs](#134-viewing-logs)
    13.5. [Log Storage Options](#135-log-storage-options)
14. [Troubleshooting](#14-troubleshooting)
    14.1. [Common Issues](#141-common-issues)
    14.2. [Using Logs for Troubleshooting](#142-using-logs-for-troubleshooting)
15. [Command Line Usage (For Developers)](#15-command-line-usage-for-developers)
    15.1. [Basic Usage](#151-basic-usage)
    15.2. [Multi-Domain Operations](#152-multi-domain-operations)
    15.3. [Examples](#153-examples)

## 1. Prerequisites

1. Ansible Automation Platform (AAP) with access to AD Management (ADMT) servers
2. ADMT servers with permission to access DNS Manager on domain controllers
3. WinRM connectivity from AAP to ADMT servers
4. Appropriate credentials for ADMT servers and domain controllers
5. wkhtmltopdf installed on the Ansible control node for PDF report generation

## 2. Architecture

This project follows a specific flow for DNS management operations:

1. **Ansible Automation Platform (AAP)** runs the playbook on localhost
2. The playbook dynamically selects the appropriate **AD Management (ADMT) server** based on the domain
3. The ADMT server executes PowerShell scripts with the necessary permissions to access the **DNS server**
4. The DNS server performs the requested DNS operations

This architecture ensures proper authentication and permission handling, as the ADMT servers have the necessary rights to manage DNS records on the domain controllers. The system uses a dedicated credential selection role to dynamically select the appropriate credentials based on the domain being processed.

## 3. Idempotency

All operations in the DNS Management system are designed to be idempotent, meaning that running the same operation multiple times will have the same effect as running it once. This is important for:

- **Reliability**: Operations can be safely retried if they fail
- **Auditing**: Clear tracking of whether changes were actually made
- **Compliance**: Demonstrating that only necessary changes are being made
- **Automation**: Enabling safe, repeatable automation workflows

For example:
- Adding a DNS record that already exists with the same values will not make any changes
- Removing a DNS record that doesn't exist will not cause an error
- Updating a DNS record with the same values will only update the TTL if it's different

Each operation returns a `changed` flag indicating whether any changes were actually made, which is reflected in the logs and reports for audit purposes.

## 4. Ansible Automation Platform Setup

This project is designed to be run from Ansible Automation Platform (AAP) using Job Templates. Here's how to set up the project in AAP:

1. **Create a Project**:
   - **Name**: DNS Management
   - **SCM Type**: Git
   - **SCM URL**: Your Git repository URL
   - **SCM Branch/Tag/Commit**: [dev][uat][prd]

2. **Create Job Templates**:
   Create separate job templates for different DNS operations. For example:

   - **Add A Record Template**:
     - **Name**: DNS Management - Add A Record
     - **Inventory**: Your inventory containing target hosts
     - **Project**: DNS Management
     - **Playbook**: playbooks/manage_dns.yml
     - **Variables**:
       ```yaml
       operation: add
       record_type: a
       ```
     - **Prompt on Launch**: hostname, domain, ip_address, ticket, description, ttl, manage_ptr

   - **Add CNAME Record Template**:
     - **Name**: DNS Management - Add CNAME Record
     - **Inventory**: Your inventory containing target hosts
     - **Project**: DNS Management
     - **Playbook**: playbooks/manage_dns.yml
     - **Variables**:
       ```yaml
       operation: add
       record_type: cname
       ```
     - **Prompt on Launch**: hostname, domain, cname_target, ticket, description, ttl

   - **Remove DNS Record Template**:
     - **Name**: DNS Management - Remove DNS Record
     - **Inventory**: Your inventory containing target hosts
     - **Project**: DNS Management
     - **Playbook**: playbooks/manage_dns.yml
     - **Variables**:
       ```yaml
       operation: remove
       ```
     - **Prompt on Launch**: record_type, hostname, domain, ip_address, ticket

## 5. Parameters

### 5.1. Required Parameters

- `operation`: The operation to perform (add, remove, update, verify)
- `record_type`: The type of DNS record (a, ptr, cname)
- `ticket`: Service ticket number for auditing and tracking (required for all operations)

#### For Single Domain Operations:
- `hostname`: The hostname part of the DNS record
- `domain`: The domain part of the DNS record
- `ip_address`: The IP address for A or PTR records (required for add, remove, and update operations)
- `cname_target`: The target hostname for CNAME records (required for add and update operations with CNAME records)

#### For Multi-Domain Operations:
- `hostnames`: Comma-separated list of hostnames (must match domains 1:1)
- `domains`: Comma-separated list of domains
- `ip_addresses`: Comma-separated list of IP addresses for A or PTR records (must match domains 1:1, required for add, remove, and update operations)
- `cname_targets`: Comma-separated list of target hostnames for CNAME records (must match domains 1:1, required for add and update operations with CNAME records)

### 5.2. Operation-Specific Parameters

#### Verify Operation
- `hostname`/`hostnames`: The hostname part of the DNS record
- `domain`/`domains`: The domain part of the DNS record
- `ip_address`/`ip_addresses`: The IP address for A or PTR records (optional)

#### Add Operation
- `hostname`/`hostnames`: The hostname part of the DNS record
- `domain`/`domains`: The domain part of the DNS record
- `ip_address`/`ip_addresses`: The IP address for A or PTR records
- `cname_target`/`cname_targets`: The target hostname for CNAME records (required for CNAME records)
- `ttl`: Time-to-Live value in seconds (default: 3600)
- `description`: Description for the DNS record
- `manage_ptr`: Whether to automatically manage PTR records for A records (default: true)

#### Remove Operation
- `hostname`/`hostnames`: The hostname part of the DNS record
- `domain`/`domains`: The domain part of the DNS record
- `ip_address`/`ip_addresses`: The IP address for A or PTR records

#### Update Operation
- `hostname`/`hostnames`: The hostname part of the DNS record
- `domain`/`domains`: The domain part of the DNS record
- `ip_address`/`ip_addresses`: The IP address for A or PTR records
- `cname_target`/`cname_targets`: The target hostname for CNAME records (required for CNAME records)
- `ttl`: Time-to-Live value in seconds (default: 3600)
- `description`: Description for the DNS record

### 5.3. Optional Parameters

- `generate_report`: Whether to generate a PDF report (default: true)
- `email_report`: Whether to email the PDF report (default: false)
- `email_consolidated_report`: Whether to email a consolidated report for multi-domain operations (default: false)
- `email_recipient`: Email address to send the report to
- `log_level`: Logging level (Error, Warning, Info, Debug) - default: Info
- `email_logs`: Whether to email logs as attachments (default: false)
- `store_logs_target_server`: Whether to store logs on target servers (default: true)

## 6. Configuration

### 6.1. Domain-to-Server Mapping

The mapping between domains, DNS servers, and ADMT servers is defined in `vars/dns_servers.yml`. Each domain entry includes:

- `domain`: The domain name
- `dns_server`: The DNS server for this domain
- `admt_server`: The ADMT server that has permission to access the DNS server
- `username`: The username for authentication to the DNS server
- `password`: The password for authentication to the DNS server
- `description`: A description of the domain

### 6.2. Default Settings

The following default settings are defined in `vars/dns_servers.yml`:

- `default_dns_ttl`: Default Time-to-Live value in seconds (default: 3600)
- `default_dns_description`: Default description for DNS records

### 6.3. Credentials

Credentials should be stored securely using one of these methods:

1. **Ansible Vault**: Create an encrypted credentials file
   ```bash
   cp vars/credentials.yml.example vars/credentials.yml
   ansible-vault encrypt vars/credentials.yml
   ```

   The credentials.yml file contains credentials for all supported domains:
   - Production domains: healthgrp.com.sg, hcloud.healthgrp.com.sg, iltc.healthgrp.com.sg, healthgrpextp.com.sg, exthealthgrp.com.sg, nhg.local, aic.local, shses.shs.com.sg
   - Staging domains: devhealthgrp.com.sg, healthgrpexts.com.sg, nnstg.local, ses.shsu.com.sg

2. **CyberArk Integration**: The playbook includes the CyberArk collection role to retrieve credentials from PAM Safe

The `credential_selection` role centralizes the logic for selecting the appropriate credentials based on the domain being processed. This role is called whenever domain-specific credentials are needed, ensuring consistent credential handling throughout the system.

## 7. CyberArk Integration

The playbook includes a pre-task that calls the `cloud_cpe.cyberark_ccp.retrieve_from_cyberark` role to retrieve credentials from CyberArk's PAM Safe. This integration is handled automatically by the Ansible Automation Platform.

**Note**: The `cloud_cpe.cyberark_ccp.retrieve_from_cyberark` role must be installed in your Ansible environment. This role is provided by CPE Automation Team.

### 7.1. Configuration

The CyberArk integration is configured using a domain mapping approach. Each domain has its required host variables defined in the `domain_mapping.yml` file:

```yaml
domain_host_vars:
  healthgrp.com.sg:
    admt_server: HISADMTVPSEC05.healthgrp.com.sg
    os: win
    environment: prd
    network_zone: psz
    dc: hdc2  # Data center location
  # Other domains...
```

The playbook dynamically selects the appropriate ADMT server based on the domain and sets all required variables for the CyberArk integration:

```yaml
pre_tasks:
  - name: Set host variables based on domain
    ansible.builtin.add_host:
      name: "{{ domain_host_vars[domain].admt_server }}"
      groups: dynamic_admt_servers
      os: "{{ domain_host_vars[domain].os }}"
      environment: "{{ domain_host_vars[domain].environment }}"
      network_zone: "{{ domain_host_vars[domain].network_zone }}"
      dc: "{{ domain_host_vars[domain].dc }}"
      var_sr_number: "{{ ticket }}"
    when: domain is defined and domain in domain_host_vars

  - name: Retrieve Password from PAM Safe
    ansible.builtin.include_role:
      name: cloud_cpe.cyberark_ccp.retrieve_from_cyberark
    delegate_to: "{{ domain_host_vars[domain].admt_server }}"
    when: domain is defined and domain in domain_host_vars
```

The role retrieves credentials from CyberArk PAM Safe using these variables and stores the retrieved credentials in variables that are used throughout the playbook.

### 7.2. Required Variables

The CyberArk integration requires the following variables, which are now dynamically set based on the domain mapping:

- `os`: The operating system type (e.g., "win" for Windows, "linux" for Linux)
  - Set to "win" for all domains in the mapping

- `var_sr_number`: The service request number for tracking
  - Uses the `ticket` parameter value

- `environment`: The environment (e.g., "stg" for staging, "prd" for production)
  - Set based on the domain in the mapping

- `network_zone`: The network zone (e.g., "tsz" for trusted security zone, "psz" for production security zone)
  - Set based on the domain in the mapping

- `dc`: The data center location (hdc1 or hdc2)
  - Set based on the domain in the mapping

### 7.3. Usage

When using CyberArk integration, you don't need to provide credentials in the command line or Job Template. The playbook will automatically retrieve the credentials from CyberArk based on the domain and the variables defined in the domain mapping.

The CyberArk integration is now fully dynamic based on the domain mapping, so there's no need to override variables in your job template. Simply specify the domain, and the system will automatically use the correct variables for that domain.

## 8. Single vs. Multi-Domain Operations

The DNS Management system supports both single-domain and multi-domain operations. Understanding the differences and when to use each is important for effective DNS management.

### 8.1. Single-Domain Operations

Single-domain operations are used when you need to manage DNS records in a single domain only.

#### When to Use Single-Domain Operations

- When managing records in only one domain
- For simple, one-off DNS changes
- When different domains require different record configurations

#### Parameters for Single-Domain Operations

- `hostname`: The hostname part of the DNS record (e.g., "server01")
- `domain`: The domain part of the DNS record (e.g., "example.com")
- `ip_address`: The IP address for A or PTR records (for add/remove/update operations)
- `cname_target`: The target hostname for CNAME records (for CNAME operations)

#### Example Job Template for Single-Domain Operations

- **Name**: DNS Management - Add A Record (Single Domain)
- **Inventory**: Your inventory
- **Project**: DNS Management
- **Playbook**: playbooks/manage_dns.yml
- **Variables**:
  ```yaml
  operation: add
  record_type: a
  ```
- **Prompt on Launch**: hostname, domain, ip_address, ticket, description, ttl, manage_ptr

### 8.2. Multi-Domain Operations

Multi-domain operations allow you to perform the same DNS operation across multiple domains in a single run. This is useful for scenarios where you need to create similar DNS records in multiple domains.

#### When to Use Multi-Domain Operations

- When the same operation needs to be performed across multiple domains
- For bulk DNS changes that affect multiple domains
- When provisioning services that require DNS records in multiple domains

#### Parameters for Multi-Domain Operations

To perform operations across multiple domains, use the `domains` and `hostnames` parameters instead of `domain` and `hostname`. These parameters accept comma-separated lists:

```yaml
domains: "healthgrp.com.sg,hcloud.healthgrp.com.sg,iltc.healthgrp.com.sg"
hostnames: "server01,server02,server03"
ip_addresses: "************,************,************"  # Required for A/PTR records (add/remove/update)
cname_targets: "target1.example.com,target2.example.com,target3.example.com"  # Required for CNAME records
ticket: "INC123456"
```

**Important**: The number of items in `domains` and `hostnames` must match exactly, creating a 1:1 relationship between each domain and hostname.

#### How Multi-Domain Operations Work

When using multi-domain parameters, the system will:

1. Validate that the number of domains matches the number of hostnames
2. Create a mapping between each domain and its corresponding hostname
3. **Process domain-hostname pairs in parallel** for improved performance
4. **Display real-time progress tracking** during execution
5. Dynamically select the appropriate ADMT server for each domain
6. Execute the requested operation on each domain with its corresponding hostname
7. Wait for all operations to complete
8. Generate separate logs and reports for each domain-hostname pair
9. Generate a consolidated summary report that includes the results of all operations

### 8.3. Example Job Template for Multi-Domain Operations

- **Name**: DNS Management - Add A Record (Multi-Domain)
- **Inventory**: Your inventory
- **Project**: DNS Management
- **Playbook**: playbooks/manage_dns.yml
- **Variables**:
  ```yaml
  operation: add
  record_type: a
  ```
- **Prompt on Launch**: hostnames, domains, ip_addresses, ticket, description, ttl, manage_ptr

### 8.4. Error Handling and Logging

#### Single-Domain Operations

For single-domain operations, errors are handled as follows:

- If an error occurs, the playbook will fail with a detailed error message
- The error is logged in both Ansible and PowerShell logs
- The error message includes the specific domain, hostname, and operation that failed
- If `generate_report` is enabled, the report will include the error details

#### Multi-Domain Operations

For multi-domain operations, errors are handled differently:

- If an error occurs in one domain-hostname pair, the operation continues for other pairs
- Each failure is logged separately in domain-specific log files
- A progress log tracks the status of each domain-hostname pair
- The consolidated report includes both successful and failed operations
- The playbook returns a partial success status if at least one operation succeeded

#### Viewing Logs for Multi-Domain Operations

To view logs for multi-domain operations:

1. **Progress Logs**: Check `logs/progress/YYYY-MM-DD_operation_ticket.log` for real-time status
2. **Domain-Specific Logs**: Check `logs/ansible/YYYY-MM-DD_hostname.domain_recordtype_operation.log` for details on specific domain operations
3. **Consolidated Report**: If `email_consolidated_report` is enabled, the report will include a summary of all operations

## 9. DNS Record Removal Safety Checks

The DNS Management system includes safety checks to prevent accidental removal of DNS records for active servers:

### 9.1. Connectivity Check

When removing DNS records (A or PTR), the system automatically checks if the host is responding to ping:

- If the host is alive (responds to ping), the removal operation is aborted with a warning message
- This prevents accidental removal of DNS records for active servers
- The check is performed with a 1-second timeout

### 9.2. Force Removal

If you need to remove a DNS record for a live host (e.g., during server migration or IP change), you can use the `force_remove` parameter:

```bash
ansible-playbook playbooks/manage_dns.yml \
  -e "operation=remove record_type=a hostname=server01 domain=example.com ip_address=************ force_remove=true"
```

This will override the connectivity check and remove the DNS record even if the host is responding to ping.

### 9.3. When to Use Force Removal

Use the `force_remove` parameter with caution and only in the following scenarios:

1. **Server Migration**: When moving a server to a new IP address
2. **Planned Decommissioning**: When removing DNS records as part of a planned server decommissioning
3. **IP Address Change**: When changing the IP address of a server
4. **False Positives**: When the connectivity check incorrectly identifies a host as alive

In all other cases, the connectivity check provides an important safety measure to prevent accidental removal of DNS records for active servers.

## 10. Special PTR Record Management

The DNS Management system includes special handling for PTR records for specific domains:

### 10.1. Domain-Specific PTR DNS Servers

When managing A records with PTR management enabled (`manage_ptr=true`), the system uses different DNS servers for PTR records based on the domain:

- For domain `ses.shsu.com.sg`, PTR records are created on `shdcvsys22h1.shsu.com.sg`
- For domain `shses.shs.com.sg`, PTR records are created on `sesdcvpsys11.shs.com.sg`
- For all other domains, PTR records are created on the same DNS server as A records

This special handling is automatic and requires no additional configuration from the user. It ensures that PTR records are created on the correct DNS servers for these specific domains.

### 10.2. How It Works

The system determines the appropriate PTR DNS server based on the domain and passes this information to the PowerShell script. The script then uses this server when creating, updating, or removing PTR records.

This approach allows for flexible PTR record management without changing the core logic of the system.

## 11. Report Generation

By default, a PDF report is generated for each operation. The report includes:

- Operation details
- Record information
- Status and result message
- Certificate of successful operation (for successful operations)

Reports are stored in the `reports` directory.

## 12. Email Notifications

The DNS Management system can send email notifications with reports and logs attached. This is useful for providing stakeholders with operation results and for troubleshooting purposes.

### 12.1. Centralized Email Configuration

All email-related configuration has been centralized in the `vars/email_config.yml` file, making it easier to manage and update. This file contains:

1. **SMTP Settings**:
   - Default SMTP server: `smtp.example.com`
   - SMTP port: `25`
   - From address: `<EMAIL>`
   - DC-specific SMTP servers:
     - hdc1: `asmtp.hcloud.healthgrp.com.sg`
     - hdc2: `fsmtp.hcloud.healthgrp.com.sg`

2. **Email Recipients**:
   - Domain-specific recipients:
     - `shses.shs.com.sg`: `<EMAIL>`
     - `ses.shsu.com.sg`: `<EMAIL>`
     - Default (all other domains): `<EMAIL>`
   - Testing mode recipient: `<EMAIL>`
   - BCC recipient: `<EMAIL>`

3. **Email Templates**:
   - Subject templates for different types of emails
   - Placeholders for dynamic content

4. **Email Flags**:
   - Default values for email-related flags

### 12.2. Email Configuration Parameters

To send email notifications with the PDF report attached:

1. Set `email_report=true` to enable sending the report via email
2. (Optional) Set `email_recipient` to override the dynamic email recipient
3. (Optional) Set `email_logs=true` to also send log files as attachments
4. (Optional) Set `testing_mode=true` to send emails only to the testing email recipient

Example command:
```bash
ansible-playbook playbooks/manage_dns.yml -e "operation=verify record_type=a hostname=server01 domain=example.com ticket=12345 email_report=true"
```

Example command with testing mode:
```bash
ansible-playbook playbooks/manage_dns.yml -e "operation=verify record_type=a hostname=server01 domain=example.com ticket=12345 email_report=true testing_mode=true"
```

### 12.3. Dynamic Email Recipients

The system now supports dynamic email recipient selection based on the domain:

- For domains `shses.shs.com.sg` and `ses.shsu.com.sg`, emails are sent to `<EMAIL>`
- For all other domains, emails are sent to `<EMAIL>`
- When `testing_mode=true`, all emails are sent to `<EMAIL>` regardless of domain
- All emails are BCC'd to `<EMAIL>` for monitoring purposes

This ensures that notifications are sent to the appropriate teams based on the domain being processed.

### 12.4. SMTP Server Configuration

The system uses dynamic SMTP server selection based on the data center (DC) location:

- For domains in `hdc1`, the SMTP server `asmtp.hcloud.healthgrp.com.sg` is used
- For domains in `hdc2`, the SMTP server `fsmtp.hcloud.healthgrp.com.sg` is used

This selection is automatic based on the `dc` field in the domain configuration, so you don't need to specify the SMTP server manually.

### 12.5. Consolidated Report Emails

For multi-domain operations, you can send a consolidated report that includes the results of all operations:

1. Set `email_report=true` to enable sending the consolidated report via email
2. The system will automatically generate and send a consolidated report for multi-domain operations

Example command for multi-domain operations with email reporting:
```bash
ansible-playbook playbooks/manage_dns.yml -e "operation=verify record_type=a hostnames=server01,server02 domains=example.com,example.org ticket=12345 email_report=true"
```

The consolidated report includes:
- Summary of all operations
- Status of each operation (success/failure)
- Details for each domain-hostname pair
- Certificate of successful operations

## 13. Logging

The DNS management system includes comprehensive logging at both the Ansible and PowerShell levels:

### 13.1. Log Locations

Logs are stored in the following locations:

- Ansible logs: `logs/ansible/`
- PowerShell logs: `logs/powershell/`
- Progress logs: `logs/progress/`

When running from Ansible Automation Platform (AAP), logs are stored temporarily in the execution environment and can be accessed through one of the following methods:

1. **Email Logs**: Logs can be emailed as attachments to specified recipients
2. **Target Server Storage**: Logs can be uploaded to designated target servers for centralized storage and access

### 13.2. Standardized Log Naming Convention

All logs now follow a standardized naming convention for easier identification and management:

#### Local Log Files

```
<YYYYMMDD>_<TICKET>_<HOSTNAME>_<DOMAIN>_<RECORDTYPE>_<OPERATION>_<LOGTYPE>.log
```

Where:
- `<YYYYMMDD>`: Date in YYYYMMDD format (e.g., 20230501)
- `<TICKET>`: Ticket number for tracking (e.g., INC123456)
- `<HOSTNAME>`: The hostname part of the DNS record
- `<DOMAIN>`: The domain part of the DNS record
- `<RECORDTYPE>`: Type of DNS record (A, CNAME, PTR) in uppercase
- `<OPERATION>`: Operation performed (ADD, REMOVE, UPDATE, VERIFY) in uppercase
- `<LOGTYPE>`: Type of log (ANSIBLE, POWERSHELL, PROGRESS, REPORT)

#### Target Server Log Files

```
<JOBID>_<YYYYMMDD>_<TICKET>_DNS_<LOGTYPE>_<OPERATION>.log
```

Where:
- `<JOBID>`: Ansible Automation Platform job ID
- Other fields are the same as local log files

This standardized naming convention makes it easier to identify logs by type, operation, and content.

### 13.3. Log Levels

You can control the verbosity of PowerShell logs by setting the `log_level` parameter:

```bash
ansible-playbook playbooks/manage_dns.yml \
  -e "operation=add record_type=a hostname=server01 domain=example.com ip_address=************" \
  -e "log_level=Debug"
```

Available log levels (from least to most verbose):
1. **Error**: Only log errors
2. **Warning**: Log errors and warnings
3. **Info**: Log errors, warnings, and informational messages (default)
4. **Debug**: Log everything, including detailed debugging information

### 13.4. Viewing Logs

A log viewing script is provided to easily view and filter logs:

```bash
./scripts/view-logs.sh --help
```

Examples:

```bash
# View all logs
./scripts/view-logs.sh

# List all PowerShell logs without viewing content
./scripts/view-logs.sh --type powershell --list

# View Ansible logs for a specific hostname and operation
./scripts/view-logs.sh --type ansible --hostname server01 --operation add

# View logs from a specific date
./scripts/view-logs.sh --date 2023-05-15

# Follow the latest log updates
./scripts/view-logs.sh --follow
```

### 13.5. Log Storage Options

The DNS management system provides several options for storing and accessing logs when running from Ansible Automation Platform. All log-related configuration is now centralized in the `vars/log_config.yml` file.

#### 13.5.1. Local Log Storage

All logs are stored locally in the following directories, as defined in the centralized `log_config.yml` file:

- Ansible logs: `logs/ansible/`
- PowerShell logs: `logs/powershell/`
- Progress logs: `logs/progress/`
- Archive logs: `logs/archive/`

All logs follow the standardized naming convention described in section 13.2.

#### 13.5.2. Email Logs

Logs can be emailed as attachments to specified recipients. To enable this option, set the following variables in your Job Template:

```yaml
email_logs: true
email_recipient: <EMAIL>
smtp_server: smtp.example.com
smtp_port: 25
smtp_username: smtp_user  # Optional
smtp_password: smtp_password  # Optional
smtp_secure: starttls  # Optional: starttls, ssl, or omit for no encryption
smtp_from: <EMAIL>
```

#### 13.5.3. Upload Logs to Target Server

Logs are automatically stored on target servers based on the environment:

- For staging environments (devhealthgrp.com.sg, healthgrpexts.com.sg, nnstg.local, ses.shsu.com.sg): SHSADMTVDSEC02.ses.shsu.com.sg
- For production environments (all other domains): SHSADMTVPSEC12.shses.shs.com.sg

Logs are stored in the `C:\OE_AAP_LOGS\` directory with the standardized naming convention:
```
<JOBID>_<YYYYMMDD>_<TICKET>_DNS_<LOGTYPE>_<OPERATION>.log
```

Where:
- `<JOBID>`: Ansible Automation Platform job ID
- `<YYYYMMDD>`: Date in YYYYMMDD format
- `<TICKET>`: Ticket number for tracking
- `<LOGTYPE>`: Type of log (ANSIBLE, POWERSHELL)
- `<OPERATION>`: Operation performed in uppercase

This storage method is enabled by default and can be controlled with the following variable:

```yaml
store_logs_target_server: true  # Set to false to disable log storage on target servers
```

#### 13.5.4. Centralized Log Configuration

All log-related settings are now centralized in the `vars/log_config.yml` file, making it easier to manage and update log configuration. This includes:

1. **Log Directories**: Paths to all log directories
2. **Log Format**: Date format, separators, and extensions
3. **Log Types**: Definitions of different log types
4. **Log Levels**: Available log levels
5. **Target Server Paths**: Base paths for target server log storage
6. **Log Flags**: Default values for log-related flags

To modify any log-related settings, edit the `vars/log_config.yml` file instead of modifying individual task files.

## 14. Troubleshooting

### 14.1. Common Issues

1. **Authentication Failures**:
   - Ensure the credentials for the domain are correct
   - Check that the ADMT server has permission to access the DNS server
   - Verify that the double-hop authentication is properly configured

2. **DNS Record Not Found**:
   - Verify that the hostname, domain, and record type are correct
   - Check if the DNS record exists in the DNS server

3. **Permission Denied**:
   - Ensure the service account has sufficient permissions to perform the operation
   - Check that the ADMT server has the necessary rights to manage DNS records

4. **WinRM Connectivity Issues**:
   - Ensure WinRM is properly configured on ADMT servers
   - Check network connectivity between AAP and ADMT servers
   - Verify that the WinRM service is running
   - Ensure the ADMT servers are properly configured in the Ansible inventory with the correct WinRM connection parameters
   - For double-hop authentication issues, verify that the credentials for the second hop are correct
   - If you're still experiencing WinRM issues, check the AAP execution environment for WinRM support

5. **DNS Module Issues**:
   - Ensure the DnsServer PowerShell module is installed on ADMT servers
   - Check that the module is properly loaded in the PowerShell session

6. **Connectivity Check Failures**:
   - If you're intentionally removing DNS records for live hosts, use the `force_remove=true` parameter
   - Check if the host is actually alive or if it's a false positive

### 14.2. Using Logs for Troubleshooting

1. **Check Ansible Logs**: Review the Ansible logs for high-level operation details and any Ansible-level errors

2. **Check PowerShell Logs**: Review the PowerShell logs for detailed information about the DNS operations, including any errors that occurred during script execution

3. **Increase Log Level**: If you're having trouble diagnosing an issue, try running the operation with `log_level=Debug` to get more detailed logs

4. **Check Progress Logs**: For multi-domain operations, check the progress logs to see if any specific domain-hostname pair is failing

## 15. Command Line Usage (For Developers)

> **Note**: This section is primarily for developers and testing purposes. Most users will interact with the system through Ansible Automation Platform (AAP) Job Templates.

### 15.1. Basic Usage

You can run the playbook from the command line:

```bash
ansible-playbook playbooks/manage_dns.yml \
  -e "operation=add record_type=a hostname=server01 domain=example.com ip_address=************ ticket=INC123456"
```

### 15.2. Multi-Domain Operations

```bash
ansible-playbook playbooks/manage_dns.yml \
  -e "operation=add record_type=a hostnames=server01,server02 domains=example.com,example.org ip_addresses=************,************ ticket=INC123456"
```

### 15.3. Examples

#### Verifying a DNS Record

```bash
ansible-playbook playbooks/manage_dns.yml \
  -e "operation=verify record_type=a hostname=server01 domain=example.com ticket=INC123456"
```

#### Adding an A Record

```bash
ansible-playbook playbooks/manage_dns.yml \
  -e "operation=add record_type=a hostname=server01 domain=example.com ip_address=************ ticket=INC123456 description='Web Server'"
```

#### Adding an A Record without PTR Record

```bash
ansible-playbook playbooks/manage_dns.yml \
  -e "operation=add record_type=a hostname=server01 domain=example.com ip_address=************ ticket=INC123456 manage_ptr=false"
```

#### Adding an A Record with Custom TTL

```bash
ansible-playbook playbooks/manage_dns.yml \
  -e "operation=add record_type=a hostname=server01 domain=example.com ip_address=************ ticket=INC123456 ttl=86400"
```

#### Adding a CNAME Record

```bash
ansible-playbook playbooks/manage_dns.yml \
  -e "operation=add record_type=cname hostname=www domain=example.com cname_target=server01.example.com ticket=INC123456 description='Website Alias'"
```

#### Removing a DNS Record

```bash
ansible-playbook playbooks/manage_dns.yml \
  -e "operation=remove record_type=a hostname=server01 domain=example.com ip_address=************ ticket=INC123456"
```

#### Removing a DNS Record for a Live Host

```bash
ansible-playbook playbooks/manage_dns.yml \
  -e "operation=remove record_type=a hostname=server01 domain=example.com ip_address=************ ticket=INC123456 force_remove=true"
```

#### Updating a DNS Record

```bash
ansible-playbook playbooks/manage_dns.yml \
  -e "operation=update record_type=a hostname=server01 domain=example.com ip_address=************ ticket=INC123456"
```

#### Special PTR Management for Specific Domains

For domains `ses.shsu.com.sg` and `shses.shs.com.sg`, PTR records are automatically created on special DNS servers:

```bash
# Adding an A record for ses.shsu.com.sg will create the PTR record on shdcvsys22h1.shsu.com.sg
ansible-playbook playbooks/manage_dns.yml \
  -e "operation=add record_type=a hostname=server01 domain=ses.shsu.com.sg ip_address=************ ticket=INC123456"

# Adding an A record for shses.shs.com.sg will create the PTR record on sesdcvpsys11.shs.com.sg
ansible-playbook playbooks/manage_dns.yml \
  -e "operation=add record_type=a hostname=server01 domain=shses.shs.com.sg ip_address=************ ticket=INC123456"
```

This special handling is automatic and requires no additional configuration.

#### Managing PTR Records Independently

You can manage PTR records independently when needed:

##### Verify a PTR record

```bash
ansible-playbook playbooks/manage_dns.yml \
  -e "operation=verify record_type=ptr hostname=server01 domain=example.com ip_address=************ ticket=INC123456"
```

##### Add a PTR record

```bash
ansible-playbook playbooks/manage_dns.yml \
  -e "operation=add record_type=ptr hostname=server01 domain=example.com ip_address=************ ticket=INC123456"
```

##### Remove a PTR record

```bash
ansible-playbook playbooks/manage_dns.yml \
  -e "operation=remove record_type=ptr hostname=server01 domain=example.com ip_address=************ ticket=INC123456"
```

##### Update a PTR record

```bash
ansible-playbook playbooks/manage_dns.yml \
  -e "operation=update record_type=ptr hostname=server01 domain=example.com ip_address=************ ticket=INC123456"
```
