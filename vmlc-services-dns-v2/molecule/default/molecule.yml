---
# =========================================================================
# Molecule Testing Configuration for DNS Management Automation v2
# =========================================================================
# This configuration defines comprehensive testing scenarios for the DNS
# management solution following OXAF quality assurance standards.
#
# Author: CES Operational Excellence Team
# Contributor: <PERSON> (7409)
# Framework: Operational Excellence Automation Framework (OXAF)
# Version: 2.0
# =========================================================================

dependency:
  name: galaxy
  options:
    requirements-file: requirements.yml
    force: true

driver:
  name: docker

platforms:
  # Windows Server 2019 for DNS server simulation
  - name: dns-server-2019
    image: mcr.microsoft.com/windows/servercore:ltsc2019
    platform: windows
    dockerfile: ../resources/Dockerfile.windows
    volumes:
      - /sys/fs/cgroup:/sys/fs/cgroup:ro
    capabilities:
      - SYS_ADMIN
    command: ""
    pre_build_image: false
    groups:
      - windows
      - dns_servers
      - production

  # Windows Server 2022 for DNS server simulation
  - name: dns-server-2022
    image: mcr.microsoft.com/windows/servercore:ltsc2022
    platform: windows
    dockerfile: ../resources/Dockerfile.windows
    volumes:
      - /sys/fs/cgroup:/sys/fs/cgroup:ro
    capabilities:
      - SYS_ADMIN
    command: ""
    pre_build_image: false
    groups:
      - windows
      - dns_servers
      - staging

  # Linux controller for Ansible execution
  - name: ansible-controller
    image: quay.io/ansible/molecule-runner:latest
    platform: linux
    dockerfile: ../resources/Dockerfile.linux
    volumes:
      - /sys/fs/cgroup:/sys/fs/cgroup:ro
    capabilities:
      - SYS_ADMIN
    command: /sbin/init
    pre_build_image: false
    groups:
      - linux
      - controllers

provisioner:
  name: ansible
  config_options:
    defaults:
      host_key_checking: false
      stdout_callback: yaml
      callback_whitelist: timer, profile_tasks
      forks: 10
      timeout: 30
    ssh_connection:
      pipelining: true
  inventory:
    host_vars:
      dns-server-2019:
        ansible_connection: winrm
        ansible_winrm_transport: ntlm
        ansible_winrm_server_cert_validation: ignore
        ansible_user: Administrator
        ansible_password: "{{ molecule_windows_password | default('Molecule123!') }}"
        dns_domain_config:
          environment: "testing"
          security_zone: "test"
      dns-server-2022:
        ansible_connection: winrm
        ansible_winrm_transport: ntlm
        ansible_winrm_server_cert_validation: ignore
        ansible_user: Administrator
        ansible_password: "{{ molecule_windows_password | default('Molecule123!') }}"
        dns_domain_config:
          environment: "testing"
          security_zone: "test"
      ansible-controller:
        ansible_connection: ssh
        ansible_user: root
    group_vars:
      all:
        # Testing configuration
        testing_mode: true
        dry_run: false
        environment: "testing"

        # Mock CyberArk configuration
        cyberark_configuration:
          enabled: false

        # Simplified logging for testing
        dns_logging:
          enabled: true
          level: "DEBUG"
          directory: "/tmp/dns_logs"

        # Reduced timeouts for faster testing
        timeout_configuration:
          connection_timeout: 10
          operation_timeout: 60
          total_execution_timeout: 300

        # Mock domain mapping for testing
        dns_domain_mapping:
          "test.local":
            primary_servers:
              - "dns-server-2019"
            backup_servers:
              - "dns-server-2022"
            environment: "testing"
            security_zone: "test"

        # Mock credential mapping
        dns_credential_mapping:
          "test.local": "test_user"

verifier:
  name: ansible

scenario:
  name: default
  test_sequence:
    - dependency
    - cleanup
    - destroy
    - syntax
    - create
    - prepare
    - converge
    - idempotence
    - side_effect
    - verify
    - cleanup
    - destroy

lint: |
  set -e
  yamllint .
  ansible-lint .
  flake8
