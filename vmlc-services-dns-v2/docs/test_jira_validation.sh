#!/bin/bash
# =========================================================================
# JIRA Ticket Format Validation Test Script
# =========================================================================
# This script tests the updated JIRA ticket validation rules for DNS 
# Management Automation v2 to ensure all supported ticket types
# (SR, SCR, INC) are properly validated
#
# Framework: Operational Excellence Automation Framework (OXAF)
# Author: CES Operational Excellence Team
# Contributor: <PERSON> (7409)
# Version: 2.0
# =========================================================================

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
CYAN='\033[0;36m'
NC='\033[0m' # No Color

# Test counters
TOTAL_TESTS=0
PASSED_TESTS=0
FAILED_TESTS=0

# Validation pattern (matches the Ansible configuration)
VALIDATION_PATTERN="^(SR|SCR|INC)-[0-9]+$"

# Test arrays
VALID_TICKETS=(
    "SR-123456"
    "SR-000001" 
    "SR-999999"
    "SCR-123456"
    "SCR-000001"
    "SCR-999999" 
    "INC-123456"
    "INC-000001"
    "INC-999999"
    "SR-1"
    "SCR-1"
    "INC-1"
)

INVALID_TICKETS=(
    "INVALID-123456"
    "REQ-123456"
    "CHG-123456"
    "TASK-123456"
    "SR123456"
    "SCR123456" 
    "INC123456"
    "SR-"
    "SCR-"
    "INC-"
    "SR-ABC123"
    "SCR-ABC123"
    "INC-ABC123"
    "sr-123456"
    "scr-123456"
    "inc-123456"
    "SR_123456"
    "SCR_123456"
    "INC_123456"
    ""
    "NOTICKET"
    "123456"
    "SR-123456-EXTRA"
    "PREFIX-SR-123456"
)

function print_header() {
    echo -e "${CYAN}==========================================================================${NC}"
    echo -e "${CYAN}JIRA Ticket Format Validation Test - DNS Management Automation v2${NC}"
    echo -e "${CYAN}==========================================================================${NC}"
    echo -e "${YELLOW}Framework: Operational Excellence Automation Framework (OXAF)${NC}"
    echo -e "${YELLOW}Validation Pattern: $VALIDATION_PATTERN${NC}"
    echo ""
    echo -e "${GREEN}Supported Ticket Types:${NC}"
    echo -e "${GREEN}  ✅ SR  - Service Requests${NC}"
    echo -e "${GREEN}  ✅ SCR - Service Change Requests${NC}"
    echo -e "${GREEN}  ✅ INC - Incidents${NC}"
    echo ""
    echo -e "Starting validation tests..."
    echo -e "${CYAN}==========================================================================${NC}"
    echo ""
}

function test_ticket_format() {
    local ticket="$1"
    local should_be_valid="$2"
    local description="$3"
    
    TOTAL_TESTS=$((TOTAL_TESTS + 1))
    
    if [[ "$ticket" =~ $VALIDATION_PATTERN ]]; then
        is_valid=true
    else
        is_valid=false
    fi
    
    if [[ "$is_valid" == "$should_be_valid" ]]; then
        PASSED_TESTS=$((PASSED_TESTS + 1))
        echo -e "✅ Test $TOTAL_TESTS: $description"
        echo -e "   Ticket: '$ticket' | Expected: $([ "$should_be_valid" == "true" ] && echo "VALID" || echo "INVALID") | Actual: $([ "$is_valid" == "true" ] && echo "VALID" || echo "INVALID") | Result: ${GREEN}PASS${NC}"
    else
        FAILED_TESTS=$((FAILED_TESTS + 1))
        echo -e "❌ Test $TOTAL_TESTS: $description"
        echo -e "   Ticket: '$ticket' | Expected: $([ "$should_be_valid" == "true" ] && echo "VALID" || echo "INVALID") | Actual: $([ "$is_valid" == "true" ] && echo "VALID" || echo "INVALID") | Result: ${RED}FAIL${NC}"
    fi
    echo ""
}

function print_summary() {
    echo -e "${CYAN}==========================================================================${NC}"
    echo -e "${CYAN}TEST SUMMARY${NC}"
    echo -e "${CYAN}==========================================================================${NC}"
    echo ""
    
    local pass_rate=0
    if [ $TOTAL_TESTS -gt 0 ]; then
        pass_rate=$((PASSED_TESTS * 100 / TOTAL_TESTS))
    fi
    
    echo -e "Total Tests: $TOTAL_TESTS"
    echo -e "${GREEN}Passed: $PASSED_TESTS${NC}"
    echo -e "${RED}Failed: $FAILED_TESTS${NC}"
    echo -e "Pass Rate: $pass_rate%"
    echo ""
    
    if [ $FAILED_TESTS -gt 0 ]; then
        echo -e "${RED}Some tests failed! Please review the validation logic.${NC}"
        echo -e "${CYAN}==========================================================================${NC}"
        return 1
    else
        echo -e "${GREEN}All tests passed! JIRA ticket validation is working correctly.${NC}"
        echo -e "${CYAN}==========================================================================${NC}"
        return 0
    fi
}

# Main test execution
print_header

# Test valid ticket formats
echo -e "${GREEN}Testing Valid Ticket Formats:${NC}"
echo -e "${GREEN}=============================${NC}"
echo ""

for ticket in "${VALID_TICKETS[@]}"; do
    ticket_type=$(echo "$ticket" | cut -d'-' -f1)
    test_ticket_format "$ticket" "true" "Valid $ticket_type ticket format"
done

# Test invalid ticket formats
echo -e "${RED}Testing Invalid Ticket Formats:${NC}"
echo -e "${RED}===============================${NC}"
echo ""

for ticket in "${INVALID_TICKETS[@]}"; do
    test_ticket_format "$ticket" "false" "Invalid ticket format (should be rejected)"
done

# Additional edge case tests
echo -e "${YELLOW}Testing Edge Cases:${NC}"
echo -e "${YELLOW}==================${NC}"
echo ""

# Test with very long numbers
test_ticket_format "SR-123456789012345" "true" "Very long ticket number"
test_ticket_format "SCR-999999999999999" "true" "Very long SCR ticket number"
test_ticket_format "INC-000000000000001" "true" "Very long INC ticket number with leading zeros"

# Test with whitespace (these should fail)
test_ticket_format " SR-123456" "false" "Ticket with leading whitespace"
test_ticket_format "SR-123456 " "false" "Ticket with trailing whitespace"
test_ticket_format "SR - 123456" "false" "Ticket with internal whitespace"

print_summary
exit_code=$?

echo ""
echo -e "${BLUE}Test completed. Check the results above.${NC}"
echo -e "${BLUE}This validation ensures DNS automation operations can be properly tracked${NC}"
echo -e "${BLUE}and linked to Service Requests (SR), Service Change Requests (SCR), and Incidents (INC).${NC}"

exit $exit_code
