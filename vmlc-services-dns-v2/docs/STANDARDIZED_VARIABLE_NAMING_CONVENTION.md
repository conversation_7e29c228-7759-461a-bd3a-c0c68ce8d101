# Standardized Variable Naming Convention - DNS Management Automation v2

## Comprehensive Variable Naming Standards for AAP/Ansible Compatibility

**Framework:** Operational Excellence Automation Framework (OXAF)  
**Version:** 2.0  
**Purpose:** Eliminate AAP/Ansible reserved variable conflicts through standardized naming  
**Date:** 2024-01-15  

---

## 🎯 **Overview**

This document establishes a standardized variable naming convention for the DNS Management Automation v2 project and serves as a template for other automation projects. The convention uses the "var_" prefix to avoid conflicts with Ansible reserved variable names, ensuring compatibility across different automation environments.

### **Key Principles:**
- ✅ **Consistency:** All custom variables use the "var_" prefix when they could conflict with Ansible reserved names
- ✅ **Predictability:** Standardized pattern makes the codebase more maintainable
- ✅ **Compatibility:** Eliminates AAP/Ansible reserved variable warnings
- ✅ **Scalability:** Provides a framework for other teams to adopt

---

## 📋 **Standardized Variable Naming Convention**

### **Core Naming Pattern:**
```yaml
# Pattern: var_{descriptive_name}
# Examples:
var_action: "add"                    # DNS operation type
var_environment: "production"        # Target environment
var_user: "automation_service"       # Custom user variable
var_host: "dns-server-01"           # Custom host variable
var_sr_number: "SR-123456"          # Service request number (existing)
```

### **Reserved Variable Mapping:**
| Ansible Reserved | Standardized Variable | Purpose | Example |
|------------------|----------------------|---------|---------|
| `action` | `var_action` | DNS operation type | `verify`, `add`, `remove`, `update` |
| `environment` | `var_environment` | Target environment | `production`, `staging`, `development` |
| `user` | `var_user` | Custom user context | `automation_service`, `dns_admin` |
| `host` | `var_host` | Custom host reference | `dns-server-01`, `target-server` |
| `name` | `var_name` | Custom name field | `dns_record_name`, `zone_name` |
| `state` | `var_state` | Custom state field | `active`, `inactive`, `pending` |

---

## 🔧 **Implementation Across Project Components**

### **1. ✅ Main Playbook Variables (`main.yml`):**
```yaml
# Standardized variable usage in main playbook
vars:
  security_context:
    operation: "{{ var_action | default('verify') }}"
    environment: "{{ var_environment | default('production') }}"
    ticket: "{{ var_sr_number | default('NO_TICKET') }}"

vars_files:
  - group_vars/all/main.yml
  - group_vars/{{ var_environment | default('production') }}/main.yml

pre_tasks:
  - name: "Validate Required Variables"
    assert:
      that:
        - var_action is defined
        - var_action in ['verify', 'add', 'remove', 'update']
      fail_msg: "Required variables missing or invalid. Required: var_action, domain, hostname"
```

### **2. ✅ Group Variables (`group_vars/`):**
```yaml
# group_vars/all/main.yml
var_environment: "{{ env | default('production') }}"

# group_vars/production/main.yml
var_environment: "production"

# group_vars/staging/main.yml
var_environment: "staging"

# group_vars/development/main.yml
var_environment: "development"
```

### **3. ✅ Role Variables (`roles/*/vars/main.yml`):**
```yaml
# Role-specific variables following the convention
dns_role_context:
  operation: "{{ var_action }}"
  environment: "{{ var_environment }}"
  target: "{{ hostname }}.{{ domain }}"
```

### **4. ✅ Task Variables:**
```yaml
# Task-level variable usage
- name: "Execute DNS Operation"
  include_tasks: "{{ var_action }}_operation.yml"
  vars:
    operation_context:
      action: "{{ var_action }}"
      environment: "{{ var_environment }}"
```

---

## 📊 **AAP Integration Standards**

### **✅ AAP Extra Variables Format:**

#### **JSON Format:**
```json
{
  "var_action": "add",
  "var_environment": "production",
  "domain": "healthgrp.com.sg",
  "hostname": "webserver01",
  "ipaddress": "*************",
  "ttl": 3600,
  "var_sr_number": "SR-123456",
  "testing_mode": false
}
```

#### **YAML Format:**
```yaml
var_action: add
var_environment: production
domain: healthgrp.com.sg
hostname: webserver01
ipaddress: *************
ttl: 3600
var_sr_number: SR-123456
testing_mode: false
```

### **✅ AAP Job Template Configuration:**
```yaml
# Job Template Extra Variables
extra_vars:
  var_action: "{{ survey_action }}"
  var_environment: "{{ survey_environment }}"
  domain: "{{ survey_domain }}"
  hostname: "{{ survey_hostname }}"
  var_sr_number: "{{ survey_sr_number }}"

# Survey Configuration
survey_spec:
  - variable: survey_action
    question_name: "DNS Operation"
    type: multiplechoice
    choices:
      - verify
      - add
      - remove
      - update
    
  - variable: survey_environment
    question_name: "Target Environment"
    type: multiplechoice
    choices:
      - production
      - staging
      - development
```

---

## 🔍 **Variable Validation Standards**

### **✅ Required Variable Validation:**
```yaml
# Standard validation pattern for all playbooks
- name: "Validate Standardized Variables"
  assert:
    that:
      - var_action is defined
      - var_action in ['verify', 'add', 'remove', 'update']
      - var_environment is defined
      - var_environment in ['production', 'staging', 'development']
      - domain is defined
      - hostname is defined
    fail_msg: |
      Required variables validation failed. Please ensure:
      - var_action: Must be one of [verify, add, remove, update]
      - var_environment: Must be one of [production, staging, development]
      - domain: Must be defined
      - hostname: Must be defined
    success_msg: "All required variables validated successfully"
```

### **✅ Variable Type Validation:**
```yaml
# Type and format validation
- name: "Validate Variable Types and Formats"
  assert:
    that:
      - var_action is string
      - var_environment is string
      - var_sr_number is match("^(SR|SCR|INC)-[0-9]+$")
      - domain is string
      - hostname is string
    fail_msg: "Variable type or format validation failed"
    success_msg: "Variable types and formats validated successfully"
```

---

## 📚 **Documentation Standards**

### **✅ User Guide Examples:**
All documentation must use the standardized variable names:

```markdown
## DNS Record Addition Example
To add a DNS record, use the following variables:
- **var_action**: "add"
- **var_environment**: "production"
- **domain**: "healthgrp.com.sg"
- **hostname**: "webserver01"
- **ipaddress**: "*************"
- **var_sr_number**: "SR-123456"
```

### **✅ API Reference Examples:**
```bash
# API call example using standardized variables
curl -X POST "https://aap.example.com/api/v2/job_templates/123/launch/" \
  -H "Authorization: Bearer ${AAP_TOKEN}" \
  -H "Content-Type: application/json" \
  -d '{
    "extra_vars": {
      "var_action": "add",
      "var_environment": "production",
      "domain": "healthgrp.com.sg",
      "hostname": "webserver01",
      "ipaddress": "*************",
      "var_sr_number": "SR-123456"
    }
  }'
```

---

## 🧪 **Testing and Validation**

### **✅ Syntax Validation:**
```bash
# Validate playbook syntax with standardized variables
ansible-playbook main.yml --syntax-check

# Test with standardized variables
ansible-playbook main.yml --check \
  -e "var_action=verify" \
  -e "var_environment=staging" \
  -e "domain=test.com" \
  -e "hostname=testserver" \
  -e "var_sr_number=SR-TEST-001"
```

### **✅ Variable Validation Test:**
```yaml
# Test playbook for variable validation
- name: "Test Standardized Variable Naming"
  hosts: localhost
  gather_facts: false
  vars:
    test_variables:
      var_action: "verify"
      var_environment: "development"
      var_sr_number: "SR-TEST-001"
  
  tasks:
    - name: "Validate Test Variables"
      assert:
        that:
          - test_variables.var_action is defined
          - test_variables.var_environment is defined
          - test_variables.var_sr_number is defined
        success_msg: "Standardized variable naming test passed"
```

---

## 🔒 **Security and Compliance**

### **✅ Sensitive Variable Handling:**
```yaml
# Sensitive variables also follow the naming convention
var_password: "{{ vault_dns_password }}"
var_api_key: "{{ vault_api_key }}"
var_token: "{{ vault_auth_token }}"

# Encryption in group_vars
# group_vars/all/vault.yml (encrypted)
vault_dns_password: !vault |
  $ANSIBLE_VAULT;1.1;AES256
  [encrypted_content]
```

### **✅ Audit Trail Variables:**
```yaml
# Audit and compliance variables
audit_context:
  operation: "{{ var_action }}"
  environment: "{{ var_environment }}"
  user: "{{ ansible_user_id }}"
  ticket: "{{ var_sr_number }}"
  timestamp: "{{ ansible_date_time.iso8601 }}"
```

---

## 📈 **Benefits and Impact**

### **✅ Immediate Benefits:**
1. **No AAP Warnings:** Eliminates all reserved variable name warnings
2. **Consistent Pattern:** Predictable naming across all automation projects
3. **Better Maintainability:** Clear variable purpose and scope
4. **Team Adoption:** Provides template for other automation teams

### **✅ Long-term Benefits:**
1. **Scalability:** Easy to extend with new variables
2. **Compatibility:** Future-proof against Ansible updates
3. **Standardization:** Organization-wide automation standards
4. **Training:** Simplified onboarding for new team members

---

## 🎯 **Adoption Guidelines for Other Teams**

### **✅ Implementation Checklist:**
- [ ] **Identify Reserved Variables:** Review Ansible reserved variable list
- [ ] **Apply var_ Prefix:** Use "var_" prefix for potentially conflicting variables
- [ ] **Update Documentation:** Ensure all examples use standardized names
- [ ] **Validate Syntax:** Test playbooks for syntax and execution
- [ ] **Train Team Members:** Educate team on new naming convention

### **✅ Common Reserved Variables to Avoid:**
```yaml
# Ansible reserved variables that should use var_ prefix:
action → var_action
environment → var_environment
user → var_user
host → var_host
name → var_name
state → var_state
mode → var_mode
path → var_path
src → var_src
dest → var_dest
```

---

## 📞 **Support and Maintenance**

### **✅ Governance:**
- **Standard Owner:** CES Operational Excellence Team
- **Review Cycle:** Quarterly review and updates
- **Change Process:** RFC process for standard modifications
- **Training:** Regular training sessions for automation teams

### **✅ Support Contacts:**
- **Standards Questions:** <EMAIL>
- **Implementation Support:** <EMAIL>
- **Training Requests:** <EMAIL>
- **Technical Issues:** <EMAIL>

---

## 🎉 **Implementation Status: COMPLETE**

**The standardized variable naming convention has been successfully implemented:**

1. ✅ **Convention Established:** "var_" prefix for potentially conflicting variables
2. ✅ **Project-wide Implementation:** Applied across all playbooks, roles, and documentation
3. ✅ **AAP Compatibility:** Eliminates all reserved variable warnings
4. ✅ **Documentation Updated:** All examples and guides use standardized names
5. ✅ **Template Created:** Provides framework for other automation projects
6. ✅ **Best Practices:** Follows industry standards for variable naming

**This standardized approach ensures consistent, maintainable, and conflict-free automation across all projects!** 🚀

---

## 📋 **Quick Reference**

### **✅ Standard Variable Examples:**
```yaml
# DNS Management Variables
var_action: "add"                    # DNS operation
var_environment: "production"        # Target environment
var_sr_number: "SR-123456"          # Service request
var_user: "dns_service"             # Service account
var_host: "dns-server-01"           # Target server

# Common Patterns
var_name: "dns_record_name"          # Custom name field
var_state: "active"                  # Custom state field
var_mode: "standard"                 # Custom mode field
var_path: "/custom/path"             # Custom path field
```

### **✅ Validation Pattern:**
```yaml
assert:
  that:
    - var_action is defined
    - var_environment is defined
    - var_sr_number is defined
  fail_msg: "Required standardized variables missing"
```

---

*This standardized variable naming convention is part of the DNS Management Automation v2 solution developed by the CES Operational Excellence Team following Operational Excellence Automation Framework (OXAF) patterns.*
