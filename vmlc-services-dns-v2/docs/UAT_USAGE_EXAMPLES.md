# DNS Management Automation v2 - UAT Script Usage Examples

## Overview

This document provides practical examples of how to use the enhanced UAT Quick Test Script with automatic report generation capabilities.

**Framework:** Operational Excellence Automation Framework (OXAF)  
**Version:** 2.0  

---

## Basic Usage Examples

### Example 1: Simple Test Run (Console Output Only)
```powershell
# Basic test execution with console output
.\UAT_QUICK_TEST_SCRIPT.ps1 -TestDomain "devhealthgrp.com.sg"
```

**Output:**
- Console display of test results
- No report files generated
- Suitable for quick validation

### Example 2: Dry Run Testing (Safe Mode)
```powershell
# Test without making actual DNS changes
.\UAT_QUICK_TEST_SCRIPT.ps1 -TestDomain "devhealthgrp.com.sg" -DryRunOnly
```

**Features:**
- All tests run in dry run mode
- No actual DNS records created
- Safe for production environments

### Example 3: Full Test with HTML Report
```powershell
# Complete test with professional HTML report
.\UAT_QUICK_TEST_SCRIPT.ps1 `
    -TestDomain "devhealthgrp.com.sg" `
    -Test<PERSON><PERSON><PERSON> "<PERSON>" `
    -Tester<PERSON><PERSON> "DNS Administrator" `
    -GenerateReport `
    -OutputPath "C:\UAT_Reports"
```

**Generated Files:**
- `DNS_UAT_Report_YYYYMMDD_HHMM.html` - Professional HTML report
- `DNS_UAT_Results_YYYYMMDD_HHMM.json` - Machine-readable data
- `DNS_UAT_Summary_YYYYMMDD_HHMM.csv` - Spreadsheet-compatible summary

### Example 4: Full Test with PDF Report
```powershell
# Complete test with PDF report generation
.\UAT_QUICK_TEST_SCRIPT.ps1 `
    -TestDomain "devhealthgrp.com.sg" `
    -TesterName "Jane Doe" `
    -TesterRole "System Engineer" `
    -GenerateReport `
    -GeneratePDF `
    -OutputPath "C:\UAT_Reports"
```

**Generated Files:**
- HTML report (as above)
- `DNS_UAT_Report_YYYYMMDD_HHMM.pdf` - PDF version (if tools available)
- JSON and CSV files

---

## Advanced Usage Scenarios

### Scenario 1: Production Environment Testing
```powershell
# Safe testing in production environment
.\UAT_QUICK_TEST_SCRIPT.ps1 `
    -TestDomain "healthgrp.com.sg" `
    -ScriptPath "C:\Scripts\Production\set-dns-v2.ps1" `
    -TesterName "Production Team" `
    -TesterRole "Production Support" `
    -DryRunOnly `
    -GenerateReport `
    -OutputPath "\\shared\UAT_Reports\Production"
```

### Scenario 2: Development Environment Full Testing
```powershell
# Complete testing in development environment
.\UAT_QUICK_TEST_SCRIPT.ps1 `
    -TestDomain "devhealthgrp.com.sg" `
    -ScriptPath "C:\Scripts\Development\set-dns-v2.ps1" `
    -TesterName "Dev Team" `
    -TesterRole "Developer" `
    -GenerateReport `
    -GeneratePDF `
    -OutputPath "C:\Dev_UAT_Reports" `
    -SkipCleanup
```

### Scenario 3: Compliance Audit Testing
```powershell
# Formal testing for compliance audit
.\UAT_QUICK_TEST_SCRIPT.ps1 `
    -TestDomain "auditdomain.healthgrp.com.sg" `
    -TesterName "Audit Team Lead" `
    -TesterRole "Compliance Auditor" `
    -GenerateReport `
    -GeneratePDF `
    -OutputPath "\\compliance\audit_reports\DNS_UAT" `
    -DryRunOnly
```

---

## Report Output Examples

### HTML Report Features
The generated HTML report includes:

#### **Executive Summary Dashboard**
- Total tests executed
- Pass/fail counts
- Overall pass rate with color coding
- Visual status indicators

#### **Environment Information**
- Tester details and timestamps
- System environment (OS, PowerShell version)
- DNS server configuration
- Test configuration parameters

#### **Detailed Test Results**
- Individual test case results
- Execution timestamps
- Detailed output for each test
- Error messages and diagnostics

#### **Professional Formatting**
- Corporate-style layout
- Print-friendly design
- Responsive design for different screen sizes
- Professional color scheme

### Sample HTML Report Structure
```html
DNS Management Automation v2 - User Acceptance Test Report
Framework: Operational Excellence Automation Framework (OXAF)

[SUMMARY CARDS]
Total Tests: 14    Passed: 13    Failed: 1    Pass Rate: 92.9%

[ENVIRONMENT DETAILS]
Tester: John Smith (DNS Administrator)
Test Date: 2024-01-15
Server: HISADMTVPSEC11
OS: Windows Server 2019

[TEST RESULTS TABLE]
Test Case                          | Status | Time     | Details
Script Help Display               | PASS   | 09:15:23 | Help documentation displayed
DNS Record Verification           | PASS   | 09:15:25 | Record found successfully
Zone Not Found Handling          | PASS   | 09:15:27 | Warning handled correctly
...
```

### PDF Report Benefits
- **Professional Presentation:** Suitable for formal documentation
- **Archival Quality:** Long-term storage and compliance
- **Easy Sharing:** Can be emailed or printed
- **Consistent Formatting:** Maintains layout across platforms

---

## Integration with Existing Processes

### ITSM Integration
```powershell
# Generate reports for ITSM ticket attachment
$reportPath = "\\itsm\attachments\SR-123456"
.\UAT_QUICK_TEST_SCRIPT.ps1 `
    -TestDomain "healthgrp.com.sg" `
    -TesterName "ITSM Automation" `
    -TesterRole "Service Request Handler" `
    -GenerateReport `
    -GeneratePDF `
    -OutputPath $reportPath
```

### Automated Testing Pipeline
```powershell
# Scheduled automated testing
$timestamp = Get-Date -Format "yyyyMMdd_HHmm"
$logPath = "\\monitoring\dns_uat\$timestamp"

.\UAT_QUICK_TEST_SCRIPT.ps1 `
    -TestDomain "healthgrp.com.sg" `
    -TesterName "Automated Testing" `
    -TesterRole "System Monitor" `
    -DryRunOnly `
    -GenerateReport `
    -OutputPath $logPath

# Email results to team
if ($LASTEXITCODE -eq 0) {
    Send-MailMessage -To "<EMAIL>" -Subject "DNS UAT - PASS" -Body "See attached report" -Attachments "$logPath\*.html"
} else {
    Send-MailMessage -To "<EMAIL>" -Subject "DNS UAT - FAIL" -Body "UAT failed - immediate attention required" -Attachments "$logPath\*.html"
}
```

---

## Troubleshooting Report Generation

### Common Issues and Solutions

#### Issue: PDF Generation Fails
**Symptoms:** HTML report generated but PDF creation fails
**Solutions:**
1. **Install wkhtmltopdf:**
   ```powershell
   # Download from https://wkhtmltopdf.org/downloads.html
   # Add to PATH environment variable
   ```

2. **Use Browser Method:**
   ```powershell
   # Open HTML report in browser
   # Press Ctrl+P → Save as PDF
   ```

3. **Use Chrome/Edge Headless:**
   ```powershell
   # Ensure Chrome or Edge is installed
   chrome --headless --disable-gpu --print-to-pdf="report.pdf" "report.html"
   ```

#### Issue: Access Denied to Output Path
**Solution:**
```powershell
# Use accessible directory
.\UAT_QUICK_TEST_SCRIPT.ps1 -TestDomain "domain.com" -OutputPath "$env:USERPROFILE\Desktop\UAT_Reports"
```

#### Issue: Report Contains No Data
**Solution:**
```powershell
# Ensure tests actually run
.\UAT_QUICK_TEST_SCRIPT.ps1 -TestDomain "domain.com" -Verbose -GenerateReport
```

---

## Best Practices

### For Testing
1. **Start with Dry Run:** Always test with `-DryRunOnly` first
2. **Use Development Domains:** Test on non-production domains initially
3. **Document Everything:** Use meaningful tester names and roles
4. **Regular Cleanup:** Remove test records after validation

### For Reporting
1. **Consistent Naming:** Use standardized tester names and roles
2. **Centralized Storage:** Store reports in shared, accessible locations
3. **Version Control:** Include timestamps in report names
4. **Backup Reports:** Maintain copies for compliance and audit

### For Automation
1. **Scheduled Testing:** Run automated tests regularly
2. **Alert Integration:** Set up notifications for failed tests
3. **Trend Analysis:** Compare results over time
4. **Documentation:** Maintain test execution logs

---

## Report File Formats

### HTML Report (.html)
- **Purpose:** Interactive viewing and web sharing
- **Features:** Responsive design, detailed tooltips, print-friendly
- **Use Case:** Daily review, team sharing, web publishing

### PDF Report (.pdf)
- **Purpose:** Formal documentation and archival
- **Features:** Fixed layout, professional appearance, universal compatibility
- **Use Case:** Compliance documentation, executive reporting, long-term storage

### JSON Data (.json)
- **Purpose:** Machine-readable data for integration
- **Features:** Structured data, API integration, automated processing
- **Use Case:** ITSM integration, monitoring systems, data analysis

### CSV Summary (.csv)
- **Purpose:** Spreadsheet analysis and reporting
- **Features:** Tabular format, Excel compatibility, data manipulation
- **Use Case:** Trend analysis, reporting dashboards, data export

---

**This enhanced UAT script provides comprehensive testing capabilities with professional reporting suitable for enterprise environments and compliance requirements.**
