# =========================================================================
# DNS Reverse Zone Detection Test Script
# =========================================================================
# This script tests the enhanced intelligent reverse zone detection logic
# for different subnet granularities (3-octet, 2-octet, 1-octet zones).
#
# Author: CES Operational Excellence Team
# Contributor: <PERSON> (7409)
# Framework: Operational Excellence Automation Framework (OXAF)
# Version: 2.0
# =========================================================================

param(
    [Parameter(Mandatory=$false)]
    [string]$DnsServer = "localhost",
    
    [Parameter(Mandatory=$false)]
    [switch]$CreateTestZones,
    
    [Parameter(Mandatory=$false)]
    [switch]$CleanupTestZones,
    
    [Parameter(Mandatory=$false)]
    [switch]$Verbose
)

# Test scenarios for different IP addresses and zone configurations
$TestScenarios = @(
    @{
        Name = "3-Octet Zone Test"
        IpAddress = "*************"
        ExpectedZones = @(
            "1.168.192.in-addr.arpa",    # 3-octet (most specific)
            "168.192.in-addr.arpa",      # 2-octet
            "192.in-addr.arpa"           # 1-octet (least specific)
        )
        ExpectedPtrNames = @{
            "3-octet" = "100"
            "2-octet" = "100.1"
            "1-octet" = "100.1.168"
        }
        Description = "Class C subnet with potential 3-octet zone"
    },
    @{
        Name = "2-Octet Zone Test"
        IpAddress = "*********"
        ExpectedZones = @(
            "5.0.10.in-addr.arpa",       # 3-octet
            "0.10.in-addr.arpa",         # 2-octet
            "10.in-addr.arpa"            # 1-octet
        )
        ExpectedPtrNames = @{
            "3-octet" = "50"
            "2-octet" = "50.5"
            "1-octet" = "50.5.0"
        }
        Description = "Class A subnet with potential 2-octet zone"
    },
    @{
        Name = "1-Octet Zone Test"
        IpAddress = "***********"
        ExpectedZones = @(
            "2.16.172.in-addr.arpa",     # 3-octet
            "16.172.in-addr.arpa",       # 2-octet
            "172.in-addr.arpa"           # 1-octet
        )
        ExpectedPtrNames = @{
            "3-octet" = "25"
            "2-octet" = "25.2"
            "1-octet" = "25.2.16"
        }
        Description = "Class B subnet with potential 1-octet zone"
    }
)

# Test zone creation scenarios
$TestZoneConfigurations = @(
    @{
        Name = "Only 3-Octet Zone"
        ZonesToCreate = @("1.168.192.in-addr.arpa")
        TestIp = "*************"
        ExpectedResult = @{
            Found = $true
            ZoneType = "3-octet"
            PtrName = "100"
        }
    },
    @{
        Name = "Only 2-Octet Zone"
        ZonesToCreate = @("168.192.in-addr.arpa")
        TestIp = "*************"
        ExpectedResult = @{
            Found = $true
            ZoneType = "2-octet"
            PtrName = "100.1"
        }
    },
    @{
        Name = "Only 1-Octet Zone"
        ZonesToCreate = @("192.in-addr.arpa")
        TestIp = "*************"
        ExpectedResult = @{
            Found = $true
            ZoneType = "1-octet"
            PtrName = "100.1.168"
        }
    },
    @{
        Name = "Mixed Zones (Should prefer 3-octet)"
        ZonesToCreate = @("1.168.192.in-addr.arpa", "168.192.in-addr.arpa", "192.in-addr.arpa")
        TestIp = "*************"
        ExpectedResult = @{
            Found = $true
            ZoneType = "3-octet"
            PtrName = "100"
        }
    },
    @{
        Name = "No Zones Available"
        ZonesToCreate = @()
        TestIp = "*************"
        ExpectedResult = @{
            Found = $false
            ZoneType = "none"
            PtrName = $null
        }
    }
)

# =========================================================================
# Helper Functions
# =========================================================================

function Write-TestLog {
    param(
        [string]$Message,
        [string]$Level = "INFO"
    )
    
    $timestamp = Get-Date -Format "yyyy-MM-dd HH:mm:ss"
    $color = switch ($Level) {
        "INFO" { "White" }
        "PASS" { "Green" }
        "FAIL" { "Red" }
        "WARN" { "Yellow" }
        "DEBUG" { "Gray" }
        default { "White" }
    }
    
    Write-Host "[$timestamp] [$Level] $Message" -ForegroundColor $color
}

function Create-TestZone {
    param(
        [string]$ZoneName,
        [string]$DnsServer
    )
    
    try {
        # Check if zone already exists
        $existingZone = Get-DnsServerZone -Name $ZoneName -ComputerName $DnsServer -ErrorAction SilentlyContinue
        
        if (-not $existingZone) {
            Add-DnsServerPrimaryZone -Name $ZoneName -ZoneFile "$ZoneName.dns" -ComputerName $DnsServer
            Write-TestLog "Created test zone: $ZoneName" "INFO"
            return $true
        } else {
            Write-TestLog "Zone already exists: $ZoneName" "DEBUG"
            return $true
        }
    } catch {
        Write-TestLog "Failed to create zone $ZoneName`: $($_.Exception.Message)" "WARN"
        return $false
    }
}

function Remove-TestZone {
    param(
        [string]$ZoneName,
        [string]$DnsServer
    )
    
    try {
        $existingZone = Get-DnsServerZone -Name $ZoneName -ComputerName $DnsServer -ErrorAction SilentlyContinue
        
        if ($existingZone) {
            Remove-DnsServerZone -Name $ZoneName -Force -ComputerName $DnsServer
            Write-TestLog "Removed test zone: $ZoneName" "INFO"
        }
    } catch {
        Write-TestLog "Failed to remove zone $ZoneName`: $($_.Exception.Message)" "WARN"
    }
}

function Test-ReverseZoneDetection {
    param(
        [string]$IpAddress,
        [string]$DnsServer,
        [hashtable]$ExpectedResult = $null
    )
    
    Write-TestLog "Testing reverse zone detection for IP: $IpAddress" "INFO"
    
    # Import the enhanced function (assuming it's in the main script)
    $scriptPath = Join-Path (Split-Path $PSScriptRoot -Parent) "roles\dns_operations\files\set-dns-v2.ps1"
    
    if (Test-Path $scriptPath) {
        # Extract just the Get-ReverseDomain function for testing
        $scriptContent = Get-Content $scriptPath -Raw
        
        # Create a test version of the function
        $testFunction = @"
function Get-ReverseDomain {
    param(
        [Parameter(Mandatory=`$true)]
        [string]`$IpAddress,
        
        [Parameter(Mandatory=`$false)]
        [string]`$DnsServer = "localhost"
    )

    `$octets = `$IpAddress.Split('.')
    if (`$octets.Length -ne 4) {
        throw "Invalid IP address format: `$IpAddress"
    }
    
    Write-Host "Starting intelligent reverse zone detection for IP: `$IpAddress" -ForegroundColor Cyan
    
    # Define possible reverse zone configurations in order of preference
    `$reverseZoneOptions = @(
        @{
            Name = "`$(`$octets[2]).`$(`$octets[1]).`$(`$octets[0]).in-addr.arpa"
            Type = "3-octet"
            Subnet = "/24"
            Description = "Class C subnet (most specific)"
            PtrNameCalculation = { param(`$ip) `$ip.Split('.')[3] }
        },
        @{
            Name = "`$(`$octets[1]).`$(`$octets[0]).in-addr.arpa"
            Type = "2-octet"
            Subnet = "/16"
            Description = "Class B subnet"
            PtrNameCalculation = { param(`$ip) `$octets = `$ip.Split('.'); "`$(`$octets[3]).`$(`$octets[2])" }
        },
        @{
            Name = "`$(`$octets[0]).in-addr.arpa"
            Type = "1-octet"
            Subnet = "/8"
            Description = "Class A subnet (least specific)"
            PtrNameCalculation = { param(`$ip) `$octets = `$ip.Split('.'); "`$(`$octets[3]).`$(`$octets[2]).`$(`$octets[1])" }
        }
    )
    
    # Test each reverse zone option in order of preference
    foreach (`$zoneOption in `$reverseZoneOptions) {
        Write-Host "Testing reverse zone: `$(`$zoneOption.Name) (`$(`$zoneOption.Description))" -ForegroundColor Yellow
        
        try {
            # Check if the reverse zone exists
            `$zone = Get-DnsServerZone -Name `$zoneOption.Name -ComputerName `$DnsServer -ErrorAction Stop
            
            if (`$zone) {
                Write-Host "Found reverse zone: `$(`$zoneOption.Name) (Type: `$(`$zoneOption.Type))" -ForegroundColor Green
                
                # Calculate the correct PTR record name for this zone type
                `$ptrRecordName = & `$zoneOption.PtrNameCalculation `$IpAddress
                
                # Return zone information with metadata
                return @{
                    ZoneName = `$zoneOption.Name
                    ZoneType = `$zoneOption.Type
                    SubnetMask = `$zoneOption.Subnet
                    Description = `$zoneOption.Description
                    PtrRecordName = `$ptrRecordName
                    Found = `$true
                }
            }
        } catch {
            Write-Host "Reverse zone not found: `$(`$zoneOption.Name)" -ForegroundColor Gray
            continue
        }
    }
    
    # If no reverse zones found, return error information
    Write-Host "No reverse DNS zones found for IP `$IpAddress" -ForegroundColor Red
    
    return @{
        ZoneName = `$null
        ZoneType = "none"
        SubnetMask = "unknown"
        Description = "No reverse zone found"
        PtrRecordName = `$null
        Found = `$false
        TestedZones = `$reverseZoneOptions | ForEach-Object { `$_.Name }
    }
}
"@
        
        # Execute the test function
        Invoke-Expression $testFunction
        
        # Call the function
        $result = Get-ReverseDomain -IpAddress $IpAddress -DnsServer $DnsServer
        
        # Validate results if expected result provided
        if ($ExpectedResult) {
            $testPassed = $true
            
            if ($result.Found -ne $ExpectedResult.Found) {
                Write-TestLog "FAIL: Expected Found=$($ExpectedResult.Found), got Found=$($result.Found)" "FAIL"
                $testPassed = $false
            }
            
            if ($ExpectedResult.Found -and $result.ZoneType -ne $ExpectedResult.ZoneType) {
                Write-TestLog "FAIL: Expected ZoneType=$($ExpectedResult.ZoneType), got ZoneType=$($result.ZoneType)" "FAIL"
                $testPassed = $false
            }
            
            if ($ExpectedResult.Found -and $result.PtrRecordName -ne $ExpectedResult.PtrName) {
                Write-TestLog "FAIL: Expected PtrName=$($ExpectedResult.PtrName), got PtrName=$($result.PtrRecordName)" "FAIL"
                $testPassed = $false
            }
            
            if ($testPassed) {
                Write-TestLog "PASS: Reverse zone detection working correctly" "PASS"
            }
            
            return @{
                Passed = $testPassed
                Result = $result
            }
        }
        
        return @{
            Passed = $true
            Result = $result
        }
        
    } else {
        Write-TestLog "Cannot find main DNS script at: $scriptPath" "FAIL"
        return @{
            Passed = $false
            Result = $null
        }
    }
}

# =========================================================================
# Main Test Execution
# =========================================================================

Write-TestLog "Starting DNS Reverse Zone Detection Tests" "INFO"
Write-TestLog "Framework: Operational Excellence Automation Framework (OXAF)" "INFO"
Write-TestLog "DNS Server: $DnsServer" "INFO"

$testResults = @{
    TotalTests = 0
    PassedTests = 0
    FailedTests = 0
    TestDetails = @()
}

# Test each zone configuration scenario
foreach ($config in $TestZoneConfigurations) {
    Write-TestLog "="*60 "INFO"
    Write-TestLog "Testing Configuration: $($config.Name)" "INFO"
    Write-TestLog "Description: $($config.Description)" "INFO"
    Write-TestLog "="*60 "INFO"
    
    # Create test zones if requested
    if ($CreateTestZones) {
        foreach ($zoneName in $config.ZonesToCreate) {
            Create-TestZone -ZoneName $zoneName -DnsServer $DnsServer
        }
    }
    
    # Run the test
    $testResult = Test-ReverseZoneDetection -IpAddress $config.TestIp -DnsServer $DnsServer -ExpectedResult $config.ExpectedResult
    
    $testResults.TotalTests++
    if ($testResult.Passed) {
        $testResults.PassedTests++
    } else {
        $testResults.FailedTests++
    }
    
    $testResults.TestDetails += @{
        ConfigurationName = $config.Name
        IpAddress = $config.TestIp
        Passed = $testResult.Passed
        Result = $testResult.Result
        Expected = $config.ExpectedResult
    }
    
    # Cleanup test zones if requested
    if ($CleanupTestZones) {
        foreach ($zoneName in $config.ZonesToCreate) {
            Remove-TestZone -ZoneName $zoneName -DnsServer $DnsServer
        }
    }
    
    Start-Sleep -Seconds 2  # Brief pause between tests
}

# =========================================================================
# Test Results Summary
# =========================================================================

Write-TestLog "="*60 "INFO"
Write-TestLog "TEST RESULTS SUMMARY" "INFO"
Write-TestLog "="*60 "INFO"

Write-TestLog "Total Tests: $($testResults.TotalTests)" "INFO"
Write-TestLog "Passed: $($testResults.PassedTests)" "PASS"
Write-TestLog "Failed: $($testResults.FailedTests)" $(if ($testResults.FailedTests -eq 0) { "PASS" } else { "FAIL" })

$passRate = if ($testResults.TotalTests -gt 0) { 
    [math]::Round(($testResults.PassedTests / $testResults.TotalTests) * 100, 2) 
} else { 0 }

Write-TestLog "Pass Rate: $passRate%" $(if ($passRate -ge 80) { "PASS" } else { "FAIL" })

if ($testResults.FailedTests -gt 0) {
    Write-TestLog "Failed Test Details:" "FAIL"
    $testResults.TestDetails | Where-Object { -not $_.Passed } | ForEach-Object {
        Write-TestLog "  - $($_.ConfigurationName): $($_.IpAddress)" "FAIL"
    }
}

Write-TestLog "Reverse Zone Detection Testing Complete!" "INFO"

# Return appropriate exit code
exit $(if ($testResults.FailedTests -eq 0) { 0 } else { 1 })
