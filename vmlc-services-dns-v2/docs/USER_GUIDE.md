# DNS Management Automation v2 - User Guide

## Overview

This comprehensive user guide provides detailed instructions for using the DNS Management Automation v2 solution, which follows OXAF infrastructure automation patterns and implements a six-phase lifecycle framework for enterprise DNS operations.

**Author:** CES Operational Excellence Team
**Contributor:** <PERSON> (7409)
**Framework:** Operational Excellence Automation Framework (OXAF)
**Version:** 2.0

## Table of Contents

1. [Getting Started](#getting-started)
2. [Supported Operations](#supported-operations)
3. [AAP Job Templates](#aap-job-templates)
4. [Variable Configuration](#variable-configuration)
5. [Execution Examples](#execution-examples)
6. [Monitoring and Logging](#monitoring-and-logging)
7. [Error Handling and Troubleshooting](#error-handling-and-troubleshooting)
8. [Security and Compliance](#security-and-compliance)

## Getting Started

### Prerequisites

Before using the DNS Management Automation v2 solution, ensure you have:

1. **Access Requirements:**
   - Ansible Automation Platform (AAP) 2.x access
   - Appropriate RBAC permissions for DNS operations
   - Network connectivity to target DNS servers

2. **Credential Requirements:**
   - CyberArk access for DNS service account credentials
   - Valid service accounts for each DNS domain
   - Proper authentication tokens for AAP

3. **Knowledge Requirements:**
   - Basic understanding of DNS concepts
   - Familiarity with AAP Job Templates
   - Understanding of organizational change management processes

### Quick Start Checklist

- [ ] Verify AAP access and permissions
- [ ] Confirm CyberArk credential availability
- [ ] Review target domain and hostname requirements
- [ ] Understand the six-phase lifecycle process
- [ ] Identify appropriate Job Template for your operation

## Supported Operations

### Core DNS Operations

#### 1. Verify Operation
**Purpose:** Check the existence and configuration of DNS records
**Use Case:** Validation, troubleshooting, compliance checking
**Required Variables:** `action`, `domain`, `hostname`
**Optional Variables:** `var_sr_number`, `testing_mode`

#### 2. Add Operation
**Purpose:** Create new DNS A records and corresponding PTR records
**Use Case:** New server deployment, service provisioning
**Required Variables:** `action`, `domain`, `hostname`, `ipaddress`
**Optional Variables:** `ttl`, `var_sr_number`, `testing_mode`

#### 3. Remove Operation
**Purpose:** Delete DNS records with proper validation and rollback capability
**Use Case:** Server decommissioning, service retirement
**Required Variables:** `action`, `domain`, `hostname`
**Optional Variables:** `var_sr_number`, `testing_mode`

#### 4. Update Operation
**Purpose:** Modify existing DNS records with change tracking
**Use Case:** IP address changes, TTL modifications
**Required Variables:** `action`, `domain`, `hostname`, `ipaddress`
**Optional Variables:** `ttl`, `var_sr_number`, `testing_mode`

#### 5. Sync Operation
**Purpose:** Synchronize A and PTR records, especially after DNS engineers create zones
**Use Case:** Post-zone creation synchronization, missing PTR record creation, record consistency checks
**Required Variables:** `action`, `domain`, `hostname`
**Optional Variables:** `ipaddress`, `ttl`, `var_sr_number`, `testing_mode`
**Special Behavior:**
- If A record exists but PTR is missing, creates only the PTR record
- If IP address provided and differs from existing A record, updates both records
- If no IP provided, uses existing A record IP for PTR creation
- Intelligent analysis of what synchronization actions are needed

### Advanced Features

#### Bulk Operations
- Process multiple DNS records in a single execution
- Batch processing with configurable batch sizes
- Progress tracking and partial failure handling

#### Rollback Capabilities
- Automatic rollback on operation failure
- Manual rollback procedures available
- State restoration with validation

#### Change Tracking
- Complete audit trail of all DNS modifications
- Before and after state capture
- Compliance reporting and documentation

## AAP Job Templates

### Production Job Templates

#### DNS Record Verification - Production
```yaml
Name: DNS Record Verification - Production
Job Type: Run
Inventory: DNS Servers - Production
Project: DNS Management v2
Playbook: main.yml
Credentials:
  - CyberArk Credential Provider
  - DNS Service Accounts
Variables:
  var_environment: production
  var_action: verify
Survey Enabled: Yes
```

#### DNS Record Addition - Production
```yaml
Name: DNS Record Addition - Production
Job Type: Run
Inventory: DNS Servers - Production
Project: DNS Management v2
Playbook: main.yml
Credentials:
  - CyberArk Credential Provider
  - DNS Service Accounts
Variables:
  var_environment: production
  var_action: add
Survey Enabled: Yes
```

#### DNS Record Removal - Production
```yaml
Name: DNS Record Removal - Production
Job Type: Run
Inventory: DNS Servers - Production
Project: DNS Management v2
Playbook: main.yml
Credentials:
  - CyberArk Credential Provider
  - DNS Service Accounts
Variables:
  var_environment: production
  var_action: remove
Survey Enabled: Yes
```

#### DNS Record Synchronization - Production
```yaml
Name: DNS Record Synchronization - Production
Job Type: Run
Inventory: DNS Servers - Production
Project: DNS Management v2
Playbook: main.yml
Credentials:
  - CyberArk Credential Provider
  - DNS Service Accounts
Variables:
  var_environment: production
  var_action: sync
Survey Enabled: Yes
Description: Synchronize A and PTR records after zone creation or for consistency checks
```

### Survey Specifications

#### Common Survey Fields
```yaml
- variable: domain
  question: "DNS Domain"
  type: multiplechoice
  choices:
    - devhealthgrp.com.sg
    - healthgrpexts.com.sg
    - nnstg.local
    - ses.shsu.com.sg
    - shses.shs.com.sg
    - nhg.local
    - aic.local
    - iltc.healthgrp.com.sg
    - healthgrp.com.sg
  required: true

- variable: hostname
  question: "Hostname (without domain)"
  type: text
  required: true

- variable: var_sr_number
  question: "Service Request Number"
  type: text
  required: true
  default: "SR-XXXXXX"

- variable: testing_mode
  question: "Enable Testing Mode"
  type: boolean
  required: false
  default: false
```

#### Add Operation Additional Fields
```yaml
- variable: ipaddress
  question: "IP Address"
  type: text
  required: true

- variable: ttl
  question: "TTL (Time To Live) in seconds"
  type: integer
  required: false
  default: 3600
  min: 60
  max: 86400
```

## Variable Configuration

### Required Variables

| Variable | Description | Example | Operations |
|----------|-------------|---------|------------|
| `action` | DNS operation to perform | `verify`, `add`, `remove`, `update` | All |
| `domain` | Target DNS domain | `healthgrp.com.sg` | All |
| `hostname` | Hostname without domain | `server01` | All |
| `ipaddress` | IP address for DNS record | `*************` | Add, Update |

### Optional Variables

| Variable | Description | Default | Example |
|----------|-------------|---------|---------|
| `ttl` | Time to live in seconds | `3600` | `7200` |
| `var_sr_number` | Service request number | `NOTICKET` | `SR-123456` |
| `testing_mode` | Enable testing mode | `false` | `true` |
| `dry_run` | Perform dry run only | `false` | `true` |
| `skip_rollback` | Disable rollback | `false` | `true` |
| `verbose` | Enable verbose logging | `false` | `true` |

### Environment Variables

| Variable | Description | Default | Options |
|----------|-------------|---------|---------|
| `environment` | Target environment | `production` | `production`, `staging`, `development` |
| `log_level` | Logging level | `INFO` | `DEBUG`, `INFO`, `WARN`, `ERROR` |

## Execution Examples

### Example 1: Verify DNS Record

**Scenario:** Check if a DNS record exists for server01.healthgrp.com.sg

**AAP Job Template:** DNS Record Verification - Production

**Survey Responses:**
```yaml
Domain: healthgrp.com.sg
Hostname: server01
Service Request Number: SR-123456
Enable Testing Mode: false
```

**Expected Output:**
```
DNS Record Verification Result: FOUND
A Record: server01.healthgrp.com.sg -> *************
TTL: 3600 seconds
PTR Record: true
```

### Example 2: Add DNS Record

**Scenario:** Create a new DNS record for server02.healthgrp.com.sg

**AAP Job Template:** DNS Record Addition - Production

**Survey Responses:**
```yaml
Domain: healthgrp.com.sg
Hostname: server02
IP Address: *************
TTL: 7200
Service Request Number: SR-123457
Enable Testing Mode: false
```

**Expected Output:**
```
DNS Record Addition Result: SUCCESS
Added A Record: server02.healthgrp.com.sg -> *************
Added PTR Record: *************.in-addr.arpa -> server02.healthgrp.com.sg
```

### Example 3: Remove DNS Record

**Scenario:** Remove DNS record for decommissioned server03.healthgrp.com.sg

**AAP Job Template:** DNS Record Removal - Production

**Survey Responses:**
```yaml
Domain: healthgrp.com.sg
Hostname: server03
Service Request Number: SR-123458
Enable Testing Mode: false
```

**Expected Output:**
```
DNS Record Removal Result: SUCCESS
Removed A Record: server03.healthgrp.com.sg
Removed PTR Record: *************.in-addr.arpa
Rollback data saved for recovery if needed
```

### Example 4: Zone Not Found Warning

**Scenario:** Attempt to verify DNS record in a non-existent zone

**Survey Responses:**
```yaml
Domain: newdomain.healthgrp.com.sg
Hostname: server01
Service Request Number: SR-123459
Enable Testing Mode: false
```

**Expected Output:**
```
DNS Record Verification Result: ZONE NOT FOUND
DNS Zone: newdomain.healthgrp.com.sg does not exist
Action Required: DNS engineers need to create zone 'newdomain.healthgrp.com.sg' manually

ZONE_NOT_FOUND_WARNING: newdomain.healthgrp.com.sg
DNS engineers need to create zone 'newdomain.healthgrp.com.sg' manually before DNS operations can proceed

Email notification sent to DNS engineers with zone creation instructions.
```

### Example 5: Testing Mode Execution

**Scenario:** Test DNS record addition without making actual changes

**Survey Responses:**
```yaml
Domain: healthgrp.com.sg
Hostname: testserver
IP Address: *************
Enable Testing Mode: true
```

**Expected Output:**
```
DRY RUN: Would add DNS A record: testserver.healthgrp.com.sg -> *************
DRY RUN: Would add DNS PTR record: *************.in-addr.arpa -> testserver.healthgrp.com.sg
No actual changes made - testing mode enabled
```

### Example 6: Post-Zone Creation Synchronization

**Scenario:** DNS engineers created a new zone after receiving a "zone not found" notification. Now we need to synchronize the A record that was manually created and ensure PTR record exists.

**Background:**
1. Initial automation attempt failed with "Zone not found"
2. DNS engineers received notification and created zone 'newapp.healthgrp.com.sg'
3. DNS engineers manually created A record: newapp01.newapp.healthgrp.com.sg -> *************
4. PTR record was not created manually
5. Automation team runs sync operation to complete the configuration

**AAP Job Template:** DNS Record Synchronization - Production

**Survey Responses:**
```yaml
Domain: newapp.healthgrp.com.sg
Hostname: newapp01
IP Address: (leave blank to use existing A record IP)lidation, troubleshooting, com
```
DNS Record Sync Analysis:
A Record Status: EXISTS
PTR Record Status: MISSING
Sync Actions Required: 1

DNS Record Sync Result: SYNCHRONIZATION NEEDED
Sync: Created PTR Record: *************.in-addr.arpa -> newapp01.newapp.healthgrp.com.sg
DNS Record Sync Result: COMPLETED
Executed 1 synchronization action(s)
```

### Example 7: IP Address Mismatch Synchronization

**Scenario:** A record exists with wrong IP address, sync operation to correct both A and PTR records.

**Survey Responses:**
```yaml
Domain: healthgrp.com.sg
Hostname: server05
IP Address: *************  # Correct IP address
Service Request Number: SR-123461
Enable Testing Mode: false
```

**Expected Output:**
```
DNS Record Sync Analysis:
A Record Status: EXISTS
PTR Record Status: EXISTS
Sync Actions Required: 2

DNS Record Sync Result: SYNCHRONIZATION NEEDED
Sync: Updated A Record: server05.healthgrp.com.sg -> *************
Sync: Updated PTR Record: *************.in-addr.arpa -> server05.healthgrp.com.sg
DNS Record Sync Result: COMPLETED
Executed 2 synchronization action(s)
```

### Example 8: No Synchronization Needed

**Scenario:** Both A and PTR records are properly synchronized.

**Survey Responses:**
```yaml
Domain: healthgrp.com.sg
Hostname: server06
Service Request Number: SR-123462
Enable Testing Mode: false
```

**Expected Output:**
```
DNS Record Sync Analysis:
A Record Status: EXISTS
PTR Record Status: EXISTS
Sync Actions Required: 0

DNS Record Sync Result: NO ACTION NEEDED
Both A and PTR records are properly synchronized
```

## Monitoring and Logging

### Six-Phase Lifecycle Monitoring

The solution provides comprehensive monitoring across all six phases:

1. **Configuration Phase:** Environment setup and validation
2. **Loading Phase:** Credential retrieval and server discovery
3. **Execution Phase:** Core DNS operations
4. **Error Handling Phase:** Rollback and recovery (if needed)
5. **Reporting Phase:** Status updates and audit logging
6. **Cleanup Phase:** Resource cleanup and session termination

### Log File Locations

#### Primary Log Files
- **Ansible Logs:** `C:\OE_AAP_LOGS\<JOB_ID>_<DATE>_<TICKET>_DNS_ANSIBLE.log`
- **PowerShell Logs:** `C:\OE_AAP_LOGS\DNS_SCRIPT_<JOB_ID>_<DATE>.log`
- **Audit Logs:** Integrated within primary log files with `[AUDIT]` tags

#### Log File Naming Convention
```
Format: <AAP_JOB_ID>_<DDMMYYYY>_<TICKET>_<OPERATION>_<TYPE>.log
Example: 12345_15012024_SR123456_DNS_ANSIBLE.log
```

### Real-Time Monitoring

#### AAP Job Monitoring
- Real-time job execution status in AAP interface
- Phase-by-phase progress tracking
- Error detection and alerting
- Performance metrics collection

#### Log Monitoring
- Structured logging with JSON format for automation
- Real-time log streaming to SIEM systems
- Automated alerting on error conditions
- Performance threshold monitoring

### Performance Metrics

#### Execution Metrics
- Total execution time per phase
- DNS operation response times
- Network connectivity metrics
- Resource utilization tracking

#### Success Metrics
- Operation success/failure rates
- Rollback frequency and success rates
- Validation accuracy metrics
- Compliance adherence rates

## Error Handling and Troubleshooting

### Automatic Error Handling

#### Error Classification
1. **Critical Errors:** Require immediate attention and automatic rollback
   - Authentication failures
   - DNS server unreachable
   - Invalid credentials

2. **Recoverable Errors:** Automatic retry with exponential backoff
   - Temporary network issues
   - DNS record conflicts
   - Timeout errors

3. **Warning Conditions:** Logged but don't stop execution
   - Record already exists
   - Record not found
   - PTR record missing
   - Zone not found (requires manual DNS zone configuration)

#### Automatic Rollback

The solution includes comprehensive rollback capabilities:

- **Automatic Trigger:** Activated on critical errors or operation failures
- **State Restoration:** Returns DNS configuration to pre-execution state
- **Validation:** Confirms successful rollback completion
- **Audit Trail:** Complete logging of rollback operations

### Manual Troubleshooting

#### Common Issues and Solutions

**Issue:** Authentication Failure
```
Error: Failed to retrieve valid credentials from CyberArk
Solution:
1. Verify CyberArk service account exists
2. Check credential safe permissions
3. Validate service account password rotation
4. Contact security team if needed
```

**Issue:** DNS Server Unreachable
```
Error: No primary DNS servers are reachable
Solution:
1. Check network connectivity to DNS servers
2. Verify DNS server service status
3. Check firewall rules for WinRM ports (5985/5986)
4. Validate DNS server mapping configuration
```

**Issue:** Zone Not Found
```
Error: Zone access failed: Zone not found
Classification: WARNING (execution continues)
Solution:
1. Verify domain name spelling and case
2. Check DNS zone exists on target server
3. Contact DNS engineers to create zone manually if needed
4. Validate service account permissions to zone
5. Confirm zone is not in read-only mode

Note: This condition is logged as a warning and reported to DNS engineers
for manual zone configuration. Execution continues with other operations.
```

#### Diagnostic Commands

**Check DNS Server Connectivity:**
```powershell
Test-NetConnection -ComputerName <DNS_SERVER> -Port 5985
```

**Verify DNS Zone Access:**
```powershell
Get-DnsServerZone -Name <DOMAIN> -ComputerName <DNS_SERVER>
```

**Check Service Account Permissions:**
```powershell
Get-Acl "AD:\CN=<DOMAIN>,CN=MicrosoftDNS,DC=DomainDnsZones,DC=<DOMAIN>,DC=<TLD>"
```

### Escalation Procedures

#### Level 1: Automatic Resolution
- Automatic retry with exponential backoff
- Automatic rollback on critical failures
- Self-healing for temporary issues

#### Level 2: Operational Team
- Manual intervention for persistent issues
- Log analysis and troubleshooting
- Coordination with infrastructure teams

#### Level 3: Engineering Team
- Code-level troubleshooting and fixes
- Infrastructure architecture changes
- Security and compliance reviews

## Security and Compliance

### Security Controls

#### Credential Management
- **CyberArk Integration:** Secure credential storage and retrieval
- **Vault Encryption:** Sensitive data protection with Ansible Vault
- **Access Controls:** Role-based access control and authorization
- **Audit Logging:** Complete security event logging and monitoring

#### Data Protection
- **Encryption in Transit:** All communications encrypted using TLS
- **Encryption at Rest:** Log files and sensitive data encrypted
- **Data Classification:** Internal data classification applied
- **Retention Policies:** 7-year retention for compliance requirements

### Compliance Features

#### Audit Trail
- Complete logging of all DNS operations
- User attribution and timestamp tracking
- Change tracking with before/after states
- Compliance reporting capabilities

#### Change Management
- Integration with ITSM processes
- Service request tracking and updates
- Approval workflows (configurable)
- Change documentation and validation

#### Regulatory Compliance
- **HIPAA:** Healthcare data protection compliance
- **ISO 27001:** Information security management
- **SOX:** Financial reporting controls (configurable)
- **PCI DSS:** Payment card industry standards (configurable)

### Security Best Practices

#### Access Management
1. Use service accounts with minimal required permissions
2. Implement regular credential rotation
3. Monitor and audit all access attempts
4. Use multi-factor authentication where possible

#### Network Security
1. Restrict network access to DNS servers
2. Use encrypted communication channels
3. Implement network segmentation
4. Monitor network traffic for anomalies

#### Operational Security
1. Follow principle of least privilege
2. Implement segregation of duties
3. Regular security assessments and reviews
4. Incident response procedures

---

## Support and Resources

### Documentation
- [Installation Guide](INSTALLATION.md)
- [Configuration Guide](CONFIGURATION.md)
- [API Reference](API_REFERENCE.md)
- [Troubleshooting Guide](TROUBLESHOOTING.md)

### Support Channels
- **Technical Support:** CES Operational Excellence Team
- **Emergency Support:** 24/7 on-call support available
- **Training:** Available workshops and documentation
- **Community:** Internal knowledge sharing platform

### Version Information
- **Current Version:** 2.0
- **Release Date:** 2024
- **Next Planned Release:** Q2 2024
- **Support Lifecycle:** 3 years from release date

---

---

## AAP Extra Variables - JSON Format Examples

### Overview
When executing DNS operations through Ansible Automation Platform (AAP), you can provide variables in JSON format through the "Extra Variables" field. This section provides comprehensive examples for all supported DNS operations.

### JSON Format Structure
```json
{
  "var_action": "operation_type",
  "domain": "target_domain",
  "hostname": "target_hostname",
  "ipaddress": "target_ip_address",
  "ttl": time_to_live_seconds,
  "var_sr_number": "service_request_number",
  "testing_mode": true/false,
  "var_environment": "target_environment"
}
```

---

## DNS Operation Examples - AAP JSON Format

### 1. DNS Record Verification

#### Basic Verification
```json
{
  "var_action": "verify",
  "domain": "healthgrp.com.sg",
  "hostname": "webserver01",
  "var_sr_number": "SR-123456",
  "testing_mode": false
}
```

#### Verification with Environment Override
```json
{
  "var_action": "verify",
  "domain": "devhealthgrp.com.sg",
  "hostname": "testserver01",
  "var_environment": "development",
  "var_sr_number": "SR-123457",
  "testing_mode": true
}
```

#### Production Verification
```json
{
  "var_action": "verify",
  "domain": "healthgrp.com.sg",
  "hostname": "prodserver01",
  "var_environment": "production",
  "var_sr_number": "SR-123458",
  "testing_mode": false
}
```

### 2. DNS Record Addition

#### Standard A and PTR Record Creation
```json
{
  "var_action": "add",
  "domain": "healthgrp.com.sg",
  "hostname": "newserver01",
  "ipaddress": "*************",
  "ttl": 3600,
  "var_sr_number": "SR-123459",
  "testing_mode": false
}
```

#### Development Environment Addition
```json
{
  "var_action": "add",
  "domain": "devhealthgrp.com.sg",
  "hostname": "devapp01",
  "ipaddress": "*********",
  "ttl": 300,
  "var_environment": "development",
  "var_sr_number": "SR-123460",
  "testing_mode": true
}
```

#### Production Addition with Custom TTL
```json
{
  "var_action": "add",
  "domain": "healthgrp.com.sg",
  "hostname": "criticalapp01",
  "ipaddress": "***********",
  "ttl": 7200,
  "var_environment": "production",
  "var_sr_number": "SR-123461",
  "testing_mode": false
}
```

#### Staging Environment Addition
```json
{
  "var_action": "add",
  "domain": "stghealthgrp.com.sg",
  "hostname": "stagingweb01",
  "ipaddress": "**************",
  "ttl": 1800,
  "var_environment": "staging",
  "var_sr_number": "SR-123462",
  "testing_mode": false
}
```

### 3. DNS Record Removal

#### Standard Record Removal
```json
{
  "var_action": "remove",
  "domain": "healthgrp.com.sg",
  "hostname": "oldserver01",
  "var_sr_number": "SR-123463",
  "testing_mode": false
}
```

#### Development Environment Removal
```json
{
  "var_action": "remove",
  "domain": "devhealthgrp.com.sg",
  "hostname": "obsoletedev01",
  "var_environment": "development",
  "var_sr_number": "SR-123464",
  "testing_mode": true
}
```

#### Production Removal with Safety Checks
```json
{
  "var_action": "remove",
  "domain": "healthgrp.com.sg",
  "hostname": "decommissioned01",
  "var_environment": "production",
  "var_sr_number": "SR-123465",
  "testing_mode": false
}
```

### 4. DNS Record Update

#### IP Address Update
```json
{
  "var_action": "update",
  "domain": "healthgrp.com.sg",
  "hostname": "migratedserver01",
  "ipaddress": "*************",
  "ttl": 3600,
  "var_sr_number": "SR-123466",
  "testing_mode": false
}
```

#### TTL Update Only
```json
{
  "var_action": "update",
  "domain": "healthgrp.com.sg",
  "hostname": "cacheserver01",
  "ipaddress": "*************",
  "ttl": 300,
  "var_sr_number": "SR-123467",
  "testing_mode": false
}
```

#### Development Environment Update
```json
{
  "var_action": "update",
  "domain": "devhealthgrp.com.sg",
  "hostname": "devdb01",
  "ipaddress": "**********",
  "ttl": 600,
  "var_environment": "development",
  "var_sr_number": "SR-123468",
  "testing_mode": true
}
```

### 5. DNS Record Synchronization

#### Basic Synchronization
```json
{
  "var_action": "sync",
  "domain": "healthgrp.com.sg",
  "hostname": "syncserver01",
  "var_sr_number": "SR-123469",
  "testing_mode": false
}
```

#### Synchronization with IP Address
```json
{
  "var_action": "sync",
  "domain": "healthgrp.com.sg",
  "hostname": "fixedserver01",
  "ipaddress": "*************",
  "var_sr_number": "SR-123470",
  "testing_mode": false
}
```

#### Post-Zone Creation Synchronization
```json
{
  "var_action": "sync",
  "domain": "newapp.healthgrp.com.sg",
  "hostname": "app01",
  "ipaddress": "*************",
  "ttl": 3600,
  "var_sr_number": "SR-123471",
  "testing_mode": false
}
```

#### Development Sync Operation
```json
{
  "var_action": "sync",
  "domain": "devhealthgrp.com.sg",
  "hostname": "devapi01",
  "var_environment": "development",
  "var_sr_number": "SR-123472",
  "testing_mode": true
}
```

---

## Advanced AAP JSON Examples

### 1. Bulk Operations (Multiple Variables)

#### Multiple Server Addition
```json
{
  "var_action": "add",
  "servers": [
    {
      "domain": "healthgrp.com.sg",
      "hostname": "web01",
      "ipaddress": "*************"
    },
    {
      "domain": "healthgrp.com.sg",
      "hostname": "web02",
      "ipaddress": "*************"
    },
    {
      "domain": "healthgrp.com.sg",
      "hostname": "web03",
      "ipaddress": "*************"
    }
  ],
  "ttl": 3600,
  "var_sr_number": "SR-123473",
  "testing_mode": false
}
```

#### Environment-Specific Bulk Operations
```json
{
  "var_action": "add",
  "var_environment": "development",
  "servers": [
    {
      "domain": "devhealthgrp.com.sg",
      "hostname": "devapp01",
      "ipaddress": "**********"
    },
    {
      "domain": "devhealthgrp.com.sg",
      "hostname": "devapp02",
      "ipaddress": "**********"
    }
  ],
  "ttl": 300,
  "var_sr_number": "SR-123474",
  "testing_mode": true
}
```

### 2. Conditional Operations

#### Testing Mode with Validation
```json
{
  "var_action": "add",
  "domain": "healthgrp.com.sg",
  "hostname": "testvalidation01",
  "ipaddress": "*************",
  "ttl": 300,
  "testing_mode": true,
  "validate_before_execution": true,
  "var_sr_number": "SR-123475"
}
```

#### Production with Enhanced Logging
```json
{
  "var_action": "add",
  "domain": "healthgrp.com.sg",
  "hostname": "criticaldb01",
  "ipaddress": "************",
  "ttl": 7200,
  "var_environment": "production",
  "enhanced_logging": true,
  "audit_level": "detailed",
  "var_sr_number": "SR-123476",
  "testing_mode": false
}
```

### 3. Error Handling and Recovery

#### Operation with Rollback Enabled
```json
{
  "var_action": "update",
  "domain": "healthgrp.com.sg",
  "hostname": "updateserver01",
  "ipaddress": "*************",
  "ttl": 3600,
  "enable_rollback": true,
  "rollback_timeout": 300,
  "var_sr_number": "SR-123477",
  "testing_mode": false
}
```

#### Retry Configuration
```json
{
  "var_action": "add",
  "domain": "healthgrp.com.sg",
  "hostname": "retryserver01",
  "ipaddress": "*************",
  "ttl": 3600,
  "retry_attempts": 3,
  "retry_delay": 30,
  "var_sr_number": "SR-123478",
  "testing_mode": false
}
```

---

## Environment-Specific JSON Templates

### Development Environment Template
```json
{
  "var_action": "add",
  "domain": "devhealthgrp.com.sg",
  "hostname": "HOSTNAME_PLACEHOLDER",
  "ipaddress": "IP_ADDRESS_PLACEHOLDER",
  "ttl": 300,
  "var_environment": "development",
  "testing_mode": true,
  "enhanced_logging": true,
  "var_sr_number": "SR_NUMBER_PLACEHOLDER"
}
```

### Staging Environment Template
```json
{
  "var_action": "add",
  "domain": "stghealthgrp.com.sg",
  "hostname": "HOSTNAME_PLACEHOLDER",
  "ipaddress": "IP_ADDRESS_PLACEHOLDER",
  "ttl": 1800,
  "var_environment": "staging",
  "testing_mode": false,
  "validate_before_execution": true,
  "var_sr_number": "SR_NUMBER_PLACEHOLDER"
}
```

### Production Environment Template
```json
{
  "var_action": "add",
  "domain": "healthgrp.com.sg",
  "hostname": "HOSTNAME_PLACEHOLDER",
  "ipaddress": "IP_ADDRESS_PLACEHOLDER",
  "ttl": 3600,
  "var_environment": "production",
  "testing_mode": false,
  "enhanced_logging": true,
  "audit_level": "detailed",
  "enable_rollback": true,
  "var_sr_number": "SR_NUMBER_PLACEHOLDER"
}
```

---

## AAP Job Template Integration

### Survey Variables Mapping
When using AAP surveys, the JSON variables map to survey prompts as follows:

| Survey Prompt | JSON Variable | Required | Default |
|---------------|---------------|----------|---------|
| DNS Action | `action` | Yes | N/A |
| Domain Name | `domain` | Yes | N/A |
| Hostname | `hostname` | Yes | N/A |
| IP Address | `ipaddress` | Conditional | N/A |
| TTL (seconds) | `ttl` | No | 3600 |
| Service Request Number | `var_sr_number` | Yes | N/A |
| Enable Testing Mode | `testing_mode` | No | false |
| Target Environment | `environment` | No | production |

### Complete AAP Execution Example
```json
{
  "var_action": "add",
  "domain": "healthgrp.com.sg",
  "hostname": "newprodserver01",
  "ipaddress": "*************",
  "ttl": 3600,
  "var_environment": "production",
  "testing_mode": false,
  "enhanced_logging": true,
  "audit_level": "detailed",
  "enable_rollback": true,
  "validate_before_execution": true,
  "var_sr_number": "SR-123479",
  "execution_context": {
    "operator": "<EMAIL>",
    "approval_reference": "CHANGE-***********",
    "maintenance_window": "2024-01-15 02:00:00 SGT"
  }
}
```

---

## AAP Extra Variables - YAML Format Examples

### Overview
In addition to JSON format, AAP also supports YAML format for extra variables. YAML is often preferred for its readability and is the native format for Ansible. This section provides comprehensive YAML examples for all supported DNS operations.

### YAML Format Structure
```yaml
var_action: operation_type
domain: target_domain
hostname: target_hostname
ipaddress: target_ip_address
ttl: time_to_live_seconds
var_sr_number: service_request_number
testing_mode: true/false
var_environment: target_environment
```

---

## DNS Operation Examples - AAP YAML Format

### 1. DNS Record Verification

#### Basic Verification
```yaml
var_action: verify
domain: healthgrp.com.sg
hostname: webserver01
var_sr_number: SR-123456
testing_mode: false
```

#### Verification with Environment Override
```yaml
var_action: verify
domain: devhealthgrp.com.sg
hostname: testserver01
var_environment: development
var_sr_number: SR-123457
testing_mode: true
```

#### Production Verification
```yaml
var_action: verify
domain: healthgrp.com.sg
hostname: prodserver01
var_environment: production
var_sr_number: SR-123458
testing_mode: false
```

### 2. DNS Record Addition

#### Standard A and PTR Record Creation
```yaml
var_action: add
domain: healthgrp.com.sg
hostname: newserver01
ipaddress: *************
ttl: 3600
var_sr_number: SR-123459
testing_mode: false
```

#### Development Environment Addition
```yaml
var_action: add
domain: devhealthgrp.com.sg
hostname: devapp01
ipaddress: *********
ttl: 300
var_environment: development
var_sr_number: SR-123460
testing_mode: true
```

#### Production Addition with Custom TTL
```yaml
var_action: add
domain: healthgrp.com.sg
hostname: criticalapp01
ipaddress: ***********
ttl: 7200
var_environment: production
var_sr_number: SR-123461
testing_mode: false
```

#### Staging Environment Addition
```yaml
var_action: add
domain: stghealthgrp.com.sg
hostname: stagingweb01
ipaddress: **************
ttl: 1800
var_environment: staging
var_sr_number: SR-123462
testing_mode: false
```

### 3. DNS Record Removal

#### Standard Record Removal
```yaml
var_action: remove
domain: healthgrp.com.sg
hostname: oldserver01
var_sr_number: SR-123463
testing_mode: false
```

#### Development Environment Removal
```yaml
var_action: remove
domain: devhealthgrp.com.sg
hostname: obsoletedev01
var_environment: development
var_sr_number: SR-123464
testing_mode: true
```

#### Production Removal with Safety Checks
```yaml
var_action: remove
domain: healthgrp.com.sg
hostname: decommissioned01
var_environment: production
var_sr_number: SR-123465
testing_mode: false
```

### 4. DNS Record Update

#### IP Address Update
```yaml
var_action: update
domain: healthgrp.com.sg
hostname: migratedserver01
ipaddress: *************
ttl: 3600
var_sr_number: SR-123466
testing_mode: false
```

#### TTL Update Only
```yaml
var_action: update
domain: healthgrp.com.sg
hostname: cacheserver01
ipaddress: *************
ttl: 300
var_sr_number: SR-123467
testing_mode: false
```

#### Development Environment Update
```yaml
var_action: update
domain: devhealthgrp.com.sg
hostname: devdb01
ipaddress: **********
ttl: 600
var_environment: development
var_sr_number: SR-123468
testing_mode: true
```

### 5. DNS Record Synchronization

#### Basic Synchronization
```yaml
var_action: sync
domain: healthgrp.com.sg
hostname: syncserver01
var_sr_number: SR-123469
testing_mode: false
```

#### Synchronization with IP Address
```yaml
var_action: sync
domain: healthgrp.com.sg
hostname: fixedserver01
ipaddress: *************
var_sr_number: SR-123470
testing_mode: false
```

#### Post-Zone Creation Synchronization
```yaml
var_action: sync
domain: newapp.healthgrp.com.sg
hostname: app01
ipaddress: *************
ttl: 3600
var_sr_number: SR-123471
testing_mode: false
```

#### Development Sync Operation
```yaml
var_action: sync
domain: devhealthgrp.com.sg
hostname: devapi01
var_environment: development
var_sr_number: SR-123472
testing_mode: true
```

---

## Advanced AAP YAML Examples

### 1. Bulk Operations (Multiple Variables)

#### Multiple Server Addition
```yaml
var_action: add
servers:
  - domain: healthgrp.com.sg
    hostname: web01
    ipaddress: *************
  - domain: healthgrp.com.sg
    hostname: web02
    ipaddress: *************
  - domain: healthgrp.com.sg
    hostname: web03
    ipaddress: *************
ttl: 3600
var_sr_number: SR-123473
testing_mode: false
```

#### Environment-Specific Bulk Operations
```yaml
var_action: add
var_environment: development
servers:
  - domain: devhealthgrp.com.sg
    hostname: devapp01
    ipaddress: **********
  - domain: devhealthgrp.com.sg
    hostname: devapp02
    ipaddress: **********
ttl: 300
var_sr_number: SR-123474
testing_mode: true
```

### 2. Conditional Operations

#### Testing Mode with Validation
```yaml
var_action: add
domain: healthgrp.com.sg
hostname: testvalidation01
ipaddress: *************
ttl: 300
testing_mode: true
validate_before_execution: true
var_sr_number: SR-123475
```

#### Production with Enhanced Logging
```yaml
var_action: add
domain: healthgrp.com.sg
hostname: criticaldb01
ipaddress: ************
ttl: 7200
var_environment: production
enhanced_logging: true
audit_level: detailed
var_sr_number: SR-123476
testing_mode: false
```

### 3. Error Handling and Recovery

#### Operation with Rollback Enabled
```yaml
var_action: update
domain: healthgrp.com.sg
hostname: updateserver01
ipaddress: *************
ttl: 3600
enable_rollback: true
rollback_timeout: 300
var_sr_number: SR-123477
testing_mode: false
```

#### Retry Configuration
```yaml
var_action: add
domain: healthgrp.com.sg
hostname: retryserver01
ipaddress: *************
ttl: 3600
retry_attempts: 3
retry_delay: 30
var_sr_number: SR-123478
testing_mode: false
```

---

## Environment-Specific YAML Templates

### Development Environment Template
```yaml
var_action: add
domain: devhealthgrp.com.sg
hostname: HOSTNAME_PLACEHOLDER
ipaddress: IP_ADDRESS_PLACEHOLDER
ttl: 300
var_environment: development
testing_mode: true
enhanced_logging: true
var_sr_number: SR_NUMBER_PLACEHOLDER
```

### Staging Environment Template
```yaml
var_action: add
domain: stghealthgrp.com.sg
hostname: HOSTNAME_PLACEHOLDER
ipaddress: IP_ADDRESS_PLACEHOLDER
ttl: 1800
var_environment: staging
testing_mode: false
validate_before_execution: true
var_sr_number: SR_NUMBER_PLACEHOLDER
```

### Production Environment Template
```yaml
var_action: add
domain: healthgrp.com.sg
hostname: HOSTNAME_PLACEHOLDER
ipaddress: IP_ADDRESS_PLACEHOLDER
ttl: 3600
var_environment: production
testing_mode: false
enhanced_logging: true
audit_level: detailed
enable_rollback: true
var_sr_number: SR_NUMBER_PLACEHOLDER
```

---

## Complex YAML Configuration Examples

### Web Server Deployment with Context
```yaml
var_action: add
domain: healthgrp.com.sg
hostname: web01
ipaddress: *************
ttl: 3600
var_sr_number: SR-WEB-001
testing_mode: false
execution_context:
  purpose: Web server deployment
  team: Infrastructure Team
  operator: <EMAIL>
  deployment_type: production
```

### Database Migration with Maintenance Window
```yaml
var_action: update
domain: healthgrp.com.sg
hostname: db01
ipaddress: ************
ttl: 7200
var_sr_number: SR-DB-MIGRATE-001
testing_mode: false
enable_rollback: true
execution_context:
  purpose: Database server migration
  maintenance_window: "2024-01-15 02:00:00 SGT"
  approval_reference: CHANGE-***********
  impact: high
  rollback_plan: available
```

### Load Balancer Configuration
```yaml
var_action: add
domain: healthgrp.com.sg
hostname: lb01
ipaddress: ************
ttl: 300
var_sr_number: SR-LB-001
testing_mode: false
execution_context:
  purpose: Load balancer setup
  service_type: critical
  health_check_required: true
  monitoring_enabled: true
```

### Multi-Environment Deployment
```yaml
var_action: add
environments:
  development:
    domain: devhealthgrp.com.sg
    hostname: api01
    ipaddress: **********
    ttl: 300
  staging:
    domain: stghealthgrp.com.sg
    hostname: api01
    ipaddress: **************0
    ttl: 1800
  production:
    domain: healthgrp.com.sg
    hostname: api01
    ipaddress: ************
    ttl: 3600
var_sr_number: SR-API-DEPLOY-001
testing_mode: false
```

---

## YAML vs JSON Comparison

### YAML Advantages
- **Readability:** More human-readable format
- **Comments:** Supports inline comments with #
- **Multi-line:** Better for complex configurations
- **Native Ansible:** Natural format for Ansible

### JSON Advantages
- **Compact:** More compact representation
- **Validation:** Easier syntax validation
- **Universal:** Widely supported across tools
- **Parsing:** Faster parsing in most systems

### When to Use YAML
- Complex configurations with multiple nested objects
- When readability is important for team collaboration
- For configurations that need inline documentation
- When working primarily in Ansible environments

### When to Use JSON
- Simple, straightforward configurations
- When working with APIs or web interfaces
- For automated generation of configurations
- When compact representation is preferred

---

*This user guide is part of the DNS Management Automation v2 solution developed by the CES Operational Excellence Team following Operational Excellence Automation Framework (OXAF) patterns.*
