# CyberArk Setup Guide - DNS Management Automation v2

## Collection Installation and Configuration

**Framework:** Operational Excellence Automation Framework (OXAF)  
**Version:** 2.0  
**Purpose:** Step-by-step guide for CyberArk collection setup and configuration  

---

## 🎯 **Prerequisites**

### **System Requirements:**
- Ansible Automation Platform (AAP) 2.x
- Network connectivity to CyberArk Credential Provider
- Appropriate service account permissions in CyberArk
- DNS Management Automation v2 project deployed

### **CyberArk Requirements:**
- CyberArk Credential Provider (CCP) accessible from AAP
- Configured safes for DNS automation credentials
- Service accounts with appropriate permissions
- Application ID configured for AAP access

---

## 📦 **Collection Installation**

### **Step 1: Install CyberArk Collection**
```bash
# Navigate to project directory
cd vmlc-services-dns-v2

# Install collections from requirements
ansible-galaxy collection install -r collections/requirements.yml

# Verify installation
ansible-galaxy collection list | grep cyberark
```

### **Step 2: Verify Collection Installation**
```bash
# Check collection version
ansible-galaxy collection list cloud_cpe.cyberark_ccp

# Test collection availability
ansible-doc cloud_cpe.cyberark_ccp.cyberark_credential
```

### **Expected Output:**
```
# ansible-galaxy collection list cloud_cpe.cyberark_ccp
Collection           Version
-------------------- -------
cloud_cpe.cyberark_ccp 1.0.0
```

---

## 🔐 **CyberArk Account Configuration**

### **Required Accounts in CyberArk:**

#### **DNS Service Account:**
```
Account Name: DNS_SERVICE_ACCOUNT
Username: [DNS service account username]
Password: [DNS service account password]
Safe: DNS_AUTOMATION
Platform: Windows Domain Account
Application ID: ANSIBLE_DNS
```

#### **JIRA Production Accounts:**
```
Account Name: JIRA_PROD_USERNAME
Username: [JIRA production username]
Safe: JIRA_AUTOMATION
Platform: Application Account
Application ID: ANSIBLE_DNS

Account Name: JIRA_PROD_PASSWORD
Password: [JIRA production password]
Safe: JIRA_AUTOMATION
Platform: Application Account
Application ID: ANSIBLE_DNS

Account Name: JIRA_PROD_GRID_TOKEN
Password: [JIRA Grid API token]
Safe: JIRA_AUTOMATION
Platform: Application Account
Application ID: ANSIBLE_DNS
```

#### **JIRA UAT Accounts:**
```
Account Name: JIRA_UAT_USERNAME
Username: [JIRA UAT username]
Safe: JIRA_AUTOMATION
Platform: Application Account
Application ID: ANSIBLE_DNS

Account Name: JIRA_UAT_PASSWORD
Password: [JIRA UAT password]
Safe: JIRA_AUTOMATION
Platform: Application Account
Application ID: ANSIBLE_DNS

Account Name: JIRA_UAT_GRID_TOKEN
Password: [JIRA UAT Grid API token]
Safe: JIRA_AUTOMATION
Platform: Application Account
Application ID: ANSIBLE_DNS
```

---

## 🔧 **CyberArk Configuration Steps**

### **Step 1: Create Safes**
```bash
# Create DNS automation safe
Safe Name: DNS_AUTOMATION
Description: DNS Management Automation v2 Credentials
Managing CPM: PasswordManager
Retention: 7 days
```

```bash
# Create JIRA automation safe
Safe Name: JIRA_AUTOMATION
Description: JIRA Integration Credentials for DNS Automation
Managing CPM: PasswordManager
Retention: 7 days
```

### **Step 2: Configure Application ID**
```bash
# Application configuration
Application ID: ANSIBLE_DNS
Description: Ansible DNS Management Automation v2
Authentication Method: Certificate/Hash
Allowed Machines: [AAP server hostnames/IPs]
```

### **Step 3: Set Permissions**
```bash
# Safe permissions for ANSIBLE_DNS application
DNS_AUTOMATION Safe:
- Use Accounts: Yes
- Retrieve Accounts: Yes
- List Accounts: Yes

JIRA_AUTOMATION Safe:
- Use Accounts: Yes
- Retrieve Accounts: Yes
- List Accounts: Yes
```

### **Step 4: Add Accounts to Safes**
```bash
# Add DNS service account
Account: DNS_SERVICE_ACCOUNT
Safe: DNS_AUTOMATION
Platform: Windows Domain Account
Username: [service account]
Password: [service password]

# Add JIRA accounts (repeat for all JIRA accounts)
Account: JIRA_PROD_USERNAME
Safe: JIRA_AUTOMATION
Platform: Application Account
Username: [JIRA username]
```

---

## 🧪 **Testing CyberArk Integration**

### **Step 1: Test Collection Functionality**
```bash
# Create test playbook
cat > test_cyberark.yml << EOF
---
- name: Test CyberArk Integration
  hosts: localhost
  gather_facts: false
  tasks:
    - name: Test DNS Credential Retrieval
      cloud_cpe.cyberark_ccp.cyberark_credential:
        account: "DNS_SERVICE_ACCOUNT"
      register: dns_test_result
      no_log: true
      
    - name: Validate Credential Retrieval
      debug:
        msg: "Credential retrieval successful for DNS_SERVICE_ACCOUNT"
      when: dns_test_result is succeeded
      
    - name: Test JIRA Credential Retrieval
      cloud_cpe.cyberark_ccp.cyberark_credential:
        account: "JIRA_PROD_USERNAME"
      register: jira_test_result
      no_log: true
      
    - name: Validate JIRA Credential Retrieval
      debug:
        msg: "Credential retrieval successful for JIRA_PROD_USERNAME"
      when: jira_test_result is succeeded
EOF

# Run test
ansible-playbook test_cyberark.yml
```

### **Step 2: Test with DNS Automation**
```bash
# Test DNS automation with CyberArk
ansible-playbook main.yml \
  -e "action=verify domain=test.com hostname=test var_sr_number=SR-123456" \
  --tags="validation" \
  --check
```

### **Expected Results:**
```
TASK [Test DNS Credential Retrieval] ******************************************
ok: [localhost]

TASK [Validate Credential Retrieval] ******************************************
ok: [localhost] => {
    "msg": "Credential retrieval successful for DNS_SERVICE_ACCOUNT"
}

TASK [Test JIRA Credential Retrieval] *****************************************
ok: [localhost]

TASK [Validate JIRA Credential Retrieval] *************************************
ok: [localhost] => {
    "msg": "Credential retrieval successful for JIRA_PROD_USERNAME"
}
```

---

## 🚨 **Troubleshooting**

### **Common Issues and Solutions:**

#### **Issue 1: Collection Not Found**
```
Error: Collection 'cloud_cpe.cyberark_ccp' not found
Solution:
1. Verify collections/requirements.yml exists
2. Run: ansible-galaxy collection install -r collections/requirements.yml
3. Check collection installation: ansible-galaxy collection list
```

#### **Issue 2: Account Not Found**
```
Error: Account 'DNS_SERVICE_ACCOUNT' not found
Solution:
1. Verify account exists in CyberArk safe
2. Check account name spelling (case-sensitive)
3. Verify safe permissions for application ID
```

#### **Issue 3: Authentication Failure**
```
Error: Failed to authenticate with CyberArk
Solution:
1. Verify AAP server is configured in CyberArk application
2. Check network connectivity to CyberArk CCP
3. Validate application ID configuration
```

#### **Issue 4: Permission Denied**
```
Error: Permission denied accessing account
Solution:
1. Verify application ID has safe permissions
2. Check account platform configuration
3. Validate safe membership for application
```

### **Debug Commands:**
```bash
# Test CyberArk connectivity
curl -k https://[cyberark-server]/AIMWebService/api/Accounts?AppID=ANSIBLE_DNS&Safe=DNS_AUTOMATION&Object=DNS_SERVICE_ACCOUNT

# Check collection installation
ansible-galaxy collection list | grep cyberark

# Validate playbook syntax
ansible-playbook main.yml --syntax-check

# Test with verbose output
ansible-playbook test_cyberark.yml -vvv
```

---

## 📋 **Validation Checklist**

### **Pre-Deployment Checklist:**
- [ ] CyberArk collection installed successfully
- [ ] All required accounts created in CyberArk
- [ ] Application ID configured with proper permissions
- [ ] Safe permissions configured correctly
- [ ] Network connectivity to CyberArk verified
- [ ] Test playbook executes successfully
- [ ] DNS automation validation passes
- [ ] JIRA integration validation passes

### **Post-Deployment Validation:**
- [ ] DNS operations retrieve credentials successfully
- [ ] JIRA integration uses CyberArk credentials
- [ ] No hardcoded credentials in configurations
- [ ] Audit logs show CyberArk credential access
- [ ] Error handling works for credential failures
- [ ] All environments (prod/staging/dev) working

---

## 📊 **Monitoring CyberArk Integration**

### **Key Metrics to Monitor:**
- **Credential Retrieval Success Rate** - Monitor CyberArk integration health
- **Authentication Failures** - Track failed credential retrievals
- **Response Time** - Monitor CyberArk response performance
- **Error Patterns** - Identify recurring issues

### **Monitoring Commands:**
```bash
# Check recent CyberArk access logs
grep "cyberark_credential" /var/log/ansible/ansible.log

# Monitor credential retrieval performance
ansible-playbook main.yml -e "action=verify" --tags="validation" -v | grep "cyberark"

# Check for authentication errors
journalctl -u ansible-automation-platform | grep -i cyberark
```

---

## 📞 **Support Contacts**

### **CyberArk Support:**
- **CyberArk Team:** <EMAIL>
- **Application Support:** <EMAIL>

### **DNS Automation Support:**
- **DNS Automation Team:** <EMAIL>
- **Infrastructure Team:** <EMAIL>

### **Emergency Contacts:**
- **24/7 Support:** Available through ITSM
- **Escalation:** <EMAIL>

---

*This CyberArk setup guide is part of the DNS Management Automation v2 solution developed by the CES Operational Excellence Team following Operational Excellence Automation Framework (OXAF) patterns.*
