# API Reference Guide - DNS Management Automation v2

## External API Integrations and Endpoints

**Framework:** Operational Excellence Automation Framework (OXAF)
**Version:** 2.0
**Purpose:** Comprehensive reference for all API integrations and external service endpoints

---

## 🎯 **Overview**

The DNS Management Automation v2 solution integrates with multiple external APIs and services to provide comprehensive DNS management, ITSM integration, monitoring, and security capabilities. This document provides detailed reference information for all API integrations.

### **Integrated APIs:**
- ✅ **JIRA REST API** - Service request management and ticket updates
- ✅ **JIRA Grid API (Idalko)** - Enhanced JIRA field updates
- ✅ **CyberArk Credential Provider API** - Secure credential retrieval
- ✅ **DNS Server APIs** - Windows DNS Server PowerShell modules
- ✅ **Monitoring APIs** - Splunk, Prometheus, and DataDog integrations
- ✅ **AAP (Ansible Automation Platform) API** - Job execution and status

---

## 🔧 **JIRA API Integration**

### **JIRA REST API v2**

#### **Base Configuration:**
```yaml
# API Base URLs
Production: https://itsm.hcloud.healthgrp.com.sg/rest/api/2
UAT: https://jsd-uat.hcloud.healthgrp.com.sg/rest/api/2

# API Version
api_version: "2"
```

#### **Supported Endpoints:**

##### **1. Issue Comments API**
```http
POST /rest/api/2/issue/{issue_key}/comment
Content-Type: application/json
Authorization: Basic {base64_credentials}

{
  "body": "DNS automation execution details..."
}
```

**Response:**
```json
{
  "id": "12345",
  "author": {
    "name": "dns-automation",
    "displayName": "DNS Automation Service"
  },
  "body": "DNS automation execution details...",
  "created": "2024-01-15T10:30:00.000+0800"
}
```

##### **2. Issue Retrieval API**
```http
GET /rest/api/2/issue/{issue_key}
Authorization: Basic {base64_credentials}
```

**Response:**
```json
{
  "key": "SR-123456",
  "fields": {
    "summary": "DNS Record Addition Request",
    "status": {
      "name": "In Progress"
    },
    "assignee": {
      "name": "dns.engineer"
    }
  }
}
```

### **JIRA Grid API (Idalko)**

#### **Base Configuration:**
```yaml
# Grid API Base URLs
Production: https://itsm.hcloud.healthgrp.com.sg/rest/idalko-grid/1.0/api/grid
UAT: https://jsd-uat.hcloud.healthgrp.com.sg/rest/idalko-grid/1.0/api/grid

# Grid API Version
grid_api_version: "1.0"
```

#### **Grid Update Endpoint:**
```http
PUT /rest/idalko-grid/1.0/api/grid/{grid_id}/issue/{issue_key}/
Content-Type: application/json
Authorization: {grid_token}

{
  "rows": [
    {
      "rowId": "1",
      "columns": {
        "remark": "AAP Job: https://aap.example.com/#/jobs/playbook/12345/"
      }
    }
  ]
}
```

**Response:**
```json
{
  "success": true,
  "message": "Grid updated successfully",
  "rowsUpdated": 1
}
```

---

## 🔐 **CyberArk Credential Provider API**

### **CyberArk Collection Integration**

#### **Credential Retrieval:**
```yaml
# DNS Service Account Retrieval
- name: "Retrieve DNS Credentials"
  cloud_cpe.cyberark_ccp.cyberark_credential:
    account: "DNS_SERVICE_ACCOUNT"
  register: dns_credentials_result
  no_log: true
```

#### **JIRA Credential Retrieval:**
```yaml
# JIRA Username Retrieval
- name: "Retrieve JIRA Username"
  cloud_cpe.cyberark_ccp.cyberark_credential:
    account: "JIRA_PROD_USERNAME"
  register: jira_username_result
  no_log: true

# JIRA Password Retrieval
- name: "Retrieve JIRA Password"
  cloud_cpe.cyberark_ccp.cyberark_credential:
    account: "JIRA_PROD_PASSWORD"
  register: jira_password_result
  no_log: true
```

#### **Response Structure:**
```yaml
# Successful Response
dns_credentials_result:
  credential:
    username: "service_account_username"
    password: "service_account_password"
  changed: false
  failed: false
```

---

## 🖥️ **DNS Server API Integration**

### **Windows DNS Server PowerShell Modules**

#### **DNS Record Operations:**

##### **1. Get DNS Record**
```powershell
# API Call
Get-DnsServerResourceRecord -ZoneName "domain.com" -Name "hostname" -RRType A -ComputerName "dns-server"

# Response Structure
@{
    HostName = "hostname"
    RecordType = "A"
    RecordData = @{
        IPv4Address = "*************"
    }
    TimeToLive = "01:00:00"
    Timestamp = $null
}
```

##### **2. Add DNS Record**
```powershell
# API Call
Add-DnsServerResourceRecordA -ZoneName "domain.com" -Name "hostname" -IPv4Address "*************" -TimeToLive (New-TimeSpan -Seconds 3600) -ComputerName "dns-server"

# Response
# Returns void on success, throws exception on failure
```

##### **3. Remove DNS Record**
```powershell
# API Call
Remove-DnsServerResourceRecord -ZoneName "domain.com" -Name "hostname" -RRType A -ComputerName "dns-server" -Force

# Response
# Returns void on success, throws exception on failure
```

##### **4. Get DNS Zone**
```powershell
# API Call
Get-DnsServerZone -Name "domain.com" -ComputerName "dns-server"

# Response Structure
@{
    ZoneName = "domain.com"
    ZoneType = "Primary"
    DynamicUpdate = "Secure"
    ReplicationScope = "Domain"
    DirectoryPartition = "DomainDnsZones.domain.com"
}
```

---

## 📊 **Monitoring API Integration**

### **Splunk HTTP Event Collector (HEC)**

#### **Configuration:**
```yaml
# Splunk HEC Endpoint
splunk_hec_url: "https://splunk.example.com:8088/services/collector/event"
splunk_hec_token: "{{ splunk_token_from_cyberark }}"
```

#### **Event Submission:**
```http
POST /services/collector/event
Authorization: Splunk {hec_token}
Content-Type: application/json

{
  "time": **********,
  "host": "aap-server",
  "source": "dns-automation",
  "sourcetype": "dns:operation",
  "event": {
    "operation": "add",
    "domain": "healthgrp.com.sg",
    "hostname": "webserver01",
    "status": "success",
    "execution_time": 45.2,
    "job_id": "12345"
  }
}
```

### **Prometheus Metrics API**

#### **Configuration:**
```yaml
# Prometheus Pushgateway
prometheus_pushgateway_url: "https://prometheus-pushgateway.example.com:9091"
```

#### **Metrics Push:**
```http
POST /metrics/job/{job_name}/instance/{instance}
Content-Type: text/plain

# DNS operation metrics
dns_operation_duration_seconds{operation="add",domain="healthgrp.com.sg"} 45.2
dns_operation_total{operation="add",status="success"} 1
dns_operation_errors_total{operation="add",error_type="none"} 0
```

### **DataDog API**

#### **Configuration:**
```yaml
# DataDog API
datadog_api_url: "https://api.datadoghq.com/api/v1"
datadog_api_key: "{{ datadog_api_key_from_cyberark }}"
```

#### **Event Submission:**
```http
POST /api/v1/events
Content-Type: application/json
DD-API-KEY: {api_key}

{
  "title": "DNS Record Added",
  "text": "Successfully added DNS record for webserver01.healthgrp.com.sg",
  "date_happened": **********,
  "priority": "normal",
  "tags": [
    "var_environment:production",
    "service:dns-automation",
    "operation:add"
  ],
  "alert_type": "success"
}
```

---

## 🤖 **Ansible Automation Platform (AAP) API**

### **Job Template Execution**

#### **Configuration:**
```yaml
# AAP API Base URL
aap_api_url: "https://aap.example.com/api/v2"
```

#### **Job Status API:**
```http
GET /api/v2/jobs/{job_id}/
Authorization: Bearer {aap_token}

# Response
{
  "id": 12345,
  "name": "DNS Management v2 - Production",
  "status": "successful",
  "started": "2024-01-15T10:30:00.000Z",
  "finished": "2024-01-15T10:32:15.000Z",
  "elapsed": 135.5,
  "job_template": 42,
  "extra_vars": "{\"action\":\"add\",\"domain\":\"healthgrp.com.sg\"}"
}
```

---

## 🔒 **API Security and Authentication**

### **Authentication Methods:**

#### **1. JIRA Authentication:**
```yaml
# Basic Authentication (REST API)
Authorization: Basic {base64(username:password)}

# Token Authentication (Grid API)
Authorization: {grid_api_token}
```

#### **2. CyberArk Authentication:**
```yaml
# Collection handles authentication automatically
# No manual authentication required
```

#### **3. Monitoring APIs:**
```yaml
# Splunk HEC Token
Authorization: Splunk {hec_token}

# DataDog API Key
DD-API-KEY: {api_key}

# Prometheus (usually no auth for pushgateway)
# Authentication handled by network security
```

### **Security Best Practices:**
- ✅ **Credential Rotation:** All credentials retrieved from CyberArk
- ✅ **TLS Encryption:** All API calls use HTTPS/TLS
- ✅ **Token Management:** API tokens stored securely in CyberArk
- ✅ **Access Logging:** All API calls logged for audit purposes
- ✅ **Error Handling:** Sensitive information masked in error logs

---

## 📈 **API Performance and Monitoring**

### **Timeout Configuration:**
```yaml
# API Timeouts
jira_api_timeout: 30 seconds
cyberark_timeout: 30 seconds
dns_operation_timeout: 300 seconds
monitoring_api_timeout: 15 seconds
```

### **Retry Configuration:**
```yaml
# Retry Settings
max_retry_attempts: 3
retry_delay_seconds: 5
exponential_backoff: true
```

### **Performance Metrics:**
- **Response Time Tracking:** Monitor API response times
- **Success Rate Monitoring:** Track API call success/failure rates
- **Error Pattern Analysis:** Identify recurring API issues
- **Throughput Monitoring:** Monitor API call volume and patterns

---

## 🚨 **Error Handling and Troubleshooting**

### **Common API Errors:**

#### **JIRA API Errors:**
```json
# 401 Unauthorized
{
  "errorMessages": ["You do not have the permission to see the specified issue."],
  "errors": {}
}

# 404 Not Found
{
  "errorMessages": ["Issue does not exist or you do not have permission to see it."],
  "errors": {}
}
```

#### **CyberArk Errors:**
```yaml
# Account Not Found
msg: "Account 'DNS_SERVICE_ACCOUNT' not found in CyberArk"

# Authentication Failure
msg: "Failed to authenticate with CyberArk Credential Provider"
```

#### **DNS API Errors:**
```powershell
# Zone Not Found
Exception: DNS zone 'domain.com' was not found on server 'dns-server'

# Record Already Exists
Exception: DNS record 'hostname.domain.com' already exists
```

### **Troubleshooting Steps:**
1. **Verify Connectivity:** Test network connectivity to API endpoints
2. **Check Credentials:** Validate credentials and permissions
3. **Review Logs:** Check detailed error logs for specific issues
4. **Test Manually:** Use manual API calls to isolate problems
5. **Check Configuration:** Verify API endpoint URLs and settings

---

## 📞 **API Support and Documentation**

### **External API Documentation:**
- **JIRA REST API:** [Atlassian JIRA REST API Documentation](https://developer.atlassian.com/server/jira/platform/rest-apis/)
- **CyberArk CCP:** [CyberArk Credential Provider Documentation](https://docs.cyberark.com/)
- **Windows DNS:** [Microsoft DNS Server PowerShell Documentation](https://docs.microsoft.com/en-us/powershell/module/dnsserver/)
- **Splunk HEC:** [Splunk HTTP Event Collector Documentation](https://docs.splunk.com/Documentation/Splunk/latest/Data/UsetheHTTPEventCollector)
- **Prometheus:** [Prometheus Pushgateway Documentation](https://prometheus.io/docs/instrumenting/pushing/)
- **DataDog:** [DataDog API Documentation](https://docs.datadoghq.com/api/)

### **Internal Support:**
- **DNS Automation Team:** <EMAIL>
- **JIRA Administration:** <EMAIL>
- **CyberArk Team:** <EMAIL>
- **Monitoring Team:** <EMAIL>

---

## 🧪 **API Testing and Validation**

### **JIRA API Testing:**
```bash
# Test JIRA REST API connectivity
curl -X GET \
  "https://itsm.hcloud.healthgrp.com.sg/rest/api/2/myself" \
  -H "Authorization: Basic $(echo -n 'username:password' | base64)" \
  -H "Content-Type: application/json"

# Test JIRA comment creation
curl -X POST \
  "https://itsm.hcloud.healthgrp.com.sg/rest/api/2/issue/SR-123456/comment" \
  -H "Authorization: Basic $(echo -n 'username:password' | base64)" \
  -H "Content-Type: application/json" \
  -d '{"body": "Test comment from DNS automation"}'
```

### **CyberArk API Testing:**
```yaml
# Test CyberArk credential retrieval
- name: "Test CyberArk Integration"
  cloud_cpe.cyberark_ccp.cyberark_credential:
    account: "DNS_SERVICE_ACCOUNT"
  register: test_result

- name: "Validate Credential Retrieval"
  debug:
    msg: "CyberArk integration successful"
  when: test_result is succeeded
```

### **DNS API Testing:**
```powershell
# Test DNS server connectivity
Test-NetConnection -ComputerName "dns-server.domain.com" -Port 53

# Test DNS zone access
try {
    Get-DnsServerZone -Name "healthgrp.com.sg" -ComputerName "dns-server"
    Write-Host "DNS zone access successful"
} catch {
    Write-Error "DNS zone access failed: $($_.Exception.Message)"
}
```

---

## 📋 **API Rate Limits and Quotas**

### **JIRA API Limits:**
- **REST API:** 300 requests per minute per user
- **Grid API:** 100 requests per minute per token
- **Concurrent Connections:** Maximum 10 per user

### **CyberArk API Limits:**
- **Credential Requests:** 1000 requests per hour per application
- **Concurrent Sessions:** Maximum 50 per application
- **Response Time SLA:** 95% of requests under 2 seconds

### **Monitoring API Limits:**
- **Splunk HEC:** 10,000 events per minute
- **Prometheus:** 1,000 metrics per push
- **DataDog:** 1,000 events per minute per API key

---

## 🔄 **API Versioning and Compatibility**

### **Supported API Versions:**
```yaml
# Current API Versions
jira_rest_api_version: "2"
jira_grid_api_version: "1.0"
cyberark_ccp_version: "v1"
splunk_hec_version: "1.0"
prometheus_api_version: "v1"
datadog_api_version: "v1"
```

### **Version Compatibility Matrix:**
| API Service | Current Version | Minimum Supported | Maximum Supported |
|-------------|----------------|-------------------|-------------------|
| JIRA REST API | v2 | v2 | v3 |
| JIRA Grid API | v1.0 | v1.0 | v1.0 |
| CyberArk CCP | v1 | v1 | v1 |
| Splunk HEC | v1.0 | v1.0 | v1.0 |
| Prometheus | v1 | v1 | v1 |
| DataDog | v1 | v1 | v2 |

---

## 📊 **API Usage Analytics**

### **Metrics Collection:**
```yaml
# API Usage Metrics
api_metrics:
  jira_api_calls_total: counter
  jira_api_response_time: histogram
  jira_api_errors_total: counter
  cyberark_credential_requests_total: counter
  dns_operations_total: counter
  monitoring_events_sent_total: counter
```

### **Performance Benchmarks:**
- **JIRA REST API:** Average response time < 2 seconds
- **JIRA Grid API:** Average response time < 1 second
- **CyberArk CCP:** Average response time < 1 second
- **DNS Operations:** Average response time < 5 seconds
- **Monitoring APIs:** Average response time < 500ms

---

## 🛠️ **API Development and Testing Tools**

### **Recommended Tools:**
- **Postman:** API testing and documentation
- **curl:** Command-line API testing
- **Ansible:** Automation and integration testing
- **PowerShell:** DNS API testing and validation
- **Splunk:** Log analysis and API monitoring

### **Testing Scripts:**
```bash
# API Health Check Script
#!/bin/bash
echo "Testing DNS Management Automation APIs..."

# Test JIRA API
echo "Testing JIRA API..."
curl -s -o /dev/null -w "%{http_code}" "https://itsm.hcloud.healthgrp.com.sg/rest/api/2/myself"

# Test monitoring endpoints
echo "Testing monitoring endpoints..."
# Add specific monitoring API tests here

echo "API health check complete."
```

---

## 📚 **API Integration Examples**

### **Complete JIRA Integration Example:**
```yaml
# Complete JIRA update workflow
- name: "Complete JIRA Integration Example"
  block:
    # 1. Retrieve credentials from CyberArk
    - name: "Get JIRA credentials"
      cloud_cpe.cyberark_ccp.cyberark_credential:
        account: "JIRA_PROD_USERNAME"
      register: jira_creds

    # 2. Update via Grid API
    - name: "Update JIRA via Grid API"
      uri:
        url: "{{ jira_grid_url }}/{{ grid_id }}/issue/{{ ticket_number }}/"
        method: PUT
        headers:
          Authorization: "{{ grid_token }}"
          Content-Type: "application/json"
        body_format: json
        body:
          rows:
            - rowId: "1"
              columns:
                remark: "DNS operation completed successfully"
      register: grid_result

    # 3. Fallback to REST API if Grid API fails
    - name: "Fallback to REST API"
      uri:
        url: "{{ jira_rest_url }}/issue/{{ ticket_number }}/comment"
        method: POST
        headers:
          Authorization: "Basic {{ (jira_creds.credential.username + ':' + jira_creds.credential.password) | b64encode }}"
          Content-Type: "application/json"
        body_format: json
        body:
          body: "DNS operation completed successfully"
      when: grid_result is failed
```

### **DNS Operation with Full API Integration:**
```yaml
# DNS operation with monitoring and JIRA integration
- name: "DNS Operation with Full Integration"
  block:
    # 1. Get DNS credentials
    - name: "Retrieve DNS credentials"
      cloud_cpe.cyberark_ccp.cyberark_credential:
        account: "DNS_SERVICE_ACCOUNT"
      register: dns_creds

    # 2. Execute DNS operation
    - name: "Execute DNS operation"
      win_shell: |
        $cred = New-Object System.Management.Automation.PSCredential("{{ dns_creds.credential.username }}", (ConvertTo-SecureString "{{ dns_creds.credential.password }}" -AsPlainText -Force))
        Add-DnsServerResourceRecordA -ZoneName "{{ domain }}" -Name "{{ hostname }}" -IPv4Address "{{ ipaddress }}" -Credential $cred
      register: dns_result

    # 3. Send metrics to monitoring
    - name: "Send metrics to Splunk"
      uri:
        url: "{{ splunk_hec_url }}"
        method: POST
        headers:
          Authorization: "Splunk {{ splunk_token }}"
        body_format: json
        body:
          event:
            operation: "add"
            domain: "{{ domain }}"
            hostname: "{{ hostname }}"
            status: "{{ 'success' if dns_result is succeeded else 'failed' }}"

    # 4. Update JIRA ticket
    - name: "Update JIRA ticket"
      include_tasks: update_jira_ticket.yml
      vars:
        ticket_status: "{{ 'completed' if dns_result is succeeded else 'failed' }}"
```

---

*This comprehensive API reference guide is part of the DNS Management Automation v2 solution developed by the CES Operational Excellence Team following Operational Excellence Automation Framework (OXAF) patterns.*
