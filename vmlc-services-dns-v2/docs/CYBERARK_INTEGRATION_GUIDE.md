# CyberArk Integration Guide - DNS Management Automation v2

## Collection-Based Credential Management

**Framework:** Operational Excellence Automation Framework (OXAF)  
**Version:** 2.0  
**Purpose:** Complete guide for CyberArk integration using collection-based approach  

---

## 🎯 **Overview**

The DNS Management Automation v2 solution uses the CyberArk Credential Provider collection for secure credential management. This implementation follows the v1 pattern of using only the collection's built-in functionality without additional custom configurations.

### **Key Features:**
- ✅ **Collection-based approach** - No custom CyberArk configurations required
- ✅ **Automatic credential retrieval** - Seamless integration with CyberArk vaults
- ✅ **Multi-environment support** - Production, Staging, and Development
- ✅ **Secure credential handling** - No credentials stored in playbooks or variables
- ✅ **OXAF framework compliance** - Follows enterprise security patterns

---

## 🔧 **CyberArk Collection Implementation**

### **Collection Requirements:**
```yaml
# collections/requirements.yml
collections:
  - name: cloud_cpe.cyberark_ccp
    version: ">=1.0.0"
```

### **Installation:**
```bash
# Install CyberArk collection
ansible-galaxy collection install -r collections/requirements.yml

# Verify installation
ansible-galaxy collection list | grep cyberark
```

---

## 🔐 **Credential Account Mapping**

### **DNS Service Accounts:**
| Environment | Account Name | Purpose |
|-------------|--------------|---------|
| All | `DNS_SERVICE_ACCOUNT` | DNS server operations |

### **JIRA Integration Accounts:**
| Environment | Account Name | Purpose |
|-------------|--------------|---------|
| Production | `JIRA_PROD_USERNAME` | JIRA production username |
| Production | `JIRA_PROD_PASSWORD` | JIRA production password |
| Production | `JIRA_PROD_GRID_TOKEN` | JIRA production Grid API token |
| UAT | `JIRA_UAT_USERNAME` | JIRA UAT username |
| UAT | `JIRA_UAT_PASSWORD` | JIRA UAT password |
| UAT | `JIRA_UAT_GRID_TOKEN` | JIRA UAT Grid API token |

---

## 🌍 **Environment Configuration**

### **Production Environment:**
```yaml
# group_vars/production/main.yml
jira_credentials:
  # CyberArk account names for credential retrieval
  username_account: "JIRA_PROD_USERNAME"
  password_account: "JIRA_PROD_PASSWORD"
  grid_token_account: "JIRA_PROD_GRID_TOKEN"
  
  # API endpoints
  api_base_url: "https://itsm.hcloud.healthgrp.com.sg/rest/api/2"
  grid_base_url: "https://itsm.hcloud.healthgrp.com.sg/rest/idalko-grid/1.0/api/grid"
```

### **Staging/Development Environment:**
```yaml
# group_vars/staging/main.yml or group_vars/development/main.yml
jira_credentials:
  # CyberArk account names for credential retrieval
  username_account: "JIRA_UAT_USERNAME"
  password_account: "JIRA_UAT_PASSWORD"
  grid_token_account: "JIRA_UAT_GRID_TOKEN"
  
  # API endpoints
  api_base_url: "https://jsd-uat.hcloud.healthgrp.com.sg/rest/api/2"
  grid_base_url: "https://jsd-uat.hcloud.healthgrp.com.sg/rest/idalko-grid/1.0/api/grid"
```

---

## 🔄 **Credential Retrieval Implementation**

### **DNS Service Account Retrieval:**
```yaml
# DNS credential retrieval in phase_2_loading.yml
- name: "Retrieve DNS Service Account Credentials from CyberArk"
  cloud_cpe.cyberark_ccp.cyberark_credential:
    account: "DNS_SERVICE_ACCOUNT"
  register: dns_credentials_result
  no_log: true

- name: "Set DNS Connection Variables"
  set_fact:
    dns_connection_user: "{{ dns_credentials_result.credential.username }}"
    dns_connection_password: "{{ dns_credentials_result.credential.password }}"
  no_log: true
```

### **JIRA Credential Retrieval:**
```yaml
# JIRA credential retrieval in update_service_request.yml
- name: "Retrieve JIRA Credentials from CyberArk"
  cloud_cpe.cyberark_ccp.cyberark_credential:
    account: "{{ jira_credentials.username_account }}"
  register: jira_username_result
  no_log: true

- name: "Retrieve JIRA Password from CyberArk"
  cloud_cpe.cyberark_ccp.cyberark_credential:
    account: "{{ jira_credentials.password_account }}"
  register: jira_password_result
  no_log: true

- name: "Retrieve JIRA Grid Token from CyberArk"
  cloud_cpe.cyberark_ccp.cyberark_credential:
    account: "{{ jira_credentials.grid_token_account }}"
  register: jira_grid_token_result
  no_log: true
```

---

## 📋 **CyberArk Account Setup Requirements**

### **Account Configuration in CyberArk:**

#### **DNS Service Account:**
- **Account Name:** `DNS_SERVICE_ACCOUNT`
- **Username:** Service account username for DNS operations
- **Password:** Service account password
- **Safe:** DNS automation safe
- **Platform:** Windows Domain Account

#### **JIRA Production Accounts:**
- **Account Name:** `JIRA_PROD_USERNAME`
  - **Username:** JIRA production service account username
  - **Safe:** JIRA automation safe
  - **Platform:** Application Account

- **Account Name:** `JIRA_PROD_PASSWORD`
  - **Password:** JIRA production service account password
  - **Safe:** JIRA automation safe
  - **Platform:** Application Account

- **Account Name:** `JIRA_PROD_GRID_TOKEN`
  - **Password:** JIRA production Grid API token
  - **Safe:** JIRA automation safe
  - **Platform:** Application Account

#### **JIRA UAT Accounts:**
- **Account Name:** `JIRA_UAT_USERNAME`
  - **Username:** JIRA UAT service account username
  - **Safe:** JIRA automation safe
  - **Platform:** Application Account

- **Account Name:** `JIRA_UAT_PASSWORD`
  - **Password:** JIRA UAT service account password
  - **Safe:** JIRA automation safe
  - **Platform:** Application Account

- **Account Name:** `JIRA_UAT_GRID_TOKEN`
  - **Password:** JIRA UAT Grid API token
  - **Safe:** JIRA automation safe
  - **Platform:** Application Account

---

## 🔍 **Validation and Testing**

### **CyberArk Integration Validation:**
```yaml
# Validation included in JIRA integration validation
- name: "JIRA Validation - Retrieve JIRA Credentials from CyberArk"
  cloud_cpe.cyberark_ccp.cyberark_credential:
    account: "{{ jira_credentials.username_account }}"
  register: jira_validation_username_result
  no_log: true
  ignore_errors: true
```

### **Test Commands:**
```bash
# Test CyberArk credential retrieval
ansible-playbook main.yml \
  -e "action=verify domain=test.com hostname=test var_sr_number=SR-123456" \
  --tags="validation" \
  --check

# Test with specific environment
ansible-playbook main.yml \
  -e "environment=production action=verify domain=test.com hostname=test" \
  --limit="production_dns_servers"
```

---

## 🚨 **Error Handling**

### **Common CyberArk Errors:**

#### **Account Not Found:**
```
Error: Account 'DNS_SERVICE_ACCOUNT' not found in CyberArk
Solution:
1. Verify account name spelling
2. Check account exists in CyberArk safe
3. Verify AAP has access to the safe
```

#### **Authentication Failure:**
```
Error: Failed to authenticate with CyberArk
Solution:
1. Verify AAP service account has CyberArk access
2. Check CyberArk connectivity from AAP
3. Validate CyberArk configuration
```

#### **Permission Denied:**
```
Error: Permission denied accessing CyberArk account
Solution:
1. Verify AAP service account permissions
2. Check safe access permissions
3. Validate account platform settings
```

### **Error Handling in Playbooks:**
```yaml
# Graceful error handling
- name: "Retrieve Credentials from CyberArk"
  cloud_cpe.cyberark_ccp.cyberark_credential:
    account: "DNS_SERVICE_ACCOUNT"
  register: dns_credentials_result
  no_log: true
  
- name: "Handle Credential Retrieval Failure"
  fail:
    msg: |
      Failed to retrieve credentials from CyberArk
      Account: DNS_SERVICE_ACCOUNT
      Error: {{ dns_credentials_result.msg | default('Unknown error') }}
      Please verify CyberArk configuration and account access
  when: dns_credentials_result is failed
```

---

## 📊 **Security Benefits**

### **Enhanced Security Features:**
- ✅ **No hardcoded credentials** - All credentials retrieved dynamically
- ✅ **Centralized credential management** - Single source of truth in CyberArk
- ✅ **Automatic credential rotation** - CyberArk handles password changes
- ✅ **Audit trail** - Complete logging of credential access
- ✅ **Secure transmission** - Encrypted credential retrieval
- ✅ **No credential storage** - Credentials never stored in playbooks

### **Compliance Advantages:**
- ✅ **SOX compliance** - Segregation of duties and audit trails
- ✅ **PCI DSS compliance** - Secure credential handling
- ✅ **HIPAA compliance** - Protected credential access
- ✅ **ISO 27001 compliance** - Information security management

---

## 🔧 **Migration from Vault to CyberArk**

### **Changes Made in v2:**
1. **Removed Ansible Vault files** - No more vault.yml files
2. **Updated credential references** - Changed from vault variables to account names
3. **Added collection dependency** - CyberArk collection in requirements.yml
4. **Modified handlers** - Updated JIRA handlers to use CyberArk credentials
5. **Enhanced validation** - Added CyberArk credential validation

### **Before (Vault-based):**
```yaml
# Old approach
jira_credentials:
  username: "{{ vault_jira_prod_username }}"
  password: "{{ vault_jira_prod_password }}"
```

### **After (CyberArk-based):**
```yaml
# New approach
jira_credentials:
  username_account: "JIRA_PROD_USERNAME"
  password_account: "JIRA_PROD_PASSWORD"

# Runtime retrieval
- name: "Retrieve JIRA Credentials"
  cloud_cpe.cyberark_ccp.cyberark_credential:
    account: "{{ jira_credentials.username_account }}"
  register: jira_username_result
```

---

## 📞 **Support and Troubleshooting**

### **CyberArk Integration Support:**
- **CyberArk Team:** <EMAIL>
- **DNS Automation Team:** <EMAIL>
- **Infrastructure Team:** <EMAIL>

### **Troubleshooting Steps:**
1. **Verify Collection Installation:** Check CyberArk collection is installed
2. **Test CyberArk Connectivity:** Validate AAP can reach CyberArk
3. **Check Account Configuration:** Verify accounts exist in CyberArk
4. **Validate Permissions:** Ensure AAP has access to required safes
5. **Review Logs:** Check AAP execution logs for CyberArk errors

### **Monitoring Points:**
- **Credential Retrieval Success Rate** - Monitor CyberArk integration health
- **Authentication Failures** - Track failed credential retrievals
- **Performance Metrics** - Monitor credential retrieval response times
- **Error Patterns** - Identify recurring CyberArk issues

---

*This CyberArk integration guide is part of the DNS Management Automation v2 solution developed by the CES Operational Excellence Team following Operational Excellence Automation Framework (OXAF) patterns.*
