# =========================================================================
# DNS Management Automation v2 - UAT Quick Test Script
# =========================================================================
# This script provides quick test commands for User Acceptance Testing
# of the DNS Management PowerShell script on ADMT servers.
#
# Author: CES Operational Excellence Team
# Contributor: <PERSON><PERSON> (7409)
# Framework: Operational Excellence Automation Framework (OXAF)
# Version: 2.0
# =========================================================================

param(
    [Parameter(Mandatory=$true)]
    [string]$TestDomain,

    [Parameter(Mandatory=$false)]
    [string]$ScriptPath = "C:\ansscripts\set-dns-v2.ps1",

    [Parameter(Mandatory=$false)]
    [switch]$DryRunOnly,

    [Parameter(Mandatory=$false)]
    [switch]$SkipCleanup,

    [Parameter(Mandatory=$false)]
    [string]$TesterName = $env:USERNAME,

    [Parameter(Mandatory=$false)]
    [string]$TesterRole = "System Administrator",

    [Parameter(Mandatory=$false)]
    [string]$OutputPath = "C:\temp",

    [Parameter(Mandatory=$false)]
    [switch]$GenerateReport,

    [Parameter(Mandatory=$false)]
    [switch]$GeneratePDF
)

# =========================================================================
# UAT Test Configuration
# =========================================================================

$TestConfig = @{
    ScriptPath = $ScriptPath
    TestDomain = $TestDomain
    TestIpAddress = "*************"
    TestTTL = 300
    LogFile = "C:\temp\uat-dns-test-$(Get-Date -Format 'yyyyMMdd-HHmm').log"
    TestHostnamePrefix = "uat-test-$(Get-Date -Format 'MMdd-HHmm')"
}

$TestResults = @{
    TotalTests = 0
    PassedTests = 0
    FailedTests = 0
    TestDetails = @()
    TestEnvironment = @{
        TesterName = $TesterName
        TesterRole = $TesterRole
        TestDate = Get-Date
        TestStartTime = Get-Date
        TestEndTime = $null
        ServerName = $env:COMPUTERNAME
        OperatingSystem = (Get-CimInstance Win32_OperatingSystem).Caption
        PowerShellVersion = $PSVersionTable.PSVersion.ToString()
        DNSServerVersion = ""
        AvailableZones = @()
    }
    TestConfiguration = $TestConfig
}

# =========================================================================
# Helper Functions
# =========================================================================

function Write-TestHeader {
    param([string]$TestName)
    Write-Host "`n" -NoNewline
    Write-Host "="*60 -ForegroundColor Cyan
    Write-Host "TEST: $TestName" -ForegroundColor Yellow
    Write-Host "="*60 -ForegroundColor Cyan
}

function Write-TestResult {
    param(
        [string]$TestName,
        [bool]$Passed,
        [string]$Details = ""
    )

    $TestResults.TotalTests++

    if ($Passed) {
        $TestResults.PassedTests++
        Write-Host "RESULT: PASS" -ForegroundColor Green
    } else {
        $TestResults.FailedTests++
        Write-Host "RESULT: FAIL" -ForegroundColor Red
    }

    if ($Details) {
        Write-Host "DETAILS: $Details" -ForegroundColor Gray
    }

    $TestResults.TestDetails += @{
        Name = $TestName
        Passed = $Passed
        Details = $Details
        Timestamp = Get-Date
    }
}

function Test-ScriptExecution {
    param(
        [string]$TestName,
        [scriptblock]$TestScript,
        [string]$ExpectedPattern = "",
        [bool]$ShouldSucceed = $true
    )

    Write-TestHeader $TestName

    try {
        $result = & $TestScript
        $output = $result | Out-String

        Write-Host "Command Output:" -ForegroundColor Gray
        Write-Host $output -ForegroundColor White

        $success = if ($ExpectedPattern) {
            $output -match $ExpectedPattern
        } else {
            $LASTEXITCODE -eq 0 -or $result -ne $null
        }

        if ($ShouldSucceed) {
            Write-TestResult $TestName $success $output
        } else {
            Write-TestResult $TestName (-not $success) $output
        }

    } catch {
        $errorMsg = $_.Exception.Message
        Write-Host "ERROR: $errorMsg" -ForegroundColor Red
        Write-TestResult $TestName (-not $ShouldSucceed) $errorMsg
    }
}

# =========================================================================
# Report Generation Functions
# =========================================================================

function Generate-HTMLReport {
    param([hashtable]$TestResults, [string]$OutputPath)

    $reportPath = Join-Path $OutputPath "DNS_UAT_Report_$(Get-Date -Format 'yyyyMMdd_HHmm').html"

    $passRate = if ($TestResults.TotalTests -gt 0) {
        [math]::Round(($TestResults.PassedTests / $TestResults.TotalTests) * 100, 2)
    } else { 0 }

    $statusColor = if ($passRate -ge 80) { "green" } elseif ($passRate -ge 60) { "orange" } else { "red" }

    $htmlContent = @"
<!DOCTYPE html>
<html>
<head>
    <title>DNS Management Automation v2 - UAT Report</title>
    <style>
        body { font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif; margin: 20px; background-color: #f5f5f5; }
        .container { max-width: 1200px; margin: 0 auto; background-color: white; padding: 30px; border-radius: 8px; box-shadow: 0 2px 10px rgba(0,0,0,0.1); }
        .header { text-align: center; border-bottom: 3px solid #0078d4; padding-bottom: 20px; margin-bottom: 30px; }
        .header h1 { color: #0078d4; margin: 0; font-size: 28px; }
        .header h2 { color: #666; margin: 5px 0; font-size: 18px; font-weight: normal; }
        .summary { display: grid; grid-template-columns: repeat(auto-fit, minmax(200px, 1fr)); gap: 20px; margin-bottom: 30px; }
        .summary-card { background: linear-gradient(135deg, #0078d4, #106ebe); color: white; padding: 20px; border-radius: 8px; text-align: center; }
        .summary-card h3 { margin: 0 0 10px 0; font-size: 16px; }
        .summary-card .value { font-size: 32px; font-weight: bold; margin: 10px 0; }
        .pass-rate { background: linear-gradient(135deg, $statusColor, $(if($statusColor -eq 'green'){'#28a745'}elseif($statusColor -eq 'orange'){'#fd7e14'}else{'#dc3545'}}) !important; }
        .section { margin-bottom: 30px; }
        .section h2 { color: #0078d4; border-bottom: 2px solid #e9ecef; padding-bottom: 10px; }
        .info-grid { display: grid; grid-template-columns: repeat(auto-fit, minmax(300px, 1fr)); gap: 20px; }
        .info-card { background-color: #f8f9fa; padding: 15px; border-radius: 6px; border-left: 4px solid #0078d4; }
        .info-card h4 { margin: 0 0 10px 0; color: #0078d4; }
        .test-results { width: 100%; border-collapse: collapse; margin-top: 20px; }
        .test-results th, .test-results td { padding: 12px; text-align: left; border-bottom: 1px solid #ddd; }
        .test-results th { background-color: #0078d4; color: white; font-weight: 600; }
        .test-results tr:nth-child(even) { background-color: #f8f9fa; }
        .pass { color: #28a745; font-weight: bold; }
        .fail { color: #dc3545; font-weight: bold; }
        .timestamp { color: #666; font-size: 12px; }
        .footer { text-align: center; margin-top: 40px; padding-top: 20px; border-top: 1px solid #e9ecef; color: #666; }
        .details { max-width: 400px; overflow: hidden; text-overflow: ellipsis; white-space: nowrap; }
        @media print { body { background-color: white; } .container { box-shadow: none; } }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1>DNS Management Automation v2</h1>
            <h2>User Acceptance Test Report</h2>
            <p><strong>Framework:</strong> Operational Excellence Automation Framework (OXAF)</p>
        </div>

        <div class="summary">
            <div class="summary-card">
                <h3>Total Tests</h3>
                <div class="value">$($TestResults.TotalTests)</div>
            </div>
            <div class="summary-card">
                <h3>Passed</h3>
                <div class="value">$($TestResults.PassedTests)</div>
            </div>
            <div class="summary-card">
                <h3>Failed</h3>
                <div class="value">$($TestResults.FailedTests)</div>
            </div>
            <div class="summary-card pass-rate">
                <h3>Pass Rate</h3>
                <div class="value">$passRate%</div>
            </div>
        </div>

        <div class="section">
            <h2>Test Environment Information</h2>
            <div class="info-grid">
                <div class="info-card">
                    <h4>Test Execution Details</h4>
                    <p><strong>Tester:</strong> $($TestResults.TestEnvironment.TesterName)</p>
                    <p><strong>Role:</strong> $($TestResults.TestEnvironment.TesterRole)</p>
                    <p><strong>Test Date:</strong> $($TestResults.TestEnvironment.TestDate.ToString('yyyy-MM-dd'))</p>
                    <p><strong>Start Time:</strong> $($TestResults.TestEnvironment.TestStartTime.ToString('HH:mm:ss'))</p>
                    <p><strong>End Time:</strong> $($TestResults.TestEnvironment.TestEndTime.ToString('HH:mm:ss'))</p>
                </div>
                <div class="info-card">
                    <h4>System Environment</h4>
                    <p><strong>Server:</strong> $($TestResults.TestEnvironment.ServerName)</p>
                    <p><strong>OS:</strong> $($TestResults.TestEnvironment.OperatingSystem)</p>
                    <p><strong>PowerShell:</strong> $($TestResults.TestEnvironment.PowerShellVersion)</p>
                    <p><strong>Test Domain:</strong> $($TestResults.TestConfiguration.TestDomain)</p>
                </div>
                <div class="info-card">
                    <h4>Test Configuration</h4>
                    <p><strong>Script Path:</strong> $($TestResults.TestConfiguration.ScriptPath)</p>
                    <p><strong>Test IP:</strong> $($TestResults.TestConfiguration.TestIpAddress)</p>
                    <p><strong>TTL:</strong> $($TestResults.TestConfiguration.TestTTL) seconds</p>
                    <p><strong>Log File:</strong> $($TestResults.TestConfiguration.LogFile)</p>
                </div>
            </div>
        </div>

        <div class="section">
            <h2>Test Results Details</h2>
            <table class="test-results">
                <thead>
                    <tr>
                        <th>Test Case</th>
                        <th>Status</th>
                        <th>Execution Time</th>
                        <th>Details</th>
                    </tr>
                </thead>
                <tbody>
"@

    foreach ($test in $TestResults.TestDetails) {
        $statusClass = if ($test.Passed) { "pass" } else { "fail" }
        $statusText = if ($test.Passed) { "PASS" } else { "FAIL" }
        $details = if ($test.Details.Length -gt 100) { $test.Details.Substring(0, 100) + "..." } else { $test.Details }

        $htmlContent += @"
                    <tr>
                        <td>$($test.Name)</td>
                        <td class="$statusClass">$statusText</td>
                        <td class="timestamp">$($test.Timestamp.ToString('HH:mm:ss'))</td>
                        <td class="details" title="$($test.Details)">$details</td>
                    </tr>
"@
    }

    $htmlContent += @"
                </tbody>
            </table>
        </div>

        <div class="footer">
            <p>Generated by DNS Management Automation v2 UAT Script</p>
            <p>Operational Excellence Automation Framework (OXAF)</p>
            <p>Report generated on: $(Get-Date -Format 'yyyy-MM-dd HH:mm:ss')</p>
        </div>
    </div>
</body>
</html>
"@

    $htmlContent | Out-File -FilePath $reportPath -Encoding UTF8
    return $reportPath
}

function Generate-PDFReport {
    param([string]$HtmlPath, [string]$OutputPath)

    $pdfPath = $HtmlPath -replace '\.html$', '.pdf'

    try {
        # Try using wkhtmltopdf if available
        if (Get-Command wkhtmltopdf -ErrorAction SilentlyContinue) {
            & wkhtmltopdf --page-size A4 --margin-top 20mm --margin-bottom 20mm --margin-left 15mm --margin-right 15mm $HtmlPath $pdfPath
            return $pdfPath
        }

        # Alternative: Use Chrome/Edge in headless mode if available
        $chromePath = @(
            "${env:ProgramFiles}\Google\Chrome\Application\chrome.exe",
            "${env:ProgramFiles(x86)}\Google\Chrome\Application\chrome.exe",
            "${env:ProgramFiles}\Microsoft\Edge\Application\msedge.exe",
            "${env:ProgramFiles(x86)}\Microsoft\Edge\Application\msedge.exe"
        ) | Where-Object { Test-Path $_ } | Select-Object -First 1

        if ($chromePath) {
            $chromeArgs = @(
                "--headless",
                "--disable-gpu",
                "--print-to-pdf=`"$pdfPath`"",
                "--print-to-pdf-no-header",
                "--no-margins",
                "`"$HtmlPath`""
            )
            & $chromePath $chromeArgs
            Start-Sleep -Seconds 3  # Wait for PDF generation

            if (Test-Path $pdfPath) {
                return $pdfPath
            }
        }

        # Fallback: Create instructions for manual PDF generation
        $instructionsPath = $HtmlPath -replace '\.html$', '_PDF_Instructions.txt'
        @"
PDF Generation Instructions
===========================

The HTML report has been generated successfully at:
$HtmlPath

To convert to PDF, you can use one of these methods:

Method 1: Browser Print to PDF
1. Open the HTML file in any web browser
2. Press Ctrl+P (or Cmd+P on Mac)
3. Select "Save as PDF" as the destination
4. Click "Save" and choose your desired location

Method 2: Install wkhtmltopdf
1. Download wkhtmltopdf from: https://wkhtmltopdf.org/downloads.html
2. Install and add to PATH
3. Run: wkhtmltopdf "$HtmlPath" "$pdfPath"

Method 3: Use Chrome/Edge command line
1. Ensure Chrome or Edge is installed
2. Run: chrome --headless --disable-gpu --print-to-pdf="$pdfPath" "$HtmlPath"

The HTML report can be viewed directly and contains all the same information
that would be in the PDF format.
"@ | Out-File -FilePath $instructionsPath -Encoding UTF8

        Write-Warning "PDF generation tools not found. Instructions created at: $instructionsPath"
        return $instructionsPath

    } catch {
        Write-Warning "PDF generation failed: $($_.Exception.Message)"
        return $null
    }
}

# =========================================================================
# Pre-Test Validation
# =========================================================================

Write-Host "DNS Management Automation v2 - UAT Quick Test Script" -ForegroundColor Magenta
Write-Host "Framework: Operational Excellence Automation Framework (OXAF)" -ForegroundColor Magenta
Write-Host "Test Domain: $($TestConfig.TestDomain)" -ForegroundColor Magenta
Write-Host "Script Path: $($TestConfig.ScriptPath)" -ForegroundColor Magenta
Write-Host "Dry Run Only: $($DryRunOnly.IsPresent)" -ForegroundColor Magenta

# Validate prerequisites
if (-not (Test-Path $TestConfig.ScriptPath)) {
    Write-Error "DNS script not found at: $($TestConfig.ScriptPath)"
    exit 1
}

if (-not (Get-Module -ListAvailable -Name DnsServer)) {
    Write-Error "DnsServer PowerShell module not available"
    exit 1
}

# Create temp directory for logs
$tempDir = Split-Path $TestConfig.LogFile -Parent
if (-not (Test-Path $tempDir)) {
    New-Item -Path $tempDir -ItemType Directory -Force | Out-Null
}

# =========================================================================
# Test Execution
# =========================================================================

# Test 1: Script Help and Parameter Validation
Test-ScriptExecution "Script Help Display" {
    Get-Help $TestConfig.ScriptPath -ErrorAction Stop
} "SYNOPSIS|DESCRIPTION"

# Test 2: Invalid Parameter Test
Test-ScriptExecution "Invalid Action Parameter" {
    & $TestConfig.ScriptPath -Action "invalid" -Domain $TestConfig.TestDomain -HostName "test" 2>&1
} "ValidateSet|parameter" $false

# Test 3: DNS Record Verification (Non-existent)
Test-ScriptExecution "Verify Non-existent Record" {
    & $TestConfig.ScriptPath -Action "verify" -Domain $TestConfig.TestDomain -HostName "nonexistent-record-test" -Verbose
} "NOT FOUND|ZONE NOT FOUND"

# Test 4: Zone Not Found Test
Test-ScriptExecution "Zone Not Found Handling" {
    & $TestConfig.ScriptPath -Action "verify" -Domain "nonexistent-zone.test.local" -HostName "test-server" -Verbose
} "ZONE NOT FOUND"

# Test 5: Dry Run Add Operation
$testHostname = "$($TestConfig.TestHostnamePrefix)-dryrun"
Test-ScriptExecution "Dry Run Add Operation" {
    & $TestConfig.ScriptPath -Action "add" -Domain $TestConfig.TestDomain -HostName $testHostname -IpAddress $TestConfig.TestIpAddress -TTL $TestConfig.TestTTL -DryRun -Verbose
} "DRY RUN|Would add"

if (-not $DryRunOnly) {
    # Test 6: Actual Add Operation
    $testHostname = "$($TestConfig.TestHostnamePrefix)-actual"
    Test-ScriptExecution "Actual Add Operation" {
        & $TestConfig.ScriptPath -Action "add" -Domain $TestConfig.TestDomain -HostName $testHostname -IpAddress $TestConfig.TestIpAddress -TTL $TestConfig.TestTTL -LogFile $TestConfig.LogFile -Verbose
    } "SUCCESS|Added"

    # Test 7: Verify Created Record
    Test-ScriptExecution "Verify Created Record" {
        & $TestConfig.ScriptPath -Action "verify" -Domain $TestConfig.TestDomain -HostName $testHostname -Verbose
    } "FOUND|EXISTS"

    # Test 8: Sync Operation
    Test-ScriptExecution "Sync Operation" {
        & $TestConfig.ScriptPath -Action "sync" -Domain $TestConfig.TestDomain -HostName $testHostname -Verbose
    } "SYNC|NO ACTION NEEDED"

    # Test 9: Add Existing Record (should handle gracefully)
    Test-ScriptExecution "Add Existing Record" {
        & $TestConfig.ScriptPath -Action "add" -Domain $TestConfig.TestDomain -HostName $testHostname -IpAddress $TestConfig.TestIpAddress -Verbose
    } "ALREADY EXISTS|EXISTS"

    # Test 10: Remove Record (Dry Run)
    Test-ScriptExecution "Dry Run Remove Operation" {
        & $TestConfig.ScriptPath -Action "remove" -Domain $TestConfig.TestDomain -HostName $testHostname -DryRun -Verbose
    } "DRY RUN|Would remove"

    # Test 11: Actual Remove Operation
    if (-not $SkipCleanup) {
        Test-ScriptExecution "Actual Remove Operation" {
            & $TestConfig.ScriptPath -Action "remove" -Domain $TestConfig.TestDomain -HostName $testHostname -Verbose
        } "SUCCESS|Removed"

        # Test 12: Verify Record Removal
        Test-ScriptExecution "Verify Record Removal" {
            & $TestConfig.ScriptPath -Action "verify" -Domain $TestConfig.TestDomain -HostName $testHostname -Verbose
        } "NOT FOUND"
    }
}

# Test 13: Log File Creation
if (Test-Path $TestConfig.LogFile) {
    Test-ScriptExecution "Log File Creation" {
        Get-Content $TestConfig.LogFile | Select-Object -Last 5
    } "DNS Management|AUDIT|INFO"
} else {
    Write-TestResult "Log File Creation" $false "Log file not created"
}

# Test 14: Performance Test
Test-ScriptExecution "Performance Test" {
    $executionTime = Measure-Command {
        & $TestConfig.ScriptPath -Action "verify" -Domain $TestConfig.TestDomain -HostName "performance-test" -Verbose
    }

    Write-Host "Execution Time: $($executionTime.TotalSeconds) seconds"

    # Should complete within 30 seconds
    $executionTime.TotalSeconds -lt 30
} ""

# =========================================================================
# Test Results Summary and Report Generation
# =========================================================================

# Update test end time
$TestResults.TestEnvironment.TestEndTime = Get-Date

# Collect additional environment information
try {
    $TestResults.TestEnvironment.DNSServerVersion = (Get-WindowsFeature -Name DNS).InstallState
    $TestResults.TestEnvironment.AvailableZones = @(Get-DnsServerZone | Select-Object -ExpandProperty ZoneName)
} catch {
    $TestResults.TestEnvironment.DNSServerVersion = "Unable to determine"
    $TestResults.TestEnvironment.AvailableZones = @("Unable to enumerate")
}

Write-Host "`n" -NoNewline
Write-Host "="*60 -ForegroundColor Magenta
Write-Host "UAT TEST RESULTS SUMMARY" -ForegroundColor Yellow
Write-Host "="*60 -ForegroundColor Magenta

Write-Host "Total Tests: $($TestResults.TotalTests)" -ForegroundColor White
Write-Host "Passed: $($TestResults.PassedTests)" -ForegroundColor Green
Write-Host "Failed: $($TestResults.FailedTests)" -ForegroundColor Red

$passRate = if ($TestResults.TotalTests -gt 0) {
    [math]::Round(($TestResults.PassedTests / $TestResults.TotalTests) * 100, 2)
} else { 0 }

Write-Host "Pass Rate: $passRate%" -ForegroundColor $(if ($passRate -ge 80) { "Green" } elseif ($passRate -ge 60) { "Yellow" } else { "Red" })

if ($TestResults.FailedTests -gt 0) {
    Write-Host "`nFailed Tests:" -ForegroundColor Red
    $TestResults.TestDetails | Where-Object { -not $_.Passed } | ForEach-Object {
        Write-Host "  - $($_.Name): $($_.Details)" -ForegroundColor Red
    }
}

Write-Host "`nTest Log File: $($TestConfig.LogFile)" -ForegroundColor Gray

# Generate Reports
if ($GenerateReport -or $GeneratePDF) {
    Write-Host "`n" -NoNewline
    Write-Host "="*60 -ForegroundColor Cyan
    Write-Host "GENERATING REPORTS" -ForegroundColor Yellow
    Write-Host "="*60 -ForegroundColor Cyan

    try {
        # Ensure output directory exists
        if (-not (Test-Path $OutputPath)) {
            New-Item -Path $OutputPath -ItemType Directory -Force | Out-Null
        }

        # Generate HTML Report
        Write-Host "Generating HTML report..." -ForegroundColor White
        $htmlReportPath = Generate-HTMLReport -TestResults $TestResults -OutputPath $OutputPath
        Write-Host "HTML Report: $htmlReportPath" -ForegroundColor Green

        # Generate PDF Report if requested
        if ($GeneratePDF) {
            Write-Host "Generating PDF report..." -ForegroundColor White
            $pdfReportPath = Generate-PDFReport -HtmlPath $htmlReportPath -OutputPath $OutputPath

            if ($pdfReportPath -and (Test-Path $pdfReportPath) -and $pdfReportPath.EndsWith('.pdf')) {
                Write-Host "PDF Report: $pdfReportPath" -ForegroundColor Green
            } elseif ($pdfReportPath) {
                Write-Host "PDF Instructions: $pdfReportPath" -ForegroundColor Yellow
            } else {
                Write-Host "PDF generation failed. HTML report is available." -ForegroundColor Yellow
            }
        }

        # Generate JSON data file for integration
        $jsonReportPath = Join-Path $OutputPath "DNS_UAT_Results_$(Get-Date -Format 'yyyyMMdd_HHmm').json"
        $TestResults | ConvertTo-Json -Depth 10 | Out-File -FilePath $jsonReportPath -Encoding UTF8
        Write-Host "JSON Data: $jsonReportPath" -ForegroundColor Green

        # Generate CSV summary for spreadsheet import
        $csvReportPath = Join-Path $OutputPath "DNS_UAT_Summary_$(Get-Date -Format 'yyyyMMdd_HHmm').csv"
        $TestResults.TestDetails | Select-Object Name, Passed, Timestamp, @{Name='Details';Expression={$_.Details -replace "`n|`r",""}} |
            Export-Csv -Path $csvReportPath -NoTypeInformation -Encoding UTF8
        Write-Host "CSV Summary: $csvReportPath" -ForegroundColor Green

    } catch {
        Write-Host "Report generation failed: $($_.Exception.Message)" -ForegroundColor Red
    }
}

# =========================================================================
# Cleanup Recommendations
# =========================================================================

if (-not $SkipCleanup -and -not $DryRunOnly) {
    Write-Host "`nCleanup Commands (run if needed):" -ForegroundColor Yellow
    Write-Host "Get-DnsServerResourceRecord -ZoneName '$($TestConfig.TestDomain)' | Where-Object {`$_.HostName -like '*uat-test*'} | Remove-DnsServerResourceRecord -Force" -ForegroundColor Gray
    Write-Host "Remove-Item '$($TestConfig.LogFile)' -ErrorAction SilentlyContinue" -ForegroundColor Gray
}

Write-Host "`nUAT Quick Test Completed!" -ForegroundColor Magenta

# Return appropriate exit code
exit $(if ($TestResults.FailedTests -eq 0) { 0 } else { 1 })
