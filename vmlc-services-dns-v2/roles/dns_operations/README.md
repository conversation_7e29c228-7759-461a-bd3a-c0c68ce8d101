# DNS Operations Role

## Overview

This role handles DNS record operations (add, remove, update, verify) for various record types (A, PTR, CNAME) across multiple domains. It supports both single-domain and multi-domain operations with a 1:1 relationship between hostnames and domains.

## Author

CES Operational Excellence Team

## Contributors

<PERSON> (7409)

## Usage

This role is called by the main playbook:

```yaml
- name: Include DNS operations role
  include_role:
    name: dns_operations
```

The role's main.yml file will handle routing to the appropriate task file based on the operation parameter and whether single or multi-domain operations are being performed.

## Parameters

### Common Parameters
- **operation**: The operation to perform (add, remove, update, verify)
- **record_type**: The type of DNS record (a, ptr, cname)

### Single Domain Operation Parameters
- **hostname**: The hostname part of the DNS record
- **domain**: The domain where the DNS record resides or will be created

### Multi-Domain Operation Parameters
- **hostnames**: Comma-separated list of hostnames (must match domains 1:1)
- **domains**: Comma-separated list of domains

### Operation-Specific Parameters
- **ip_address**: The IP address for A or PTR records
- **cname_target**: The target hostname for CNAME records
- **ttl**: Time-to-Live value in seconds (default: 3600)
- **description**: Description for the DNS record
- **manage_ptr**: Whether to automatically manage PTR records for A records (default: true)
- **force_remove**: Whether to force removal of DNS records for live hosts (default: false)

### Automatically Determined Parameters
- **admt_server**: The ADMT server to use for the operation (determined based on domain)
- **dns_server**: The DNS server to use for the operation (determined based on domain)
- **dc_username**: The username to use for authentication (determined based on domain by the credential_selection role)
- **dc_password**: The password to use for authentication (determined based on domain by the credential_selection role)

## Task Files

- **main.yml**: Entry point that handles domain routing and validation
- **process_domain.yml**: Processes each domain in multi-domain operations
- **process_domain_async.yml**: Processes each domain asynchronously in multi-domain operations
- **process_single_async_result.yml**: Processes the result of a single asynchronous operation
- **add.yml**: Adds DNS records
- **remove.yml**: Removes DNS records
- **update.yml**: Updates DNS records
- **verify.yml**: Verifies DNS records
- **add_async.yml**: Adds DNS records asynchronously
- **remove_async.yml**: Removes DNS records asynchronously
- **update_async.yml**: Updates DNS records asynchronously
- **verify_async.yml**: Verifies DNS records asynchronously

## PowerShell Scripts

The role uses PowerShell scripts to perform the actual DNS operations on the target servers. These scripts are executed on the ADMT servers with the necessary permissions to access the DNS servers.

## Error Handling

The role includes comprehensive error handling to ensure that operations are performed correctly and that any errors are properly reported back to the user.

## Logging

All operations are logged for auditing and troubleshooting purposes. Logs can be stored locally, uploaded to a central repository, or emailed to specified recipients.

## Report Generation

The role can generate PDF reports with certificates of successful operations. These reports can be stored locally or emailed to specified recipients.
