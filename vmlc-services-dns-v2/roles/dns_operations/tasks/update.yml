---
# Tasks for updating DNS records
# Author: CES Operational Excellence Team
# Contributors: <PERSON> (7409)

- name: Copy DNS PowerShell script to ADMT server
  win_copy:
    src: "../../scripts/set-dns.ps1"
    dest: "C:\\Temp\\set-dns.ps1"
  delegate_to: "{{ domain_config[domain].admt_server }}"
  vars:
    ansible_connection: "winrm"
    ansible_winrm_server_cert_validation: "ignore"
    ansible_user: "{{ domain_config[domain].dns_username }}"
    ansible_password: "{{ domain_config[domain].dns_password }}"

- name: Set log paths
  ansible.builtin.set_fact:
    ps_log_path: "C:\\Temp\\dns_management_logs\\{{ ansible_date_time.date }}\\{{ hostname }}.{{ domain }}_{{ record_type }}_{{ operation }}.log"
    ansible_log_path: "{{ playbook_dir }}/../logs/ansible/{{ ansible_date_time.date }}_{{ hostname }}.{{ domain }}_{{ record_type }}_{{ operation }}.log"

- name: Ensure PowerShell log directory exists
  win_file:
    path: "C:\\Temp\\dns_management_logs\\{{ ansible_date_time.date }}"
    state: directory
  delegate_to: "{{ domain_config[domain].admt_server }}"
  vars:
    ansible_connection: "winrm"
    ansible_winrm_server_cert_validation: "ignore"
    ansible_user: "{{ domain_config[domain].dns_username }}"
    ansible_password: "{{ domain_config[domain].dns_password }}"

- name: Update DNS record
  win_shell: |
    $params = @{
      Operation = 'Update'
      RecordType = '{{ record_type | upper }}'
      Hostname = '{{ hostname }}'
      Domain = '{{ domain }}'
      Description = '{{ description }}'
      TTL = {{ ttl }}
      LogPath = '{{ ps_log_path }}'
      LogLevel = '{{ log_level | default("Info") }}'
      DNSServer = '{{ domain_config[domain].dns_server }}'
      {% if record_type | lower == 'a' and manage_ptr | bool %}
      PTRDNSServer = '{{ domain_config[domain].ptr_dns_server }}'
      {% endif %}
    }

    {% if record_type | lower == 'a' or record_type | lower == 'ptr' %}
    $params.Add('IPAddress', '{{ ip_address }}')
    {% endif %}
    {% if record_type | lower == 'a' %}
    $params.Add('ManagePTR', ${{ manage_ptr | lower }})
    {% elif record_type | lower == 'cname' %}
    $params.Add('Target', '{{ cname_target }}')
    {% endif %}

    C:\\Temp\\set-dns.ps1 @params -AsJson
  args:
    chdir: C:\Temp\
  become: true
  become_method: runas
  become_flags: logon_type=new_credentials logon_flags=netcredentials_only
  register: dns_result
  delegate_to: "{{ domain_config[domain].admt_server }}"
  vars:
    ansible_connection: "winrm"
    ansible_winrm_server_cert_validation: "ignore"
    ansible_user: "{{ domain_config[domain].dns_username }}"
    ansible_password: "{{ domain_config[domain].dns_password }}"
    ansible_become_user: "{{ domain_config[domain].dns_username }}"
    ansible_become_password: "{{ domain_config[domain].dns_password }}"
  no_log: true
  failed_when: false

- name: Log Ansible operation details
  ansible.builtin.copy:
    content: |
      Operation: {{ operation }}
      Record Type: {{ record_type | upper }}
      Hostname: {{ hostname }}
      Domain: {{ domain }}
      FQDN: {{ hostname }}.{{ domain }}
      {% if record_type | lower == 'a' or record_type | lower == 'ptr' %}
      IP Address: {{ ip_address }}
      {% elif record_type | lower == 'cname' %}
      Target: {{ cname_target }}
      {% endif %}
      ADMT Server: {{ domain_config[domain].admt_server }}
      DNS Server: {{ domain_config[domain].dns_server }}
      PowerShell Log: {{ ps_log_path }}
      Timestamp: {{ ansible_date_time.iso8601 }}

      Result: {{ dns_result | to_nice_json }}
    dest: "{{ ansible_log_path }}"
    mode: '0644'
  delegate_to: localhost
  vars:
    ansible_connection: "local"

- name: Check for PowerShell execution errors
  ansible.builtin.fail:
    msg: "PowerShell execution failed: {{ dns_result.stderr }}"
  when: dns_result.stderr is defined and dns_result.stderr != ''

- name: Parse JSON result
  ansible.builtin.set_fact:
    dns_operation_result: "{{ dns_result.stdout | from_json }}"
  when: dns_result.stdout is defined and dns_result.stdout != ''

- name: Set error result if JSON parsing failed
  ansible.builtin.set_fact:
    dns_operation_result:
      success: false
      message: "Failed to parse PowerShell output as JSON: {{ dns_result.stdout }}"
      error: true
  when: dns_result.stdout is not defined or dns_result.stdout == '' or dns_result.rc != 0

- name: Display operation result
  ansible.builtin.debug:
    var: dns_operation_result

- name: Retrieve PowerShell log file
  fetch:
    src: "{{ ps_log_path }}"
    dest: "{{ playbook_dir }}/../logs/powershell/"
    flat: true
  delegate_to: "{{ domain_config[domain].admt_server }}"
  vars:
    ansible_connection: "winrm"
    ansible_winrm_server_cert_validation: "ignore"
    ansible_user: "{{ domain_config[domain].dns_username }}"
    ansible_password: "{{ domain_config[domain].dns_password }}"
  ignore_errors: true
