---
# Tasks for verifying DNS records asynchronously
# Author: CES Operational Excellence Team
# Contributors: <PERSON> Bin <PERSON> (7409)

- name: Copy DNS PowerShell script to ADMT server
  win_copy:
    src: "../../scripts/set-dns.ps1"
    dest: "C:\\Temp\\set-dns.ps1"
  delegate_to: "{{ admt_server }}"
  vars:
    ansible_connection: "winrm"
    ansible_winrm_server_cert_validation: "ignore"

- name: Set log paths
  ansible.builtin.set_fact:
    ps_log_path: "C:\\Temp\\dns_management_logs\\{{ ansible_date_time.date }}\\{{ hostname }}.{{ domain }}_{{ record_type }}_{{ operation }}.log"
    ansible_log_path: "{{ playbook_dir }}/../logs/ansible/{{ ansible_date_time.date }}_{{ hostname }}.{{ domain }}_{{ record_type }}_{{ operation }}.log"
    report_dir: "{{ playbook_dir }}/../reports"
    report_filename: "dns_operation_{{ operation }}_{{ record_type }}_{{ hostname }}.{{ domain }}_{{ ansible_date_time.date }}.pdf"

- name: Ensure PowerShell log directory exists
  win_file:
    path: "C:\\Temp\\dns_management_logs\\{{ ansible_date_time.date }}"
    state: directory
  delegate_to: "{{ admt_server }}"
  vars:
    ansible_connection: "winrm"
    ansible_winrm_server_cert_validation: "ignore"

- name: Verify DNS record (async)
  win_shell: |
    $params = @{
      Operation = 'Verify'
      RecordType = '{{ record_type | upper }}'
      Hostname = '{{ hostname }}'
      Domain = '{{ domain }}'
      LogPath = '{{ ps_log_path }}'
      LogLevel = '{{ log_level | default("Info") }}'
      DNSServer = '{{ dns_server }}'
      {% if record_type | lower == 'a' and manage_ptr | bool %}
      PTRDNSServer = '{{ ptr_dns_server }}'
      {% endif %}
    }

    {% if record_type | lower == 'a' or record_type | lower == 'ptr' %}
    $params.Add('IPAddress', '{{ ip_address }}')
    {% if record_type | lower == 'a' %}
    $params.Add('ManagePTR', ${{ manage_ptr | lower }})
    {% endif %}
    {% elif record_type | lower == 'cname' %}
    $params.Add('Target', '{{ cname_target }}')
    {% endif %}

    C:\\Temp\\set-dns.ps1 @params -AsJson
  args:
    chdir: C:\Temp\
  become: true
  become_method: runas
  become_flags: logon_type=new_credentials logon_flags=netcredentials_only
  vars:
    ansible_become_user: "{{ dc_username }}"
    ansible_become_password: "{{ dc_password }}"
  no_log: true
  register: dns_async_result
  delegate_to: "{{ admt_server }}"
  vars:
    ansible_connection: "winrm"
    ansible_winrm_server_cert_validation: "ignore"
  no_log: true
  failed_when: false
  async: 3600  # 1 hour timeout
  poll: 0  # Don't wait for completion

- name: Create additional info dictionary
  ansible.builtin.set_fact:
    additional_info:
      domain: "{{ domain }}"
      hostname: "{{ hostname }}"
      record_type: "{{ record_type }}"
      operation: "{{ operation }}"
      ps_log_path: "{{ ps_log_path }}"
      ansible_log_path: "{{ ansible_log_path }}"
      report_path: "{{ report_dir }}/{{ report_filename }}"
      timestamp: "{{ ansible_date_time.iso8601 }}"
      fqdn: "{{ hostname }}.{{ domain }}"
      dns_server: "{{ dns_server }}"
      admt_server: "{{ admt_server }}"
      log_level: "{{ log_level | default('Info') }}"

- name: Register domain and hostname for async task
  ansible.builtin.set_fact:
    dns_async_result: "{{ dns_async_result | combine(additional_info) }}"
