---
# Process a single async result
# Author: CES Operational Excellence Team
# Contributors: <PERSON> (7409)

- name: Get async job result
  ansible.builtin.async_status:
    jid: "{{ current_async_result.ansible_job_id }}"
  register: job_status

- name: Log Ansible operation details
  ansible.builtin.copy:
    content: |
      Operation: {{ current_async_result.operation }}
      Record Type: {{ current_async_result.record_type | upper }}
      Hostname: {{ current_async_result.hostname }}
      Domain: {{ current_async_result.domain }}
      FQDN: {{ current_async_result.hostname }}.{{ current_async_result.domain }}
      {% if current_async_result.record_type | lower == 'a' or current_async_result.record_type | lower == 'ptr' %}
      IP Address: {{ ip_address }}
      {% elif current_async_result.record_type | lower == 'cname' %}
      Target: {{ cname_target }}
      {% endif %}
      TTL: {{ ttl }}
      Description: {{ description }}
      PowerShell Log: {{ current_async_result.ps_log_path }}

      Result: {{ job_status.stdout | to_nice_json }}
    dest: "{{ current_async_result.ansible_log_path }}"
    mode: '0644'
  when: job_status.finished | bool

- name: Retrieve PowerShell log file
  fetch:
    src: "{{ current_async_result.ps_log_path }}"
    dest: "{{ playbook_dir }}/../logs/powershell/"
    flat: true
  delegate_to: "{{ domain_config[current_async_result.domain].admt_server }}"
  ignore_errors: true
  when: job_status.finished | bool

- name: Add to consolidated results and update progress
  ansible.builtin.set_fact:
    consolidated_results: "{{ consolidated_results + [{ 'domain': current_async_result.domain, 'hostname': current_async_result.hostname, 'success': job_status.stdout | from_json | json_query('success'), 'changed': job_status.stdout | from_json | json_query('changed'), 'message': job_status.stdout | from_json | json_query('message'), 'report_path': current_async_result.report_path }] }}"
    processed_count: "{{ processed_count | int + 1 }}"
  when: job_status.finished | bool

- name: Display individual result progress
  ansible.builtin.debug:
    msg: |
      Processing: {{ processed_count }}/{{ total_async_count }} ({{ (processed_count | int * 100) // total_async_count | int }}%)
      Domain: {{ current_async_result.domain }}
      Hostname: {{ current_async_result.hostname }}
      Status: {{ 'Success' if job_status.stdout | from_json | json_query('success') else 'Failed' }}
      Changed: {{ 'Yes' if job_status.stdout | from_json | json_query('changed') else 'No' }}
  when: job_status.finished | bool and show_progress | default(true) | bool
