---
# Process domain tasks asynchronously
# Author: CES Operational Excellence Team
# Contributors: <PERSON> (7409)

- name: Set current domain, hostname, and IP address for this iteration
  ansible.builtin.set_fact:
    domain: "{{ current_domain }}"
    hostname: "{{ domain_hostname_map[current_domain] }}"
    ip_address: "{{ domain_ip_map[current_domain] | default('') if record_type | lower in ['a', 'ptr'] and domain_ip_map is defined else ip_address | default('') }}"

- name: Validate current domain exists in domain configuration
  ansible.builtin.assert:
    that:
      - current_domain in domain_config
    fail_msg: "Domain '{{ current_domain }}' not found in unified_domain_config.yml configuration"

- name: Set domain information from unified configuration for current domain
  ansible.builtin.set_fact:
    domain_info: "{{ domain_config[current_domain] }}"

- name: Set connection parameters for ADMT server
  ansible.builtin.set_fact:
    ansible_connection: "winrm"
    ansible_winrm_server_cert_validation: "ignore"
    ansible_user: "{{ domain_config[current_domain].dns_username }}"
    ansible_password: "{{ domain_config[current_domain].dns_password }}"
  no_log: true

- name: Set ADMT server, DNS server, and credentials for current domain
  ansible.builtin.set_fact:
    admt_server: "{{ domain_config[current_domain].admt_server }}"
    dns_server: "{{ domain_config[current_domain].dns_server }}"
    dc_server: "{{ domain_config[current_domain].dns_server }}"  # Set dc_server to match dns_server for consistency
    dc_username: "{{ domain_config[current_domain].dns_username }}"
    dc_password: "{{ domain_config[current_domain].dns_password }}"
  no_log: true

- name: Set PTR DNS server based on domain for current domain
  ansible.builtin.set_fact:
    ptr_dns_server: "{{ domain_config[current_domain].ptr_dns_server }}"

- name: Launch async DNS operation task
  ansible.builtin.include_tasks: "{{ operation }}_async.yml"

- name: Add async job to results list
  ansible.builtin.set_fact:
    async_results: "{{ async_results + [dns_async_result] }}"
  when: dns_async_result is defined

- name: Update progress tracking
  ansible.builtin.set_fact:
    pending_domains: "{{ pending_domains | default(domains.split(',') | map('trim') | list) | difference([current_domain]) }}"
    completed_domains: "{{ completed_domains | default([]) + [current_domain] }}"
    completed_count: "{{ (completed_domains | default([]) + [current_domain]) | length }}"
    total_count: "{{ domains.split(',') | map('trim') | list | length }}"

- name: Track progress
  ansible.builtin.include_tasks: track_progress.yml
