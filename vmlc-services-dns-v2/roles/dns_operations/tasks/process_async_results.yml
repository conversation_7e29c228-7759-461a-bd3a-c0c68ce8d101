---
# Process async results
# Author: CES Operational Excellence Team
# Contributors: <PERSON> (7409)

- name: Initialize consolidated results and progress tracking
  ansible.builtin.set_fact:
    consolidated_results: []
    processed_count: 0
    total_async_count: "{{ async_results | length }}"

- name: Process each async result
  ansible.builtin.include_tasks: process_single_async_result.yml
  loop: "{{ async_results }}"
  loop_control:
    loop_var: current_async_result

- name: Display final progress
  ansible.builtin.debug:
    msg: |
      DNS Management Operation Complete
      ✓ Successfully processed {{ total_async_count }} domain-hostname pairs
      ✓ Operation: {{ operation | capitalize }}
      ✓ Record Type: {{ record_type | upper }}
      ✓ Timestamp: {{ ansible_date_time.iso8601 }}
  when: show_progress | default(true) | bool

- name: Log final progress to file
  ansible.builtin.copy:
    content: |
      Timestamp: {{ ansible_date_time.iso8601 }}
      Operation: {{ operation | capitalize }}
      Record Type: {{ record_type | upper }}
      Status: Complete
      Total Processed: {{ total_async_count }} domain-hostname pairs

      Results Summary:
      - Success: {{ consolidated_results | selectattr('success', 'equalto', true) | list | length }}
      - Failed: {{ consolidated_results | selectattr('success', 'equalto', false) | list | length }}
      - Changes Made: {{ consolidated_results | selectattr('changed', 'equalto', true) | list | length }}
    dest: "{{ playbook_dir }}/../logs/progress/{{ ansible_date_time.date }}_{{ operation }}_{{ record_type }}_final.log"
    mode: '0644'
  when: log_progress | default(true) | bool
