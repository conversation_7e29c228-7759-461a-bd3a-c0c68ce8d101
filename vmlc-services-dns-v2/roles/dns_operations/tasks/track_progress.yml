---
# Track progress of multi-domain operations
# Author: CES Operational Excellence Team
# Contributors: <PERSON> (7409)

- name: Calculate progress percentage
  ansible.builtin.set_fact:
    progress_percentage: "{{ (completed_count | int * 100) // total_count | int }}"
    remaining_count: "{{ total_count | int - completed_count | int }}"

- name: Display progress bar
  ansible.builtin.debug:
    msg: |
      DNS Management Progress: {{ progress_percentage }}% complete
      [{{ "=" * (progress_percentage // 5) }}{{ " " * ((100 - progress_percentage) // 5) }}] {{ completed_count }}/{{ total_count }}
      ✓ Completed: {{ completed_count }} domain-hostname pairs
      ⧗ Remaining: {{ remaining_count }} domain-hostname pairs
      {% if completed_count > 0 %}
      Last completed: {{ current_domain }} - {{ domain_hostname_map[current_domain] }}
      {% endif %}
  when: show_progress | default(true) | bool

- name: Log progress to file
  ansible.builtin.copy:
    content: |
      Timestamp: {{ ansible_date_time.iso8601 }}
      Operation: {{ operation | capitalize }}
      Record Type: {{ record_type | upper }}
      Progress: {{ progress_percentage }}% ({{ completed_count }}/{{ total_count }})
      {% if completed_count > 0 %}
      Last completed: {{ current_domain }} - {{ domain_hostname_map[current_domain] }}
      {% endif %}

      Completed domains:
      {% for domain in completed_domains %}
      - {{ domain }}: {{ domain_hostname_map[domain] }}
      {% endfor %}

      Pending domains:
      {% for domain in pending_domains %}
      - {{ domain }}: {{ domain_hostname_map[domain] }}
      {% endfor %}
    dest: "{{ playbook_dir }}/../logs/progress/{{ ansible_date_time.date }}_{{ operation }}_{{ record_type }}_progress.log"
    mode: '0644'
  when: log_progress | default(true) | bool
