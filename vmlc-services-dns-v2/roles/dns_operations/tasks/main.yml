---
# Main tasks file for dns_operations role
# Author: CES Operational Excellence Team
# Contributors: <PERSON> (7409)

# Handle multiple domains and hostnames if specified
- name: Process multiple domains and hostnames if specified
  block:
    - name: Validate domains, hostnames, and IP addresses have the same number of items
      ansible.builtin.assert:
        that:
          - domains.split(',') | map('trim') | list | length == hostnames.split(',') | map('trim') | list | length
          - (record_type | lower not in ['a', 'ptr']) or (ip_addresses is defined and ip_addresses | length > 0 and ip_addresses.split(',') | map('trim') | list | length == domains.split(',') | map('trim') | list | length)
        fail_msg: "For multi-domain operations, the number of domains, hostnames, and IP addresses (for A/PTR records) must match. Please provide one hostname and one IP address per domain."
      when: domains is defined and domains | length > 0 and hostnames is defined and hostnames | length > 0

    - name: Create domain to hostname mapping
      ansible.builtin.set_fact:
        domain_hostname_map: "{{ dict(domains.split(',') | map('trim') | list | zip(hostnames.split(',') | map('trim') | list)) }}"
      when: domains is defined and domains | length > 0 and hostnames is defined and hostnames | length > 0

    - name: Create domain to IP address mapping for A/PTR records
      ansible.builtin.set_fact:
        domain_ip_map: "{{ dict(domains.split(',') | map('trim') | list | zip(ip_addresses.split(',') | map('trim') | list)) }}"
      when: domains is defined and domains | length > 0 and record_type | lower in ['a', 'ptr'] and ip_addresses is defined and ip_addresses | length > 0

    - name: Initialize async results list
      ansible.builtin.set_fact:
        async_results: []
      when: domains is defined and domains | length > 0 and hostnames is defined and hostnames | length > 0

    - name: Process domains and their corresponding hostnames in parallel
      ansible.builtin.include_tasks: process_domain_async.yml
      loop: "{{ domains.split(',') | map('trim') | list }}"
      loop_control:
        loop_var: current_domain
      when: domains is defined and domains | length > 0 and hostnames is defined and hostnames | length > 0

    - name: Wait for all async tasks to complete
      ansible.builtin.async_status:
        jid: "{{ item.ansible_job_id }}"
      register: job_result
      until: job_result.finished
      retries: 300  # 30 minutes with 6-second intervals
      delay: 6
      loop: "{{ async_results }}"
      when: async_results is defined and async_results | length > 0
  when: domains is defined and domains | length > 0 and hostnames is defined and hostnames | length > 0

# Handle case when only domains is specified but not hostnames
- name: Fail if domains is specified without hostnames
  ansible.builtin.fail:
    msg: "When using multiple domains, you must specify hostnames with a matching number of hostnames (one per domain)."
  when: domains is defined and domains | length > 0 and (hostnames is not defined or hostnames | length == 0)

# Handle case when IP address is required but not provided
- name: Fail if IP address is required but not provided for A/PTR records
  ansible.builtin.fail:
    msg: "For A/PTR records, you must specify an IP address."
  when: record_type | lower in ['a', 'ptr'] and (ip_address is not defined or ip_address | length == 0) and (ip_addresses is not defined or ip_addresses | length == 0) and (domains is not defined or domains | length == 0)

# Default behavior for single domain
- name: Include tasks based on operation for single domain
  ansible.builtin.include_tasks: "{{ operation }}.yml"
  when: (domains is not defined or domains | length == 0) and (hostnames is not defined or hostnames | length == 0)
