---
# =========================================================================
# Audit Logging Role Metadata
# =========================================================================
# This role provides comprehensive audit logging capabilities for DNS
# management operations following enterprise compliance requirements.
#
# Author: CES Operational Excellence Team
# Contributor: <PERSON> (7409)
# Framework: Operational Excellence Automation Framework (OXAF)
# Version: 2.0
# =========================================================================

galaxy_info:
  role_name: audit_logging
  namespace: vmlc_services
  author: CES Operational Excellence Team
  description: Comprehensive audit logging for DNS operations
  company: Healthcare Organization
  license: Internal Use Only

  min_ansible_version: "2.12"

  platforms:
    - name: Windows
      versions:
        - "2019"
        - "2022"
    - name: EL
      versions:
        - "8"
        - "9"

  galaxy_tags:
    - audit
    - logging
    - compliance
    - security
    - oxaf

dependencies: []

collections:
  - ansible.windows
  - community.general
  - ansible.posix
