---
# =========================================================================
# DNS Lifecycle - Emergency Rollback Handler
# =========================================================================
# This handler performs emergency rollback procedures when critical errors
# occur during DNS management operations.
#
# Author: CES Operational Excellence Team
# Contributor: <PERSON> (7409)
# Framework: Operational Excellence Automation Framework (OXAF)
# Version: 2.0
# =========================================================================

- name: "Emergency Rollback - Initialize"
  debug:
    msg: |
      EMERGENCY ROLLBACK INITIATED
      ===========================
      Trigger: {{ rollback_trigger | default('Critical error detected') }}
      Execution ID: {{ execution_context.id | default('unknown') }}
      Job ID: {{ execution_context.job_id | default('unknown') }}
      Timestamp: {{ ansible_date_time.iso8601 }}

      WARNING: Emergency rollback procedures are being executed!

- name: "Log Emergency Rollback Initiation"
  ansible.builtin.lineinfile:
    path: "{{ dns_log_config.directory | default('C:\\OE_AAP_LOGS') }}\\{{ dns_log_config.file_name | default('dns_emergency.log') }}"
    line: |
      {{ ansible_date_time.iso8601 }} [EMERGENCY] ROLLBACK_INITIATED: Execution={{ execution_context.id | default('unknown') }} Job={{ execution_context.job_id | default('unknown') }} Trigger={{ rollback_trigger | default('Critical error') }}
    create: true
  delegate_to: "{{ dns_execution_context.primary_server | default('localhost') }}"
  vars:
    ansible_user: "{{ dns_connection_user | default(ansible_user) }}"
    ansible_password: "{{ dns_connection_password | default(ansible_password) }}"
    ansible_connection: "{{ 'winrm' if dns_execution_context.primary_server is defined else 'local' }}"
    ansible_winrm_transport: ntlm
    ansible_winrm_server_cert_validation: ignore
  when: dns_execution_context.primary_server is defined
  ignore_errors: true

- name: "Execute Emergency DNS Rollback"
  ansible.windows.win_shell: |
    $ErrorActionPreference = "Stop"

    Write-Output "EMERGENCY ROLLBACK EXECUTION"
    Write-Output "==========================="
    Write-Output "Timestamp: $(Get-Date)"
    Write-Output "Execution ID: {{ execution_context.id | default('unknown') }}"
    Write-Output "Job ID: {{ execution_context.job_id | default('unknown') }}"
    Write-Output "Target: {{ hostname | default('unknown') }}.{{ domain | default('unknown') }}"
    Write-Output ""

    try {
        # Check if enhanced DNS script is available
        if (Test-Path "C:\ansscripts\set-dns-v2.ps1") {
            Write-Output "Using enhanced DNS script for emergency rollback..."

            $rollbackParams = @{
                Action = "emergency_rollback"
                Domain = "{{ domain | default('unknown') }}"
                HostName = "{{ hostname | default('unknown') }}"
                LogFile = "{{ dns_log_config.directory | default('C:\\OE_AAP_LOGS') }}\\{{ dns_log_config.file_name | default('dns_emergency.log') }}"
                ExecutionId = "{{ execution_context.id | default('unknown') }}"
                JobId = "{{ execution_context.job_id | default('unknown') }}"
                EnableRollback = $true
                Verbose = $true
            }

            Write-Output "Executing emergency rollback with parameters:"
            $rollbackParams.GetEnumerator() | ForEach-Object { Write-Output "  $($_.Key): $($_.Value)" }
            Write-Output ""

            & "C:\ansscripts\set-dns-v2.ps1" @rollbackParams

            if ($LASTEXITCODE -eq 0) {
                Write-Output "Emergency rollback completed successfully"
            } else {
                Write-Error "Emergency rollback failed with exit code: $LASTEXITCODE"
            }

        } else {
            Write-Warning "Enhanced DNS script not found - performing basic rollback"

            # Basic emergency rollback procedures
            Write-Output "Performing basic emergency rollback procedures..."

            # Log the emergency state
            Write-Output "Emergency rollback logged - manual intervention may be required"
            Write-Output "Please verify DNS state manually and contact support if needed"
        }

    } catch {
        Write-Error "Emergency rollback execution failed: $($_.Exception.Message)"
        Write-Output "CRITICAL: Emergency rollback failed - immediate manual intervention required!"
        throw
    }
  delegate_to: "{{ dns_execution_context.primary_server }}"
  vars:
    ansible_user: "{{ dns_connection_user }}"
    ansible_password: "{{ dns_connection_password }}"
    ansible_connection: winrm
    ansible_winrm_transport: ntlm
    ansible_winrm_server_cert_validation: ignore
  register: emergency_rollback_result
  when: dns_execution_context.primary_server is defined
  ignore_errors: true

- name: "Validate Emergency Rollback"
  ansible.windows.win_shell: |
    Write-Output "Validating emergency rollback results..."

    try {
        # Verify DNS state after rollback
        $params = @{
            Action = "verify"
            Domain = "{{ domain | default('unknown') }}"
            HostName = "{{ hostname | default('unknown') }}"
            LogFile = "{{ dns_log_config.directory | default('C:\\OE_AAP_LOGS') }}\\{{ dns_log_config.file_name | default('dns_emergency.log') }}"
            ExecutionId = "{{ execution_context.id | default('unknown') }}"
            JobId = "{{ execution_context.job_id | default('unknown') }}"
            Verbose = $true
        }

        if (Test-Path "C:\ansscripts\set-dns-v2.ps1") {
            & "C:\ansscripts\set-dns-v2.ps1" @params
            Write-Output "Emergency rollback validation completed"
        } else {
            Write-Output "Validation skipped - enhanced script not available"
        }

    } catch {
        Write-Warning "Emergency rollback validation failed: $($_.Exception.Message)"
    }
  delegate_to: "{{ dns_execution_context.primary_server }}"
  vars:
    ansible_user: "{{ dns_connection_user }}"
    ansible_password: "{{ dns_connection_password }}"
    ansible_connection: winrm
    ansible_winrm_transport: ntlm
    ansible_winrm_server_cert_validation: ignore
  register: rollback_validation_result
  when:
    - dns_execution_context.primary_server is defined
    - emergency_rollback_result is defined
  ignore_errors: true

- name: "Send Emergency Notification"
  debug:
    msg: |
      EMERGENCY NOTIFICATION REQUIRED
      ==============================
      Subject: CRITICAL - DNS Emergency Rollback Executed
      Severity: CRITICAL
      Execution ID: {{ execution_context.id | default('unknown') }}
      Job ID: {{ execution_context.job_id | default('unknown') }}
      Target: {{ hostname | default('unknown') }}.{{ domain | default('unknown') }}
      Rollback Status: {{ 'SUCCESS' if emergency_rollback_result.rc == 0 else 'FAILED' }}
      Validation Status: {{ 'SUCCESS' if rollback_validation_result.rc == 0 else 'FAILED' if rollback_validation_result is defined else 'SKIPPED' }}

      IMMEDIATE ACTION REQUIRED:
      - Verify DNS system state
      - Review emergency logs
      - Contact on-call support team
      - Escalate to infrastructure team if needed
  notify: "Send Error Notification"

- name: "Log Emergency Rollback Completion"
  ansible.builtin.lineinfile:
    path: "{{ dns_log_config.directory | default('C:\\OE_AAP_LOGS') }}\\{{ dns_log_config.file_name | default('dns_emergency.log') }}"
    line: |
      {{ ansible_date_time.iso8601 }} [EMERGENCY] ROLLBACK_COMPLETE: Status={{ 'SUCCESS' if emergency_rollback_result.rc == 0 else 'FAILED' }} Validation={{ 'SUCCESS' if rollback_validation_result.rc == 0 else 'FAILED' if rollback_validation_result is defined else 'SKIPPED' }} ManualInterventionRequired={{ emergency_rollback_result.rc != 0 or (rollback_validation_result.rc != 0 if rollback_validation_result is defined else false) }}
    create: true
  delegate_to: "{{ dns_execution_context.primary_server | default('localhost') }}"
  vars:
    ansible_user: "{{ dns_connection_user | default(ansible_user) }}"
    ansible_password: "{{ dns_connection_password | default(ansible_password) }}"
    ansible_connection: "{{ 'winrm' if dns_execution_context.primary_server is defined else 'local' }}"
    ansible_winrm_transport: ntlm
    ansible_winrm_server_cert_validation: ignore
  when: dns_execution_context.primary_server is defined
  ignore_errors: true

- name: "Set Emergency Rollback Results"
  set_fact:
    emergency_rollback_results:
      executed: true
      status: "{{ 'success' if emergency_rollback_result.rc == 0 else 'failed' }}"
      validation_status: "{{ 'success' if rollback_validation_result.rc == 0 else 'failed' if rollback_validation_result is defined else 'skipped' }}"
      manual_intervention_required: "{{ emergency_rollback_result.rc != 0 or (rollback_validation_result.rc != 0 if rollback_validation_result is defined else false) }}"
      timestamp: "{{ ansible_date_time.iso8601 }}"
      execution_id: "{{ execution_context.id | default('unknown') }}"
      job_id: "{{ execution_context.job_id | default('unknown') }}"

- name: "Emergency Rollback Handler - Complete"
  debug:
    msg: |
      EMERGENCY ROLLBACK HANDLER - COMPLETED
      =====================================
      Status: {{ emergency_rollback_results.status | upper }}
      Validation: {{ emergency_rollback_results.validation_status | upper }}
      Manual Intervention Required: {{ emergency_rollback_results.manual_intervention_required }}

      CRITICAL ALERT: Emergency rollback has been executed!
      Please review system state and logs immediately.

      Log Files:
      - Emergency Log: {{ dns_log_config.directory | default('C:\\OE_AAP_LOGS') }}\\{{ dns_log_config.file_name | default('dns_emergency.log') }}
      - Main Log: {{ dns_log_config.directory | default('C:\\OE_AAP_LOGS') }}\\{{ dns_log_config.file_name | default('dns_main.log') }}

      Next Steps:
      1. Verify DNS system state manually
      2. Review all log files for details
      3. Contact support team immediately
      4. Document incident for post-mortem analysis
