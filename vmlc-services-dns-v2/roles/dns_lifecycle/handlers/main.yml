---
# =========================================================================
# DNS Lifecycle Role - Handlers
# =========================================================================
# This file contains handlers for the DNS lifecycle role that manage
# notifications, emergency procedures, and cleanup operations.
#
# Author: CES Operational Excellence Team
# Contributor: <PERSON> (7409)
# Framework: Operational Excellence Automation Framework (OXAF)
# Version: 2.0
# =========================================================================

- name: "Emergency Rollback Handler"
  include_tasks: emergency_rollback.yml
  when: rollback_required | default(false) | bool

- name: "Send Error Notification"
  include_tasks: send_error_notification.yml
  when: error_notification_data is defined

- name: "Update Service Request"
  include_tasks: update_service_request.yml
  when:
    - var_sr_number is defined
    - var_sr_number != "NOTICKET"

- name: "Send Success Notification"
  include_tasks: send_success_notification.yml
  when:
    - dns_execution_results is defined
    - dns_execution_results.operation_status == "success"

- name: "Trigger Security Alert"
  include_tasks: security_alert.yml
  when:
    - security_incident_detected | default(false) | bool
    - dns_security.access_control.audit_all_access | default(true) | bool

- name: "Archive Execution Logs"
  include_tasks: archive_logs.yml
  when:
    - dns_logging.enabled | default(true) | bool
    - dns_logging.retention.compression | default(true) | bool

- name: "Performance Metrics Collection"
  include_tasks: collect_metrics.yml
  when:
    - performance_monitoring.enabled | default(true) | bool
    - dns_reporting_results is defined
