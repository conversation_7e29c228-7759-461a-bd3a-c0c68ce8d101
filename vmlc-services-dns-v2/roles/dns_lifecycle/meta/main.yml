---
# =========================================================================
# DNS Lifecycle Role Metadata
# =========================================================================
# This role implements the six-phase lifecycle framework for DNS management
# operations following Operational Excellence Automation Framework (OXAF) patterns.
#
# Author: CES Operational Excellence Team
# Contributor: <PERSON> (7409)
# Framework: Operational Excellence Automation Framework (OXAF)
# Version: 2.0
# =========================================================================

galaxy_info:
  role_name: dns_lifecycle
  namespace: vmlc_services
  author: CES Operational Excellence Team
  description: Six-phase lifecycle management for DNS operations
  company: Healthcare Organization
  license: Internal Use Only

  min_ansible_version: "2.12"

  platforms:
    - name: Windows
      versions:
        - "2019"
        - "2022"
    - name: EL
      versions:
        - "8"
        - "9"

  galaxy_tags:
    - dns
    - lifecycle
    - automation
    - infrastructure
    - oxaf

dependencies:
  - role: audit_logging
    vars:
      audit_scope: "dns_lifecycle"

collections:
  - ansible.windows
  - community.general
  - ansible.posix
