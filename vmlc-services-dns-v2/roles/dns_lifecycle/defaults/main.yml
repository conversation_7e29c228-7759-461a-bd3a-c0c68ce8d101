---
# =========================================================================
# DNS Lifecycle Role Default Variables
# =========================================================================
# Default variables for the DNS lifecycle management role implementing
# the six-phase framework with comprehensive error handling and logging.
#
# Author: CES Operational Excellence Team
# Contributor: <PERSON> (7409)
# Framework: Operational Excellence Automation Framework (OXAF)
# Version: 2.0
# =========================================================================

# =========================
# Lifecycle Configuration
# =========================

# Phase execution settings
dns_lifecycle:
  phases:
    configuration:
      enabled: true
      timeout: 300
      retry_count: 3
      critical: true
    loading:
      enabled: true
      timeout: 600
      retry_count: 3
      critical: true
    execution:
      enabled: true
      timeout: 1800
      retry_count: 2
      critical: true
    error_handling:
      enabled: true
      timeout: 900
      retry_count: 1
      critical: false
    reporting:
      enabled: true
      timeout: 300
      retry_count: 2
      critical: false
    cleanup:
      enabled: true
      timeout: 300
      retry_count: 1
      critical: false

# =========================
# DNS Operation Settings
# =========================

# Supported DNS operations
dns_operations:
  supported_actions:
    - verify
    - add
    - remove
    - update
    - sync

  default_settings:
    ttl: 3600
    record_type: "A"
    validation_required: true
    backup_before_change: true

  validation:
    hostname_pattern: "^[a-zA-Z0-9]([a-zA-Z0-9-]{0,61}[a-zA-Z0-9])?$"
    ip_validation: true
    domain_validation: true

# =========================
# Domain and Server Mapping
# =========================

# DNS server mapping by domain (enhanced from v1)
dns_domain_mapping:
  "devhealthgrp.com.sg":
    primary_servers:
      - "HISADMTVDSEC01.devhealthgrp.com.sg"
    backup_servers: []
    environment: "development"
    security_zone: "dev"

  "healthgrpexts.com.sg":
    primary_servers:
      - "HISADMTVSSEC01.healthgrpexts.com.sg"
    backup_servers: []
    environment: "staging"
    security_zone: "external"

  "nnstg.local":
    primary_servers:
      - "HISADMTVSSEC02.nnstg.local"
    backup_servers: []
    environment: "staging"
    security_zone: "internal"

  "ses.shsu.com.sg":
    primary_servers:
      - "SHSADMTVDSEC02.ses.shsu.com.sg"
    backup_servers: []
    environment: "staging"
    security_zone: "singhealth"

  "shses.shs.com.sg":
    primary_servers:
      - "SHSADMTVPSEC12.shses.shs.com.sg"
    backup_servers: []
    environment: "production"
    security_zone: "singhealth"

  "nhg.local":
    primary_servers:
      - "HISADMTVPSEC11.nhg.local"
    backup_servers: []
    environment: "production"
    security_zone: "nhg"

  "aic.local":
    primary_servers:
      - "AICADMTVPSEC11.aic.local"
    backup_servers: []
    environment: "production"
    security_zone: "aic"

  "iltc.healthgrp.com.sg":
    primary_servers:
      - "HISADMTVPSEC11.iltc.healthgrp.com.sg"
    backup_servers: []
    environment: "production"
    security_zone: "iltc"

  "healthgrp.com.sg":
    primary_servers:
      - "HISADMTVPSEC11.healthgrp.com.sg"
    backup_servers: []
    environment: "production"
    security_zone: "main"

# =========================
# Credential Management
# =========================

# CyberArk credential mapping by domain
dns_credential_mapping:
  "devhealthgrp.com.sg": "{{ var_dns_devhealthgrp_username | default('') }}"
  "healthgrpexts.com.sg": "{{ var_dns_healthgrpexts_username | default('') }}"
  "nnstg.local": "{{ var_dns_nnstg_username | default('') }}"
  "ses.shsu.com.sg": "{{ var_dns_ses_username | default('') }}"
  "shses.shs.com.sg": "{{ var_dns_shses_username | default('') }}"
  "nhg.local": "{{ var_dns_nhg_username | default('') }}"
  "aic.local": "{{ var_dns_aic_username | default('') }}"
  "iltc.healthgrp.com.sg": "{{ var_dns_iltc_username | default('') }}"
  "healthgrp.com.sg": "{{ var_dns_healthgrp_username | default('') }}"

# =========================
# Logging Configuration
# =========================

# Logging settings following organizational standards
dns_logging:
  enabled: true
  level: "INFO"
  format: "structured"

  # Log file naming convention: <AAP JOB ID>_<DDMMYYYY>_<TICKET>_<OPERATION>_ANSIBLE.log
  file_naming:
    pattern: "{{ tower_job_id | default('LOCAL') }}_{{ ansible_date_time.day }}{{ ansible_date_time.month }}{{ ansible_date_time.year }}_{{ var_sr_number | default('NOTICKET') }}_DNS_ANSIBLE.log"
    directory: "C:\\OE_AAP_LOGS"

  # Log retention and management
  retention:
    days: 90
    max_size_mb: 100
    compression: true

  # Audit logging requirements
  audit:
    enabled: true
    include_sensitive: false
    compliance_format: true

# =========================
# Error Handling Configuration
# =========================

# Error handling and rollback settings
dns_error_handling:
  enabled: true

  # Rollback configuration
  rollback:
    enabled: true
    automatic: true
    backup_required: true
    validation_after_rollback: true

  # Error classification
  error_types:
    critical:
      - "authentication_failure"
      - "dns_server_unreachable"
      - "invalid_credentials"
    recoverable:
      - "temporary_network_issue"
      - "dns_record_conflict"
      - "validation_warning"
    warning:
      - "record_already_exists"
      - "record_not_found"
      - "zone_not_found"
      - "ptr_record_missing"

  # Retry configuration
  retry:
    max_attempts: 3
    delay_seconds: 30
    exponential_backoff: true

# =========================
# Security Configuration
# =========================

# Security settings and controls
dns_security:
  encryption:
    in_transit: true
    at_rest: true

  access_control:
    rbac_enabled: true
    audit_all_access: true

  compliance:
    pci_dss: false
    hipaa: true
    sox: false

  validation:
    certificate_validation: true
    hostname_verification: true

# =========================
# Performance Configuration
# =========================

# Performance and optimization settings
dns_performance:
  timeouts:
    connection: 30
    operation: 300
    total: 1800

  concurrency:
    max_parallel_operations: 5
    batch_size: 10

  caching:
    enabled: true
    ttl_seconds: 300

  monitoring:
    performance_metrics: true
    execution_timing: true

# =========================
# Notification Configuration
# =========================

# Notification and alerting settings
dns_notifications:
  enabled: true

  channels:
    email: true
    teams: false
    slack: false
    sms: false

  triggers:
    success: true
    failure: true
    warning: true

  recipients:
    default: "{{ var_notification_email | default('<EMAIL>') }}"
    escalation: "{{ var_escalation_email | default('<EMAIL>') }}"

# =========================
# Testing Configuration
# =========================

# Testing and validation settings
dns_testing:
  enabled: true

  validation:
    pre_execution: true
    post_execution: true
    idempotency: true

  test_modes:
    dry_run: false
    validation_only: false
    rollback_test: false
