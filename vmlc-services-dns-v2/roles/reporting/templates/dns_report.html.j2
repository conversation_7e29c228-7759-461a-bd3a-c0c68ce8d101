<!DOCTYPE html>
<html>
<head>
    <title>{{ report_title }}</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            margin: 40px;
            line-height: 1.6;
        }
        h1 {
            color: #2c3e50;
            border-bottom: 1px solid #eee;
            padding-bottom: 10px;
        }
        .success {
            color: green;
        }
        .failure {
            color: red;
        }
        .section {
            margin: 20px 0;
            padding: 15px;
            background-color: #f9f9f9;
            border-radius: 5px;
        }
        table {
            width: 100%;
            border-collapse: collapse;
        }
        th, td {
            padding: 8px;
            text-align: left;
            border-bottom: 1px solid #ddd;
        }
        th {
            background-color: #f2f2f2;
        }
        .header {
            text-align: center;
            margin-bottom: 30px;
        }
        .logo {
            max-width: 150px;
            margin-bottom: 20px;
        }
        .footer {
            margin-top: 50px;
            text-align: center;
            font-size: 0.8em;
            color: #777;
        }
        .certificate {
            border: 2px solid #2c3e50;
            padding: 20px;
            text-align: center;
            margin-top: 30px;
            background-color: #f9f9f9;
        }
        .certificate h2 {
            color: #2c3e50;
            margin-bottom: 20px;
        }
        .certificate p {
            margin-bottom: 10px;
        }
        .signature {
            margin-top: 50px;
            border-top: 1px solid #ddd;
            padding-top: 10px;
            width: 200px;
            margin-left: auto;
            margin-right: auto;
        }
    </style>
</head>
<body>
    <div class="header">
        <h1>{{ report_title }}</h1>
        <p>Generated by DNS Management System</p>
        <p>Author: Contributors: Muhammad Syazani Bin Mohamed Khairi (7409)</p>
        <p>Team: Contributors: Muhammad Syazani Bin Mohamed Khairi (7409)</p>
    </div>

    <div class="section">
        <h2>Operation Summary</h2>
        <p>Status:
            {% if dns_operation_result.success %}
            <span class="success">Successful</span>
            {% else %}
            <span class="failure">Failed</span>
            {% endif %}
        </p>
        <p>Message: {{ dns_operation_result.message }}</p>
    </div>

    <div class="section">
        <h2>Operation Details</h2>
        <table>
            <tr>
                <th>Property</th>
                <th>Value</th>
            </tr>
            <tr>
                <td>Operation</td>
                <td>{{ operation | upper }}</td>
            </tr>
            <tr>
                <td>Record Type</td>
                <td>{{ record_type | upper }}</td>
            </tr>
            <tr>
                <td>FQDN</td>
                <td>{{ hostname }}.{{ domain }}</td>
            </tr>
            {% if record_type == 'a' or record_type == 'ptr' %}
            <tr>
                <td>IP Address</td>
                <td>{{ ip_address }}</td>
            </tr>
            {% endif %}
            {% if record_type == 'cname' %}
            <tr>
                <td>Target</td>
                <td>{{ cname_target }}</td>
            </tr>
            {% endif %}
            <tr>
                <td>TTL</td>
                <td>{{ ttl }}</td>
            </tr>
            <tr>
                <td>Description</td>
                <td>{{ description }}</td>
            </tr>
            <tr>
                <td>ADMT Server</td>
                <td>{{ admt_server }}</td>
            </tr>
            <tr>
                <td>DNS Server</td>
                <td>{{ dns_server }}</td>
            </tr>
            <tr>
                <td>Ticket</td>
                <td>{{ ticket }}</td>
            </tr>
            <tr>
                <td>User</td>
                <td>{{ lookup('env', 'USER') | default(ansible_user_id) }}</td>
            </tr>
            <tr>
                <td>Changed</td>
                <td>{{ dns_operation_result.changed }}</td>
            </tr>
        </table>
    </div>

    <div class="section">
        <h2>Timestamp</h2>
        <p>Report generated on: {{ ansible_date_time.iso8601 }}</p>
    </div>

    {% if dns_operation_result.success %}
    <div class="certificate">
        <h2>Certificate of Successful DNS Operation</h2>
        <p>This certifies that the following DNS operation was completed successfully:</p>
        <p><strong>{{ operation | capitalize }}</strong> operation for {{ record_type | upper }} record <strong>{{ hostname }}.{{ domain }}</strong></p>
        <p>Completed on: {{ ansible_date_time.date }} at {{ ansible_date_time.time }}</p>
        <p>Ticket: {{ ticket }}</p>

        <div class="signature">
            Automated by Ansible
        </div>
    </div>
    {% endif %}

    <div class="footer">
        <p>This is an automatically generated report. Please do not reply to this email.</p>
        <p>© {{ ansible_date_time.year }} Contributors: Muhammad Syazani Bin Mohamed Khairi (7409). All rights reserved.</p>
    </div>
</body>
</html>
