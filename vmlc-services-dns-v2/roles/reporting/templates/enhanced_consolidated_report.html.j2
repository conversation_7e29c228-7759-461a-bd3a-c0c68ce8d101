<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>DNS Management - Consolidated Operation Report</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            line-height: 1.6;
            color: #333;
            margin: 0;
            padding: 20px;
        }
        .header {
            text-align: center;
            margin-bottom: 30px;
            border-bottom: 2px solid #2c3e50;
            padding-bottom: 10px;
        }
        .header h1 {
            color: #2c3e50;
            margin-bottom: 5px;
        }
        .header p {
            color: #7f8c8d;
            font-size: 14px;
            margin-top: 5px;
        }
        .summary {
            margin-bottom: 30px;
            padding: 15px;
            background-color: #f8f9fa;
            border-radius: 5px;
            box-shadow: 0 2px 4px rgba(0,0,0,0.1);
        }
        .summary h2 {
            margin-top: 0;
            color: #2c3e50;
            border-bottom: 1px solid #ddd;
            padding-bottom: 10px;
        }
        .summary-grid {
            display: grid;
            grid-template-columns: repeat(2, 1fr);
            gap: 15px;
            margin-top: 15px;
        }
        .summary-item {
            padding: 10px;
            background-color: #fff;
            border-radius: 4px;
            box-shadow: 0 1px 3px rgba(0,0,0,0.1);
        }
        .summary-item h3 {
            margin-top: 0;
            font-size: 16px;
            color: #2c3e50;
        }
        .summary-item p {
            margin-bottom: 0;
            font-size: 24px;
            font-weight: bold;
        }
        table {
            width: 100%;
            border-collapse: collapse;
            margin-bottom: 30px;
            box-shadow: 0 2px 4px rgba(0,0,0,0.1);
        }
        th, td {
            padding: 12px 15px;
            text-align: left;
            border-bottom: 1px solid #ddd;
        }
        th {
            background-color: #2c3e50;
            color: white;
        }
        tr:nth-child(even) {
            background-color: #f2f2f2;
        }
        tr:hover {
            background-color: #e9f7fe;
        }
        .success {
            color: #27ae60;
            font-weight: bold;
        }
        .failure {
            color: #e74c3c;
            font-weight: bold;
        }
        .warning {
            color: #f39c12;
            font-weight: bold;
        }
        .badge {
            display: inline-block;
            padding: 4px 8px;
            border-radius: 4px;
            font-size: 12px;
            font-weight: bold;
        }
        .badge-success {
            background-color: #d4edda;
            color: #155724;
        }
        .badge-danger {
            background-color: #f8d7da;
            color: #721c24;
        }
        .badge-warning {
            background-color: #fff3cd;
            color: #856404;
        }
        .badge-info {
            background-color: #d1ecf1;
            color: #0c5460;
        }
        .operation-details {
            margin-bottom: 30px;
            padding: 15px;
            background-color: #f8f9fa;
            border-radius: 5px;
            box-shadow: 0 2px 4px rgba(0,0,0,0.1);
        }
        .operation-details h2 {
            margin-top: 0;
            color: #2c3e50;
            border-bottom: 1px solid #ddd;
            padding-bottom: 10px;
        }
        .footer {
            margin-top: 50px;
            text-align: center;
            font-size: 12px;
            color: #7f8c8d;
            border-top: 1px solid #ddd;
            padding-top: 20px;
        }
        .report-link {
            color: #3498db;
            text-decoration: none;
        }
        .report-link:hover {
            text-decoration: underline;
        }
    </style>
</head>
<body>
    <div class="header">
        <h1>DNS Management - Consolidated Operation Report</h1>
        <p>Generated on: {{ ansible_date_time.date }} at {{ ansible_date_time.time }}</p>
        <p>Operation: <span class="badge badge-info">{{ operation | capitalize }}</span></p>
        <p>Record Type: <span class="badge badge-info">{{ record_type | upper }}</span></p>
    </div>

    <div class="summary">
        <h2>Operation Summary</h2>
        <div class="summary-grid">
            <div class="summary-item">
                <h3>Total Operations</h3>
                <p>{{ consolidated_results | length }}</p>
            </div>
            <div class="summary-item">
                <h3>Successful Operations</h3>
                <p class="success">{{ consolidated_results | selectattr('success', 'equalto', true) | list | length }}</p>
            </div>
            <div class="summary-item">
                <h3>Failed Operations</h3>
                <p class="{% if consolidated_results | selectattr('success', 'equalto', false) | list | length > 0 %}failure{% else %}success{% endif %}">
                    {{ consolidated_results | selectattr('success', 'equalto', false) | list | length }}
                </p>
            </div>
            <div class="summary-item">
                <h3>Changes Made</h3>
                <p>{{ consolidated_results | selectattr('changed', 'equalto', true) | list | length }}</p>
            </div>
        </div>
    </div>

    <div class="operation-details">
        <h2>Operation Details</h2>
        <p><strong>Operation Type:</strong> {{ operation | capitalize }}</p>
        <p><strong>Record Type:</strong> {{ record_type | upper }}</p>
        <p><strong>Ticket:</strong> {{ ticket }}</p>
        {% if operation == 'add' or operation == 'update' %}
            {% if record_type == 'a' or record_type == 'ptr' %}
                <p><strong>IP Address:</strong> {{ ip_address }}</p>
            {% elif record_type == 'cname' %}
                <p><strong>CNAME Target:</strong> {{ cname_target }}</p>
            {% endif %}
            <p><strong>TTL:</strong> {{ ttl | default('3600') }} seconds</p>
            <p><strong>Description:</strong> {{ description | default('Managed by Ansible') }}</p>
            {% if record_type == 'a' %}
                <p><strong>Manage PTR:</strong> {{ manage_ptr | default(true) }}</p>
            {% endif %}
        {% endif %}
    </div>

    <h2>Detailed Results</h2>
    <table>
        <thead>
            <tr>
                <th>Domain</th>
                <th>Hostname</th>
                <th>Status</th>
                <th>Changed</th>
                <th>Message</th>
                <th>Report</th>
            </tr>
        </thead>
        <tbody>
            {% for result in consolidated_results %}
            <tr>
                <td>{{ result.domain }}</td>
                <td>{{ result.hostname }}</td>
                <td>
                    <span class="badge {% if result.success %}badge-success{% else %}badge-danger{% endif %}">
                        {{ 'Success' if result.success else 'Failed' }}
                    </span>
                </td>
                <td>
                    <span class="badge {% if result.changed %}badge-warning{% else %}badge-info{% endif %}">
                        {{ 'Yes' if result.changed else 'No' }}
                    </span>
                </td>
                <td>{{ result.message }}</td>
                <td>
                    <a class="report-link" href="{{ result.report_path | replace(playbook_dir + '/../', '') }}">View Report</a>
                </td>
            </tr>
            {% endfor %}
        </tbody>
    </table>

    <div class="footer">
        <p>Generated by DNS Management System</p>
        <p>Author: Contributors: Muhammad Syazani Bin Mohamed Khairi (7409)</p>
        <p>Contributors: Muhammad Syazani Bin Mohamed Khairi (7409)</p>
        <p>© {{ ansible_date_time.year }} All rights reserved.</p>
    </div>
</body>
</html>
