---
# Tasks for emailing logs as attachments
# Author: CES Operational Excellence Team
# Contributors: <PERSON> Bin <PERSON> (7409)

- name: Include email configuration
  ansible.builtin.include_vars:
    file: "{{ playbook_dir }}/../vars/email_config.yml"

- name: Include standardized log file paths
  ansible.builtin.include_tasks: set_log_paths.yml

- name: Validate email recipient is provided
  ansible.builtin.assert:
    that:
      - email_recipient != ""
    fail_msg: "Email recipient is required for sending logs"
  when: email_logs | default(email_flags.email_logs) | bool

- name: Include dynamic email recipient selection
  ansible.builtin.include_tasks: set_email_recipient.yml
  when: email_logs | default(email_flags.email_logs) | bool

- name: Include dynamic SMTP server selection
  ansible.builtin.include_tasks: set_smtp_server.yml
  when: email_logs | default(email_flags.email_logs) | bool

- name: Set initial email subject
  ansible.builtin.set_fact:
    email_subject: "{{ email_settings.templates.logs_subject }}"
  when: email_logs | default(email_flags.email_logs) | bool

- name: Format email subject with operation
  ansible.builtin.set_fact:
    email_subject: "{{ email_subject | replace('{operation}', operation | capitalize) }}"
  when: email_logs | default(email_flags.email_logs) | bool

- name: Format email subject with record type
  ansible.builtin.set_fact:
    email_subject: "{{ email_subject | replace('{record_type}', record_type | upper) }}"
  when: email_logs | default(email_flags.email_logs) | bool

- name: Format email subject with hostname
  ansible.builtin.set_fact:
    email_subject: "{{ email_subject | replace('{hostname}', hostname) }}"
  when: email_logs | default(email_flags.email_logs) | bool

- name: Format email subject with domain
  ansible.builtin.set_fact:
    email_subject: "{{ email_subject | replace('{domain}', domain) }}"
  when: email_logs | default(email_flags.email_logs) | bool

- name: Create email body
  ansible.builtin.set_fact:
    email_body: |
      DNS Management Operation Logs

      Operation: {{ operation | capitalize }}
      Record Type: {{ record_type | upper }}
      Hostname: {{ hostname }}.{{ domain }}
      Status: {{ 'Successful' if dns_operation_result.success else 'Failed' }}

      Details: {{ dns_operation_result.message }}

      Please see the attached log files for complete details.

      This is an automated message from the DNS Management System.
  when: email_logs | default(email_flags.email_logs) | bool

- name: Send email with logs
  community.general.mail:
    host: "{{ smtp_server }}"
    port: "{{ smtp_port }}"
    from: "{{ smtp_from }}"
    to: "{{ email_recipient }}"
    bcc: "{{ email_bcc }}"
    subject: "{{ email_subject }}"
    body: "{{ email_body }}"
    attach:
      - "{{ ansible_log_path }}"
      - "{{ powershell_log_path }}"
  ignore_errors: true
  register: email_result
  no_log: true
  delegate_to: localhost
  when: email_logs | default(email_flags.email_logs) | bool

- name: Display email status
  ansible.builtin.debug:
    msg: "{{ 'Email sent successfully to ' + email_recipient if email_result.changed else 'Failed to send email: ' + email_result.msg }}"
  when: email_logs | default(email_flags.email_logs) | bool
