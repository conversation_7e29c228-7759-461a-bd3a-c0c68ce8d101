---
# Tasks for setting SMTP server based on DC location
# Author: CES Operational Excellence Team
# Contributors: <PERSON> Bin <PERSON> (7409), <PERSON><PERSON> (9094), <PERSON><PERSON> (9413)

- name: Include email configuration
  ansible.builtin.include_vars:
    file: "{{ playbook_dir }}/../vars/email_config.yml"

- name: Determine DC location from domain configuration
  ansible.builtin.set_fact:
    dc_location: "{{ domain_config[domain].dc | default('') }}"
  when: domain is defined and domain in domain_config

- name: Set SMTP server based on DC location (hdc1)
  ansible.builtin.set_fact:
    smtp_server: "{{ email_settings.smtp.hdc1_server }}"
    smtp_port: "{{ email_settings.smtp.port }}"
    smtp_from: "{{ email_settings.smtp.from }}"
  when: dc_location == 'hdc1'

- name: Set SMTP server based on DC location (hdc2)
  ansible.builtin.set_fact:
    smtp_server: "{{ email_settings.smtp.hdc2_server }}"
    smtp_port: "{{ email_settings.smtp.port }}"
    smtp_from: "{{ email_settings.smtp.from }}"
  when: dc_location == 'hdc2'

- name: Set default SMTP server if no DC location is found
  ansible.builtin.set_fact:
    smtp_server: "{{ email_settings.smtp.default_server }}"
    smtp_port: "{{ email_settings.smtp.port }}"
    smtp_from: "{{ email_settings.smtp.from }}"
  when: smtp_server is not defined

- name: Display selected SMTP server
  ansible.builtin.debug:
    msg: "Using SMTP server: {{ smtp_server }} for DC location: {{ dc_location | default('unknown') }}"
  when: email_report | bool or email_logs | bool
