---
# Tasks for generating consolidated reports for multi-domain operations using WeasyPrint
# Author: CES Operational Excellence Team
# Contributors: <PERSON> Bin <PERSON> (7409)

- name: Set consolidated report paths
  ansible.builtin.set_fact:
    consolidated_html_report_path: "{{ playbook_dir }}/../reports/consolidated/html/{{ ansible_date_time.date }}_{{ operation }}_{{ record_type }}_consolidated.html"
    consolidated_pdf_report_path: "{{ playbook_dir }}/../reports/consolidated/pdf/{{ ansible_date_time.date }}_{{ operation }}_{{ record_type }}_consolidated.pdf"
    operation_timestamp: "{{ ansible_date_time.date }}_{{ ansible_date_time.time | replace(':', '-') }}"

- name: Ensure consolidated report directories exist
  ansible.builtin.file:
    path: "{{ item }}"
    state: directory
    mode: '0755'
  loop:
    - "{{ playbook_dir }}/../reports/consolidated/html"
    - "{{ playbook_dir }}/../reports/consolidated/pdf"
  delegate_to: localhost
  run_once: true

- name: Generate consolidated HTML report
  ansible.builtin.template:
    src: "{{ playbook_dir }}/../roles/reporting/templates/enhanced_consolidated_report.html.j2"
    dest: "{{ consolidated_html_report_path }}"
    mode: '0644'
  delegate_to: localhost
  run_once: true

- name: Create a copy with timestamp for archiving
  ansible.builtin.copy:
    src: "{{ consolidated_html_report_path }}"
    dest: "{{ playbook_dir }}/../reports/consolidated/html/{{ operation }}_{{ record_type }}_consolidated_{{ operation_timestamp }}.html"
    mode: '0644'
    remote_src: true
  delegate_to: localhost
  run_once: true

- name: Convert HTML consolidated report to PDF using WeasyPrint
  ansible.builtin.script:
    cmd: |
      #!/usr/bin/env python3
      from weasyprint import HTML
      import sys
      
      html_path = sys.argv[1]
      pdf_path = sys.argv[2]
      
      HTML(html_path).write_pdf(pdf_path)
    args: "{{ consolidated_html_report_path }} {{ consolidated_pdf_report_path }}"
  delegate_to: localhost
  register: pdf_result
  failed_when: pdf_result.rc != 0
  run_once: true

- name: Display consolidated report paths
  ansible.builtin.debug:
    msg: |
      Consolidated report generated:
      - HTML: {{ consolidated_html_report_path }}
      - PDF: {{ consolidated_pdf_report_path }}
  delegate_to: localhost
  run_once: true
