---
# Tasks for emailing PDF reports
# Author: CES Operational Excellence Team
# Contributors: <PERSON> (7409)

- name: Include email configuration
  ansible.builtin.include_vars:
    file: "{{ playbook_dir }}/../vars/email_config.yml"

- name: Validate email recipient is provided
  ansible.builtin.assert:
    that:
      - email_recipient != ""
    fail_msg: "Email recipient is required for sending reports"

- name: Include dynamic email recipient selection
  ansible.builtin.include_tasks: set_email_recipient.yml

- name: Include dynamic SMTP server selection
  ansible.builtin.include_tasks: set_smtp_server.yml

- name: Set initial email subject
  ansible.builtin.set_fact:
    email_subject: "{{ email_settings.templates.report_subject }}"

- name: Format email subject with operation
  ansible.builtin.set_fact:
    email_subject: "{{ email_subject | replace('{operation}', operation | capitalize) }}"

- name: Format email subject with record type
  ansible.builtin.set_fact:
    email_subject: "{{ email_subject | replace('{record_type}', record_type | upper) }}"

- name: Format email subject with hostname
  ansible.builtin.set_fact:
    email_subject: "{{ email_subject | replace('{hostname}', hostname) }}"

- name: Format email subject with domain
  ansible.builtin.set_fact:
    email_subject: "{{ email_subject | replace('{domain}', domain) }}"

- name: Send email with report
  community.general.mail:
    host: "{{ smtp_server }}"
    port: "{{ smtp_port }}"
    from: "{{ smtp_from }}"
    to: "{{ email_recipient }}"
    bcc: "{{ email_bcc }}"
    subject: "{{ email_subject }}"
    body: |
      DNS Management Operation Report

      Operation: {{ operation | capitalize }}
      Record Type: {{ record_type | upper }}
      Hostname: {{ hostname }}.{{ domain }}
      Status: {{ 'Successful' if dns_operation_result.success else 'Failed' }}

      Details: {{ dns_operation_result.message }}

      Please see the attached PDF report for complete details.

      This is an automated message from the DNS Management System.
    attach:
      - "{{ report_dir }}/{{ report_filename }}"
  ignore_errors: true
  register: email_result

- name: Display email status
  ansible.builtin.debug:
    msg: "{{ 'Email sent successfully to ' + email_recipient if email_result.changed else 'Failed to send email: ' + email_result.msg }}"
