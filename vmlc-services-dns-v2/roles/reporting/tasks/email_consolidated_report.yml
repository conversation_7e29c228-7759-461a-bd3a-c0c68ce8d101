---
# Tasks for emailing consolidated reports
# Author: CES Operational Excellence Team
# Contributors: <PERSON> (7409)

- name: Include email configuration
  ansible.builtin.include_vars:
    file: "{{ playbook_dir }}/../vars/email_config.yml"

- name: Validate email recipient is provided
  ansible.builtin.assert:
    that:
      - email_recipient != ""
    fail_msg: "Email recipient is required for sending consolidated reports"

- name: Include dynamic email recipient selection
  ansible.builtin.include_tasks: set_email_recipient.yml

- name: Include dynamic SMTP server selection
  ansible.builtin.include_tasks: set_smtp_server.yml

- name: Set initial email subject
  ansible.builtin.set_fact:
    email_subject: "{{ email_settings.templates.consolidated_report_subject }}"

- name: Format email subject with operation
  ansible.builtin.set_fact:
    email_subject: "{{ email_subject | replace('{operation}', operation | capitalize) }}"

- name: Format email subject with record type
  ansible.builtin.set_fact:
    email_subject: "{{ email_subject | replace('{record_type}', record_type | upper) }}"

- name: Email consolidated report
  community.general.mail:
    host: "{{ smtp_server }}"
    port: "{{ smtp_port }}"
    from: "{{ smtp_from }}"
    to: "{{ email_recipient }}"
    bcc: "{{ email_bcc }}"
    subject: "{{ email_subject }}"
    body: |
      Hello,

      Please find attached the consolidated DNS Management report for the {{ operation | capitalize }} operation on {{ record_type | upper }} records.

      Operation details:
      - Operation: {{ operation | capitalize }}
      - Record Type: {{ record_type | upper }}
      - Ticket: {{ ticket }}
      - Domains: {{ domains }}
      - Hostnames: {{ hostnames }}

      This is an automated message from the DNS Management System.
    attach:
      - "{{ consolidated_pdf_report_path }}"
  ignore_errors: true
  register: email_result
  no_log: true
  delegate_to: localhost
  run_once: true

- name: Display email status
  ansible.builtin.debug:
    msg: "{{ 'Email sent successfully to ' + email_recipient if email_result.changed else 'Failed to send email: ' + email_result.msg }}"
  run_once: true
