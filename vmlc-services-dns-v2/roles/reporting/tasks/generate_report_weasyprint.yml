---
# Tasks for generating individual operation reports using WeasyPrint
# Author: CES Operational Excellence Team
# Contributors: <PERSON> Bin <PERSON> (7409)

- name: Set report paths
  ansible.builtin.set_fact:
    html_report_path: "{{ playbook_dir }}/../reports/html/{{ ansible_date_time.date }}_{{ hostname }}.{{ domain }}_{{ record_type }}_{{ operation }}.html"
    pdf_report_path: "{{ playbook_dir }}/../reports/pdf/{{ ansible_date_time.date }}_{{ hostname }}.{{ domain }}_{{ record_type }}_{{ operation }}.pdf"
    operation_timestamp: "{{ ansible_date_time.date }}_{{ ansible_date_time.time | replace(':', '-') }}"

- name: Ensure report directories exist
  ansible.builtin.file:
    path: "{{ item }}"
    state: directory
    mode: '0755'
  loop:
    - "{{ playbook_dir }}/../reports/html"
    - "{{ playbook_dir }}/../reports/pdf"
  delegate_to: localhost
  run_once: true

- name: Generate HTML report
  ansible.builtin.template:
    src: "{{ playbook_dir }}/../roles/reporting/templates/dns_report.j2"
    dest: "{{ html_report_path }}"
    mode: '0644'
  delegate_to: localhost

- name: Create a copy with timestamp for archiving
  ansible.builtin.copy:
    src: "{{ html_report_path }}"
    dest: "{{ playbook_dir }}/../reports/html/{{ hostname }}.{{ domain }}_{{ record_type }}_{{ operation }}_{{ operation_timestamp }}.html"
    mode: '0644'
    remote_src: true
  delegate_to: localhost

- name: Convert HTML report to PDF using WeasyPrint
  ansible.builtin.script:
    cmd: |
      #!/usr/bin/env python3
      from weasyprint import HTML
      import sys
      
      html_path = sys.argv[1]
      pdf_path = sys.argv[2]
      
      HTML(html_path).write_pdf(pdf_path)
    args: "{{ html_report_path }} {{ pdf_report_path }}"
  delegate_to: localhost
  register: pdf_result
  failed_when: pdf_result.rc != 0

- name: Display report paths
  ansible.builtin.debug:
    msg: |
      Report generated:
      - HTML: {{ html_report_path }}
      - PDF: {{ pdf_report_path }}
  delegate_to: localhost
