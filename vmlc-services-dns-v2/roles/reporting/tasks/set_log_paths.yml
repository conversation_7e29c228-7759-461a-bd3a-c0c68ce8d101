---
# Tasks for setting standardized log file paths
# Author: CES Operational Excellence Team
# Contributors: <PERSON> (7409), <PERSON><PERSON> (9094), <PERSON><PERSON> (9413)

- name: Include log configuration
  ansible.builtin.include_vars:
    file: "{{ playbook_dir }}/../vars/log_config.yml"

- name: Format date for log filenames
  ansible.builtin.set_fact:
    log_date: "{{ ansible_date_time.year }}{{ ansible_date_time.month }}{{ ansible_date_time.day }}"

- name: Set ticket number with default
  ansible.builtin.set_fact:
    log_ticket: "{{ ticket | default('NOTICKET') }}"

- name: Set hostname and domain for log filenames
  ansible.builtin.set_fact:
    log_hostname: "{{ hostname | default('unknown') }}"
    log_domain: "{{ domain | default('unknown') }}"
    log_record_type: "{{ record_type | default('unknown') | upper }}"
    log_operation: "{{ operation | default('unknown') | upper }}"

- name: Construct base log filename
  ansible.builtin.set_fact:
    log_base_name: "{{ log_date }}_{{ log_ticket }}_{{ log_hostname }}_{{ log_domain }}_{{ log_record_type }}_{{ log_operation }}"

- name: Set specific log file paths
  ansible.builtin.set_fact:
    ansible_log_path: "{{ log_settings.directories.ansible }}/{{ log_base_name }}_{{ log_settings.types.ansible }}{{ log_settings.format.extension }}"
    powershell_log_path: "{{ log_settings.directories.powershell }}/{{ log_base_name }}_{{ log_settings.types.powershell }}{{ log_settings.format.extension }}"
    progress_log_path: "{{ log_settings.directories.progress }}/{{ log_base_name }}_{{ log_settings.types.progress }}{{ log_settings.format.extension }}"
    report_log_path: "{{ log_settings.directories.ansible }}/{{ log_base_name }}_{{ log_settings.types.report }}{{ log_settings.format.extension }}"

- name: Set target server log paths
  ansible.builtin.set_fact:
    target_ansible_log_path: "{{ log_settings.target_server.base_path }}{{ log_base_name }}_{{ log_settings.types.ansible }}{{ log_settings.format.extension }}"
    target_powershell_log_path: "{{ log_settings.target_server.base_path }}{{ log_base_name }}_{{ log_settings.types.powershell }}{{ log_settings.format.extension }}"

- name: Display log paths
  ansible.builtin.debug:
    msg: "Log files will be created at: {{ ansible_log_path }} and {{ powershell_log_path }}"
  when: log_level | default(log_settings.levels.info) | upper == log_settings.levels.debug
