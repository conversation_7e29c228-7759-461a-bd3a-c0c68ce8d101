---
# Main tasks file for reporting role
# Author: CES Operational Excellence Team
# Contributors: <PERSON> (7409)

- name: Generate individual report
  ansible.builtin.include_tasks: generate_report_weasyprint.yml
  when:
    - generate_report | bool
    - (domains is not defined or domains == "")
    - (hostnames is not defined or hostnames == "")

- name: Generate consolidated report
  ansible.builtin.include_tasks: generate_consolidated_report_weasyprint.yml
  when:
    - generate_report | bool
    - domains is defined and domains != ""
    - hostnames is defined and hostnames != ""
    - consolidated_results is defined and consolidated_results | length > 0

- name: Email individual report
  ansible.builtin.include_tasks: email_report.yml
  when:
    - generate_report | bool
    - email_report | bool
    - email_recipient != ""
    - (domains is not defined or domains == "")
    - (hostnames is not defined or hostnames == "")

- name: Email consolidated report
  ansible.builtin.include_tasks: email_consolidated_report.yml
  when:
    - generate_report | bool
    - email_report | bool
    - email_recipient != ""
    - domains is defined and domains != ""
    - hostnames is defined and hostnames != ""
    - consolidated_results is defined and consolidated_results | length > 0

- name: <PERSON>ail logs
  ansible.builtin.include_tasks: email_logs.yml
  when:
    - email_logs | bool
    - email_recipient != ""
