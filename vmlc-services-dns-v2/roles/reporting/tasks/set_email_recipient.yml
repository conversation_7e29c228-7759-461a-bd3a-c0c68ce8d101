---
# Tasks for setting email recipient based on domain
# Author: CES Operational Excellence Team
# Contributors: <PERSON> Bin <PERSON> (7409), <PERSON><PERSON> (9094), <PERSON><PERSON> (9413)

- name: Include email configuration
  ansible.builtin.include_vars:
    file: "{{ playbook_dir }}/../vars/email_config.yml"

- name: Set domain for email recipient selection
  ansible.builtin.set_fact:
    email_domain: "{{ current_domain | default(domain) }}"
  when: current_domain is defined or domain is defined

- name: Set email recipient based on domain (for SHS domains)
  ansible.builtin.set_fact:
    dynamic_email_recipient: "{{ email_settings.recipients.domain_specific[email_domain] }}"
    email_bcc: "{{ email_settings.recipients.bcc }}"
  when:
    - email_domain is defined
    - email_domain in email_settings.recipients.domain_specific
    - not testing_mode | default(email_flags.testing_mode) | bool

- name: Set default email recipient (for other domains)
  ansible.builtin.set_fact:
    dynamic_email_recipient: "{{ email_settings.recipients.domain_specific.default }}"
    email_bcc: "{{ email_settings.recipients.bcc }}"
  when:
    - email_domain is defined
    - email_domain not in email_settings.recipients.domain_specific
    - not testing_mode | default(email_flags.testing_mode) | bool

- name: Set testing email recipient (when testing mode is enabled)
  ansible.builtin.set_fact:
    dynamic_email_recipient: "{{ email_settings.recipients.testing }}"
    email_bcc: "{{ email_settings.recipients.bcc }}"
  when: testing_mode | default(email_flags.testing_mode) | bool

- name: Override email_recipient with dynamic recipient
  ansible.builtin.set_fact:
    email_recipient: "{{ dynamic_email_recipient }}"
  when: dynamic_email_recipient is defined

- name: Display selected email recipient
  ansible.builtin.debug:
    msg: "Email will be sent to: {{ email_recipient }} (BCC: {{ email_bcc }}) {{ '(TESTING MODE)' if testing_mode | default(email_flags.testing_mode) | bool else '' }}"
  when: email_report | default(email_flags.email_report) | bool or email_logs | default(email_flags.email_logs) | bool
