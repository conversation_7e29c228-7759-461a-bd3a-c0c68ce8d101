# Reporting Role

## Overview

This role handles report generation, email notifications, and log management for DNS operations.

## Author

CES Operational Excellence Team

## Contributors

<PERSON> Bin <PERSON> (7409)

## Usage

This role is called by the main playbook after DNS operations are completed:

```yaml
- name: Include reporting role
  include_role:
    name: reporting
```

## Parameters

- **generate_report**: Whether to generate a PDF report (default: true)
- **email_report**: Whether to email the PDF report (default: false)
- **email_consolidated_report**: Whether to email a consolidated report for multi-domain operations (default: false)
- **email_recipient**: Email address to send the report to
- **email_logs**: Whether to email logs as attachments (default: false)
- **store_logs_target_server**: Whether to store logs on target servers (default: true)

## Task Files

- **main.yml**: Entry point that handles report generation and distribution
- **generate_report.yml**: Generates HTML and PDF reports
- **email_report.yml**: Emails reports to specified recipients
- **email_consolidated_report.yml**: Emails consolidated reports for multi-domain operations
- **email_logs.yml**: Emails logs as attachments
- **store_logs.yml**: Stores logs on target servers

## Report Types

The role can generate several types of reports:

1. **HTML Reports**: Basic HTML reports with operation details
2. **PDF Reports**: Professional PDF reports converted from HTML
3. **Consolidated Reports**: Combined reports for multi-domain operations

## Email Configuration

To send email notifications, the following variables must be set:

```yaml
smtp_server: smtp.example.com
smtp_port: 25
smtp_username: user  # Optional
smtp_password: password  # Optional
smtp_secure: starttls  # Optional: starttls, ssl, or omit for no encryption
smtp_from: <EMAIL>
```

## Log Storage

Logs are stored on target servers based on environment:
- For staging environments: SHSADMTVDSEC02.ses.shsu.com.sg
- For production environments: SHSADMTVPSEC12.shses.shs.com.sg

Logs are stored in the `C:\OE_AAP_LOGS\` directory with the following naming convention:
```
<AAP JOB ID>_<DDMMYYYY>_<TICKET>_DNS_<ANSIBLE/POWERSHELL>_<OPERATION>.log
```
