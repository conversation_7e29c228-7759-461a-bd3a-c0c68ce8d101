# =========================================================================
# Ansible Configuration for DNS Management Automation v2
# =========================================================================
# This configuration file optimizes Ansible for DNS management operations
# following OXAF infrastructure automation patterns and enterprise
# security requirements.
#
# Author: CES Operational Excellence Team
# Contributor: <PERSON><PERSON> (7409)
# Framework: Operational Excellence Automation Framework (OXAF)
# Version: 2.0
# =========================================================================

[defaults]
# Basic Configuration
inventory = inventory/production
remote_user = ansible
ask_pass = false
ask_sudo_pass = false
ask_vault_pass = false

# Host Key Checking (disabled for Windows environments)
host_key_checking = false
ssh_args = -o ControlMaster=auto -o ControlPersist=60s -o UserKnownHostsFile=/dev/null -o StrictHostKeyChecking=no

# Performance Optimization
forks = 10
poll_interval = 15
timeout = 30
gathering = smart
fact_caching = memory
fact_caching_timeout = 86400

# Logging Configuration following organizational standards
log_path = /var/log/ansible/dns-automation.log
display_skipped_hosts = false
display_ok_hosts = true
display_failed_stderr = true

# Callback Plugins for enhanced logging
stdout_callback = yaml
callback_whitelist = timer, profile_tasks, log_plays

# Security Configuration
vault_password_file = ~/.ansible/vault_pass
vault_identity_list = default@~/.ansible/vault_pass

# Retry Configuration
retry_files_enabled = true
retry_files_save_path = ~/.ansible/retry

# Role and Collection Paths
roles_path = roles:~/.ansible/roles:/usr/share/ansible/roles:/etc/ansible/roles
collections_paths = ~/.ansible/collections:/usr/share/ansible/collections

# Module Configuration
module_utils = module_utils
action_plugins = action_plugins
lookup_plugins = lookup_plugins
filter_plugins = filter_plugins
test_plugins = test_plugins

# Deprecation Warnings
deprecation_warnings = true
command_warnings = false

# Error Handling
error_on_undefined_vars = true
force_valid_group_names = ignore

# Jinja2 Configuration
jinja2_extensions = jinja2.ext.do,jinja2.ext.i18n

[inventory]
# Inventory Configuration
enable_plugins = host_list, script, auto, yaml, ini, toml
cache = true
cache_plugin = memory
cache_timeout = 3600
cache_connection = ~/.ansible/tmp

[privilege_escalation]
# Privilege Escalation (primarily for Linux systems)
become = false
become_method = sudo
become_user = root
become_ask_pass = false

[paramiko_connection]
# Paramiko SSH Configuration
record_host_keys = false
pty = false

[ssh_connection]
# SSH Connection Configuration
ssh_args = -C -o ControlMaster=auto -o ControlPersist=60s -o UserKnownHostsFile=/dev/null -o StrictHostKeyChecking=no
pipelining = true
control_path_dir = ~/.ansible/cp
control_path = %(directory)s/%%h-%%p-%%r

[persistent_connection]
# Persistent Connection Configuration
connect_timeout = 30
connect_retry_timeout = 15
command_timeout = 30

[colors]
# Output Colorization
highlight = white
verbose = blue
warn = bright purple
error = red
debug = dark gray
deprecate = purple
skip = cyan
unreachable = red
ok = green
changed = yellow
diff_add = green
diff_remove = red
diff_lines = cyan

[selinux]
# SELinux Configuration
special_context_filesystems = fuse, nfs, vboxsf, ramfs, 9p, vfat

[diff]
# Diff Configuration
always = false
context = 3

[galaxy]
# Ansible Galaxy Configuration
server_list = automation_hub, galaxy

[galaxy_server.automation_hub]
url = https://cloud.redhat.com/api/automation-hub/
auth_url = https://sso.redhat.com/auth/realms/redhat-external/protocol/openid-connect/token

[galaxy_server.galaxy]
url = https://galaxy.ansible.com/
