---
# =========================================================================
# Production Inventory for DNS Management Automation v2
# =========================================================================
# This inventory defines the production DNS servers and their configuration
# for DNS management operations following OXAF patterns.
#
# Author: CES Operational Excellence Team
# Contributor: <PERSON> (7409)
# Framework: Operational Excellence Automation Framework (OXAF)
# Version: 2.0
# =========================================================================

all:
  children:
    dns_servers:
      children:
        production_dns:
          hosts:
            HISADMTVPSEC11.healthgrp.com.sg:
              dns_domain: "healthgrp.com.sg"
              var_environment: "production"
              dns_security_zone: "main"
              ansible_host: "HISADMTVPSEC11.healthgrp.com.sg"

            HISADMTVPSEC11.nhg.local:
              dns_domain: "nhg.local"
              var_environment: "production"
              dns_security_zone: "nhg"
              ansible_host: "HISADMTVPSEC11.nhg.local"

            AICADMTVPSEC11.aic.local:
              dns_domain: "aic.local"
              var_environment: "production"
              dns_security_zone: "aic"
              ansible_host: "AICADMTVPSEC11.aic.local"

            HISADMTVPSEC11.iltc.healthgrp.com.sg:
              dns_domain: "iltc.healthgrp.com.sg"
              var_environment: "production"
              dns_security_zone: "iltc"
              ansible_host: "HISADMTVPSEC11.iltc.healthgrp.com.sg"

            SHSADMTVPSEC12.shses.shs.com.sg:
              dns_domain: "shses.shs.com.sg"
              var_environment: "production"
              dns_security_zone: "singhealth"
              ansible_host: "SHSADMTVPSEC12.shses.shs.com.sg"

        staging_dns:
          hosts:
            HISADMTVSSEC01.healthgrpexts.com.sg:
              dns_domain: "healthgrpexts.com.sg"
              var_environment: "staging"
              dns_security_zone: "external"
              ansible_host: "HISADMTVSSEC01.healthgrpexts.com.sg"

            HISADMTVSSEC02.nnstg.local:
              dns_domain: "nnstg.local"
              var_environment: "staging"
              dns_security_zone: "internal"
              ansible_host: "HISADMTVSSEC02.nnstg.local"

            SHSADMTVDSEC02.ses.shsu.com.sg:
              dns_domain: "ses.shsu.com.sg"
              var_environment: "staging"
              dns_security_zone: "singhealth"
              ansible_host: "SHSADMTVDSEC02.ses.shsu.com.sg"

        development_dns:
          hosts:
            HISADMTVDSEC01.devhealthgrp.com.sg:
              dns_domain: "devhealthgrp.com.sg"
              var_environment: "development"
              dns_security_zone: "dev"
              ansible_host: "HISADMTVDSEC01.devhealthgrp.com.sg"

  vars:
    # Common Windows connection settings
    ansible_connection: winrm
    ansible_winrm_transport: ntlm
    ansible_winrm_server_cert_validation: ignore
    ansible_port: 5985
    ansible_winrm_scheme: http

    # Performance settings
    ansible_winrm_operation_timeout_sec: 300
    ansible_winrm_read_timeout_sec: 600

    # DNS Management specific settings
    dns_management_version: "2.0"
    dns_framework: "OXAF"

    # Security settings
    ansible_winrm_ca_trust_path: ""
    ansible_winrm_cert_pem: ""
    ansible_winrm_cert_key_pem: ""
