---
# =========================================================================
# Ansible Collections Requirements for DNS Management Automation v2
# =========================================================================
# This file defines the required Ansible collections for the DNS automation
# solution, following the v1 pattern for CyberArk integration
#
# Framework: Operational Excellence Automation Framework (OXAF)
# Author: CES Operational Excellence Team
# Contributor: <PERSON> (7409)
# Version: 2.0
# =========================================================================

collections:
  # CyberArk Credential Provider Collection (from v1)
  - name: cloud_cpe.cyberark_ccp
    version: ">=1.0.0"
    
  # Core Ansible Collections
  - name: ansible.windows
    version: ">=1.0.0"
    
  - name: community.general
    version: ">=1.0.0"
    
  - name: ansible.posix
    version: ">=1.0.0"
    
  # Additional collections for enhanced functionality
  - name: community.crypto
    version: ">=1.0.0"
    
  - name: ansible.utils
    version: ">=1.0.0"
