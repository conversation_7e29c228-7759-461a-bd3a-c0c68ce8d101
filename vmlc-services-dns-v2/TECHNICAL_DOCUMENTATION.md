# DNS Management System - Technical Documentation

## Author

CES Operational Excellence Team

## Contributors

<PERSON> (7409)

## Table of Contents

1. [Introduction](#1-introduction)
   1.1. [Key Features](#11-key-features)
2. [Architecture](#2-architecture)
3. [Component Overview](#3-component-overview)
   3.1. [Ansible Playbooks](#31-ansible-playbooks)
   3.2. [Ansible Roles](#32-ansible-roles)
   3.3. [PowerShell Scripts](#33-powershell-scripts)
4. [Sequence Diagram](#4-sequence-diagram)
   4.1. [Key Interactions](#41-key-interactions)
5. [Authentication and Security](#5-authentication-and-security)
   5.1. [Credential Management](#51-credential-management)
   5.2. [Secure Communication](#52-secure-communication)
6. [Logging System](#6-logging-system)
   6.1. [Log Levels](#61-log-levels)
   6.2. [Log Storage](#62-log-storage)
   6.3. [Log Format](#63-log-format)
7. [Reporting System](#7-reporting-system)
   7.1. [PDF Generation](#71-pdf-generation)
   7.2. [Report Types](#72-report-types)
   7.3. [Report Content](#73-report-content)
   7.4. [External Storage](#74-external-storage)
8. [Error Handling](#8-error-handling)
   8.1. [Validation Checks](#81-validation-checks)
   8.2. [Graceful Failure](#82-graceful-failure)
   8.3. [Retry Mechanism](#83-retry-mechanism)
9. [Configuration](#9-configuration)
   9.1. [DNS Server Configuration](#91-dns-server-configuration)
   9.2. [DNS Record Removal Safety Checks](#92-dns-record-removal-safety-checks)
   9.3. [Special PTR Record Management](#93-special-ptr-record-management)
   9.4. [Playbook Parameters](#94-playbook-parameters)
10. [Idempotency](#10-idempotency)
    10.1. [Idempotency Implementation](#101-idempotency-implementation)
    10.2. [Examples of Idempotent Behavior](#102-examples-of-idempotent-behavior)
    10.3. [Audit Trail](#103-audit-trail)
11. [Best Practices](#11-best-practices)
    11.1. [DNS Operations](#111-dns-operations)
    11.2. [Ansible Usage](#112-ansible-usage)
    11.3. [PowerShell Scripts](#113-powershell-scripts)
12. [Performance Optimization](#12-performance-optimization)
    12.1. [Parallel Processing](#121-parallel-processing)
    12.2. [Progress Tracking](#122-progress-tracking)

## 1. Introduction

The DNS Management System is an enterprise-level Ansible project designed to manage DNS records (A, PTR, CNAME) across multiple domains. It leverages PowerShell scripts executed on domain controllers via Ansible for direct interaction with DNS servers, while using Ansible for orchestration, reporting, and logging.

### 1.1. Key Features

- Domain-to-server mapping approach
- Dynamic ADMT server selection based on domain
- Support for multi-domain operations
- Automatic PTR record management when adding/removing A records
- Customizable TTL values for DNS records
- Comprehensive error handling and logging
- PDF report generation with certificates of successful operations
- Secure credential handling with hardcoded credentials
- Support for running on target servers via Ansible Automation Platform
- Parallel processing for improved performance
- Progress tracking for multi-domain operations

## 2. Architecture

The DNS Management System follows a layered architecture:

1. **Orchestration Layer** (Ansible Playbooks)
   - Manages the overall workflow
   - Handles parameter validation
   - Coordinates between different roles

2. **Operation Layer** (DNS Operations Role)
   - Executes DNS operations via PowerShell on ADMT servers
   - Manages logging and error handling
   - ADMT servers interact with domain controllers for DNS operations

3. **Reporting Layer** (Reporting Role)
   - Generates PDF reports
   - Sends email notifications
   - Uploads logs to external systems

4. **Infrastructure Layer** (Domain Controllers)
   - Executes DNS commands
   - Manages DNS records
   - Provides feedback to the Operation Layer

## 3. Component Overview

### 3.1. Ansible Playbooks

- **manage_dns.yml**: Main playbook that orchestrates DNS operations
  - Validates parameters
  - Sets up server and credential variables
  - Includes appropriate roles based on operation
  - Handles reporting and notification

### 3.2. Ansible Roles

- **credential_selection**: Handles domain-specific credential selection
  - Centralizes credential selection logic
  - Dynamically selects credentials based on domain
  - Sets appropriate variables for authentication
  - Maintains security with no_log directives

- **dns_operations**: Executes DNS operations
  - Copies PowerShell script to ADMT server
  - Executes the script with appropriate parameters on ADMT server
  - ADMT server communicates with domain controller for DNS operations
  - Handles logging and error reporting

- **reporting**: Generates and distributes reports
  - Creates PDF reports
  - Sends email notifications
  - Uploads logs to external systems

### 3.3. PowerShell Scripts

- **set-dns.ps1**: Core script for DNS operations
  - Supports add, remove, update, verify operations
  - Handles A, PTR, and CNAME records
  - Supports domain-specific PTR DNS servers
  - Provides detailed logging
  - Returns operation results as JSON

## 4. Sequence Diagram

![DNS Management Flow Diagram](../images/DNS_Management_Flow.png)

*The diagram above illustrates the complete workflow of the DNS Management system, showing the interactions between the user, Ansible Automation Platform, CyberArk, ADMT servers, DNS Servers, and the Reporting System. It includes the special handling for PTR records when managing A records.*

### 4.1. Key Interactions

1. **User Initiation**: The user executes the Ansible playbook with required parameters.

2. **Pre-tasks**:
   - Validate parameters
   - Retrieve domain information
   - Set server and credential variables

3. **Preparation**:
   - Create log directories
   - Copy PowerShell script to ADMT server

4. **DNS Operations**:
   - Set log paths
   - Create log directory on ADMT server
   - Execute the appropriate DNS operation (add, remove, update, verify) from ADMT server
   - ADMT server communicates with domain controller to perform DNS operations
   - For A records, handle corresponding PTR records based on configuration
   - Return operation result and logs

5. **Reporting**:
   - Generate HTML report
   - Convert HTML to PDF using WeasyPrint (Python-based HTML to PDF converter)
   - Email report if configured
   - Email logs if configured
   - Store logs on target servers based on environment (staging/production)

6. **Completion**: Return operation status to the user

## 5. Authentication and Security

### 5.1. Credential Management

The DNS Management System uses a secure approach to credential management:

1. **Hardcoded Credentials**:
   - Credentials are stored in the `credentials.yml` file (encrypted with ansible-vault)
   - The `credential_selection` role is used to select the appropriate credentials based on the domain
   - Credentials are mapped to domains in the unified domain configuration
   - This approach ensures that the correct credentials are used for each domain

2. **Double-Hop Authentication**:
   - Ansible's `become` mechanism is used for double-hop authentication
   - The following parameters are used:
     ```yaml
     become: true
     become_method: runas
     become_flags: logon_type=new_credentials logon_flags=netcredentials_only
     ```

3. **Domain-Specific Credentials**:
   - Each domain has its own set of credentials
   - Credentials are mapped in the `dns_servers.yml` configuration file
   - The `credential_selection` role centralizes credential selection logic
   - Credentials are dynamically selected based on the domain being processed

### 5.2. Secure Communication

- All communication with DNS servers is encrypted
- PowerShell scripts do not store or expose credentials
- Sensitive information is not logged
- Secure channel is used for all DNS operations
- Kerberos authentication is used where possible

## 6. Logging System

The DNS Management System includes comprehensive logging functionality:

### 6.1. Log Levels

- **Error**: Critical errors that prevent operation
- **Warning**: Non-critical issues that might need attention
- **Info**: Normal operational information
- **Debug**: Detailed information for troubleshooting

### 6.2. Log Storage

- **Ansible Logs**: Stored in the `logs/ansible` directory
- **PowerShell Logs**: Stored in the `logs/powershell` directory
- **Log Rotation**: Logs older than the specified retention period are automatically removed

### 6.3. Log Format

Each log entry includes:
- Timestamp
- Log level
- Message
- Error details (for Error level)

## 7. Reporting System

The DNS Management System generates comprehensive reports for DNS operations:

### 7.1. PDF Generation

The system uses `WeasyPrint` (a Python-based HTML to PDF converter) to convert HTML reports to PDF format. This library is installed in the Python environment of the Ansible control node and is called via a Python script.

### 7.2. Report Types

- **PDF Reports**: Professional PDF reports with operation details
- **Email Notifications**: Reports can be sent via email
- **Log Emails**: Logs can be sent via email

### 7.3. Report Content

- Operation details (type, hostname, domain, etc.)
- Operation result (success/failure)
- Timestamp
- Domain controller used
- ADMT server used
- Error messages (if applicable)
- User who initiated the operation
- Ticket number for auditing

### 7.4. External Storage

Reports and logs are stored on target servers based on environment:
- For staging environments (devhealthgrp.com.sg, healthgrpexts.com.sg, nnstg.local, ses.shsu.com.sg): SHSADMTVDSEC02.ses.shsu.com.sg
- For production environments (all other domains): SHSADMTVPSEC12.shses.shs.com.sg

Logs are stored in the `C:\OE_AAP_LOGS\` directory with the following naming convention:
```
<AAP JOB ID>_<DDMMYYYY>_<TICKET>_DNS_<ANSIBLE/POWERSHELL>_<OPERATION>.log
```

## 8. Error Handling

The DNS Management System includes robust error handling:

### 8.1. Validation Checks

- Parameter validation before operations
- Domain existence validation
- Required parameter checks
- Ticket parameter is validated for auditing and tracking
- Operation-specific parameters are validated based on the operation type
- Multi-domain operations validate that hostnames, domains, and IP addresses have the same number of entries

### 8.2. Graceful Failure

- Detailed error messages
- Non-zero exit codes for failed operations
- Error logging at appropriate levels
- PowerShell script includes try-catch blocks for all operations
- Detailed error messages are returned as JSON
- Error information is logged to the PowerShell log file

### 8.3. Retry Mechanism

- DNS server failover if primary is unavailable
- Verification after operations
- Automatic retry for transient network issues
- Ansible tasks include proper error handling
- Failed operations are reported with detailed error messages
- Logs are retrieved from the ADMT server for troubleshooting

## 9. Configuration

### 9.1. Domain Mapping Configuration

The `domain_mapping.yml` file maps domains to their ADMT servers and required variables for CyberArk integration:

```yaml
domain_host_vars:
  devhealthgrp.com.sg:
    admt_server: HISADMTVDSEC01.devhealthgrp.com.sg
    os: win
    environment: stg
    network_zone: tsz
    dc: hdc1  # Data center location
  healthgrpexts.com.sg:
    admt_server: HISADMTVSSEC01.healthgrpexts.com.sg
    os: win
    environment: stg
    network_zone: tsz
    dc: hdc1  # Data center location
  # Other domains...
```

This mapping is used to:
1. Select the appropriate ADMT server based on the domain parameter
2. Set the required variables for CyberArk integration
3. Provide a centralized configuration for domain-specific settings

### 9.2. Unified Domain Configuration

The `unified_domain_config.yml` file serves as a single source of truth for all domain-related configuration:

```yaml
domain_config:
  example.com:
    # ADMT server information (for first hop)
    admt_server: admt01.example.com
    os: win
    environment: prd
    network_zone: mgt
    dc: hdc1

    # DNS server information (for second hop)
    dns_server: dc01.example.com
    dns_username: "{{ example_dns_username }}"
    dns_password: "{{ example_dns_password }}"
    description: "Example Domain"

    # Special configurations
    ptr_dns_server: "{{ dns_server }}"  # Default to same as DNS server
```

This unified approach eliminates duplication and provides a clear, centralized configuration for all domain-related settings.

### 9.2. DNS Record Removal Safety Checks

The DNS Management System includes safety checks to prevent accidental removal of DNS records for live servers:

1. **Connectivity Check**:
   - Before removing A or PTR records, the system checks if the host is responding to ping
   - If the host is alive, the removal operation is aborted with a warning message
   - This prevents accidental removal of DNS records for active servers

2. **Force Removal Option**:
   - The `force_remove` parameter can be used to override the safety check
   - This allows removal of DNS records for live hosts when necessary
   - The parameter is passed to the PowerShell script as a `-Force` switch

3. **Implementation**:
   - The connectivity check is performed using the `Test-HostConnectivity` function in the PowerShell script
   - The function uses the .NET `System.Net.NetworkInformation.Ping` class with a 1-second timeout
   - The check is performed at both the A record and PTR record removal stages

### 9.3. Special PTR Record Management

The DNS Management System supports special PTR record management for specific domains:

1. **Domain-Specific PTR DNS Servers**:
   - For domain `ses.shsu.com.sg`, PTR records are created on `shdcvsys22h1.shsu.com.sg`
   - For domain `shses.shs.com.sg`, PTR records are created on `sesdcvpsys11.shs.com.sg`
   - For all other domains, PTR records are created on the same DNS server as A records

2. **Implementation**:
   - The PowerShell script accepts a `PTRDNSServer` parameter
   - The Ansible playbook sets this parameter based on the domain
   - When managing A records with PTR management enabled, the appropriate PTR DNS server is used

3. **Configuration**:
   - The domain-to-PTR server mapping is defined in the playbook and process_domain tasks
   - This approach allows for flexible PTR record management without changing the core logic

### 9.4. Playbook Parameters

The `manage_dns.yml` playbook accepts various parameters:

#### Required Parameters
- `operation`: The operation to perform (add, remove, update, verify)
- `record_type`: The type of DNS record (A, PTR, CNAME)
- `hostname`: The hostname part of the DNS record
- `domain`: The domain part of the DNS record (for single domain operations)
- `ticket`: Ticket number for auditing and tracking (also used for CyberArk integration as `var_sr_number`)

#### Operation-Specific Parameters
- `ip_address`: The IP address for A or PTR records (required for add/remove/update operations)
- `cname_target`: The target hostname for CNAME records (required for CNAME operations)
- `domains`: Comma-separated list of domains (for multi-domain operations)
- `hostnames`: Comma-separated list of hostnames (for multi-domain operations)
- `ip_addresses`: Comma-separated list of IP addresses (for multi-domain operations)

#### Optional Parameters
- `ttl`: Time to live for the DNS record (default: 3600)
- `description`: Description for the DNS record
- `manage_ptr`: Whether to manage PTR records for A records (default: true)
- `force_remove`: Override the connectivity check when removing DNS records (default: false)
- `generate_report`: Whether to generate a PDF report (default: true)
- `email_report`: Whether to email the report (default: false)
- `email_recipient`: Email recipient for the report

#### Credential Selection Parameters
The credential selection role uses the following parameters:
- `domain`: The domain for which to select credentials
- `domain_config`: The unified domain configuration containing credential information

These parameters are used to select the appropriate credentials for each domain from the unified domain configuration.

### 9.5. WinRM Connection Parameters

The DNS Management System uses inventory-based WinRM connection parameters for Windows ADMT servers:

1. **Inventory-Based Configuration**:
   - WinRM connection parameters are defined in the Ansible inventory
   - This includes `ansible_connection`, `ansible_user`, `ansible_password`, etc.
   - The system uses these parameters automatically when delegating tasks

2. **Two-Step Authentication Process**:
   - First step: Hardcoded credentials are used for initial authentication to ADMT servers
   - Second step: For tasks that need to access DNS servers, hardcoded credentials are used with `become` parameters
   - This approach is necessary because Kerberos tickets cannot be reused for the second hop

3. **Double-Hop Authentication**:
   - For tasks that require access to DNS servers, the system uses `become` with `logon_type=new_credentials logon_flags=netcredentials_only`
   - This allows for proper authentication to the DNS servers via the ADMT server
   - The credentials for this second hop are provided via `ansible_become_user` and `ansible_become_password`

### 9.6. Role Path Configuration

The DNS Management System uses a dynamic role path configuration to ensure that roles are correctly located regardless of the execution environment:

1. **Role Path Variable**:
   - `roles_path: "{{ playbook_dir }}/../roles"` is defined in the playbook
   - This variable points to the correct location of the roles directory

2. **Role Includes**:
   - All role includes use the `roles_path` variable
   - Example: `name: "{{ roles_path }}/dns_operations"`

3. **Task Includes**:
   - Task includes also use the `roles_path` variable
   - Example: `ansible.builtin.include_tasks: "{{ roles_path }}/dns_operations/tasks/process_async_results.yml"`

This approach ensures that the playbook can find the roles regardless of how it's executed (command line, AAP, etc.).

## 10. Idempotency

The DNS Management System is designed to be fully idempotent, ensuring that operations can be safely repeated without causing unintended side effects. This is a critical feature for auditing, reliability, and compliance purposes.

### 10.1. Idempotency Implementation

- **Existence Checks**: Before performing any operation, the system checks the current state to determine if changes are needed
- **State Comparison**: For update operations, the system compares the desired state with the current state
- **Change Tracking**: All operations return a `changed` flag indicating whether any changes were actually made

### 10.2. Examples of Idempotent Behavior

#### Add Operation
```powershell
# When adding a record that already exists with the same values
return @{
    success = $true
    message = "A record $fqdn already exists with IP $IPAddress. No changes made."
    changed = $false
}
```

#### Remove Operation
```powershell
# When removing a record that doesn't exist
return @{
    success = $true
    message = "A record $fqdn does not exist. No changes made."
    changed = $false
}
```

#### Update Operation
```powershell
# When updating a record with the same values (only TTL changes)
if ($existingTarget -eq $Target) {
    $newRecord = $existingRecord.Clone()
    $newRecord.TimeToLive = New-TimeSpan -Seconds $TTL
    Set-DnsServerResourceRecord -ZoneName $Domain -OldInputObject $existingRecord -NewInputObject $newRecord
    return @{
        success = $true
        message = "Updated TTL for CNAME record $fqdn"
        changed = $true
    }
}
```

### 10.3. Audit Trail

The idempotent design provides a clear audit trail:
- Logs indicate whether changes were made or if the system determined no changes were necessary
- Reports include the state before and after operations
- The `changed` flag in the JSON output can be used for compliance reporting

## 11. Best Practices

### 11.1. DNS Operations

1. **Verify Before Modify**: Always verify records before modifying them
2. **PTR Record Management**: Maintain consistency between A and PTR records
3. **Descriptive Names**: Use descriptive names for DNS records
4. **Appropriate TTL**: Set appropriate TTL values based on record purpose

### 11.2. Ansible Usage

1. **Idempotent Operations**: Ensure operations are idempotent
2. **Proper Error Handling**: Handle errors gracefully
3. **Comprehensive Logging**: Log all operations with appropriate detail
4. **Secure Credential Management**: Use CyberArk or other secure credential stores

### 11.3. PowerShell Scripts

1. **Error Handling**: Include proper error handling in all scripts
2. **Logging**: Implement comprehensive logging
3. **Parameter Validation**: Validate all parameters before use
4. **Return Structured Data**: Return operation results as structured data (JSON)

## 12. Performance Optimization

### 12.1. Parallel Processing

The DNS Management System uses Ansible's asynchronous task execution to process multiple domain-hostname pairs in parallel:

1. **Asynchronous Execution**: Each domain-hostname pair is processed asynchronously using Ansible's `async` and `poll` parameters
2. **Job Tracking**: Each asynchronous job is tracked and monitored for completion
3. **Result Collection**: Results from all jobs are collected and consolidated after completion
4. **Scalability**: The system can handle many domain-hostname pairs efficiently, with performance limited only by available system resources

This parallel processing approach significantly improves performance when working with multiple domains, especially in environments with many domain controllers.

### 12.2. Progress Tracking

The system includes a comprehensive progress tracking mechanism for multi-domain operations:

1. **Real-time Progress Display**:
   - Visual progress bar showing percentage complete
   - Count of completed and remaining domain-hostname pairs
   - Information about the most recently completed operation

2. **Progress Logging**:
   - Detailed progress logs stored in the `logs/progress` directory
   - Timestamped entries for each stage of the operation
   - Lists of completed and pending domains
   - Final summary log with operation statistics

3. **Configurable Behavior**:
   - Progress display can be enabled/disabled via the `show_progress` variable
   - Progress logging can be enabled/disabled via the `log_progress` variable
   - Both are enabled by default in `vars/defaults.yml`

This progress tracking system provides visibility into long-running operations, making it easier to monitor and troubleshoot multi-domain tasks.

