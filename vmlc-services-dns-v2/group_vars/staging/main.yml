# =========================================================================
# Staging Environment Configuration for DNS Management Automation v2
# =========================================================================
# This file contains staging-specific configuration variables
# It includes JIRA integration settings for the UAT environment
#
# Framework: Operational Excellence Automation Framework (OXAF)
# Author: CES Operational Excellence Team
# Contributor: <PERSON> (7409)
# Version: 2.0
# =========================================================================

# =========================
# Staging Environment Settings
# =========================

# Environment identification
var_environment: "staging"
deployment_mode: "staging"

# =========================
# Staging Integration Configuration
# =========================

# ITSM Integration for staging
itsm_integration:
#  enabled: true
  system: "jira"
  update_tickets: true
  create_change_requests: false
  approval_required: false
  environment: "staging"

  # Staging JIRA Configuration (uses UAT instance)
  jira_config:
    instance: "uat"
    base_url: "https://jsd-uat.hcloud.healthgrp.com.sg"
    project_key: "SR"

    # Staging-specific settings
    timeout_seconds: 30
    retry_attempts: 3
    validate_ssl: false

    # Relaxed error handling for staging
    ignore_errors: true
    log_failures: true
    notify_on_failure: false
    escalate_on_failure: false

# Staging JIRA Credentials (CyberArk collection-based)
jira_credentials:
  # CyberArk account names for credential retrieval
  username_account: "JIRA_UAT_USERNAME"
  password_account: "JIRA_UAT_PASSWORD"
  grid_token_account: "JIRA_UAT_GRID_TOKEN"

  # API endpoints
  api_base_url: "https://jsd-uat.hcloud.healthgrp.com.sg/rest/api/2"
  grid_base_url: "https://jsd-uat.hcloud.healthgrp.com.sg/rest/idalko-grid/1.0/api/grid"

# =========================
# Staging Notification Configuration
# =========================

# Staging notification recipients
notification_recipients:
  default: "{{ var_notification_email | default('<EMAIL>') }}"
  escalation: "{{ var_escalation_email | default('<EMAIL>') }}"
  security: "{{ var_security_email | default('<EMAIL>') }}"
  testing: "{{ var_testing_email | default('<EMAIL>') }}"

# =========================
# Staging Monitoring Configuration
# =========================

# Staging monitoring integration
#monitoring_integration:
#  enabled: true
#  platforms:
#    - "splunk"
#    - "prometheus"
#  metrics_endpoint: "{{ metrics_endpoint_staging }}"
#  alerting_enabled: false
#  dashboard_enabled: true

# =========================
# Staging Compliance Configuration
# =========================

# Relaxed compliance for staging
compliance_configuration:
  frameworks:
    hipaa: false
    pci_dss: false
    sox: false
    iso27001: false

  # Basic audit requirements for staging
  audit_requirements:
    change_tracking: true
    approval_workflows: false
    segregation_of_duties: false
    data_retention: false
    real_time_monitoring: false

# =========================
# Staging Security Configuration
# =========================

# Staging security settings
dns_security:
  access_control:
    enforce_rbac: false
    require_mfa: false
    audit_all_access: true
    session_timeout: 3600

  encryption:
    encrypt_communications: true
    encrypt_logs: false
    encrypt_credentials: true

  monitoring:
    log_security_events: true
    alert_on_anomalies: false
    integrate_with_siem: false

# =========================
# Staging Performance Configuration
# =========================

# Staging performance settings
dns_performance:
  execution_timeout: 1200
  dns_query_timeout: 15
  retry_attempts: 2
  parallel_execution: false

  # Resource limits
  max_concurrent_operations: 3
  memory_limit_mb: 512
  cpu_limit_percent: 50

# =========================
# Staging Testing Configuration
# =========================

# Testing-specific settings for staging
testing_configuration:
  # Test data settings
  use_test_data: true
  test_domain_suffix: ".staging.test"
  test_ip_range: "*************/24"

  # Test execution settings
  enable_dry_run: true
  validate_before_execution: true
  cleanup_after_test: true

  # Test reporting
  generate_test_reports: true
  test_report_format: "json"
  test_report_retention_days: 30

# =========================
# Staging Backup Configuration
# =========================

# Staging backup settings
backup_configuration:
  enabled: false
  frequency: "weekly"
  retention_days: 30
  backup_location: "/backup/staging"

# Recovery settings
recovery_configuration:
  automatic_rollback: true
  manual_rollback_available: true
  point_in_time_recovery: false
  disaster_recovery_enabled: false

# =========================
# Staging Maintenance Configuration
# =========================

# Maintenance windows for staging
maintenance_windows:
  staging:
    start_time: "01:00"
    end_time: "05:00"
    timezone: "Asia/Singapore"
    days: ["Saturday", "Sunday"]

# Health checks for staging
health_checks:
#  enabled: true
  frequency_minutes: 30
  timeout_seconds: 60
  alert_on_failure: false

# =========================
# Staging Data Protection
# =========================

# Data protection for staging
data_protection:
  encryption_at_rest: false
  encryption_in_transit: true
  data_classification: "internal"
  retention_period_days: 90  # 3 months

# =========================
# Staging Logging Configuration
# =========================

# Enhanced logging for staging
dns_log_config:
#  enabled: true
  level: "DEBUG"
  format: "json"
  directory: "C:\\OE_AAP_LOGS\\STAGING"
  file_name: "{{ tower_job_id | default('LOCAL') }}_{{ ansible_date_time.day }}{{ ansible_date_time.month }}{{ ansible_date_time.year }}_{{ var_sr_number | default('NOTICKET') }}_DNS_STAGING.log"

  # Staging-specific logging
  log_test_data: true
  log_performance_metrics: true
  log_debug_information: true

  # Log retention
  retention_days: 90
  compress_old_logs: true
  archive_location: "C:\\OE_AAP_LOGS\\STAGING\\ARCHIVE"
