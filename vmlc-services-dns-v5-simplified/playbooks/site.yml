---
# Main DNS Management Playbook
# Author: CES Operational Excellence Team
# Contributors: <PERSON> (7409)

- name: DNS Management Operations
  hosts: localhost
  gather_facts: true

  vars:
    # Default operation parameters
    operation: "{{ operation | default('verify') }}"
    record_type: "{{ record_type | default('a') }}"
    hostname: "{{ hostname | default('') }}"
    domain: "{{ domain | default('') }}"
    ip_address: "{{ ip_address | default('') }}"
    cname_target: "{{ cname_target | default('') }}"
    ttl: "{{ ttl | default(0) }}"
    description: "{{ description | default('') }}"
    ticket: "{{ ticket | default('') }}"
    
    # Multi-domain operation parameters
    is_multi_domain: "{{ is_multi_domain | default(false) | bool }}"
    hostnames: "{{ hostnames | default('') }}"
    domains: "{{ domains | default('') }}"
    ip_addresses: "{{ ip_addresses | default('') }}"
    cname_targets: "{{ cname_targets | default('') }}"
    
    # Control parameters
    manage_ptr: "{{ manage_ptr | default(true) | bool }}"
    force_remove: "{{ force_remove | default(false) | bool }}"
    testing_mode: "{{ testing_mode | default(false) | bool }}"
    email_report: "{{ email_report | default(true) | bool }}"
    email_logs: "{{ email_logs | default(true) | bool }}"
    
    # Execution parameters
    async_operations: "{{ async_operations | default(false) | bool }}"
    is_canary: "{{ is_canary | default(false) | bool }}"
    is_rollback: "{{ is_rollback | default(false) | bool }}"
    job_id: "{{ job_id | default('') }}"

  pre_tasks:
    # CyberArk Integration
    - name: Retrieve credentials from CyberArk
      ansible.builtin.include_role:
        name: cloud_cpe.cyberark_ccp.retrieve_from_cyberark
      vars:
        cyberark_app_id: "{{ cyberark_app_id | default(integration.cyberark.app_id) }}"
        cyberark_safe: "{{ cyberark_safe | default(integration.cyberark.safes.dns_admin.name) }}"
        cyberark_object: "{{ cyberark_object | default(integration.cyberark.safes.dns_admin.object) }}"
        cyberark_output_format: json
        cyberark_output_var: dns_credentials
      when: use_cyberark | default(integration.cyberark.enabled) | bool
      tags:
        - always
        - credentials
        - cyberark

    # Preparation Phase
    - name: Preparation Phase
      block:
        # Load configuration
        - name: Load configuration
          ansible.builtin.include_role:
            name: utils
            tasks_from: load_config
          
        # Validate parameters
        - name: Validate parameters
          ansible.builtin.include_role:
            name: utils
            tasks_from: validate_params
          
        # Setup credentials
        - name: Setup credentials
          ansible.builtin.include_role:
            name: utils
            tasks_from: setup_credentials
          
        # Setup logging
        - name: Setup logging
          ansible.builtin.include_role:
            name: utils
            tasks_from: setup_logging
      tags:
        - always
        - preparation

  tasks:
    # Execution Phase
    - name: Execution Phase
      block:
        # Single domain operations
        - name: Execute DNS operation
          ansible.builtin.include_role:
            name: dns
            tasks_from: "{{ operation }}"
          when: not is_multi_domain
        
        # Multi-domain operations
        - name: Execute multi-domain DNS operation
          ansible.builtin.include_role:
            name: dns
            tasks_from: multi_domain
          when: is_multi_domain
      rescue:
        # Error handling
        - name: Handle errors
          ansible.builtin.include_role:
            name: utils
            tasks_from: handle_errors
      tags:
        - always
        - execution

  post_tasks:
    # Reporting Phase
    - name: Reporting Phase
      block:
        # Process results
        - name: Process operation results
          ansible.builtin.include_role:
            name: utils
            tasks_from: process_results
        
        # Send notifications
        - name: Send notifications
          ansible.builtin.include_role:
            name: notifications
            tasks_from: main
      tags:
        - always
        - reporting
    
    # Cleanup Phase
    - name: Cleanup Phase
      block:
        # Perform cleanup
        - name: Perform cleanup
          ansible.builtin.include_role:
            name: utils
            tasks_from: cleanup
        
        # Display summary
        - name: Display operation summary
          ansible.builtin.include_role:
            name: utils
            tasks_from: display_summary
      tags:
        - always
        - cleanup
