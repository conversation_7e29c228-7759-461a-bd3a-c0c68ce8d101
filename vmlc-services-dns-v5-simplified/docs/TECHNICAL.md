# DNS Management System - Technical Documentation

**Author**: CES Operational Excellence Team  
**Contributors**: <PERSON> (7409)

## Table of Contents

1. [Architecture](#1-architecture)
2. [Workflow](#2-workflow)
3. [Configuration](#3-configuration)
4. [Roles](#4-roles)
5. [Playbooks](#5-playbooks)
6. [PowerShell Scripts](#6-powershell-scripts)
7. [Templates](#7-templates)
8. [Multi-Domain Operations](#8-multi-domain-operations)
9. [Rollback Operations](#9-rollback-operations)
10. [Logging and Reporting](#10-logging-and-reporting)
11. [Integration](#11-integration)
12. [Security](#12-security)

## 1. Architecture

The DNS Management System is built on Ansible and follows a modular architecture with three main roles:

1. **DNS Role**: Handles DNS record operations
2. **Utils Role**: Provides utility functions
3. **Notifications Role**: Manages notifications

The system is designed to be run exclusively through Ansible Automation Platform (AAP) and interacts with Windows DNS servers using WinRM.

### 1.1. System Components

```
┌─────────────────────┐     ┌─────────────────────┐     ┌─────────────────────┐
│                     │     │                     │     │                     │
│  Ansible Automation ├────►│   DNS Management    ├────►│   Windows DNS       │
│  Platform (AAP)     │     │   System            │     │   Servers           │
│                     │     │                     │     │                     │
└─────────────────────┘     └──────────┬──────────┘     └─────────────────────┘
                                       │
                                       │
                            ┌──────────▼──────────┐
                            │                     │
                            │   Notifications     │
                            │   (Email, Jira,     │
                            │   Bitbucket)        │
                            │                     │
                            └─────────────────────┘
```

## 2. Workflow

The DNS Management System follows a four-phase workflow:

### 2.1. Preparation Phase

1. Load configuration
2. Validate parameters
3. Setup credentials
4. Setup logging

### 2.2. Execution Phase

1. Execute DNS operation (add, remove, update, verify)
2. Handle errors

### 2.3. Reporting Phase

1. Process results
2. Send notifications

### 2.4. Cleanup Phase

1. Perform cleanup
2. Display summary

## 3. Configuration

### 3.1. Main Configuration

The main configuration is stored in `group_vars/all/config.yml` and includes:

- DNS settings
- Domain configuration
- Email settings
- Logging settings
- Integration settings
- Security settings

Example:

```yaml
# DNS Operation Settings
dns_settings:
  default_ttl: 3600
  manage_ptr_by_default: true
  verify_before_remove: true
  force_remove_allowed: true
  max_retries: 3
  retry_delay: 5  # seconds

# Domain Configuration
domains:
  example.com:
    dns_server: dns1.example.com
    ptr_server: dns1.example.com
    admin_team: <EMAIL>
    description: "Example Domain"
```

### 3.2. Credentials

Credentials are stored in `group_vars/all/vault.yml` and should be encrypted in production using Ansible Vault.

Example:

```yaml
# Domain credentials
credentials:
  domains:
    example.com:
      username: "admin"
      password: "password"
```

## 4. Roles

### 4.1. DNS Role

The DNS role handles all DNS record operations:

- Adding A, PTR, and CNAME records
- Removing records
- Updating records
- Verifying records
- Multi-domain operations
- Rollback operations

### 4.2. Utils Role

The Utils role provides utility functions:

- Configuration loading
- Parameter validation
- Credential management
- Logging setup
- Error handling
- Result processing

### 4.3. Notifications Role

The Notifications role manages external communications:

- Email notifications
- Jira ticket updates
- Bitbucket repository updates

## 5. Playbooks

### 5.1. site.yml

The main playbook for DNS operations. It handles all operations (add, remove, update, verify) and supports both single-domain and multi-domain operations.

### 5.2. rollback.yml

The rollback playbook for reverting previous operations. It loads the operation log from a previous job and performs the opposite operation.

## 6. PowerShell Scripts

The system uses PowerShell scripts to interact with Windows DNS servers. These scripts are executed on the DNS servers using WinRM.

### 6.1. DNS Operations

The `dns_operations.ps1` script provides functions for:

- Adding A, PTR, and CNAME records
- Removing records
- Updating records
- Verifying records

## 7. Templates

### 7.1. Email Report

The `email_report.html.j2` template generates HTML email reports for DNS operations.

### 7.2. Bitbucket Log

The `bitbucket_log.md.j2` template generates Markdown logs for Bitbucket.

## 8. Multi-Domain Operations

The system supports operations across multiple domains in a single request. This is useful for managing records for the same service across different domains.

### 8.1. Sequential Processing

By default, multi-domain operations are processed sequentially.

### 8.2. Parallel Processing

Multi-domain operations can also be processed in parallel using the `async_operations` parameter.

## 9. Rollback Operations

The system supports rollback operations to revert previous changes. Rollbacks are performed using the job ID of the operation to be rolled back.

### 9.1. Operation Logs

Operation logs are stored in the `logs` directory and contain all the information needed to rollback an operation.

## 10. Logging and Reporting

### 10.1. Local Logging

Logs are stored in the `logs` directory and include:

- Operation logs
- Bitbucket logs
- Email reports

### 10.2. Remote Logging

Logs are also stored on the target server in `C:\OE_AAP_LOGS`.

### 10.3. Email Reporting

Email reports are sent to the appropriate recipients based on the domain.

## 11. Integration

### 11.1. Jira Integration

The system integrates with Jira for ticket management:

- Creating and updating tickets
- Adding operation details as comments
- Linking to DNS operations

### 11.2. Bitbucket Integration

The system integrates with Bitbucket for code storage and operation logs:

- Storing operation logs
- Creating operation reports

### 11.3. CyberArk Integration

The system integrates with CyberArk for secure credential retrieval:

- Retrieving credentials from CyberArk
- Using credentials for DNS operations

## 12. Security

### 12.1. Credential Management

- Credentials stored in encrypted vault
- CyberArk integration for secure credential retrieval
- No-log for sensitive tasks
- Clearing sensitive variables after use

### 12.2. Input Validation

- Validation of all input parameters
- Verification before destructive operations
- Connectivity checks before removing records
