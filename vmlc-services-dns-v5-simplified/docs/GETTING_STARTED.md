# DNS Management System - Getting Started Guide

**Author**: CES Operational Excellence Team  
**Contributors**: <PERSON> (7409)

## Table of Contents

1. [Introduction](#1-introduction)
2. [Prerequisites](#2-prerequisites)
3. [Installation](#3-installation)
4. [Configuration](#4-configuration)
5. [Usage](#5-usage)
6. [Job Templates](#6-job-templates)
7. [Troubleshooting](#7-troubleshooting)

## 1. Introduction

The DNS Management System is an Ansible-based solution for managing DNS records across multiple domains. It provides a streamlined approach to adding, removing, updating, and verifying DNS records with comprehensive logging, reporting, and integration capabilities.

This guide will help you get started with the DNS Management System, including installation, configuration, and basic usage.

## 2. Prerequisites

- Ansible Automation Platform (AAP) 2.0 or later
- Windows DNS servers
- WinRM connectivity to DNS servers
- Appropriate permissions to manage DNS records

## 3. Installation

1. Clone the repository to your AAP server:
   ```bash
   git clone https://your-repository/vmlc-services-dns-v5.git
   ```

2. Install required collections:
   ```bash
   ansible-galaxy collection install -r collections/requirements.yml
   ```

3. Create AAP job templates (see [Job Templates](#6-job-templates) section).

## 4. Configuration

1. Edit `group_vars/all/config.yml` to configure domains, email settings, and integration options.
2. Edit `group_vars/all/vault.yml` to configure credentials (encrypt this file in production).
3. Configure CyberArk integration if needed.

## 5. Usage

This project is designed to be run exclusively through Ansible Automation Platform (AAP). Users and developers do not have direct access to run ansible-playbook commands on the servers.

### Basic Usage (via AAP Extra Vars)

When launching a job template in AAP, you can provide the following extra variables:

```yaml
# Add an A record
operation: add
record_type: a
hostname: webserver
domain: example.com
ip_address: ********
ticket: INC123456

# Verify a record
operation: verify
record_type: a
hostname: webserver
domain: example.com
ticket: INC123456

# Update a record
operation: update
record_type: a
hostname: webserver
domain: example.com
ip_address: ********
ticket: INC123456

# Remove a record
operation: remove
record_type: a
hostname: webserver
domain: example.com
ticket: INC123456
```

### Multi-Domain Operations (via AAP Extra Vars)

```yaml
# Add records to multiple domains
operation: add
record_type: a
is_multi_domain: true
hostnames: web01,web02
domains: domain1.com,domain2.com
ip_addresses: ********,********
ticket: INC123456
```

### Rollback Operations (via AAP Extra Vars)

```yaml
# Rollback an operation
job_id: 12345
ticket: INC123456
```

## 6. Job Templates

Create the following job templates in AAP:

### 1. DNS Record Management

- **Name**: DNS Record Management
- **Description**: Add, update, or remove DNS records
- **Inventory**: Your inventory
- **Project**: Your project
- **Playbook**: playbooks/site.yml
- **Credentials**: Machine credentials for DNS servers
- **Variables**:
  ```yaml
  ---
  # Default values
  operation: add
  record_type: a
  manage_ptr: true
  force_remove: false
  testing_mode: false
  email_report: true
  ```
- **Survey**:
  - Operation (select: add, remove, update, verify)
  - Record Type (select: a, ptr, cname)
  - Hostname (text)
  - Domain (select: list of domains)
  - IP Address (text, for A records)
  - CNAME Target (text, for CNAME records)
  - TTL (number, default: 3600)
  - Description (text)
  - Ticket (text)
  - Manage PTR (checkbox, default: true)

### 2. DNS Multi-Domain Management

- **Name**: DNS Multi-Domain Management
- **Description**: Manage DNS records across multiple domains
- **Inventory**: Your inventory
- **Project**: Your project
- **Playbook**: playbooks/site.yml
- **Credentials**: Machine credentials for DNS servers
- **Variables**:
  ```yaml
  ---
  # Default values
  operation: add
  record_type: a
  is_multi_domain: true
  manage_ptr: true
  async_operations: false
  testing_mode: false
  email_report: true
  ```
- **Survey**:
  - Operation (select: add, remove, update, verify)
  - Record Type (select: a, ptr, cname)
  - Hostnames (text, comma-separated)
  - Domains (text, comma-separated)
  - IP Addresses (text, comma-separated, for A records)
  - CNAME Targets (text, comma-separated, for CNAME records)
  - TTL (number, default: 3600)
  - Description (text)
  - Ticket (text)
  - Manage PTR (checkbox, default: true)
  - Async Operations (checkbox, default: false)

### 3. DNS Rollback

- **Name**: DNS Rollback
- **Description**: Rollback a previous DNS operation
- **Inventory**: Your inventory
- **Project**: Your project
- **Playbook**: playbooks/rollback.yml
- **Credentials**: Machine credentials for DNS servers
- **Variables**:
  ```yaml
  ---
  # Default values
  testing_mode: false
  email_report: true
  ```
- **Survey**:
  - Job ID (text)
  - Ticket (text)

## 7. Troubleshooting

### Common Issues

1. **WinRM Connection Failures**
   - Ensure WinRM is properly configured on the DNS servers
   - Verify credentials are correct
   - Check network connectivity

2. **Permission Issues**
   - Ensure the user has appropriate permissions to manage DNS records
   - Check domain credentials in vault.yml

3. **Record Already Exists**
   - Use the verify operation to check if the record exists
   - Use the update operation instead of add

4. **Record Does Not Exist**
   - Use the verify operation to check if the record exists
   - Use the add operation instead of update or remove

### Logs

Logs are stored in the following locations:
- AAP job logs
- Local logs in the `logs` directory
- Remote logs on the target server in `C:\OE_AAP_LOGS`

For more detailed troubleshooting, refer to the Technical Documentation.
