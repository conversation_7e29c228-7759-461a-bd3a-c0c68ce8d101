# DNS Operations PowerShell Module
# Author: CES Operational Excellence Team
# Contributors: <PERSON> (7409)

# Error handling
$ErrorActionPreference = "Stop"

# Function to add A record
function Add-DNSARecord {
    param(
        [Parameter(Mandatory=$true)]
        [string]$Hostname,
        
        [Parameter(Mandatory=$true)]
        [string]$Domain,
        
        [Parameter(Mandatory=$true)]
        [string]$IPAddress,
        
        [Parameter(Mandatory=$false)]
        [int]$TTL = 3600,
        
        [Parameter(Mandatory=$false)]
        [string]$Description = ""
    )
    
    try {
        $fqdn = "$Hostname.$Domain"
        $dnsServer = [System.Net.Dns]::GetHostEntry($env:COMPUTERNAME).HostName
        
        # Add A record
        Add-DnsServerResourceRecordA -Name $Hostname -ZoneName $Domain -IPv4Address $IPAddress -TimeToLive (New-TimeSpan -Seconds $TTL) -ComputerName $dnsServer
        
        # Add description if provided
        if ($Description) {
            $record = Get-DnsServerResourceRecord -Name $Hostname -ZoneName $Domain -RRType A -ComputerName $dnsServer
            $record.TimeStamp = $null
            $record.RecordData.TimeStamp = $null
            $record.Description = $Description
            Set-DnsServerResourceRecord -OldInputObject $record -NewInputObject $record -ZoneName $Domain -ComputerName $dnsServer
        }
        
        return @{
            Success = $true
            Message = "Successfully added A record $fqdn with IP $IPAddress"
        } | ConvertTo-Json
    }
    catch {
        return @{
            Success = $false
            Message = "Failed to add A record: $_"
        } | ConvertTo-Json
    }
}

# Function to add PTR record
function Add-DNSPTRRecord {
    param(
        [Parameter(Mandatory=$true)]
        [string]$Hostname,
        
        [Parameter(Mandatory=$true)]
        [string]$Domain,
        
        [Parameter(Mandatory=$true)]
        [string]$IPAddress,
        
        [Parameter(Mandatory=$false)]
        [int]$TTL = 3600,
        
        [Parameter(Mandatory=$false)]
        [string]$Description = ""
    )
    
    try {
        $fqdn = "$Hostname.$Domain"
        $dnsServer = [System.Net.Dns]::GetHostEntry($env:COMPUTERNAME).HostName
        
        # Convert IP to reverse lookup format
        $ipParts = $IPAddress.Split('.')
        $ptrZone = "$($ipParts[2]).$($ipParts[1]).$($ipParts[0]).in-addr.arpa"
        $ptrName = $ipParts[3]
        
        # Add PTR record
        Add-DnsServerResourceRecordPtr -Name $ptrName -ZoneName $ptrZone -PtrDomainName $fqdn -TimeToLive (New-TimeSpan -Seconds $TTL) -ComputerName $dnsServer
        
        # Add description if provided
        if ($Description) {
            $record = Get-DnsServerResourceRecord -Name $ptrName -ZoneName $ptrZone -RRType Ptr -ComputerName $dnsServer
            $record.TimeStamp = $null
            $record.RecordData.TimeStamp = $null
            $record.Description = $Description
            Set-DnsServerResourceRecord -OldInputObject $record -NewInputObject $record -ZoneName $ptrZone -ComputerName $dnsServer
        }
        
        return @{
            Success = $true
            Message = "Successfully added PTR record for $IPAddress pointing to $fqdn"
        } | ConvertTo-Json
    }
    catch {
        return @{
            Success = $false
            Message = "Failed to add PTR record: $_"
        } | ConvertTo-Json
    }
}

# Function to add CNAME record
function Add-DNSCNAMERecord {
    param(
        [Parameter(Mandatory=$true)]
        [string]$Hostname,
        
        [Parameter(Mandatory=$true)]
        [string]$Domain,
        
        [Parameter(Mandatory=$true)]
        [string]$CnameTarget,
        
        [Parameter(Mandatory=$false)]
        [int]$TTL = 3600,
        
        [Parameter(Mandatory=$false)]
        [string]$Description = ""
    )
    
    try {
        $fqdn = "$Hostname.$Domain"
        $dnsServer = [System.Net.Dns]::GetHostEntry($env:COMPUTERNAME).HostName
        
        # Add CNAME record
        Add-DnsServerResourceRecordCName -Name $Hostname -ZoneName $Domain -HostNameAlias $CnameTarget -TimeToLive (New-TimeSpan -Seconds $TTL) -ComputerName $dnsServer
        
        # Add description if provided
        if ($Description) {
            $record = Get-DnsServerResourceRecord -Name $Hostname -ZoneName $Domain -RRType CName -ComputerName $dnsServer
            $record.TimeStamp = $null
            $record.RecordData.TimeStamp = $null
            $record.Description = $Description
            Set-DnsServerResourceRecord -OldInputObject $record -NewInputObject $record -ZoneName $Domain -ComputerName $dnsServer
        }
        
        return @{
            Success = $true
            Message = "Successfully added CNAME record $fqdn pointing to $CnameTarget"
        } | ConvertTo-Json
    }
    catch {
        return @{
            Success = $false
            Message = "Failed to add CNAME record: $_"
        } | ConvertTo-Json
    }
}

# Function to remove A record
function Remove-DNSARecord {
    param(
        [Parameter(Mandatory=$true)]
        [string]$Hostname,
        
        [Parameter(Mandatory=$true)]
        [string]$Domain
    )
    
    try {
        $fqdn = "$Hostname.$Domain"
        $dnsServer = [System.Net.Dns]::GetHostEntry($env:COMPUTERNAME).HostName
        
        # Remove A record
        Remove-DnsServerResourceRecord -Name $Hostname -ZoneName $Domain -RRType A -ComputerName $dnsServer -Force
        
        return @{
            Success = $true
            Message = "Successfully removed A record $fqdn"
        } | ConvertTo-Json
    }
    catch {
        return @{
            Success = $false
            Message = "Failed to remove A record: $_"
        } | ConvertTo-Json
    }
}

# Function to remove PTR record
function Remove-DNSPTRRecord {
    param(
        [Parameter(Mandatory=$true)]
        [string]$IPAddress
    )
    
    try {
        $dnsServer = [System.Net.Dns]::GetHostEntry($env:COMPUTERNAME).HostName
        
        # Convert IP to reverse lookup format
        $ipParts = $IPAddress.Split('.')
        $ptrZone = "$($ipParts[2]).$($ipParts[1]).$($ipParts[0]).in-addr.arpa"
        $ptrName = $ipParts[3]
        
        # Remove PTR record
        Remove-DnsServerResourceRecord -Name $ptrName -ZoneName $ptrZone -RRType Ptr -ComputerName $dnsServer -Force
        
        return @{
            Success = $true
            Message = "Successfully removed PTR record for $IPAddress"
        } | ConvertTo-Json
    }
    catch {
        return @{
            Success = $false
            Message = "Failed to remove PTR record: $_"
        } | ConvertTo-Json
    }
}

# Function to remove CNAME record
function Remove-DNSCNAMERecord {
    param(
        [Parameter(Mandatory=$true)]
        [string]$Hostname,
        
        [Parameter(Mandatory=$true)]
        [string]$Domain
    )
    
    try {
        $fqdn = "$Hostname.$Domain"
        $dnsServer = [System.Net.Dns]::GetHostEntry($env:COMPUTERNAME).HostName
        
        # Remove CNAME record
        Remove-DnsServerResourceRecord -Name $Hostname -ZoneName $Domain -RRType CName -ComputerName $dnsServer -Force
        
        return @{
            Success = $true
            Message = "Successfully removed CNAME record $fqdn"
        } | ConvertTo-Json
    }
    catch {
        return @{
            Success = $false
            Message = "Failed to remove CNAME record: $_"
        } | ConvertTo-Json
    }
}

# Function to verify A or CNAME record
function Get-DNSRecord {
    param(
        [Parameter(Mandatory=$true)]
        [string]$Hostname,
        
        [Parameter(Mandatory=$true)]
        [string]$Domain,
        
        [Parameter(Mandatory=$true)]
        [string]$RecordType
    )
    
    try {
        $fqdn = "$Hostname.$Domain"
        $dnsServer = [System.Net.Dns]::GetHostEntry($env:COMPUTERNAME).HostName
        
        # Get record
        $record = Get-DnsServerResourceRecord -Name $Hostname -ZoneName $Domain -RRType $RecordType -ComputerName $dnsServer -ErrorAction SilentlyContinue
        
        if ($record) {
            $result = @{
                Exists = $true
                RecordType = $RecordType
                Hostname = $Hostname
                Domain = $Domain
                TTL = $record.TimeToLive.TotalSeconds
                Description = $record.Description
            }
            
            # Add record-type specific data
            if ($RecordType -eq "A") {
                $result.IPAddress = $record.RecordData.IPv4Address.ToString()
            }
            elseif ($RecordType -eq "CNAME") {
                $result.CnameTarget = $record.RecordData.HostNameAlias
            }
            
            return $result | ConvertTo-Json
        }
        else {
            return @{
                Exists = $false
                RecordType = $RecordType
                Hostname = $Hostname
                Domain = $Domain
                Message = "Record $fqdn of type $RecordType does not exist"
            } | ConvertTo-Json
        }
    }
    catch {
        return @{
            Exists = $false
            RecordType = $RecordType
            Hostname = $Hostname
            Domain = $Domain
            Message = "Error verifying record: $_"
        } | ConvertTo-Json
    }
}

# Function to verify PTR record
function Get-DNSPTRRecord {
    param(
        [Parameter(Mandatory=$true)]
        [string]$IPAddress
    )
    
    try {
        $dnsServer = [System.Net.Dns]::GetHostEntry($env:COMPUTERNAME).HostName
        
        # Convert IP to reverse lookup format
        $ipParts = $IPAddress.Split('.')
        $ptrZone = "$($ipParts[2]).$($ipParts[1]).$($ipParts[0]).in-addr.arpa"
        $ptrName = $ipParts[3]
        
        # Get PTR record
        $record = Get-DnsServerResourceRecord -Name $ptrName -ZoneName $ptrZone -RRType Ptr -ComputerName $dnsServer -ErrorAction SilentlyContinue
        
        if ($record) {
            return @{
                Exists = $true
                RecordType = "PTR"
                IPAddress = $IPAddress
                PtrTarget = $record.RecordData.PtrDomainName
                TTL = $record.TimeToLive.TotalSeconds
                Description = $record.Description
            } | ConvertTo-Json
        }
        else {
            return @{
                Exists = $false
                RecordType = "PTR"
                IPAddress = $IPAddress
                Message = "PTR record for $IPAddress does not exist"
            } | ConvertTo-Json
        }
    }
    catch {
        return @{
            Exists = $false
            RecordType = "PTR"
            IPAddress = $IPAddress
            Message = "Error verifying PTR record: $_"
        } | ConvertTo-Json
    }
}

# Function to update A record
function Update-DNSARecord {
    param(
        [Parameter(Mandatory=$true)]
        [string]$Hostname,
        
        [Parameter(Mandatory=$true)]
        [string]$Domain,
        
        [Parameter(Mandatory=$true)]
        [string]$IPAddress,
        
        [Parameter(Mandatory=$false)]
        [int]$TTL = 3600,
        
        [Parameter(Mandatory=$false)]
        [string]$Description = ""
    )
    
    try {
        $fqdn = "$Hostname.$Domain"
        $dnsServer = [System.Net.Dns]::GetHostEntry($env:COMPUTERNAME).HostName
        
        # Get existing record
        $oldRecord = Get-DnsServerResourceRecord -Name $Hostname -ZoneName $Domain -RRType A -ComputerName $dnsServer
        $oldIP = $oldRecord.RecordData.IPv4Address.ToString()
        
        # Create new record
        $newRecord = $oldRecord.Clone()
        $newRecord.TimeToLive = [System.TimeSpan]::FromSeconds($TTL)
        $newRecord.RecordData.IPv4Address = [System.Net.IPAddress]::Parse($IPAddress)
        
        if ($Description) {
            $newRecord.Description = $Description
        }
        
        # Update record
        Set-DnsServerResourceRecord -OldInputObject $oldRecord -NewInputObject $newRecord -ZoneName $Domain -ComputerName $dnsServer
        
        return @{
            Success = $true
            Message = "Successfully updated A record $fqdn from $oldIP to $IPAddress"
            OldIPAddress = $oldIP
        } | ConvertTo-Json
    }
    catch {
        return @{
            Success = $false
            Message = "Failed to update A record: $_"
        } | ConvertTo-Json
    }
}

# Function to update CNAME record
function Update-DNSCNAMERecord {
    param(
        [Parameter(Mandatory=$true)]
        [string]$Hostname,
        
        [Parameter(Mandatory=$true)]
        [string]$Domain,
        
        [Parameter(Mandatory=$true)]
        [string]$CnameTarget,
        
        [Parameter(Mandatory=$false)]
        [int]$TTL = 3600,
        
        [Parameter(Mandatory=$false)]
        [string]$Description = ""
    )
    
    try {
        $fqdn = "$Hostname.$Domain"
        $dnsServer = [System.Net.Dns]::GetHostEntry($env:COMPUTERNAME).HostName
        
        # Get existing record
        $oldRecord = Get-DnsServerResourceRecord -Name $Hostname -ZoneName $Domain -RRType CName -ComputerName $dnsServer
        $oldTarget = $oldRecord.RecordData.HostNameAlias
        
        # Create new record
        $newRecord = $oldRecord.Clone()
        $newRecord.TimeToLive = [System.TimeSpan]::FromSeconds($TTL)
        $newRecord.RecordData.HostNameAlias = $CnameTarget
        
        if ($Description) {
            $newRecord.Description = $Description
        }
        
        # Update record
        Set-DnsServerResourceRecord -OldInputObject $oldRecord -NewInputObject $newRecord -ZoneName $Domain -ComputerName $dnsServer
        
        return @{
            Success = $true
            Message = "Successfully updated CNAME record $fqdn from $oldTarget to $CnameTarget"
        } | ConvertTo-Json
    }
    catch {
        return @{
            Success = $false
            Message = "Failed to update CNAME record: $_"
        } | ConvertTo-Json
    }
}
