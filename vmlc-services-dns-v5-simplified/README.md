# DNS Management System (vmlc-services-dns-v5-simplified)

**Author**: CES Operational Excellence Team  
**Contributors**: <PERSON> Bin <PERSON> (7409)

## Overview

The DNS Management System is an Ansible-based solution for managing DNS records across multiple domains. It provides a streamlined approach to adding, removing, updating, and verifying DNS records with comprehensive logging, reporting, and integration capabilities.

## Features

- **Record Management**: Add, remove, update, and verify A, PTR, and CNAME records
- **Multi-Domain Support**: Manage records across multiple domains in a single operation
- **Automatic PTR Management**: Automatically manage corresponding PTR records for A records
- **Rollback Capability**: Roll back operations to previous state
- **Comprehensive Logging**: Detailed logging of all operations
- **Integration**: Jira and Bitbucket integration for tracking and documentation
- **Email Notifications**: Configurable email notifications for operation results
- **CyberArk Integration**: Secure credential management with CyberArk

## Project Structure

```
vmlc-services-dns-v5-simplified/
├── collections/
│   └── requirements.yml       # Ansible collections requirements
├── docs/                      # Documentation
│   ├── GETTING_STARTED.md     # Getting started guide
│   └── TECHNICAL.md           # Technical documentation
├── files/                     # Static files
│   └── dns_operations.ps1     # PowerShell module for DNS operations
├── group_vars/                # Group variables
│   ├── all/                   # All group variables
│   │   ├── config.yml         # Main configuration
│   │   └── vault.yml          # Encrypted credentials
│   ├── dns_servers/           # DNS servers group variables
│   └── admt_servers/          # ADMT servers group variables
├── inventory/                 # Inventory directory
│   └── hosts                  # Inventory file
├── logs/                      # Logs directory
│   └── bitbucket/             # Bitbucket logs directory
├── playbooks/                 # Playbooks directory
│   ├── rollback.yml           # Rollback playbook
│   └── site.yml               # Main playbook
├── roles/                     # Roles directory
│   ├── dns/                   # DNS role
│   │   └── tasks/             # DNS tasks
│   ├── notifications/         # Notifications role
│   │   └── tasks/             # Notification tasks
│   └── utils/                 # Utils role
│       └── tasks/             # Utility tasks
├── scripts/                   # Scripts directory
└── templates/                 # Templates directory
    ├── email_report.html.j2   # Email report template
    └── bitbucket_log.md.j2    # Bitbucket log template
```

## Usage

This project is designed to be run exclusively through Ansible Automation Platform (AAP). Users and developers do not have direct access to run ansible-playbook commands on the servers.

### Basic Usage (via AAP Job Templates)

The system is operated through AAP job templates with either surveys or extra variables:

1. **DNS Record Management** - For adding, updating, or removing DNS records
2. **DNS Record Verification** - For verifying the existence of DNS records
3. **DNS Multi-Domain Operations** - For managing records across multiple domains
4. **DNS Rollback Operations** - For rolling back previous operations

### Example Extra Variables

```yaml
# Add an A record
operation: add
record_type: a
hostname: webserver
domain: example.com
ip_address: ********
ticket: INC123456

# Multi-domain operation
operation: add
record_type: a
is_multi_domain: true
hostnames: web01,web02
domains: domain1.com,domain2.com
ip_addresses: ********,********
ticket: INC123456

# Rollback operation
job_id: 12345
ticket: INC123456
```

For detailed usage instructions, see the [Getting Started Guide](docs/GETTING_STARTED.md).

## Configuration

The system is configured through the `group_vars/all/config.yml` file, which includes:

- DNS settings
- Domain configuration
- Email settings
- Integration settings
- Security settings

## Credentials

Credentials are stored in the `group_vars/all/vault.yml` file, which should be encrypted in production using Ansible Vault. The system also supports retrieving credentials from CyberArk.

## Documentation

For detailed documentation, please refer to the `docs/` directory:

- [Getting Started Guide](docs/GETTING_STARTED.md)
- [Technical Documentation](docs/TECHNICAL.md)

## License

Proprietary - All rights reserved.

## Support

For support, please contact the CES Operational Excellence Team.
