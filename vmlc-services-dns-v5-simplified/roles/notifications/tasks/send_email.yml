---
# Send Email Tasks
# Author: CES Operational Excellence Team
# Contributors: <PERSON> (7409)

- name: Generate email report
  ansible.builtin.template:
    src: email_report.html.j2
    dest: "{{ log_dir }}/email_report_{{ ansible_job_id | default('local') }}.html"
  when: log_dir is defined

- name: Send email report
  community.general.mail:
    host: "{{ smtp_server }}"
    port: 25
    from: "{{ email_settings.default_sender }}"
    to: "{{ email_recipient }}"
    bcc: "{{ email_settings.always_bcc }}"
    subject: "DNS {{ operation | upper }} {{ 'SUCCESS' if dns_operation_result.success else 'FAILED' }} - {{ ticket }} - {{ hostname | default('Multiple') }}.{{ domain | default('domains') }}"
    body: "{{ lookup('file', log_dir + '/email_report_' + (ansible_job_id | default('local')) + '.html') }}"
    subtype: html
  when: 
    - log_dir is defined
    - smtp_server is defined
    - email_recipient is defined
    - dns_operation_result is defined
  ignore_errors: true

- name: Log email sent
  ansible.builtin.debug:
    msg: "Email report sent to {{ email_recipient }}"
  when: email_recipient is defined
