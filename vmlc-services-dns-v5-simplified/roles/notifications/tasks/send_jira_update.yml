---
# Send Jira Update Tasks
# Author: CES Operational Excellence Team
# Contributors: <PERSON> (7409)

- name: Generate Jira comment
  ansible.builtin.set_fact:
    jira_comment: |
      h3. DNS Operation Report
      
      *Operation:* {{ operation | capitalize }}{% if is_rollback %} (Rollback){% endif %}
      *Record Type:* {{ record_type | upper }}
      *{{ 'Hostname:' if not is_multi_domain else 'Domains:' }}* {{ hostname if not is_multi_domain else domains }}
      *{{ 'Domain:' if not is_multi_domain else 'Total Domains:' }}* {{ domain if not is_multi_domain else multi_domain_results | length }}
      *Status:* {{ 'SUCCESS' if dns_operation_result.success else 'FAILED' }}
      
      h4. Details
      
      {% if not is_multi_domain %}
      {{ dns_operation_result.message }}
      {% else %}
      {{ multi_domain_results | selectattr('success', 'equalto', true) | list | length }} successful, {{ multi_domain_results | selectattr('success', 'equalto', false) | list | length }} failed
      
      {% for result in multi_domain_results %}
      * *{{ result.record.hostname }}.{{ result.record.domain }}:* {{ 'Success' if result.success else 'Failure' }} - {{ result.message }}
      {% endfor %}
      {% endif %}
      
      h4. System Information
      
      *Job ID:* {{ ansible_job_id | default('local') }}
      *Timestamp:* {{ ansible_date_time.iso8601 }}
  when: dns_operation_result is defined

- name: Update Jira ticket
  ansible.builtin.uri:
    url: "{{ integration.jira.url }}/{{ ticket }}/comment"
    method: POST
    user: "{{ jira_credentials.username }}"
    password: "{{ jira_credentials.password }}"
    force_basic_auth: true
    body_format: json
    body:
      body: "{{ jira_comment }}"
    status_code: 201
    headers:
      Content-Type: "application/json"
  when: 
    - jira_comment is defined
    - jira_credentials is defined
    - integration.jira.url is defined
  ignore_errors: true
  no_log: "{{ security.use_no_log | default(true) }}"

- name: Log Jira update
  ansible.builtin.debug:
    msg: "Jira ticket {{ ticket }} updated"
  when: ticket is defined
