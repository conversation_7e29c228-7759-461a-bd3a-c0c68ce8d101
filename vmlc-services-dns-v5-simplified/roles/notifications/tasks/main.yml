---
# Notifications Role Main Tasks
# Author: CES Operational Excellence Team
# Contributors: <PERSON> (7409)

- name: Send Jira update
  ansible.builtin.include_tasks: send_jira_update.yml
  when: 
    - integration.jira.enabled | bool
    - ticket | length > 0
  tags:
    - always
    - notifications
    - jira

- name: Send Bitbucket update
  ansible.builtin.include_tasks: send_bitbucket_update.yml
  when: integration.bitbucket.enabled | bool
  tags:
    - always
    - notifications
    - bitbucket

- name: Send email report
  ansible.builtin.include_tasks: send_email.yml
  when: email_report | bool
  tags:
    - always
    - notifications
    - email
