---
# Send Bitbucket Update Tasks
# Author: CES Operational Excellence Team
# Contributors: <PERSON> (7409)

- name: Generate Bitbucket log
  ansible.builtin.template:
    src: bitbucket_log.md.j2
    dest: "{{ log_dir }}/bitbucket/{{ ticket }}_{{ ansible_date_time.date }}_{{ operation }}.md"
  when: 
    - log_dir is defined
    - dns_operation_result is defined

- name: Log Bitbucket update
  ansible.builtin.debug:
    msg: "Bitbucket log created at {{ log_dir }}/bitbucket/{{ ticket }}_{{ ansible_date_time.date }}_{{ operation }}.md"
  when: 
    - log_dir is defined
    - ticket is defined
