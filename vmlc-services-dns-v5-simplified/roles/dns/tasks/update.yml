---
# DNS Update Record Tasks
# Author: CES Operational Excellence Team
# Contributors: <PERSON> (7409)

- name: Set default TTL if not provided
  ansible.builtin.set_fact:
    ttl: "{{ dns_settings.default_ttl }}"
  when: ttl == 0

- name: Verify if record exists
  ansible.builtin.include_tasks: verify.yml
  vars:
    verify_only: true

- name: Fail if record does not exist
  ansible.builtin.fail:
    msg: "Record {{ hostname }}.{{ domain }} does not exist"
  when:
    - not record_exists | bool
    - not is_multi_domain

- name: Update DNS record
  ansible.builtin.include_tasks: "update_{{ record_type }}.yml"
  when: not is_multi_domain

- name: Set operation result
  ansible.builtin.set_fact:
    dns_operation_result:
      success: true
      changed: true
      message: "Successfully updated {{ record_type | upper }} record {{ hostname }}.{{ domain if record_type != 'ptr' else '' }}"
      record:
        hostname: "{{ hostname }}"
        domain: "{{ domain }}"
        record_type: "{{ record_type }}"
        ip_address: "{{ ip_address | default('') }}"
        cname_target: "{{ cname_target | default('') }}"
        ttl: "{{ ttl }}"
    current_operation_index: "{{ current_operation_index | default(0) }}"
  when: not is_multi_domain or is_async_operation | default(false) | bool
