---
# DNS Role Main Tasks
# Author: CES Operational Excellence Team
# Contributors: <PERSON> (7409)

- name: Include appropriate task file based on operation
  ansible.builtin.include_tasks: "{{ operation }}.yml"
  when: operation in ['add', 'remove', 'update', 'verify']

- name: Fail if operation is not supported
  ansible.builtin.fail:
    msg: "Operation '{{ operation }}' is not supported. Supported operations are: add, remove, update, verify"
  when: operation not in ['add', 'remove', 'update', 'verify']
