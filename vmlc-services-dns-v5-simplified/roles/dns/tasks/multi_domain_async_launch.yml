---
# Multi-Domain Async Launch Tasks
# Author: CES Operational Excellence Team
# Contributors: <PERSON> (7409)

- name: Initialize async jobs list if not exists
  ansible.builtin.set_fact:
    async_jobs: []
  when: async_jobs is not defined

- name: Launch async DNS operation
  ansible.builtin.include_role:
    name: dns
    tasks_from: "{{ operation }}.yml"
  vars:
    hostname: "{{ current_hostname }}"
    domain: "{{ current_domain }}"
    ip_address: "{{ current_ip_address }}"
    cname_target: "{{ current_cname_target }}"
    is_multi_domain: false
    is_async_operation: true
    current_operation_index: "{{ current_index }}"
  async: 300
  poll: 0
  register: async_job

- name: Add job to async jobs list
  ansible.builtin.set_fact:
    async_jobs: "{{ async_jobs + [async_job] }}"
