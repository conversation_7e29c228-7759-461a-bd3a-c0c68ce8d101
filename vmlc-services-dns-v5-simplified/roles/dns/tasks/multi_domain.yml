---
# Multi-Domain DNS Operations
# Author: CES Operational Excellence Team
# Contributors: <PERSON> (7409)

- name: Parse multi-domain parameters
  ansible.builtin.set_fact:
    hostname_list: "{{ hostnames.split(',') }}"
    domain_list: "{{ domains.split(',') }}"
    ip_address_list: "{{ ip_addresses.split(',') if ip_addresses else [] }}"
    cname_target_list: "{{ cname_targets.split(',') if cname_targets else [] }}"

- name: Validate multi-domain parameters
  ansible.builtin.fail:
    msg: "Number of hostnames ({{ hostname_list | length }}) must match number of domains ({{ domain_list | length }})"
  when: 
    - hostname_list | length != domain_list | length
    - record_type != 'ptr'

- name: Validate IP addresses for A records
  ansible.builtin.fail:
    msg: "Number of IP addresses ({{ ip_address_list | length }}) must match number of hostnames ({{ hostname_list | length }})"
  when: 
    - record_type == 'a'
    - ip_address_list | length != hostname_list | length

- name: Validate CNAME targets
  ansible.builtin.fail:
    msg: "Number of CNAME targets ({{ cname_target_list | length }}) must match number of hostnames ({{ hostname_list | length }})"
  when: 
    - record_type == 'cname'
    - cname_target_list | length != hostname_list | length

- name: Initialize multi-domain results
  ansible.builtin.set_fact:
    multi_domain_results: []

- name: Process domains sequentially
  ansible.builtin.include_tasks: multi_domain_operation.yml
  vars:
    current_index: "{{ item }}"
    current_hostname: "{{ hostname_list[item] }}"
    current_domain: "{{ domain_list[item] }}"
    current_ip_address: "{{ ip_address_list[item] if ip_address_list else '' }}"
    current_cname_target: "{{ cname_target_list[item] if cname_target_list else '' }}"
  loop: "{{ range(0, hostname_list | length) | list }}"
  when: not async_operations | bool

- name: Process domains in parallel
  ansible.builtin.include_tasks: multi_domain_async.yml
  when: async_operations | bool

- name: Set operation result for multi-domain operation
  ansible.builtin.set_fact:
    dns_operation_result:
      success: "{{ multi_domain_results | selectattr('success', 'equalto', true) | list | length == multi_domain_results | length }}"
      changed: "{{ multi_domain_results | selectattr('changed', 'equalto', true) | list | length > 0 }}"
      message: "Multi-domain operation completed: {{ multi_domain_results | selectattr('success', 'equalto', true) | list | length }} successful, {{ multi_domain_results | selectattr('success', 'equalto', false) | list | length }} failed"
      results: "{{ multi_domain_results }}"
