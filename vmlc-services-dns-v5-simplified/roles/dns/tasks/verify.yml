---
# Verify DNS Record Tasks
# Author: CES Operational Excellence Team
# Contributors: <PERSON> (7409)

- name: Get DNS server for domain
  ansible.builtin.set_fact:
    dns_server: "{{ domains[domain].dns_server }}"
  when: record_type != 'ptr'

- name: Get PTR server for IP
  ansible.builtin.set_fact:
    dns_server: "{{ domains[domain].ptr_server }}"
  when: record_type == 'ptr'

- name: Copy DNS operations script to remote server
  ansible.windows.win_copy:
    src: dns_operations.ps1
    dest: C:\Temp\dns_operations.ps1
  delegate_to: "{{ dns_server }}"

- name: Verify A record
  ansible.windows.win_powershell:
    script: |
      param(
        [string]$Hostname,
        [string]$Domain
      )

      $ErrorActionPreference = "Stop"

      # Import the DNS operations module
      . C:\Temp\dns_operations.ps1

      # Call the function from the module
      Get-DNSRecord -Hostname $Hostname -Domain $Domain -RecordType "A"
    parameters:
      Hostname: "{{ hostname }}"
      Domain: "{{ domain }}"
  register: verify_a_result
  delegate_to: "{{ dns_server }}"
  when: record_type == 'a'

- name: Verify CNAME record
  ansible.windows.win_powershell:
    script: |
      param(
        [string]$Hostname,
        [string]$Domain
      )

      $ErrorActionPreference = "Stop"

      # Import the DNS operations module
      . C:\Temp\dns_operations.ps1

      # Call the function from the module
      Get-DNSRecord -Hostname $Hostname -Domain $Domain -RecordType "CNAME"
    parameters:
      Hostname: "{{ hostname }}"
      Domain: "{{ domain }}"
  register: verify_cname_result
  delegate_to: "{{ dns_server }}"
  when: record_type == 'cname'

- name: Verify PTR record
  ansible.windows.win_powershell:
    script: |
      param(
        [string]$IPAddress
      )

      $ErrorActionPreference = "Stop"

      # Import the DNS operations module
      . C:\Temp\dns_operations.ps1

      # Call the function from the module
      Get-DNSPTRRecord -IPAddress $IPAddress
    parameters:
      IPAddress: "{{ ip_address }}"
  register: verify_ptr_result
  delegate_to: "{{ dns_server }}"
  when: record_type == 'ptr'

- name: Set verification result
  ansible.builtin.set_fact:
    verification_result: "{{ (verify_a_result.output | from_json) if record_type == 'a' else (verify_cname_result.output | from_json) if record_type == 'cname' else (verify_ptr_result.output | from_json) }}"

- name: Set record exists flag
  ansible.builtin.set_fact:
    record_exists: "{{ verification_result.Exists }}"
    existing_record: "{{ verification_result }}"

- name: Set operation result for verify operation
  ansible.builtin.set_fact:
    dns_operation_result:
      success: true
      changed: false
      message: "{{ 'Record exists: ' + verification_result.Hostname + '.' + verification_result.Domain if verification_result.Exists else verification_result.Message }}"
      record_exists: "{{ verification_result.Exists }}"
      record: "{{ verification_result }}"
    current_operation_index: "{{ current_operation_index | default(0) }}"
  when:
    - operation == 'verify'
    - (not is_multi_domain or is_async_operation | default(false) | bool)
    - verify_only is not defined or not verify_only | bool
