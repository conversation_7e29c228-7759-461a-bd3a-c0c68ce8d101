---
# Add A Record Tasks
# Author: CES Operational Excellence Team
# Contributors: <PERSON> (7409)

- name: Get DNS server for domain
  ansible.builtin.set_fact:
    dns_server: "{{ domains[domain].dns_server }}"
    ptr_server: "{{ domains[domain].ptr_server }}"

- name: Copy DNS operations script to remote server
  ansible.windows.win_copy:
    src: dns_operations.ps1
    dest: C:\Temp\dns_operations.ps1
  delegate_to: "{{ dns_server }}"

- name: Add A record
  ansible.windows.win_powershell:
    script: |
      param(
        [string]$Hostname,
        [string]$Domain,
        [string]$IPAddress,
        [int]$TTL,
        [string]$Description
      )

      $ErrorActionPreference = "Stop"

      # Import the DNS operations module
      . C:\Temp\dns_operations.ps1

      # Call the function from the module
      Add-DNSARecord -Hostname $Hostname -Domain $Domain -IPAddress $IPAddress -TTL $TTL -Description $Description
    parameters:
      Hostname: "{{ hostname }}"
      Domain: "{{ domain }}"
      IPAddress: "{{ ip_address }}"
      TTL: "{{ ttl }}"
      Description: "{{ description }}"
  register: add_a_result
  delegate_to: "{{ dns_server }}"

- name: Parse A record result
  ansible.builtin.set_fact:
    a_record_result: "{{ add_a_result.output | from_json }}"

- name: Add PTR record if requested
  ansible.builtin.include_tasks: add_ptr.yml
  when:
    - manage_ptr | bool
    - a_record_result.Success | bool

- name: Fail if A record addition failed
  ansible.builtin.fail:
    msg: "Failed to add A record: {{ a_record_result.Message }}"
  when: not a_record_result.Success | bool
