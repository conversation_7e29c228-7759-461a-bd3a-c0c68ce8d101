---
# Add PTR Record Tasks
# Author: CES Operational Excellence Team
# Contributors: <PERSON> (7409)

- name: Copy DNS operations script to remote server
  ansible.windows.win_copy:
    src: dns_operations.ps1
    dest: C:\Temp\dns_operations.ps1
  delegate_to: "{{ ptr_server }}"

- name: Add PTR record
  ansible.windows.win_powershell:
    script: |
      param(
        [string]$Hostname,
        [string]$Domain,
        [string]$IPAddress,
        [int]$TTL,
        [string]$Description
      )

      $ErrorActionPreference = "Stop"

      # Import the DNS operations module
      . C:\Temp\dns_operations.ps1

      # Call the function from the module
      Add-DNSPTRRecord -Hostname $Hostname -Domain $Domain -IPAddress $IPAddress -TTL $TTL -Description $Description
    parameters:
      Hostname: "{{ hostname }}"
      Domain: "{{ domain }}"
      IPAddress: "{{ ip_address }}"
      TTL: "{{ ttl }}"
      Description: "{{ description }}"
  register: add_ptr_result
  delegate_to: "{{ ptr_server }}"

- name: Parse PTR record result
  ansible.builtin.set_fact:
    ptr_record_result: "{{ add_ptr_result.output | from_json }}"

- name: Log PTR record result
  ansible.builtin.debug:
    msg: "PTR record result: {{ ptr_record_result.Message }}"
  when: ptr_record_result.Success | bool

- name: Log PTR record failure
  ansible.builtin.debug:
    msg: "Failed to add PTR record: {{ ptr_record_result.Message }}"
  when: not ptr_record_result.Success | bool
