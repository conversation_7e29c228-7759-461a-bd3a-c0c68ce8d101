---
# Remove PTR Record Tasks
# Author: CES Operational Excellence Team
# Contributors: <PERSON> (7409)

- name: Remove PTR record
  ansible.windows.win_powershell:
    script: |
      param(
        [string]$IPAddress
      )
      
      $ErrorActionPreference = "Stop"
      
      try {
        $dnsServer = [System.Net.Dns]::GetHostEntry($env:COMPUTERNAME).HostName
        
        # Convert IP to reverse lookup format
        $ipParts = $IPAddress.Split('.')
        $ptrZone = "$($ipParts[2]).$($ipParts[1]).$($ipParts[0]).in-addr.arpa"
        $ptrName = $ipParts[3]
        
        # Check if PTR record exists
        $record = Get-DnsServerResourceRecord -Name $ptrName -ZoneName $ptrZone -RRType Ptr -ComputerName $dnsServer -ErrorAction SilentlyContinue
        
        if ($record) {
          # Remove PTR record
          Remove-DnsServerResourceRecord -Name $ptrName -ZoneName $ptrZone -RRType Ptr -ComputerName $dnsServer -Force
          
          return @{
            Success = $true
            Message = "Successfully removed PTR record for $IPAddress"
          } | ConvertTo-Json
        }
        else {
          return @{
            Success = $true
            Message = "PTR record for $IPAddress does not exist"
          } | ConvertTo-Json
        }
      }
      catch {
        return @{
          Success = $false
          Message = "Failed to remove PTR record: $_"
        } | ConvertTo-Json
      }
    parameters:
      IPAddress: "{{ ip_address }}"
  register: remove_ptr_result
  delegate_to: "{{ ptr_server }}"

- name: Parse PTR record result
  ansible.builtin.set_fact:
    ptr_record_result: "{{ remove_ptr_result.output | from_json }}"

- name: Log PTR record result
  ansible.builtin.debug:
    msg: "PTR record result: {{ ptr_record_result.Message }}"
  when: ptr_record_result.Success | bool

- name: Log PTR record failure
  ansible.builtin.debug:
    msg: "Failed to remove PTR record: {{ ptr_record_result.Message }}"
  when: not ptr_record_result.Success | bool
