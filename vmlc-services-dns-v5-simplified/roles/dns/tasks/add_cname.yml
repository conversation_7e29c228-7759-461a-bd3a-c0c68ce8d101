---
# Add CNAME Record Tasks
# Author: CES Operational Excellence Team
# Contributors: <PERSON> (7409)

- name: Get DNS server for domain
  ansible.builtin.set_fact:
    dns_server: "{{ domains[domain].dns_server }}"

- name: Copy DNS operations script to remote server
  ansible.windows.win_copy:
    src: dns_operations.ps1
    dest: C:\Temp\dns_operations.ps1
  delegate_to: "{{ dns_server }}"

- name: Add CNAME record
  ansible.windows.win_powershell:
    script: |
      param(
        [string]$Hostname,
        [string]$Domain,
        [string]$CnameTarget,
        [int]$TTL,
        [string]$Description
      )

      $ErrorActionPreference = "Stop"

      # Import the DNS operations module
      . C:\Temp\dns_operations.ps1

      # Call the function from the module
      Add-DNSCNAMERecord -Hostname $Hostname -Domain $Domain -CnameTarget $CnameTarget -TTL $TTL -Description $Description
    parameters:
      Hostname: "{{ hostname }}"
      Domain: "{{ domain }}"
      CnameTarget: "{{ cname_target }}"
      TTL: "{{ ttl }}"
      Description: "{{ description }}"
  register: add_cname_result
  delegate_to: "{{ dns_server }}"

- name: Parse CNAME record result
  ansible.builtin.set_fact:
    cname_record_result: "{{ add_cname_result.output | from_json }}"

- name: Fail if CNAME record addition failed
  ansible.builtin.fail:
    msg: "Failed to add CNAME record: {{ cname_record_result.Message }}"
  when: not cname_record_result.Success | bool
