---
# Update CNAME Record Tasks
# Author: CES Operational Excellence Team
# Contributors: <PERSON> (7409)

- name: Get DNS server for domain
  ansible.builtin.set_fact:
    dns_server: "{{ domains[domain].dns_server }}"

- name: Update CNAME record
  ansible.windows.win_powershell:
    script: |
      param(
        [string]$Hostname,
        [string]$Domain,
        [string]$CnameTarget,
        [int]$TTL,
        [string]$Description
      )
      
      $ErrorActionPreference = "Stop"
      
      try {
        $fqdn = "$Hostname.$Domain"
        $dnsServer = [System.Net.Dns]::GetHostEntry($env:COMPUTERNAME).HostName
        
        # Get existing record
        $oldRecord = Get-DnsServerResourceRecord -Name $Hostname -ZoneName $Domain -RRType CName -ComputerName $dnsServer
        $oldTarget = $oldRecord.RecordData.HostNameAlias
        
        # Create new record
        $newRecord = $oldRecord.Clone()
        $newRecord.TimeToLive = [System.TimeSpan]::FromSeconds($TTL)
        $newRecord.RecordData.HostNameAlias = $CnameTarget
        
        if ($Description) {
          $newRecord.Description = $Description
        }
        
        # Update record
        Set-DnsServerResourceRecord -OldInputObject $oldRecord -NewInputObject $newRecord -ZoneName $Domain -ComputerName $dnsServer
        
        return @{
          Success = $true
          Message = "Successfully updated CNAME record $fqdn from $oldTarget to $CnameTarget"
        } | ConvertTo-Json
      }
      catch {
        return @{
          Success = $false
          Message = "Failed to update CNAME record: $_"
        } | ConvertTo-Json
      }
    parameters:
      Hostname: "{{ hostname }}"
      Domain: "{{ domain }}"
      CnameTarget: "{{ cname_target }}"
      TTL: "{{ ttl }}"
      Description: "{{ description }}"
  register: update_cname_result
  delegate_to: "{{ dns_server }}"

- name: Parse CNAME record result
  ansible.builtin.set_fact:
    cname_record_result: "{{ update_cname_result.output | from_json }}"

- name: Fail if CNAME record update failed
  ansible.builtin.fail:
    msg: "Failed to update CNAME record: {{ cname_record_result.Message }}"
  when: not cname_record_result.Success | bool
