---
# Remove A Record Tasks
# Author: CES Operational Excellence Team
# Contributors: <PERSON> (7409)

- name: Get DNS server for domain
  ansible.builtin.set_fact:
    dns_server: "{{ domains[domain].dns_server }}"
    ptr_server: "{{ domains[domain].ptr_server }}"

- name: Remove A record
  ansible.windows.win_powershell:
    script: |
      param(
        [string]$Hostname,
        [string]$Domain
      )
      
      $ErrorActionPreference = "Stop"
      
      try {
        $fqdn = "$Hostname.$Domain"
        $dnsServer = [System.Net.Dns]::GetHostEntry($env:COMPUTERNAME).HostName
        
        # Get record first to check if it exists
        $record = Get-DnsServerResourceRecord -Name $Hostname -ZoneName $Domain -RRType A -ComputerName $dnsServer -ErrorAction SilentlyContinue
        
        if ($record) {
          # Store IP address for PTR removal
          $ipAddress = $record.RecordData.IPv4Address.ToString()
          
          # Remove A record
          Remove-DnsServerResourceRecord -Name $Hostname -ZoneName $Domain -RRType A -ComputerName $dnsServer -Force
          
          return @{
            Success = $true
            Message = "Successfully removed A record $fqdn"
            IPAddress = $ipAddress
          } | ConvertTo-Json
        }
        else {
          return @{
            Success = $true
            Message = "A record $fqdn does not exist"
            IPAddress = ""
          } | ConvertTo-Json
        }
      }
      catch {
        return @{
          Success = $false
          Message = "Failed to remove A record: $_"
        } | ConvertTo-Json
      }
    parameters:
      Hostname: "{{ hostname }}"
      Domain: "{{ domain }}"
  register: remove_a_result
  delegate_to: "{{ dns_server }}"

- name: Parse A record result
  ansible.builtin.set_fact:
    a_record_result: "{{ remove_a_result.output | from_json }}"

- name: Remove PTR record if requested
  ansible.builtin.include_tasks: remove_ptr.yml
  vars:
    ip_address: "{{ a_record_result.IPAddress }}"
  when: 
    - manage_ptr | bool
    - a_record_result.Success | bool
    - a_record_result.IPAddress | length > 0

- name: Fail if A record removal failed
  ansible.builtin.fail:
    msg: "Failed to remove A record: {{ a_record_result.Message }}"
  when: not a_record_result.Success | bool
