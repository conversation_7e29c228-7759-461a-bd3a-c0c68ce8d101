---
# Cleanup Tasks
# Author: CES Operational Excellence Team
# Contributors: <PERSON> (7409)

- name: Clear sensitive variables
  ansible.builtin.set_fact:
    domain_credentials: null
    jira_credentials: null
    bitbucket_credentials: null
    dns_credentials: null
  when: security.clear_after_use | default(true) | bool
  no_log: "{{ security.use_no_log | default(true) }}"

- name: Remove old log files
  ansible.builtin.find:
    paths: "{{ log_dir }}"
    patterns: "*.log"
    age: "{{ logging_settings.log_retention | default(30) }}d"
  register: old_logs
  when: log_dir is defined

- name: Delete old log files
  ansible.builtin.file:
    path: "{{ item.path }}"
    state: absent
  loop: "{{ old_logs.files }}"
  when: old_logs is defined and old_logs.files | length > 0

- name: Remove old operation logs
  ansible.builtin.find:
    paths: "{{ log_dir }}"
    patterns: "operation_*.json"
    age: "{{ logging_settings.log_retention | default(30) }}d"
  register: old_operation_logs
  when: log_dir is defined

- name: Delete old operation logs
  ansible.builtin.file:
    path: "{{ item.path }}"
    state: absent
  loop: "{{ old_operation_logs.files }}"
  when: old_operation_logs is defined and old_operation_logs.files | length > 0
