---
# Validate Parameters Tasks
# Author: CES Operational Excellence Team
# Contributors: <PERSON> (7409)

- name: Validate operation parameter
  ansible.builtin.fail:
    msg: "Operation parameter is required"
  when: operation is not defined or operation | length == 0

- name: Validate record type parameter
  ansible.builtin.fail:
    msg: "Record type parameter is required"
  when: record_type is not defined or record_type | length == 0

- name: Validate hostname parameter for non-multi-domain operations
  ansible.builtin.fail:
    msg: "Hostname parameter is required for {{ record_type }} records"
  when: 
    - not is_multi_domain | bool
    - record_type != 'ptr'
    - (hostname is not defined or hostname | length == 0)

- name: Validate domain parameter for non-multi-domain operations
  ansible.builtin.fail:
    msg: "Domain parameter is required"
  when: 
    - not is_multi_domain | bool
    - record_type != 'ptr'
    - (domain is not defined or domain | length == 0)

- name: Validate IP address parameter for A records
  ansible.builtin.fail:
    msg: "IP address parameter is required for A records"
  when: 
    - not is_multi_domain | bool
    - record_type == 'a'
    - operation in ['add', 'update']
    - (ip_address is not defined or ip_address | length == 0)

- name: Validate IP address parameter for PTR records
  ansible.builtin.fail:
    msg: "IP address parameter is required for PTR records"
  when: 
    - not is_multi_domain | bool
    - record_type == 'ptr'
    - (ip_address is not defined or ip_address | length == 0)

- name: Validate CNAME target parameter for CNAME records
  ansible.builtin.fail:
    msg: "CNAME target parameter is required for CNAME records"
  when: 
    - not is_multi_domain | bool
    - record_type == 'cname'
    - operation in ['add', 'update']
    - (cname_target is not defined or cname_target | length == 0)

- name: Validate multi-domain parameters
  ansible.builtin.fail:
    msg: "Hostnames and domains parameters are required for multi-domain operations"
  when: 
    - is_multi_domain | bool
    - (hostnames is not defined or hostnames | length == 0 or domains is not defined or domains | length == 0)

- name: Validate multi-domain IP addresses for A records
  ansible.builtin.fail:
    msg: "IP addresses parameter is required for multi-domain A records"
  when: 
    - is_multi_domain | bool
    - record_type == 'a'
    - operation in ['add', 'update']
    - (ip_addresses is not defined or ip_addresses | length == 0)

- name: Validate multi-domain CNAME targets for CNAME records
  ansible.builtin.fail:
    msg: "CNAME targets parameter is required for multi-domain CNAME records"
  when: 
    - is_multi_domain | bool
    - record_type == 'cname'
    - operation in ['add', 'update']
    - (cname_targets is not defined or cname_targets | length == 0)

- name: Validate ticket parameter
  ansible.builtin.fail:
    msg: "Ticket parameter is required"
  when: ticket is not defined or ticket | length == 0

- name: Validate domain exists in configuration
  ansible.builtin.fail:
    msg: "Domain {{ domain }} is not configured in the system"
  when: 
    - not is_multi_domain | bool
    - record_type != 'ptr'
    - domain is defined and domain | length > 0
    - domains is defined
    - domain not in domains
