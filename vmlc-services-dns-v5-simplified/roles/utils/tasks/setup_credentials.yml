---
# Setup Credentials Tasks
# Author: CES Operational Excellence Team
# Contributors: <PERSON> (7409)

- name: Set domain credentials from vault
  ansible.builtin.set_fact:
    domain_credentials: "{{ credentials.domains[domain] }}"
  when: 
    - not is_multi_domain | bool
    - domain is defined and domain | length > 0
    - credentials is defined
    - credentials.domains is defined
    - domain in credentials.domains
    - not use_cyberark | default(integration.cyberark.enabled) | bool
  no_log: "{{ security.use_no_log | default(true) }}"

- name: Set domain credentials from CyberArk
  ansible.builtin.set_fact:
    domain_credentials:
      username: "{{ dns_credentials.username }}"
      password: "{{ dns_credentials.password }}"
  when: 
    - use_cyberark | default(integration.cyberark.enabled) | bool
    - dns_credentials is defined
  no_log: "{{ security.use_no_log | default(true) }}"

- name: Set Jira credentials
  ansible.builtin.set_fact:
    jira_credentials: "{{ credentials.jira }}"
  when: 
    - integration.jira.enabled | bool
    - credentials is defined
    - credentials.jira is defined
  no_log: "{{ security.use_no_log | default(true) }}"

- name: Set Bitbucket credentials
  ansible.builtin.set_fact:
    bitbucket_credentials: "{{ credentials.bitbucket }}"
  when: 
    - integration.bitbucket.enabled | bool
    - credentials is defined
    - credentials.bitbucket is defined
  no_log: "{{ security.use_no_log | default(true) }}"
