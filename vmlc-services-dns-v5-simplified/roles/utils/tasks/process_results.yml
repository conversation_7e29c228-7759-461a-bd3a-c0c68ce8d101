---
# Process Results Tasks
# Author: CES Operational Excellence Team
# Contributors: <PERSON> (7409)

- name: Log operation result
  ansible.builtin.debug:
    msg: "{{ dns_operation_result.message }}"
  when: dns_operation_result is defined

- name: Write operation result to log file
  ansible.builtin.copy:
    content: "{{ ansible_date_time.iso8601 }} - INFO - {{ dns_operation_result.message }}\n"
    dest: "{{ log_file }}"
    mode: '0644'
  when: 
    - log_file is defined
    - dns_operation_result is defined

- name: Write operation result to remote log file
  ansible.windows.win_copy:
    content: "{{ ansible_date_time.iso8601 }} - INFO - {{ dns_operation_result.message }}\n"
    dest: "{{ remote_log_file }}"
  delegate_to: "{{ remote_log_server }}"
  when: 
    - remote_log_file is defined
    - remote_log_server is defined
    - dns_operation_result is defined
  ignore_errors: true

- name: Save operation log for rollback
  ansible.builtin.copy:
    content: "{{ operation_log | to_json }}"
    dest: "{{ log_dir }}/operation_{{ ansible_job_id | default('local') }}.json"
    mode: '0644'
  vars:
    operation_log:
      job_id: "{{ ansible_job_id | default('local') }}"
      timestamp: "{{ ansible_date_time.iso8601 }}"
      operation: "{{ operation }}"
      record_type: "{{ record_type }}"
      hostname: "{{ hostname | default('') }}"
      domain: "{{ domain | default('') }}"
      ip_address: "{{ ip_address | default('') }}"
      cname_target: "{{ cname_target | default('') }}"
      ttl: "{{ ttl | default(dns_settings.default_ttl) }}"
      description: "{{ description | default('') }}"
      ticket: "{{ ticket }}"
      success: "{{ dns_operation_result.success }}"
      message: "{{ dns_operation_result.message }}"
  when: 
    - log_dir is defined
    - dns_operation_result is defined
    - not is_multi_domain | bool
    - not is_rollback | bool
