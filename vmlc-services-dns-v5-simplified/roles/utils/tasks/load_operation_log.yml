---
# Load Operation Log Tasks
# Author: CES Operational Excellence Team
# Contributors: <PERSON> (7409)

- name: Load operation log
  ansible.builtin.slurp:
    src: "{{ log_dir }}/operation_{{ job_id }}.json"
  register: operation_log_content
  when: log_dir is defined

- name: Parse operation log
  ansible.builtin.set_fact:
    operation_log: "{{ operation_log_content.content | b64decode | from_json }}"
  when: operation_log_content is defined and operation_log_content.content is defined
