---
# Setup Logging Tasks
# Author: CES Operational Excellence Team
# Contributors: <PERSON> (7409)

- name: Set log directory
  ansible.builtin.set_fact:
    log_dir: "{{ logging_settings.log_dir }}"
  when: logging_settings is defined and logging_settings.log_dir is defined

- name: Create log directory
  ansible.builtin.file:
    path: "{{ log_dir }}"
    state: directory
    mode: '0755'
  when: log_dir is defined

- name: Set log file name
  ansible.builtin.set_fact:
    log_file: "{{ log_dir }}/{{ ansible_date_time.date }}_{{ ansible_date_time.time | replace(':', '') }}_{{ ticket }}_DNS_{{ operation | upper }}.log"
  when: log_dir is defined and ticket is defined

- name: Set remote log directory
  ansible.builtin.set_fact:
    remote_log_dir: "{{ logging_settings.remote_logging.target_path }}"
  when: 
    - logging_settings is defined
    - logging_settings.remote_logging is defined
    - logging_settings.remote_logging.enabled | bool
    - logging_settings.remote_logging.target_path is defined

- name: Set remote log file name
  ansible.builtin.set_fact:
    remote_log_file: "{{ remote_log_dir }}{{ ansible_job_id | default('local') }}_{{ ansible_date_time.date | replace('-', '') }}_{{ ticket }}_DNS_{{ operation | upper }}.log"
  when: 
    - remote_log_dir is defined
    - ticket is defined

- name: Set remote log server
  ansible.builtin.set_fact:
    remote_log_server: "{{ logging_settings.remote_logging.target_server }}"
  when: 
    - logging_settings is defined
    - logging_settings.remote_logging is defined
    - logging_settings.remote_logging.enabled | bool
    - logging_settings.remote_logging.target_server is defined
