---
# Handle Errors Tasks
# Author: CES Operational Excellence Team
# Contributors: <PERSON> (7409)

- name: Set error result
  ansible.builtin.set_fact:
    dns_operation_result:
      success: false
      changed: false
      message: "{{ ansible_failed_result.msg }}"
      error: "{{ ansible_failed_result }}"
      record:
        hostname: "{{ hostname | default('') }}"
        domain: "{{ domain | default('') }}"
        record_type: "{{ record_type | default('') }}"
        ip_address: "{{ ip_address | default('') }}"
        cname_target: "{{ cname_target | default('') }}"

- name: Log error
  ansible.builtin.debug:
    msg: "Error: {{ ansible_failed_result.msg }}"

- name: Write error to log file
  ansible.builtin.copy:
    content: "{{ ansible_date_time.iso8601 }} - ERROR - {{ ansible_failed_result.msg }}\n"
    dest: "{{ log_file }}"
    mode: '0644'
  when: log_file is defined

- name: Write error to remote log file
  ansible.windows.win_copy:
    content: "{{ ansible_date_time.iso8601 }} - ERROR - {{ ansible_failed_result.msg }}\n"
    dest: "{{ remote_log_file }}"
  delegate_to: "{{ remote_log_server }}"
  when: 
    - remote_log_file is defined
    - remote_log_server is defined
  ignore_errors: true
