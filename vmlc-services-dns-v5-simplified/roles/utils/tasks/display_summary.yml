---
# Display Summary Tasks
# Author: CES Operational Excellence Team
# Contributors: <PERSON> (7409)

- name: Display operation summary
  ansible.builtin.debug:
    msg: |
      DNS Operation Summary:
      ---------------------
      Operation: {{ operation | capitalize }}{% if is_rollback %} (Rollback){% endif %}
      Record Type: {{ record_type | upper }}
      {% if not is_multi_domain %}
      Hostname: {{ hostname }}
      Domain: {{ domain }}
      {% if record_type == 'a' %}
      IP Address: {{ ip_address }}
      {% endif %}
      {% if record_type == 'cname' %}
      CNAME Target: {{ cname_target }}
      {% endif %}
      {% else %}
      Domains: {{ domains }}
      Total Records: {{ multi_domain_results | length }}
      Successful: {{ multi_domain_results | selectattr('success', 'equalto', true) | list | length }}
      Failed: {{ multi_domain_results | selectattr('success', 'equalto', false) | list | length }}
      {% endif %}
      Ticket: {{ ticket }}
      Status: {{ 'SUCCESS' if dns_operation_result.success else 'FAILED' }}
      Message: {{ dns_operation_result.message }}
  when: dns_operation_result is defined
