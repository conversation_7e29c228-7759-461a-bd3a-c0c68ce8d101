---
# Validate Rollback Parameters Tasks
# Author: CES Operational Excellence Team
# Contributors: <PERSON> (7409)

- name: Validate job ID parameter
  ansible.builtin.fail:
    msg: "Job ID parameter is required for rollback operations"
  when: job_id is not defined or job_id | length == 0

- name: Validate ticket parameter
  ansible.builtin.fail:
    msg: "Ticket parameter is required for rollback operations"
  when: ticket is not defined or ticket | length == 0

- name: Check if operation log exists
  ansible.builtin.stat:
    path: "{{ log_dir }}/operation_{{ job_id }}.json"
  register: operation_log_stat
  when: log_dir is defined

- name: Fail if operation log does not exist
  ansible.builtin.fail:
    msg: "Operation log for job ID {{ job_id }} not found"
  when: 
    - log_dir is defined
    - operation_log_stat is defined
    - not operation_log_stat.stat.exists
