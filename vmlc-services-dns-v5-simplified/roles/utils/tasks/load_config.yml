---
# Load Configuration Tasks
# Author: CES Operational Excellence Team
# Contributors: <PERSON> (7409)

- name: Load DNS settings
  ansible.builtin.set_fact:
    dns_settings: "{{ dns_settings }}"
  when: dns_settings is defined

- name: Load domain configuration
  ansible.builtin.set_fact:
    domains: "{{ domains }}"
  when: domains is defined

- name: Load email settings
  ansible.builtin.set_fact:
    email_settings: "{{ email_settings }}"
  when: email_settings is defined

- name: Load logging settings
  ansible.builtin.set_fact:
    logging_settings: "{{ logging_settings }}"
  when: logging_settings is defined

- name: Load integration settings
  ansible.builtin.set_fact:
    integration: "{{ integration }}"
  when: integration is defined

- name: Set data center based on hostname
  ansible.builtin.set_fact:
    data_center: "{{ 'hdc1' if 'hdc1' in ansible_hostname else 'hdc2' if 'hdc2' in ansible_hostname else 'hdc1' }}"

- name: Set SMTP server based on data center
  ansible.builtin.set_fact:
    smtp_server: "{{ email_settings.smtp_servers[data_center] }}"
  when: email_settings is defined and email_settings.smtp_servers is defined

- name: Set email recipient based on domain
  ansible.builtin.set_fact:
    email_recipient: "{{ email_settings.domain_recipients[domain] if domain is defined and domain in email_settings.domain_recipients else email_settings.default_recipient }}"
  when: email_settings is defined and email_settings.domain_recipients is defined

- name: Set testing email recipient if in testing mode
  ansible.builtin.set_fact:
    email_recipient: "{{ email_settings.testing_recipient }}"
  when: 
    - email_settings is defined
    - email_settings.testing_recipient is defined
    - testing_mode | bool
