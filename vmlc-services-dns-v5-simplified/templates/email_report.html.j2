<!DOCTYPE html>
<html>
<head>
    <meta charset="UTF-8">
    <style>
        body {
            font-family: Arial, sans-serif;
            line-height: 1.6;
            color: #333;
            max-width: 800px;
            margin: 0 auto;
        }
        .header {
            background-color: #0066cc;
            color: white;
            padding: 20px;
            text-align: center;
        }
        .content {
            padding: 20px;
        }
        .footer {
            background-color: #f2f2f2;
            padding: 10px;
            text-align: center;
            font-size: 12px;
            color: #666;
        }
        table {
            width: 100%;
            border-collapse: collapse;
            margin: 20px 0;
        }
        th, td {
            border: 1px solid #ddd;
            padding: 8px;
            text-align: left;
        }
        th {
            background-color: #f2f2f2;
        }
        .success {
            color: green;
        }
        .failure {
            color: red;
        }
    </style>
</head>
<body>
    <div class="header">
        <h1>DNS Management Operation Report</h1>
    </div>
    <div class="content">
        <h2>Operation Details</h2>
        <table>
            <tr>
                <th>Parameter</th>
                <th>Value</th>
            </tr>
            <tr>
                <td>Operation</td>
                <td>{{ operation | capitalize }}{% if is_rollback %} (Rollback){% endif %}</td>
            </tr>
            <tr>
                <td>Record Type</td>
                <td>{{ record_type | upper }}</td>
            </tr>
            {% if not is_multi_domain %}
            <tr>
                <td>Hostname</td>
                <td>{{ hostname }}</td>
            </tr>
            <tr>
                <td>Domain</td>
                <td>{{ domain }}</td>
            </tr>
            {% if record_type == 'a' %}
            <tr>
                <td>IP Address</td>
                <td>{{ ip_address }}</td>
            </tr>
            {% endif %}
            {% if record_type == 'cname' %}
            <tr>
                <td>CNAME Target</td>
                <td>{{ cname_target }}</td>
            </tr>
            {% endif %}
            {% else %}
            <tr>
                <td>Domains</td>
                <td>{{ domains }}</td>
            </tr>
            <tr>
                <td>Total Records</td>
                <td>{{ multi_domain_results | length }}</td>
            </tr>
            {% endif %}
            <tr>
                <td>Ticket</td>
                <td>{{ ticket }}</td>
            </tr>
            <tr>
                <td>Timestamp</td>
                <td>{{ '%Y-%m-%d %H:%M:%S' | strftime }}</td>
            </tr>
            <tr>
                <td>Status</td>
                <td class="{% if dns_operation_result.success %}success{% else %}failure{% endif %}">
                    {{ 'SUCCESS' if dns_operation_result.success else 'FAILED' }}
                </td>
            </tr>
        </table>

        {% if not is_multi_domain %}
        <h2>Result</h2>
        <p>{{ dns_operation_result.message }}</p>
        {% else %}
        <h2>Results Summary</h2>
        <table>
            <tr>
                <th>Status</th>
                <th>Count</th>
                <th>Percentage</th>
            </tr>
            <tr>
                <td class="success">Successful</td>
                <td>{{ multi_domain_results | selectattr('success', 'equalto', true) | list | length }}</td>
                <td>{{ (multi_domain_results | selectattr('success', 'equalto', true) | list | length / multi_domain_results | length * 100) | round(2) }}%</td>
            </tr>
            <tr>
                <td class="failure">Failed</td>
                <td>{{ multi_domain_results | selectattr('success', 'equalto', false) | list | length }}</td>
                <td>{{ (multi_domain_results | selectattr('success', 'equalto', false) | list | length / multi_domain_results | length * 100) | round(2) }}%</td>
            </tr>
        </table>

        <h2>Detailed Results</h2>
        <table>
            <tr>
                <th>Hostname</th>
                <th>Domain</th>
                <th>Status</th>
                <th>Message</th>
            </tr>
            {% for result in multi_domain_results %}
            <tr>
                <td>{{ result.record.hostname }}</td>
                <td>{{ result.record.domain }}</td>
                <td class="{% if result.success %}success{% else %}failure{% endif %}">
                    {{ 'SUCCESS' if result.success else 'FAILED' }}
                </td>
                <td>{{ result.message }}</td>
            </tr>
            {% endfor %}
        </table>
        {% endif %}
    </div>
    <div class="footer">
        <p>This is an automated email from the DNS Management System. Please do not reply to this email.</p>
        <p>© {{ '%Y' | strftime }} CES Operational Excellence Team</p>
    </div>
</body>
</html>
