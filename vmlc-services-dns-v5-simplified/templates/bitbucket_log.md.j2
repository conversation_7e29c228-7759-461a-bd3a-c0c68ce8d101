# DNS Operation Log - {{ ticket }}

## Operation Details
- **Operation:** {{ operation | capitalize }}{% if is_rollback %} (Rollback){% endif %}
- **Record Type:** {{ record_type | upper }}
- **{{ 'Hostname:' if not is_multi_domain else 'Domains:' }}** {{ hostname if not is_multi_domain else domains }}
- **{{ 'Domain:' if not is_multi_domain else 'Total Domains:' }}** {{ domain if not is_multi_domain else multi_domain_results | length }}
- **Ticket:** {{ ticket }}
- **Timestamp:** {{ '%Y-%m-%d %H:%M:%S' | strftime }}

## Results
{% if not is_multi_domain %}
{{ dns_operation_result.message }}
{% else %}
{{ multi_domain_results | selectattr('success', 'equalto', true) | list | length }} successful, {{ multi_domain_results | selectattr('success', 'equalto', false) | list | length }} failed

### Domain Details
{% for result in multi_domain_results %}
- **{{ result.record.hostname }}.{{ result.record.domain }}:** {{ 'Success' if result.success else 'Failure' }} - {{ result.message }}
{% endfor %}
{% endif %}

## System Information
- **Server:** {{ ansible_hostname }}
- **Job ID:** {{ ansible_job_id | default('N/A') }}
- **User:** {{ ansible_user_id }}

---
*Generated by DNS Management System*
