---
# Vault configuration for DNS Management (encrypted in production)
# Author: CES Operational Excellence Team
# Contributors: <PERSON> Bin <PERSON> (7409)

# Domain credentials
credentials:
  domains:
    ses.shsu.com.sg:
      username: "{{ var_dns_shsu_username }}"
      password: "{{ var_dns_shsu_password }}"
    
    shses.shs.com.sg:
      username: "{{ var_dns_shs_username }}"
      password: "{{ var_dns_shs_password }}"
    
    healthgrp.com.sg:
      username: "{{ var_dns_healthgrp_username }}"
      password: "{{ var_dns_healthgrp_password }}"
    
    devhealthgrp.com.sg:
      username: "{{ var_dns_devhealthgrp_username }}"
      password: "{{ var_dns_devhealthgrp_password }}"
  
  # Integration credentials
  jira:
    username: "{{ var_jira_username }}"
    password: "{{ var_jira_password }}"
  
  bitbucket:
    username: "{{ var_bitbucket_username }}"
    password: "{{ var_bitbucket_password }}"

# Variables for local testing (these would be encrypted in production)
var_dns_shsu_username: "administrator"
var_dns_shsu_password: "Password123!"
var_dns_shs_username: "administrator"
var_dns_shs_password: "Password123!"
var_dns_healthgrp_username: "administrator"
var_dns_healthgrp_password: "Password123!"
var_dns_devhealthgrp_username: "administrator"
var_dns_devhealthgrp_password: "Password123!"

var_jira_username: "jira_api_user"
var_jira_password: "jira_api_password"
var_bitbucket_username: "bitbucket_api_user"
var_bitbucket_password: "bitbucket_api_password"
