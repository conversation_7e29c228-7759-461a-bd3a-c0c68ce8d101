---
# Defaults for variables used in the 'rollback_handler' role

# Default operation ID to rollback (though this should always be provided as an extra var)
# operation_id_to_rollback: "" # This being empty would be caught by validation in main.yml

# Default path format for finding logs (should align with common role's logging setup)
# log_dir: "{{ playbook_dir }}/logs" # Inherited from common or group_vars
# log_file_format: "{{ project_name }}_{{ ansible_date_time.iso8601_basic_short }}_{{ operation_id | default('adhoc') }}.log" # Inherited

# Flag to indicate if the parsed log operations should be processed in reverse order for rollback
# reverse_rollback_order: true # Usually true for proper rollback sequencing

# Placeholder for the structure of parsed operations from the log
# This helps document what 'execute_rollback.yml' expects.
# default_parsed_operation_structure:
#   - action: "original_action"      # e.g., add, remove, update
#     rollback_action: "inverted_action" # e.g., remove, add, update (to previous state)
#     dns_zone: "domain.com"
#     record_item:
#       name: "hostname"
#       type: "A"
#       value: "current_value_or_value_to_revert_to"
#       ttl: 3600
#       # Potentially other details like original_value if action was 'update'
#     original_record_item: {} # Populated if original action was 'update', to store the state before update