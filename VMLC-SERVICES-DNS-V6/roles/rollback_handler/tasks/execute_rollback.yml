---
# Executes rollback actions based on 'original_operations_parsed' fact.
# This fact should be a list of operations, each with a 'rollback_action'
# and the necessary 'record_item' and 'dns_zone' to perform it.

- name: "ROLLBACK: Executing rollback for {{ original_operations_parsed | length }} logged operation(s)"
  ansible.builtin.debug:
    msg: "Starting rollback execution phase."
  when: original_operations_parsed is defined and original_operations_parsed | length > 0
  tags:
    - rollback
    - execute

# Loop through the parsed operations (which should be in reverse order of application for proper rollback)
# For simplicity, this example assumes original_operations_parsed is already in the correct order for rollback.
# If not, it might need to be reversed: loop: "{{ original_operations_parsed | reverse }}"
- name: "ROLLBACK: Perform rollback action for a logged operation"
  ansible.builtin.include_role:
    name: dns_management # Call the dns_management role to perform the actual DNS change
    tasks_from: perform_dns_action.yml # Directly call the action task
  vars:
    # Map the parsed log item to the variables expected by perform_dns_action.yml
    # 'item' here is an element from 'original_operations_parsed'
    dns_zone: "{{ item.dns_zone }}"
    record_item:
      action: "{{ item.rollback_action }}" # This is the crucial part: use the inverted action
      name: "{{ item.record_item.name }}"
      type: "{{ item.record_item.type }}"
      # Value might be from original_record_item for 'update' rollbacks, or empty for 'remove' rollbacks
      value: "{{ item.record_item.value if item.rollback_action != 'remove' else (item.original_record_item.value if item.action == 'update' and item.rollback_action == 'add' else '') }}"
      ttl: "{{ item.record_item.ttl | default(default_ttl | default(3600)) }}" # Use original TTL if available
      # manage_ptr might need specific handling during rollback
      manage_ptr: "{{ item.record_item.manage_ptr | default(manage_ptr | default(default_manage_ptr | default(false))) }}" # Typically false for rollbacks unless explicitly logged
  loop: "{{ original_operations_parsed }}"
  loop_control:
    loop_var: item # Each 'item' is a parsed operation from the log
  when: original_operations_parsed is defined and original_operations_parsed | length > 0
  tags:
    - rollback
    - execute

- name: "ROLLBACK: Completed rollback execution phase"
  ansible.builtin.debug:
    msg: "Rollback execution attempted for all parsed operations."
  when: original_operations_parsed is defined and original_operations_parsed | length > 0
  tags:
    - rollback
    - execute

# Important considerations for rollback:
# 1. Order of operations: Rollback should typically happen in the reverse order of the original operations.
#    The log parsing or this loop might need to handle that.
# 2. Statefulness: True rollback to a *previous state* (especially for 'update') requires knowing the *exact*
#    previous state of the record, not just inverting the 'update' action. The log must capture this.
#    If an 'update' was 'hostA ******* -> *******', rollback is 'hostA ******* -> *******'.
#    The `original_record_item` in the parsed log example addresses this.
# 3. PTR records: If PTRs were automatically managed, their rollback also needs to be handled.
#    This might involve explicitly logging PTR changes or deriving them.
#    The `manage_ptr` flag in the loop above is a starting point.