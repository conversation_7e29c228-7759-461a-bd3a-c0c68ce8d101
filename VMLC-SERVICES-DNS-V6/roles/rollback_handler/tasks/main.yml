---
# Main tasks for the rollback_handler role

- name: Validate rollback parameters
  ansible.builtin.assert:
    that:
      - operation_id_to_rollback is defined
      - operation_id_to_rollback | length > 0
    msg: "An 'operation_id_to_rollback' must be provided to the rollback handler."
  tags:
    - rollback
    - validation

- name: Display rollback operation ID
  ansible.builtin.debug:
    msg: "Attempting to roll back operations for Operation ID: {{ operation_id_to_rollback }}"
  tags:
    - rollback
    - debug_info

- name: Load and parse original operation log for rollback
  ansible.builtin.include_tasks: load_operation_log.yml
  vars:
    target_operation_id: "{{ operation_id_to_rollback }}"
  tags:
    - rollback
    - load_log

- name: Execute rollback actions based on parsed log
  ansible.builtin.include_tasks: execute_rollback.yml
  # This task will loop through the reversed operations from the log
  # and call the dns_management role with appropriate parameters.
  when: original_operations_parsed is defined and original_operations_parsed | length > 0
  tags:
    - rollback
    - execute

- name: Handle case where no operations found for rollback ID
  ansible.builtin.debug:
    msg: "No operations found or log unparseable for Operation ID: {{ operation_id_to_rollback }}. Nothing to roll back."
  when: original_operations_parsed is not defined or original_operations_parsed | length == 0
  tags:
    - rollback