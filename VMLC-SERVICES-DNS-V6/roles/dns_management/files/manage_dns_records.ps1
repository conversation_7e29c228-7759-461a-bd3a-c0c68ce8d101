<#
.SYNOPSIS
Manages DNS records (A, CNAME, PTR) on a Windows DNS server.
This script is designed to be idempotent.

.DESCRIPTION
This PowerShell script performs add, remove, update, and verify operations for DNS records.
It returns a JSON object indicating the status of the operation.

.PARAMETER DnsZone
The DNS zone name (e.g., "example.com" for A/CNAME, or "1.168.192.in-addr.arpa" for PTR).

.PARAMETER RecordName
The name of the DNS record.
For A/CNAME: "myhost". Use "@" for records at the zone apex.
For PTR: The host part of the reversed IP address (e.g., "10" if DnsZone is "1.168.192.in-addr.arpa" and IP is ************).

.PARAMETER RecordType
The type of DNS record (A, CNAME, PTR).

.PARAMETER RecordValue
The value of the DNS record.
For A: IP address (e.g., "************0").
For CNAME: Target FQDN (e.g., "target.example.com").
For PTR: Target FQDN (e.g., "myhost.example.com").
Not required for 'remove' or 'verify' actions if just checking existence by name/type.

.PARAMETER RecordAction
The action to perform: "add", "remove", "update", "verify".

.PARAMETER RecordTTL
The Time To Live for the DNS record (in seconds). Defaults to 3600 if not specified for 'add'/'update'.

.PARAMETER ComputerName
The name of the DNS server to target. Defaults to the local machine if not specified.
Ansible will typically pass the inventory_hostname here.

.PARAMETER Force
Switch to force operations, e.g., overwrite existing CNAME when adding A record or vice-versa.

.OUTPUTS
JSON object with status, action, record details, message, and changed status.

.NOTES
Ensure the account running this script has appropriate permissions on the DNS server.
For 'update', if the record doesn't exist, it will be added.
For 'remove', if the record doesn't exist, it's considered a success (idempotency).
For 'verify', it checks if the record exists with the specified parameters.
#>
[CmdletBinding(SupportsShouldProcess=$true)]
param (
    [Parameter(Mandatory=$true)]
    [string]$DnsZone,

    [Parameter(Mandatory=$true)]
    [string]$RecordName,

    [Parameter(Mandatory=$true)]
    [ValidateSet("A", "CNAME", "PTR")]
    [string]$RecordType,

    [string]$RecordValue, # Mandatory for add/update for A/CNAME, and PTR

    [Parameter(Mandatory=$true)]
    [ValidateSet("add", "remove", "update", "verify")]
    [string]$RecordAction,

    [uint32]$RecordTTL = 3600,

    [string]$ComputerName = $env:COMPUTERNAME,

    [switch]$Force
)

$output = @{
    status = "success"
    action = $RecordAction
    record_name = $RecordName # For PTR, this is the host part of reversed IP
    record_type = $RecordType
    record_value = $RecordValue # For PTR, this is the FQDN
    dns_zone = $DnsZone # For PTR, this is the reverse lookup zone
    ttl = $RecordTTL
    message = ""
    changed = $false
    error_details = ""
}

try {
    # Input Validation
    if (($RecordAction -eq "add" -or $RecordAction -eq "update") -and ([string]::IsNullOrEmpty($RecordValue))) {
        throw "RecordValue is mandatory for 'add' or 'update' actions for $RecordType records."
    }
    if ($RecordType -eq "A" -and $RecordValue -ne $null -and $RecordValue -notmatch "^\d{1,3}\.\d{1,3}\.\d{1,3}\.\d{1,3}$") {
        if ($RecordAction -ne "remove" -and $RecordAction -ne "verify"){
             throw "Invalid IPAddress format '$RecordValue' for A record."
        }
    }
    if (($RecordType -eq "CNAME" -or $RecordType -eq "PTR") -and $RecordValue -ne $null -and $RecordValue -notlike "*.*") { # Value should be an FQDN
         if ($RecordAction -ne "remove" -and $RecordAction -ne "verify"){
            throw "Invalid FQDN format '$RecordValue' for $RecordType record value."
         }
    }

    # Determine effective names for operations
    $GetNameParameter = if ($RecordName -eq "@" -and $RecordType -ne "PTR") { $DnsZone } else { $RecordName } # For Get-DnsServerResourceRecord
    $AddNameParameter = $RecordName # For Add-DnsServerResourceRecord cmdlets, -Name is the relative name

    $FullRecordName = if ($RecordName -eq "@" -and $RecordType -ne "PTR") { $DnsZone } else { "$RecordName.$DnsZone" }

    $TargetComputerName = if ($ComputerName -notlike "\\*") { "\\$ComputerName" } else { $ComputerName }

    Write-Verbose "Processing Action: $RecordAction, Type: $RecordType, Name: $RecordName (Effective GetName: $GetNameParameter, AddName: $AddNameParameter), Zone: $DnsZone, Value: $RecordValue, TTL: $RecordTTL, Server: $TargetComputerName, FullRecordName: $FullRecordName"

    switch ($RecordAction) {
        "verify" {
            $existingRecord = Get-DnsServerResourceRecord -ComputerName $TargetComputerName -ZoneName $DnsZone -Name $GetNameParameter -RRType $RecordType -ErrorAction SilentlyContinue
            if ($existingRecord) {
                $match = $false
                if ($RecordType -eq "A") { $match = $existingRecord | Where-Object { $_.RecordData.IPv4Address -eq $RecordValue } }
                elseif ($RecordType -eq "CNAME") { $match = $existingRecord | Where-Object { $_.RecordData.HostNameAlias.ToString() -eq $RecordValue } }
                elseif ($RecordType -eq "PTR") { $match = $existingRecord | Where-Object { $_.RecordData.PtrDomainName.ToString() -eq $RecordValue } }

                if ($match) {
                    $output.message = "$RecordType record '$FullRecordName' with value '$RecordValue' exists."
                } else {
                    $output.status = "error"
                    $foundValue = switch($RecordType) {
                        "A" { $existingRecord.RecordData.IPv4Address -join ', ' }
                        "CNAME" { $existingRecord.RecordData.HostNameAlias.ToString() }
                        "PTR" { $existingRecord.RecordData.PtrDomainName.ToString() }
                    }
                    $output.message = "$RecordType record '$FullRecordName' exists, but not with value '$RecordValue'. Found: '$foundValue'"
                }
            } else {
                $output.status = "error"
                $output.message = "$RecordType record '$FullRecordName' does not exist."
            }
            $output.changed = $false
        }
        "add" {
            if ($RecordType -eq "A" -or $RecordType -eq "CNAME") {
                $conflictingType = if ($RecordType -eq "A") { "CNAME" } else { "A" }
                $conflict = Get-DnsServerResourceRecord -ComputerName $TargetComputerName -ZoneName $DnsZone -Name $GetNameParameter -RRType $conflictingType -ErrorAction SilentlyContinue
                if ($conflict -and !$Force) { throw "$conflictingType record already exists for '$FullRecordName'. Use -Force to overwrite." }
                elseif ($conflict -and $Force) {
                    if ($PSCmdlet.ShouldProcess("$FullRecordName (Conflict: $conflictingType)", "Remove Conflicting Record for $RecordType Add on $TargetComputerName")) {
                        $conflict | Remove-DnsServerResourceRecord -ComputerName $TargetComputerName -ZoneName $DnsZone -Force -PassThru
                        Write-Verbose "Removed conflicting $conflictingType record for '$FullRecordName' due to -Force."
                    } else { throw "Skipped removing conflicting $conflictingType record for '$FullRecordName' due to -WhatIf with -Force." }
                }
            }

            $processAdd = $false
            $existingRecord = $null
            if ($RecordType -eq "A") { $existingRecord = Get-DnsServerResourceRecord -ComputerName $TargetComputerName -ZoneName $DnsZone -Name $GetNameParameter -RRType A -ErrorAction SilentlyContinue | Where-Object { $_.RecordData.IPv4Address -eq $RecordValue } }
            elseif ($RecordType -eq "CNAME") { $existingRecord = Get-DnsServerResourceRecord -ComputerName $TargetComputerName -ZoneName $DnsZone -Name $GetNameParameter -RRType CNAME -ErrorAction SilentlyContinue | Where-Object { $_.RecordData.HostNameAlias.ToString() -eq $RecordValue } }
            elseif ($RecordType -eq "PTR") { $existingRecord = Get-DnsServerResourceRecord -ComputerName $TargetComputerName -ZoneName $DnsZone -Name $GetNameParameter -RRType PTR -ErrorAction SilentlyContinue | Where-Object { $_.RecordData.PtrDomainName.ToString() -eq $RecordValue } }

            if ($existingRecord) { $output.message = "$RecordType record '$FullRecordName' with value '$RecordValue' already exists." } else { $processAdd = $true }

            if ($processAdd) {
                $whatIfMessage = switch($RecordType) {
                    "A"     { "$FullRecordName (Value: $RecordValue, TTL: $RecordTTL)" }
                    "CNAME" { "$FullRecordName (Points to: $RecordValue, TTL: $RecordTTL)" }
                    "PTR"   { "$FullRecordName (Points to: $RecordValue, TTL: $RecordTTL)" }
                }
                if ($PSCmdlet.ShouldProcess($whatIfMessage, "Add $RecordType Record to Zone $DnsZone on $TargetComputerName")) {
                    switch($RecordType) {
                        "A"     { Add-DnsServerResourceRecordA -ComputerName $TargetComputerName -ZoneName $DnsZone -Name $AddNameParameter -IPv4Address $RecordValue -TimeToLive ([System.TimeSpan]::FromSeconds($RecordTTL)) -PassThru }
                        "CNAME" { Add-DnsServerResourceRecordCName -ComputerName $TargetComputerName -ZoneName $DnsZone -Name $AddNameParameter -HostNameAlias $RecordValue -TimeToLive ([System.TimeSpan]::FromSeconds($RecordTTL)) -PassThru }
                        "PTR"   { Add-DnsServerResourceRecordPtr -ComputerName $TargetComputerName -ZoneName $DnsZone -Name $AddNameParameter -PtrDomainName $RecordValue -TimeToLive ([System.TimeSpan]::FromSeconds($RecordTTL)) -PassThru }
                    }
                    $output.message = "$RecordType record '$FullRecordName' with value '$RecordValue' added successfully."
                    $output.changed = $true
                } else {
                    $output.message = "Skipped adding $RecordType record '$FullRecordName' due to -WhatIf."
                    $output.changed = $false
                }
            } else { $output.changed = $false }
        }
        "remove" {
            $recordsToRemove = Get-DnsServerResourceRecord -ComputerName $TargetComputerName -ZoneName $DnsZone -Name $GetNameParameter -RRType $RecordType -ErrorAction SilentlyContinue
            if ($RecordValue) { # If a specific value is provided for removal
                if ($RecordType -eq "A") { $recordsToRemove = $recordsToRemove | Where-Object { $_.RecordData.IPv4Address -eq $RecordValue } }
                elseif ($RecordType -eq "CNAME") { $recordsToRemove = $recordsToRemove | Where-Object { $_.RecordData.HostNameAlias.ToString() -eq $RecordValue } }
                elseif ($RecordType -eq "PTR") { $recordsToRemove = $recordsToRemove | Where-Object { $_.RecordData.PtrDomainName.ToString() -eq $RecordValue } }
            }

            if ($recordsToRemove) {
                $valuesBeingRemoved = switch($RecordType){ "A" { $recordsToRemove.RecordData.IPv4Address -join ', ' } "CNAME" { $recordsToRemove.RecordData.HostNameAlias -join ', ' } "PTR" { $recordsToRemove.RecordData.PtrDomainName -join ', ' } }
                if ($PSCmdlet.ShouldProcess("$FullRecordName (Type: $RecordType, Value(s): $valuesBeingRemoved)", "Remove Record(s) from Zone $DnsZone on $TargetComputerName")) {
                    $recordsToRemove | Remove-DnsServerResourceRecord -ComputerName $TargetComputerName -ZoneName $DnsZone -Force -PassThru
                    $output.message = "$RecordType record(s) '$FullRecordName' removed successfully."
                    $output.changed = $true
                } else {
                    $output.message = "Skipped removing $RecordType record(s) '$FullRecordName' due to -WhatIf."
                    $output.changed = $false
                }
            } else {
                $output.message = "No $RecordType record '$FullRecordName' (Value: $RecordValue) found to remove."
                $output.changed = $false
            }
        }
        "update" {
            Write-Verbose "Attempting to update $RecordType record '$FullRecordName' to value '$RecordValue' and TTL '$RecordTTL'."
            $allCurrentRecords = Get-DnsServerResourceRecord -ComputerName $TargetComputerName -ZoneName $DnsZone -Name $GetNameParameter -RRType $RecordType -ErrorAction SilentlyContinue

            $perfectMatchExists = $false
            $recordToUpdateOrRemove = $null # Specific record that differs in value but not name/type

            if ($allCurrentRecords) {
                foreach ($currentRecord in $allCurrentRecords) {
                    $currentRecordValue = switch($RecordType) {
                        "A"     { $currentRecord.RecordData.IPv4Address }
                        "CNAME" { $currentRecord.RecordData.HostNameAlias.ToString() }
                        "PTR"   { $currentRecord.RecordData.PtrDomainName.ToString() }
                    }
                    $currentTTLSeconds = $currentRecord.TimeToLive.TotalSeconds

                    if ($currentRecordValue -eq $RecordValue -and $currentTTLSeconds -eq $RecordTTL) {
                        $perfectMatchExists = $true
                        break
                    }
                    # If value matches but TTL differs, or if value differs (for single-value records like CNAME/PTR, or first A)
                    if ($currentRecordValue -eq $RecordValue) { # Value matches, TTL must be different
                        $recordToUpdateOrRemove = $currentRecord # Candidate for TTL update
                    } elseif (-not $recordToUpdateOrRemove) { # First differing record found
                         $recordToUpdateOrRemove = $currentRecord
                    }
                }
            }

            if ($perfectMatchExists) {
                $output.message = "$RecordType record '$FullRecordName' already exists with desired value and TTL. No change needed."
                $output.changed = $false
            } else {
                # If we are here, either no record exists, or existing records are not a perfect match.
                # We will remove all existing records of this name/type and add the new one.
                if ($allCurrentRecords) {
                    if ($PSCmdlet.ShouldProcess("$FullRecordName (Type: $RecordType, existing records will be removed)", "Remove existing $RecordType records for update on $TargetComputerName")) {
                        $allCurrentRecords | Remove-DnsServerResourceRecord -ComputerName $TargetComputerName -ZoneName $DnsZone -Force -PassThru
                        Write-Verbose "Removed existing $RecordType record(s) for '$FullRecordName' as part of update."
                        $output.changed = $true # Changed because we removed something
                    } else {
                        throw "Skipped removing existing $RecordType record(s) for '$FullRecordName' during update due to -WhatIf."
                    }
                }

                # Now add the new/updated record
                $whatIfMessageAdd = switch($RecordType) {
                    "A"     { "$FullRecordName (Value: $RecordValue, TTL: $RecordTTL)" }
                    "CNAME" { "$FullRecordName (Points to: $RecordValue, TTL: $RecordTTL)" }
                    "PTR"   { "$FullRecordName (Points to: $RecordValue, TTL: $RecordTTL)" }
                }
                if ($PSCmdlet.ShouldProcess($whatIfMessageAdd, "Add $RecordType Record (as part of update) to Zone $DnsZone on $TargetComputerName")) {
                    switch($RecordType) {
                        "A"     { Add-DnsServerResourceRecordA -ComputerName $TargetComputerName -ZoneName $DnsZone -Name $AddNameParameter -IPv4Address $RecordValue -TimeToLive ([System.TimeSpan]::FromSeconds($RecordTTL)) -PassThru }
                        "CNAME" { Add-DnsServerResourceRecordCName -ComputerName $TargetComputerName -ZoneName $DnsZone -Name $AddNameParameter -HostNameAlias $RecordValue -TimeToLive ([System.TimeSpan]::FromSeconds($RecordTTL)) -PassThru }
                        "PTR"   { Add-DnsServerResourceRecordPtr -ComputerName $TargetComputerName -ZoneName $DnsZone -Name $AddNameParameter -PtrDomainName $RecordValue -TimeToLive ([System.TimeSpan]::FromSeconds($RecordTTL)) -PassThru }
                    }
                    $output.message = "$RecordType record '$FullRecordName' updated/created with value '$RecordValue' and TTL '$RecordTTL'."
                    $output.changed = $true # Definitely changed if we added, or if we removed and added.
                } else {
                    $output.message = "Skipped adding $RecordType record '$FullRecordName' during update due to -WhatIf."
                    # If we didn't remove anything above and skipped add here, changed should be false.
                    # If we DID remove something above, changed is already true.
                }
            }
        }
        default {
            throw "Invalid RecordAction: $RecordAction"
        }
    }

} catch {
    $output.status = "error"
    $output.message = "Error during DNS operation: $($_.Exception.Message)"
    $output.error_details = $_.ToString()
    $output.changed = $false # Ensure changed is false on error
}

# Output the result as JSON
Write-Output ($output | ConvertTo-Json -Depth 5 -Compress)