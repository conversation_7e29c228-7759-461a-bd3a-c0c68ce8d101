---
# Defaults for variables used in the 'dns_management' role

# Default action if not specified in a record item (though validation should catch this)
# default_dns_action: "add"

# Default record type if not specified
# default_dns_record_type: "A"

# Default TTL for DNS records (can also be set globally in group_vars/all/main.yml)
# default_ttl: 3600

# Default for managing PTR records automatically for A records
# default_manage_ptr: true

# Default behavior for PowerShell script execution
# ps_script_computer_name: "{{ inventory_hostname }}" # Usually set directly in the task

# Placeholder for expected input variable structures if not using complex types directly in defaults
# default_dns_records_input_format:
#   - name: ""
#     type: "" # A, CNAME, PTR
#     value: ""
#     action: "" # add, remove, update, verify
#     ttl: "{{ default_ttl }}"
#     manage_ptr: "{{ default_manage_ptr }}"
#     # zone: "" # Zone is typically passed per domain in multi-domain scenarios

# default_dns_operations_input_format:
#   - name: "Default Operation Set" # Optional name for the set of operations
#     domains: [] # List of domain/zone names
#     records: [] # List of record items as defined above

# Verbose debugging for DNS script results
verbose_debug: false