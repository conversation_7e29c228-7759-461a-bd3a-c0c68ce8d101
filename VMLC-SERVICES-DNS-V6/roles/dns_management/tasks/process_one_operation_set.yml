---
# Processes a single 'operation_set' which contains a list of domains and a list of records.
# This file is included by 'multi_domain_wrapper.yml'.
# 'operation_set' is the loop_var from the parent file.

- name: "Iterate through domains for operation set: {{ operation_set.name | default('Unnamed Operation Set') }}"
  ansible.builtin.include_tasks: process_records_for_domain.yml
  loop: "{{ operation_set.domains }}"
  loop_control:
    loop_var: current_domain_zone
  vars:
    # Pass the records from the current operation_set to the next include
    records_to_process: "{{ operation_set.records }}"
    operation_set_name: "{{ operation_set.name | default('Unnamed Operation Set') }}" # For logging/tracking
  tags:
    - dns_management
    - multi_domain