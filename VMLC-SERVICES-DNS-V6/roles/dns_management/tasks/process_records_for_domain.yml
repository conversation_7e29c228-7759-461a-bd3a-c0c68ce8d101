---
# Processes a list of DNS records for a specific domain ('current_domain_zone').
# This file is included by 'process_one_operation_set.yml'.
# 'current_domain_zone' and 'records_to_process' are passed from the parent include.

- name: "Processing records for domain: {{ current_domain_zone }} in operation set: {{ operation_set_name }}"
  ansible.builtin.debug:
    msg: "Domain: {{ current_domain_zone }}, Records: {{ records_to_process | length }}"
  tags:
    - dns_management
    - multi_domain
    - debug_info

- name: "Iterate through records for domain: {{ current_domain_zone }}"
  ansible.builtin.include_tasks: perform_dns_action.yml # This will call the PowerShell script
  loop: "{{ records_to_process }}"
  loop_control:
    loop_var: record_item # This 'record_item' will be used by perform_dns_action.yml
  vars:
    # Ensure current_domain_zone is available to perform_dns_action.yml
    # record_item already contains action, name, type, value etc.
    # We just need to ensure the zone is explicitly passed or set.
    # The PowerShell script will need the zone name.
    dns_zone: "{{ current_domain_zone }}"
  tags:
    - dns_management
    - multi_domain