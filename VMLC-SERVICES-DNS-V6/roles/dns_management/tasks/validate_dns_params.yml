---
# Tasks for validating DNS operation parameters

# This task file is a placeholder for parameter validation logic.
# It should be expanded based on the expected input structure for dns_operations or dns_records.

- name: "VALIDATION: Ensure 'dns_operations' or 'dns_records' is defined"
  ansible.builtin.fail:
    msg: "Either 'dns_operations' (for multi-domain) or 'dns_records' (for single domain) must be defined."
  when: dns_operations is not defined and dns_records is not defined
  tags:
    - dns_management
    - validation

# Example validation for dns_records structure (single domain focus)
- name: "VALIDATION (dns_records): Ensure each record item has an action"
  ansible.builtin.fail:
    msg: "Each item in 'dns_records' must have an 'action' defined (e.g., add, remove, update, verify). Offending item: {{ item }}"
  loop: "{{ dns_records | default([]) }}"
  when:
    - dns_records is defined
    - item.action is not defined or item.action not in ['add', 'remove', 'update', 'verify']
  tags:
    - dns_management
    - validation

- name: "VALIDATION (dns_records): Ensure each record item has a name and type"
  ansible.builtin.fail:
    msg: "Each item in 'dns_records' must have 'name' and 'type' defined. Offending item: {{ item }}"
  loop: "{{ dns_records | default([]) }}"
  when:
    - dns_records is defined
    - item.name is not defined or item.type is not defined
  tags:
    - dns_management
    - validation

- name: "VALIDATION (dns_records): Ensure 'value' is present for add/update actions"
  ansible.builtin.fail:
    msg: "Each item in 'dns_records' with action 'add' or 'update' must have a 'value'. Offending item: {{ item }}"
  loop: "{{ dns_records | default([]) }}"
  when:
    - dns_records is defined
    - item.action in ['add', 'update']
    - item.value is not defined
  tags:
    - dns_management
    - validation

# Example validation for dns_operations structure (multi-domain focus)
- name: "VALIDATION (dns_operations): Ensure 'dns_operations' is a list"
  ansible.builtin.fail:
    msg: "'dns_operations' must be a list."
  when:
    - dns_operations is defined
    - dns_operations | type_debug != 'list'
  tags:
    - dns_management
    - validation

- name: "VALIDATION (dns_operations): Ensure each operation has 'domains' and 'records'"
  ansible.builtin.fail:
    msg: "Each item in 'dns_operations' must have 'domains' (list) and 'records' (list). Offending item: {{ item }}"
  loop: "{{ dns_operations | default([]) }}"
  when:
    - dns_operations is defined
    - dns_operations | type_debug == 'list'
    - item.domains is not defined or item.domains | type_debug != 'list' or item.records is not defined or item.records | type_debug != 'list'
  tags:
    - dns_management
    - validation

# Further validation within the 'records' list of 'dns_operations' would be similar to dns_records checks above,
# but looped within each operation_item and then its records.

# Add more specific validation as needed:
# - Record type (A, CNAME, PTR) validity
# - IP address format for A records
# - FQDN format for CNAME values and record names
# - Zone name validity
# - TTL value range