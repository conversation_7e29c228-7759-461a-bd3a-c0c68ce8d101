---
# Main tasks for the dns_management role

- name: Validate DNS operation parameters
  ansible.builtin.include_tasks: validate_dns_params.yml
  tags:
    - dns_management
    - validation

- name: Include multi-domain wrapper if multiple domains are specified
  ansible.builtin.include_tasks: multi_domain_wrapper.yml
  when: dns_operations is defined and dns_operations | type_debug == 'list' and dns_operations[0].domains is defined
  tags:
    - dns_management
    - multi_domain

# Fallback to single domain operation if not using the multi_domain_wrapper structure
- name: Perform single DNS operation (add, remove, update, verify)
  ansible.builtin.include_tasks: "perform_dns_action.yml"
  loop: "{{ dns_records | default([]) }}" # Assumes dns_records list for single domain ops
  loop_control:
    loop_var: record_item
  when:
    - dns_operations is not defined or not (dns_operations | type_debug == 'list' and dns_operations[0].domains is defined)
    - record_item.action is defined
  tags:
    - dns_management
    - single_domain

# The perform_dns_action.yml will decide which specific task (add_record, remove_record etc.) to call
# based on record_item.action.

# Example of how it might be structured if dns_operations is the primary input:
# - name: Process DNS operations
#   ansible.builtin.include_tasks: process_operation.yml
#   loop: "{{ dns_operations | default([]) }}"
#   loop_control:
#     loop_var: operation_item
#   tags:
#     - dns_management

# process_operation.yml would then loop through domains and records within each operation_item.
# This provides flexibility based on how input variables are structured.
# The current structure leans towards multi_domain_wrapper.yml handling the outer loop.