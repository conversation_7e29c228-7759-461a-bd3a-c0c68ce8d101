---
# Wraps DNS operations to be performed across multiple domains.
# Expects 'dns_operations' list as input. Each item in the list should define:
#   domains: a list of domain names (zones)
#   records: a list of record operations to perform on those domains

- name: Iterate through each multi-domain operation set
  ansible.builtin.include_tasks: process_one_operation_set.yml
  loop: "{{ dns_operations }}"
  loop_control:
    loop_var: operation_set
  tags:
    - dns_management
    - multi_domain

# 'process_one_operation_set.yml' will then iterate through 'operation_set.domains'
# and then 'operation_set.records'.