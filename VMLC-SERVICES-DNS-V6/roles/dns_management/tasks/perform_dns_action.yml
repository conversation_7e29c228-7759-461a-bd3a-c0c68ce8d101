---
# Performs a single DNS action (add, remove, update, verify) using the PowerShell script.
# Expects 'record_item' (loop_var from parent) and 'dns_zone' to be defined.

- name: "DNS ACTION [{{ record_item.action | upper }}]: {{ record_item.type | upper }} record '{{ record_item.name }}' in zone '{{ dns_zone }}'"
  ansible.windows.win_powershell:
    script: |
      # This is a placeholder for the actual PowerShell script content.
      # The real script will be in roles/dns_management/files/manage_dns_records.ps1
      # and called using the 'script' parameter of win_powershell or ansible.builtin.script.
      # For now, simulate parameters and output.

      param(
          [string]$DnsZone,
          [string]$RecordName,
          [string]$RecordType,
          [string]$RecordValue,
          [string]$RecordAction, # add, remove, update, verify
          [string]$RecordTTL,
          [string]$ComputerName # DNS Server to target, defaults to localhost if not specified
      )

      $output = @{
          status = "success"
          action = $RecordAction
          record_name = $RecordName
          record_type = $RecordType
          record_value = $RecordValue
          dns_zone = $DnsZone
          ttl = $RecordTTL
          message = "Simulated: $RecordAction $RecordType record $RecordName in $DnsZone"
          changed = $false # Default to false
      }

      # Simulate change state
      if ($RecordAction -ne "verify") {
          $output.changed = $true
      }

      Write-Output ($output | ConvertTo-Json -Depth 5)
    parameters:
      DnsZone: "{{ dns_zone }}"
      RecordName: "{{ record_item.name }}"
      RecordType: "{{ record_item.type }}"
      RecordValue: "{{ record_item.value | default('') }}" # Ensure value is string, even if empty
      RecordAction: "{{ record_item.action }}"
      RecordTTL: "{{ record_item.ttl | default(default_ttl | default(3600)) }}"
      ComputerName: "{{ ansible_host }}" # Target the specific DNS server from inventory
  register: dns_script_result
  changed_when: dns_script_result.output.changed | default(false) | bool
  failed_when: dns_script_result.output.status == 'error' or dns_script_result.rc != 0
  vars:
    # Ensure ansible_host is available if this task is delegated or run on localhost for some reason
    ansible_host: "{{ inventory_hostname }}"
  tags:
    - dns_management
    - dns_action

- name: "Debug DNS script result for {{ record_item.name }} in {{ dns_zone }}"
  ansible.builtin.debug:
    var: dns_script_result.output
  when: dns_script_result is defined and (dns_script_result.output.status == 'error' or verbose_debug | default(false) | bool)
  tags:
    - dns_management
    - dns_action
    - debug_info

# After A record 'add' or 'update', manage PTR if enabled
- name: "Manage PTR record for A record {{ record_item.name }}"
  ansible.builtin.include_tasks: manage_ptr.yml
  when:
    - record_item.type | lower == 'a'
    - record_item.action in ['add', 'update']
    - record_item.manage_ptr | default(manage_ptr | default(default_manage_ptr | default(true))) | bool
    - dns_script_result.output.status | default('') != 'error' # Only if A record op was successful
  vars:
    a_record_name: "{{ record_item.name }}"
    a_record_value: "{{ record_item.value }}" # This is the IP address
    # PTR zone needs to be derived or provided
    # ptr_zone: # This needs logic to determine (e.g., reverse of IP)
  tags:
    - dns_management
    - ptr_management