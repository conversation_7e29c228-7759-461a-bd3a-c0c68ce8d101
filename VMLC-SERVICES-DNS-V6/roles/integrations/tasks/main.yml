---
# Main tasks for the integrations role

- name: "INTEGRATIONS: Fetch credentials from CyberArk (if enabled)"
  ansible.builtin.include_tasks: cyberark_fetch_creds.yml
  when: enable_cyberark_integration | default(false) | bool
  tags:
    - integrations
    - cyberark
    - pre_dns_ops # Should run before DNS operations if creds are needed

# Jira and Bitbucket updates usually happen after DNS operations are complete.
# These could be called conditionally based on the success of the main operations.

- name: "INTEGRATIONS: Update Jira ticket (if enabled and ticket ID provided)"
  ansible.builtin.include_tasks: jira_update.yml
  when:
    - enable_jira_integration | default(false) | bool
    - jira_ticket_id is defined and jira_ticket_id | length > 0
    # - main_dns_operations_successful | default(true) | bool # Condition on success
  tags:
    - integrations
    - jira
    - post_dns_ops

- name: "INTEGRATIONS: Commit operation summary to Bitbucket (if enabled)"
  ansible.builtin.include_tasks: bitbucket_commit.yml
  when:
    - enable_bitbucket_integration | default(false) | bool
    # - main_dns_operations_successful | default(true) | bool # Condition on success
  tags:
    - integrations
    - bitbucket
    - post_dns_ops

# The variable 'main_dns_operations_successful' would need to be set by the main playbook
# based on the outcome of the dns_management role.