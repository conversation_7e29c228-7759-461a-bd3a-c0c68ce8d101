---
# Tasks for committing operation summary to Bitbucket

- name: "BITBUCKET: Ensure Bitbucket server URL, project, repo, and auth are defined"
  ansible.builtin.assert:
    that:
      - bitbucket_server_url is defined and bitbucket_server_url | length > 0
      - bitbucket_project_key is defined and bitbucket_project_key | length > 0
      - bitbucket_repo_slug is defined and bitbucket_repo_slug | length > 0
      # - bitbucket_user is defined or bitbucket_app_password is defined # Depending on auth
    msg: "Bitbucket server URL, project key, repo slug, and authentication details must be defined."
  tags:
    - integrations
    - bitbucket

- name: "BITBUCKET: Define local path for cloning the Bitbucket repo"
  ansible.builtin.set_fact:
    bitbucket_local_repo_path: "{{ playbook_dir }}/.tmp_bitbucket_repo_{{ operation_id | default('default') }}"
  delegate_to: localhost
  run_once: true
  tags:
    - integrations
    - bitbucket

- name: "BITBUCKET: Clean up existing local repo path (if any)"
  ansible.builtin.file:
    path: "{{ bitbucket_local_repo_path }}"
    state: absent
  delegate_to: localhost
  run_once: true
  tags:
    - integrations
    - bitbucket

- name: "BITBUCKET: Clone the designated Bitbucket repository"
  ansible.builtin.git:
    repo: "{{ bitbucket_server_url }}/scm/{{ bitbucket_project_key | lower }}/{{ bitbucket_repo_slug | lower }}.git" # Adjust URL format as needed
    dest: "{{ bitbucket_local_repo_path }}"
    version: "{{ bitbucket_branch | default('main') }}"
    # For HTTPS with username/password (or app password):
    # repo: "https://{{ bitbucket_user | urlencode }}:{{ bitbucket_app_password | urlencode }}@{{ bitbucket_server_url | replace('https://', '') }}/scm/{{ bitbucket_project_key | lower }}/{{ bitbucket_repo_slug | lower }}.git"
    # SSH key based auth is also an option.
    accept_hostkey: yes # If using SSH and host key is new
    force: yes # Overwrite local changes if any (should be clean clone)
  delegate_to: localhost
  run_once: true
  # no_log: true # If credentials are in the repo URL
  register: git_clone_result
  tags:
    - integrations
    - bitbucket

- name: "BITBUCKET: Fail if git clone failed"
  ansible.builtin.fail:
    msg: "Failed to clone Bitbucket repository. Error: {{ git_clone_result.msg | default('') }} {{ git_clone_result.stderr | default('') }}"
  when: git_clone_result.failed
  delegate_to: localhost
  run_once: true
  tags:
    - integrations
    - bitbucket

- name: "BITBUCKET: Prepare operation summary content for commit"
  ansible.builtin.set_fact:
    bitbucket_summary_filename: "dns_op_{{ operation_id | default('adhoc') }}.md"
    bitbucket_summary_content: |
      # DNS Operation Summary

      **Operation ID:** {{ operation_id | default("N/A") }}
      **Timestamp:** {{ ansible_date_time.iso8601 }}
      **Project:** {{ project_name | default("VMLC-SERVICES-DNS-V6") }}
      **Status:** {{ 'Success' if main_dns_operations_successful | default(true) else 'Failed (or partial success)' }}
      **Jira Ticket:** {{ jira_ticket_id | default("N/A") }}

      ## Summary of Changes
      ```
      {{ dns_operations_summary | default("No detailed summary generated.") }}
      ```

      ## Affected Records
      {{ affected_records_summary | default("Details on affected records not generated.") }}
      # This could be a more structured list of records that were touched.

      Log file on Ansible controller: {{ current_run_log_file | default("N/A") }}
  delegate_to: localhost
  run_once: true
  tags:
    - integrations
    - bitbucket

- name: "BITBUCKET: Create summary file in local repo"
  ansible.builtin.copy:
    content: "{{ bitbucket_summary_content }}"
    dest: "{{ bitbucket_local_repo_path }}/{{ bitbucket_summary_filename }}"
  delegate_to: localhost
  run_once: true
  tags:
    - integrations
    - bitbucket

- name: "BITBUCKET: Add, commit, and push changes to Bitbucket"
  ansible.builtin.command: "git add . && git commit -m '{{ bitbucket_commit_message_prefix | default(\"[Ansible DNS]\") }} Operation {{ operation_id }}' && git push origin {{ bitbucket_branch | default('main') }}"
  args:
    chdir: "{{ bitbucket_local_repo_path }}"
  delegate_to: localhost
  run_once: true
  register: git_push_result
  changed_when: "'pushed' in git_push_result.stdout or 'new branch' in git_push_result.stdout or 'committed' in git_push_result.stdout" # Heuristic
  tags:
    - integrations
    - bitbucket

- name: "BITBUCKET: Debug Git push result"
  ansible.builtin.debug:
    var: git_push_result
  when: git_push_result.rc != 0 or verbose_debug | default(false) | bool
  delegate_to: localhost
  run_once: true
  tags:
    - integrations
    - bitbucket
    - debug_info

- name: "BITBUCKET: Set Bitbucket commit URL fact (placeholder)"
  ansible.builtin.set_fact:
    # This is a placeholder. Actual commit URL would need to be parsed from git_push_result or constructed.
    bitbucket_commit_url: "{{ bitbucket_server_url }}/projects/{{ bitbucket_project_key | upper }}/repos/{{ bitbucket_repo_slug }}/commits/<commit_hash_from_git_push_result>"
  when: git_push_result.rc == 0
  delegate_to: localhost
  run_once: true
  tags:
    - integrations
    - bitbucket

- name: "BITBUCKET: Clean up local repo path"
  ansible.builtin.file:
    path: "{{ bitbucket_local_repo_path }}"
    state: absent
  delegate_to: localhost
  run_once: true
  tags:
    - integrations
    - bitbucket
    - cleanup

# Notes:
# - 'dns_operations_summary', 'affected_records_summary', 'main_dns_operations_successful' are facts
#   that need to be generated by earlier stages of the playbook.
# - Git authentication needs to be handled (SSH keys, HTTPS with app password/token).
#   The example uses a basic HTTPS URL structure; adjust for your auth method.
# - Error handling for git operations should be robust.