---
# Tasks for fetching credentials from CyberArk AIM CCP

- name: "CYBERARK: Ensure CyberArk CCP URL and App ID are defined"
  ansible.builtin.assert:
    that:
      - cyberark_ccp_url is defined and cyberark_ccp_url | length > 0
      - cyberark_app_id is defined and cyberark_app_id | length > 0
    msg: "CyberArk CCP URL (cyberark_ccp_url) and Application ID (cyberark_app_id) must be defined in variables."
  tags:
    - integrations
    - cyberark

- name: "CYBERARK: Determine credential query for DNS server {{ inventory_hostname }}"
  ansible.builtin.set_fact:
    # This query format depends on your CyberArk AIM CCP setup and how objects are named.
    # Example: Safe=MySafe;Object=DNS_Server_dc01.example.com
    # Or: Safe=MySafe;Folder=Servers;Object=dc01.example.com
    cyberark_query: "Safe={{ cyberark_dns_server_safe | default('DefaultSafeNotSet') }};Object={{ cyberark_dns_server_object_name_format | format(inventory_hostname=inventory_hostname) | default('ObjectFormatNotSet_' + inventory_hostname) }}"
  tags:
    - integrations
    - cyberark

- name: "CYBERARK: Fetch credentials for {{ inventory_hostname }} using query '{{ cyberark_query }}'"
  ansible.builtin.uri:
    url: "{{ cyberark_ccp_url }}"
    method: GET
    # The CCP API typically takes parameters in the URL query string
    # Example: {{ cyberark_ccp_url }}?AppID={{ cyberark_app_id }}&Query={{ cyberark_query | urlencode }}
    # This needs to be adjusted based on the exact API endpoint and parameters for your CCP version.
    # For this placeholder, we'll assume parameters are part of the main URL or handled by a custom module.
    # url: "{{ cyberark_ccp_url }}?AppID={{ cyberark_app_id }}&Safe={{ cyberark_dns_server_safe }}&Object={{ cyberark_dns_server_object_name_format | format(inventory_hostname=inventory_hostname) }}" # Simplified example
    # For a more realistic approach, you might use a custom module or a more complex URI task.
    # This is a highly simplified placeholder.
    # body_format: json # if sending data, not typical for GET credential requests
    # client_cert: "{{ cyberark_client_cert | default(omit) }}" # Path to client cert for auth
    # client_key: "{{ cyberark_client_key | default(omit) }}"   # Path to client key
    # ca_path: "{{ cyberark_ca_bundle | default(omit) }}"      # Path to CA bundle for SSL verification
    validate_certs: "{{ false if cyberark_ca_bundle is not defined else true }}" # Basic cert validation toggle
    return_content: yes
    status_code: 200 # Expect a 200 OK on success
  register: cyberark_response
  # no_log: true # Important to hide sensitive output from logs in real scenarios
  delegate_to: localhost # CCP calls are usually made from the control node
  run_once: false # Run for each host in the play that needs creds, if creds are host-specific
  tags:
    - integrations
    - cyberark

- name: "CYBERARK: Set facts for fetched credentials (username and password)"
  ansible.builtin.set_fact:
    # The structure of cyberark_response.json.Content (or .content if not JSON) depends on CCP API.
    # Assuming it returns JSON with 'UserName' and 'Content' (for password) fields.
    retrieved_dns_server_user: "{{ cyberark_response.json.UserName | default(omit) }}"
    retrieved_dns_server_password: "{{ cyberark_response.json.Content | default(omit) }}" # 'Content' is often the password field
    # Cache these per host if needed, or directly use them.
    # ansible_user: "{{ cyberark_response.json.UserName }}" # Override ansible_user for this host
    # ansible_password: "{{ cyberark_response.json.Content }}" # Override ansible_password
  no_log: true # CRITICAL: Do not log passwords or sensitive credential data
  when: cyberark_response.status == 200 and cyberark_response.json is defined
  tags:
    - integrations
    - cyberark

- name: "CYBERARK: Debug retrieved username (DO NOT USE IN PRODUCTION)"
  ansible.builtin.debug:
    var: retrieved_dns_server_user
  when: retrieved_dns_server_user is defined and verbose_debug | default(false) | bool
  no_log: false # Temporarily allow logging for this debug task if verbose_debug is true
  tags:
    - integrations
    - cyberark
    - debug_info

- name: "CYBERARK: Failed to fetch credentials for {{ inventory_hostname }}"
  ansible.builtin.fail:
    msg: "CyberArk credential fetch failed for {{ inventory_hostname }}. Response: {{ cyberark_response }}"
  when: cyberark_response.status != 200 or cyberark_response.json is not defined or 'Content' not in cyberark_response.json
  tags:
    - integrations
    - cyberark

# Note: This is a simplified example. Real CyberArk CCP integration might involve:
# - A dedicated Ansible collection/module for CyberArk AIM.
# - More complex error handling and retry logic.
# - Specific API endpoints and authentication methods (e.g., client certificate authentication).
# - Storing the fetched credentials securely (e.g., directly into ansible_user/ansible_password for the connection).
# The `community.cyberark.aim_credential` module is a good candidate if available and suitable.