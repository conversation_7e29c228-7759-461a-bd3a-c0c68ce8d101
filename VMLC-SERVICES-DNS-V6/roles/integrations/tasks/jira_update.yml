---
# Tasks for updating Jira tickets

- name: "JIRA: Ensure Jira server URL and ticket ID are defined"
  ansible.builtin.assert:
    that:
      - jira_server_url is defined and jira_server_url | length > 0
      - jira_ticket_id is defined and jira_ticket_id | length > 0
      # - jira_api_user is defined or jira_api_token is defined # Depending on auth method
    msg: "Jira server URL (jira_server_url) and Ticket ID (jira_ticket_id) must be defined. Auth (user/token) might also be needed."
  tags:
    - integrations
    - jira

- name: "JIRA: Prepare comment for Jira ticket {{ jira_ticket_id }}"
  ansible.builtin.set_fact:
    jira_comment_body: |
      Ansible operation ID '{{ operation_id | default("N/A") }}' has completed.
      Project: {{ project_name | default("VMLC-SERVICES-DNS-V6") }}
      Status: {{ 'Success' if main_dns_operations_successful | default(true) else 'Failed (or partial success)' }}

      Summary of changes:
      {{ dns_operations_summary | default("No detailed summary available.") }}

      Log file: {{ current_run_log_file | default("N/A") }}
      # Add more details as needed, e.g., link to Bitbucket commit if available
      # Bitbucket Commit: {{ bitbucket_commit_url | default("N/A") }}
  delegate_to: localhost # Comment preparation is local
  run_once: true # Prepare comment once
  tags:
    - integrations
    - jira

- name: "JIRA: Add comment to Jira ticket {{ jira_ticket_id }}"
  community.general.jira:
    uri: "{{ jira_server_url }}"
    username: "{{ jira_api_user | default(omit) }}"
    password: "{{ jira_api_token | default(omit) }}" # Or API token
    project: "{{ jira_ticket_id.split('-')[0] }}" # Attempt to derive project key from ticket ID
    issue: "{{ jira_ticket_id }}"
    operation: comment
    comment: "{{ jira_comment_body }}"
    validate_certs: "{{ false if jira_validate_certs is not defined else jira_validate_certs }}" # Default to false for ease, but should be true in prod
  register: jira_update_result
  # no_log: true # Hide credentials if they were passed directly
  delegate_to: localhost # Jira operations are typically done from the control node
  run_once: true # Update Jira ticket once per playbook run
  tags:
    - integrations
    - jira

- name: "JIRA: Debug Jira update result"
  ansible.builtin.debug:
    var: jira_update_result
  when: jira_update_result.failed or verbose_debug | default(false) | bool
  delegate_to: localhost
  run_once: true
  tags:
    - integrations
    - jira
    - debug_info

- name: "JIRA: Failed to update Jira ticket {{ jira_ticket_id }}"
  ansible.builtin.fail:
    msg: "Failed to update Jira ticket {{ jira_ticket_id }}. Response: {{ jira_update_result }}"
  when: jira_update_result.failed
  delegate_to: localhost
  run_once: true
  tags:
    - integrations
    - jira

# Notes:
# - 'dns_operations_summary' and 'main_dns_operations_successful' are facts that
#   would need to be set by the main playbook or a post-processing task after DNS operations.
# - Authentication methods for Jira can vary (basic auth, token, OAuth). Adjust as needed.
# - The `community.general.jira` module has many options for interacting with Jira beyond comments.