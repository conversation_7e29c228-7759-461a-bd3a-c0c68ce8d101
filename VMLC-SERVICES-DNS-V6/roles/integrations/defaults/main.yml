---
# Defaults for variables used in the 'integrations' role

# CyberArk Defaults
# enable_cyberark_integration: true # Usually set in group_vars/all
# cyberark_ccp_url: "https://your-ccp-server/AIMWebService/api/Accounts"
# cyberark_app_id: "YourAppID"
# cyberark_dns_server_safe: "DNS_Servers_Safe"
# cyberark_dns_server_object_name_format: "Operating System-UnixSSH-{{ inventory_hostname }}-ansible" # Example format
# cyberark_client_cert: "/path/to/client.pem" # Optional, if using cert auth
# cyberark_client_key: "/path/to/client.key"   # Optional
# cyberark_ca_bundle: "/path/to/ca.pem"        # Optional, for validating CCP server cert

# Jira Defaults
# enable_jira_integration: true # Usually set in group_vars/all
# jira_server_url: "https://your-jira-instance.com"
# jira_api_user: "" # Store in vault
# jira_api_token: "" # Store in vault
# jira_ticket_id: "" # Should be passed as extra_var
# jira_validate_certs: true # Recommended for production

# Bitbucket Defaults
# enable_bitbucket_integration: true # Usually set in group_vars/all
# bitbucket_server_url: "https://your-bitbucket-server.com"
# bitbucket_project_key: "PROJ"
# bitbucket_repo_slug: "dns-automation-log"
# bitbucket_branch: "main"
# bitbucket_user: "" # Store in vault or use SSH key auth
# bitbucket_app_password: "" # Store in vault
# bitbucket_commit_message_prefix: "[Ansible DNS]"

# Default summary messages (can be overridden by facts generated during playbook run)
dns_operations_summary: "No specific DNS operation summary was generated."
affected_records_summary: "No specific list of affected records was generated."
main_dns_operations_successful: true # Assume success unless a task sets it to false

# Verbose debugging for integration tasks
verbose_debug: false