---
# Main tasks for the notifications role

- name: "NOTIFICATIONS: Send email notification (if enabled)"
  ansible.builtin.include_tasks: send_email.yml
  when: enable_email_notifications | default(false) | bool
  tags:
    - notifications
    - email
    - post_ops # Typically run after all other operations

# Add other notification channel tasks here if needed (e.g., <PERSON><PERSON><PERSON>, Teams)
# - name: "NOTIFICATIONS: Send Slack message (if enabled)"
#   ansible.builtin.include_tasks: send_slack.yml
#   when: enable_slack_notifications | default(false) | bool
#   tags:
#     - notifications
#     - slack
#     - post_ops