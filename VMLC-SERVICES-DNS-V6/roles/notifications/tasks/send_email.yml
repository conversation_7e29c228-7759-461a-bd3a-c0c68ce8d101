---
# Tasks for sending email notifications

- name: "EMAIL: Ensure email configuration is defined"
  ansible.builtin.assert:
    that:
      - email_smtp_server is defined and email_smtp_server | length > 0
      - email_from is defined and email_from | length > 0
      - email_to_default is defined and email_to_default | length > 0
    msg: "Email SMTP server, from address, and default to address(es) must be defined."
  tags:
    - notifications
    - email

- name: "EMAIL: Determine recipient list"
  ansible.builtin.set_fact:
    email_recipients: "{{ email_to_custom | default(email_to_dns_server_group | default(email_to_default)) }}"
    # Allows overriding with 'email_to_custom', then 'email_to_dns_server_group' (from dns_servers group_vars), then 'email_to_default'
  delegate_to: localhost
  run_once: true
  tags:
    - notifications
    - email

- name: "EMAIL: Prepare email subject"
  ansible.builtin.set_fact:
    email_final_subject: "{{ email_subject_prefix | default('[Ansible]') }} DNS Operation {{ operation_id | default('N/A') }} - {{ 'Success' if main_dns_operations_successful | default(true) else 'Failed/Partial' }}"
  delegate_to: localhost
  run_once: true
  tags:
    - notifications
    - email

- name: "EMAIL: Render email body from template"
  ansible.builtin.template:
    src: email_report.html.j2 # Path to the Jinja2 template in roles/notifications/templates/
    dest: "{{ playbook_dir }}/.tmp_email_body_{{ operation_id | default('default') }}.html" # Temporary file for the rendered body
    mode: '0600'
  delegate_to: localhost # Template rendering is local
  run_once: true
  vars:
    # Pass necessary variables to the template
    op_id: "{{ operation_id | default('N/A') }}"
    proj_name: "{{ project_name | default('VMLC-SERVICES-DNS-V6') }}"
    op_status: "{{ 'Success' if main_dns_operations_successful | default(true) else 'Failed (or partial success)' }}"
    op_summary: "{{ dns_operations_summary | default('No detailed summary available.') }}"
    affected_records: "{{ affected_records_summary | default('Details on affected records not generated.') }}"
    log_file_path: "{{ current_run_log_file | default('N/A') }}"
    jira_id: "{{ jira_ticket_id | default('N/A') }}"
    bitbucket_url: "{{ bitbucket_commit_url | default('N/A') }}"
  tags:
    - notifications
    - email

- name: "EMAIL: Send notification email"
  community.general.mail:
    host: "{{ email_smtp_server }}"
    port: "{{ email_smtp_port | default(25) }}"
    username: "{{ email_smtp_user | default(omit) }}" # Omit if no auth
    password: "{{ email_smtp_password | default(omit) }}" # Omit if no auth, store in vault
    sender: "{{ email_from }}"
    to: "{{ email_recipients }}" # This should be a list
    subject: "{{ email_final_subject }}"
    body: "{{ lookup('file', playbook_dir + '/.tmp_email_body_' + (operation_id | default('default')) + '.html') }}"
    subtype: html
    charset: utf8
    secure: "{{ 'starttls' if email_use_tls | default(false) else ('ssl' if email_use_ssl | default(false) else 'none') }}"
    timeout: "{{ email_smtp_timeout | default(30) }}" # Timeout in seconds
  delegate_to: localhost # Email sending is from the control node
  run_once: true # Send one email summary per playbook run
  # no_log: true # If username/password are used directly and not omitted
  register: email_send_result
  tags:
    - notifications
    - email

- name: "EMAIL: Debug email send result"
  ansible.builtin.debug:
    var: email_send_result
  when: email_send_result.failed or verbose_debug | default(false) | bool
  delegate_to: localhost
  run_once: true
  tags:
    - notifications
    - email
    - debug_info

- name: "EMAIL: Clean up temporary email body file"
  ansible.builtin.file:
    path: "{{ playbook_dir }}/.tmp_email_body_{{ operation_id | default('default') }}.html"
    state: absent
  delegate_to: localhost
  run_once: true
  tags:
    - notifications
    - email
    - cleanup

# Notes:
# - Ensure SMTP server details and credentials (if any) are correctly configured in group_vars (and vaulted).
# - The facts like 'main_dns_operations_successful', 'dns_operations_summary', etc.,
#   need to be populated by earlier parts of the playbook.