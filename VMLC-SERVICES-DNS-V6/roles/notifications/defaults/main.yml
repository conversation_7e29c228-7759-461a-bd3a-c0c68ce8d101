---
# Defaults for variables used in the 'notifications' role

# Email Notification Defaults (many of these are better set in group_vars/all/main.yml)
# enable_email_notifications: true
# email_smtp_server: "smtp.example.com"
# email_smtp_port: 587 # Or 25, 465
# email_smtp_user: "" # Store in vault
# email_smtp_password: "" # Store in vault
# email_use_tls: true
# email_use_ssl: false
# email_from: "<EMAIL>"
# email_to_default:
#   - "<EMAIL>"
# email_subject_prefix: "[Ansible DNS]"
# email_smtp_timeout: 30 # seconds

# Default status for email subject if not determined by playbook
# default_operation_status_for_email: "Status Unknown"

# Variables that the email template expects (these should be set by the playbook)
# op_id: "N/A"
# proj_name: "N/A"
# op_status: "Unknown"
# op_summary: "No summary available."
# affected_records: "No affected records details available."
# log_file_path: "N/A"
# jira_id: "N/A"
# bitbucket_url: "N/A"

# Slack Notification Defaults (if implemented)
# enable_slack_notifications: false
# slack_api_token: "" # Store in vault
# slack_channel: "#dns-alerts"
# slack_username: "AnsibleDNSBot"
# slack_icon_emoji: ":satellite_antenna:"

# Verbose debugging for notification tasks
verbose_debug: false