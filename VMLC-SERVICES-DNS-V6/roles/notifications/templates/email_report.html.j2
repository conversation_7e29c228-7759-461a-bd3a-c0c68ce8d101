<!DOCTYPE html>
<html>
<head>
    <title>Ansible DNS Operation Report</title>
    <style>
        body { font-family: Arial, sans-serif; margin: 20px; color: #333; }
        .container { border: 1px solid #ddd; padding: 20px; border-radius: 5px; background-color: #f9f9f9; }
        h1 { color: #0056b3; font-size: 24px; }
        h2 { color: #0056b3; font-size: 20px; border-bottom: 1px solid #eee; padding-bottom: 5px; margin-top: 30px; }
        table { width: 100%; border-collapse: collapse; margin-top: 10px; }
        th, td { text-align: left; padding: 8px; border: 1px solid #ddd; }
        th { background-color: #e9ecef; }
        .status-success { color: green; font-weight: bold; }
        .status-failure { color: red; font-weight: bold; }
        .code-block { background-color: #f0f0f0; padding: 10px; border-radius: 4px; font-family: monospace; white-space: pre-wrap; word-wrap: break-word; }
        .footer { margin-top: 30px; font-size: 12px; color: #777; text-align: center; }
    </style>
</head>
<body>
    <div class="container">
        <h1>Ansible DNS Operation Report</h1>

        <h2>Operation Overview</h2>
        <table>
            <tr><th>Project Name</th><td>{{ proj_name | e }}</td></tr>
            <tr><th>Operation ID</th><td>{{ op_id | e }}</td></tr>
            <tr><th>Timestamp</th><td>{{ ansible_date_time.iso8601 }}</td></tr>
            <tr>
                <th>Status</th>
                <td class="{{ 'status-success' if op_status == 'Success' else 'status-failure' }}">
                    {{ op_status | e }}
                </td>
            </tr>
            {% if jira_id and jira_id != 'N/A' %}<tr><th>Jira Ticket</th><td><a href="{{ jira_server_url | e }}/browse/{{ jira_id | e }}">{{ jira_id | e }}</a></td></tr>{% endif %}
            {% if bitbucket_url and bitbucket_url != 'N/A' %}<tr><th>Bitbucket Commit</th><td><a href="{{ bitbucket_url | e }}">View Commit</a></td></tr>{% endif %}
            <tr><th>Log File</th><td>{{ log_file_path | e }}</td></tr>
        </table>

        {% if op_summary and op_summary != 'No detailed summary available.' %}
        <h2>Operation Summary</h2>
        <div class="code-block">{{ op_summary | e }}</div>
        {% endif %}

        {% if affected_records and affected_records != 'Details on affected records not generated.' %}
        <h2>Affected Records/Details</h2>
        <div class="code-block">{{ affected_records | e }}</div>
        {% endif %}

        <p>This is an automated notification from the Ansible DNS management system.</p>
    </div>
    <div class="footer">
        Generated by Ansible on {{ ansible_date_time.date }} at {{ ansible_date_time.time }} {{ ansible_date_time.tz }}
    </div>
</body>
</html>