---
# Defaults for variables used in the 'common' role

# Default operation_id if not set by any other means (e.g., extra_vars or load_config.yml)
# operation_id: "default_op_{{ ansible_date_time.epoch }}" # load_config.yml already handles this better

# Default log directory and format (these are usually better set in group_vars/all/main.yml for project-wide config)
# log_dir: "{{ playbook_dir }}/logs"
# log_file_format: "{{ project_name }}_{{ ansible_date_time.iso8601_basic_short }}_{{ operation_id | default('adhoc') }}.log"
# archive_logs: true
# archive_log_dir: "{{ log_dir }}/archive"
# archive_retention_days: 30

# Default Ansible version check
# ansible_min_version_required: '2.12'

# You can define other defaults specific to the 'common' role's tasks here.
# For example, if 'pre_flight_checks.yml' had a check for a specific tool:
# required_tool_path: "/usr/bin/mytool"

# Default for whether to run certain pre-flight checks
# enable_connectivity_checks: false