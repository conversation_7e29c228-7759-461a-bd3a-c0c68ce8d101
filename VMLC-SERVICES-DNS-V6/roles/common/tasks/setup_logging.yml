---
# Tasks for setting up logging for the current playbook run

- name: Define log file path for the current run
  ansible.builtin.set_fact:
    current_run_log_file: "{{ log_dir }}/{{ log_file_format | format(operation_id=operation_id) }}"
  run_once: true
  delegate_to: localhost
  tags:
    - always
    - setup_logging

- name: Ensure log directory exists (redundant if main.yml already did, but safe)
  ansible.builtin.file:
    path: "{{ log_dir }}"
    state: directory
    mode: '0755'
  run_once: true
  delegate_to: localhost
  tags:
    - always
    - setup_logging

- name: Display log file path being used
  ansible.builtin.debug:
    msg: "Logging playbook output to: {{ current_run_log_file }}"
  run_once: true
  delegate_to: localhost
  tags:
    - always
    - setup_logging
    - debug_info

# Note: Actual logging to the file is typically handled by ansible.cfg settings (log_path)
# or by using a custom callback plugin if more advanced logging is needed.
# This task file primarily sets up variables and ensures directories.

# Example: If you wanted to write an initial message to the log file
# - name: Write initial message to log file
#   ansible.builtin.lineinfile:
#     path: "{{ current_run_log_file }}"
#     line: " playbook_name }} started at {{ ansible_date_time.iso8601 }} with Operation ID: {{ operation_id }}"
#     create: true
#     mode: '0644'
#   run_once: true
#   delegate_to: localhost
#   when: current_run_log_file is defined # Ensure path is set

# Task for archiving old logs (can be part of a separate utility playbook or role too)
- name: Ensure archive log directory exists
  ansible.builtin.file:
    path: "{{ archive_log_dir }}"
    state: directory
    mode: '0755'
  when: archive_logs | bool
  run_once: true
  delegate_to: localhost
  tags:
    - setup_logging
    - archive_logs

- name: Archive old log files
  ansible.builtin.command: "find {{ log_dir }} -maxdepth 1 -name '*.log' -type f -mtime +{{ archive_retention_days }} -exec mv {} {{ archive_log_dir }}/ \\;"
  changed_when: true # Assume find and mv will change something if files are found
  when: archive_logs | bool
  run_once: true
  delegate_to: localhost
  tags:
    - setup_logging
    - archive_logs
  ignore_errors: true # Don't fail playbook if find has issues (e.g., no old logs)