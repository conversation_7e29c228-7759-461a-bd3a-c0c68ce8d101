---
# Common pre-flight checks

- name: Check Ansible version
  ansible.builtin.assert:
    that:
      - ansible_version.full is version('2.12', '>=') # Example: require Ansible 2.12+
    msg: "Ansible version must be 2.12 or newer. Current version: {{ ansible_version.full }}"
  run_once: true
  delegate_to: localhost
  tags:
    - always
    - preflight

- name: Check if required collections are available (basic check)
  ansible.builtin.assert:
    that:
      - lookup('community.general.collection_version', 'community.general') is defined
    msg: "The 'community.general' collection does not seem to be available. Please install it using 'ansible-galaxy collection install -r requirements.yml -p ./collections'."
  run_once: true
  delegate_to: localhost
  tags:
    - always
    - preflight

# - name: Ensure operation_id is defined (if required globally)
#   ansible.builtin.fail:
#     msg: "An 'operation_id' must be provided for this playbook run. Please pass it using -e 'operation_id=your_id'."
#   when: operation_id is not defined or operation_id | length == 0
#   run_once: true
#   delegate_to: localhost
#   tags:
#     - always
#     - preflight

# Add other essential pre-flight checks here, for example:
# - Check for vault password availability
# - Check for connectivity to essential services (e.g., CyberArk, Jira, Bitbucket if always needed)
# - Validate key input variables if they are globally required for all playbooks using this role.