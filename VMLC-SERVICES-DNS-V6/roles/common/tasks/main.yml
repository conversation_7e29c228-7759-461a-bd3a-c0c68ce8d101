---
# Main tasks for the common role

- name: Include common pre-flight checks
  ansible.builtin.include_tasks: pre_flight_checks.yml
  tags:
    - always

- name: Ensure log directory exists
  ansible.builtin.file:
    path: "{{ log_dir }}"
    state: directory
    mode: '0755'
  run_once: true # Ensure this runs only once per playbook
  delegate_to: localhost # Create log directory on the control node
  tags:
    - always
    - setup_logging

- name: Load common configuration variables
  ansible.builtin.include_tasks: load_config.yml
  tags:
    - always
    - load_config

- name: Setup logging for the current run
  ansible.builtin.include_tasks: setup_logging.yml
  tags:
    - always
    - setup_logging

# Add other common tasks here that should run for most playbooks
# For example, generating a unique operation ID if not provided

# - name: Include common post-flight tasks
#   ansible.builtin.include_tasks: post_flight_summary.yml
#   tags:
#     - always