---
# Handlers for the 'common' role

# Example handler: Restart a service (not directly applicable to DNS management usually, but for illustration)
# - name: Restart example service
#   ansible.builtin.service:
#     name: exampleservice
#     state: restarted
#   listen: "Restart exampleservice"

# Handler to display a summary message at the end of a play
- name: Display playbook completion summary
  ansible.builtin.debug:
    msg: |
      Playbook run with Operation ID: {{ operation_id | default('N/A') }} completed.
      Status: {{ ansible_play_batch_hosts_summary['ok'] | default(0) }} ok, {{ ansible_play_batch_hosts_summary['changed'] | default(0) }} changed, {{ ansible_play_batch_hosts_summary['unreachable'] | default(0) }} unreachable, {{ ansible_play_batch_hosts_summary['failed'] | default(0) }} failed.
      Log file: {{ current_run_log_file | default('N/A (check ansible.cfg log_path)') }}
  listen: "Display Playbook Summary" # Tasks can notify this handler

# Handler for cleanup tasks
# - name: Perform cleanup tasks
#   ansible.builtin.debug:
#     msg: "Performing common cleanup tasks..."
#   listen: "Perform Common Cleanup"

# Add other common handlers here. For example:
# - A handler to send a generic error notification if a critical common task fails.
# - A handler to aggregate results from looped tasks.