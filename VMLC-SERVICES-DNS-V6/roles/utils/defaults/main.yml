---
# Defaults for variables used in the 'utils' role

# Example: Default flag for whether to run generic input validation from this role
# utils_validate_input: false

# Example: Default list of temporary file patterns to clean up
# utils_temp_file_patterns:
#   - "*.tmp"
#   - "._*"
#   - "output_*.txt"

# Default path for temporary files if the utils role manages them
# utils_temp_dir: "/tmp/ansible_utils_temp"

# Verbose debugging for utility tasks
verbose_debug: false