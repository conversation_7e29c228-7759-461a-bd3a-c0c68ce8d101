# VMLC-SERVICES-DNS-V6 Ansible Project

This project manages DNS records for VMLC services using Ansible.

## Overview

This Ansible project provides automation for managing DNS A, CNAME, and PTR records across multiple domains. It includes features for:

*   Record addition, removal, update, and verification.
*   Automatic PTR record management.
*   Rollback capabilities.
*   Comprehensive logging.
*   Integrations with Jira and Bitbucket.
*   Email notifications.
*   Secure credential management via CyberArk.

Refer to [`VMLC-SERVICES-DNS-V6_PLAN.md`](VMLC-SERVICES-DNS-V6_PLAN.md:1) for the detailed project plan and architecture.

## Prerequisites

*   Ansible (version X.Y.Z or later)
*   Python (version X.Y.Z or later)
*   Access to DNS servers
*   Credentials for CyberArk, Jira, and Bitbucket (as applicable)
*   Required Ansible collections (see `requirements.yml`)

## Setup

1.  **Clone the repository:**
    ```bash
    git clone <repository_url>
    cd VMLC-SERVICES-DNS-V6
    ```

2.  **Install Ansible collections:**
    ```bash
    ansible-galaxy collection install -r requirements.yml -p ./collections
    ```

3.  **Configure Inventory:**
    Update `inventory/hosts.yml` with your DNS server details.
    Update `inventory/group_vars/` with necessary configurations and vaulted secrets.

4.  **Vault Password:**
    Create a vault password file or supply the password when prompted.
    Example: `echo "your_vault_password" > .vault_pass.txt`
    Then update `ansible.cfg` with `vault_password_file = .vault_pass.txt` or use `--vault-password-file .vault_pass.txt` with `ansible-playbook` commands.

## Usage

### Running Playbooks

*   **Main DNS Operations (Add, Remove, Update, Verify):**
    ```bash
    ansible-playbook playbooks/site.yml -e "var1=value1 var2=value2"
    ```
    (Specific extra variables will be defined for operations)

*   **Rollback Operations:**
    ```bash
    ansible-playbook playbooks/rollback.yml -e "operation_id=your_operation_id_to_rollback"
    ```

### Variables

Key variables are defined in:
*   `inventory/group_vars/all/main.yml`
*   `inventory/group_vars/dns_servers/main.yml`
*   Encrypted variables in corresponding `vault.yml` files.
*   Operation-specific variables passed via `-e` or defined in `vars/operation_config.yml` (example).

## Project Structure

Refer to the `Project Structure` section in [`VMLC-SERVICES-DNS-V6_PLAN.md`](VMLC-SERVICES-DNS-V6_PLAN.md:1).

## Contributing

Please refer to the contribution guidelines (to be created).

## License

Specify your project license here.