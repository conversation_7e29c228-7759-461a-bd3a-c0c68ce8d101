[defaults]
inventory = ./inventory/hosts.yml
roles_path = ./roles
collections_paths = ./collections:~/.ansible/collections:/usr/share/ansible/collections
host_key_checking = False
log_path = ./logs/ansible.log
deprecation_warnings = False
stdout_callback = yaml
# Enable more verbose output for callbacks to see details
bin_ansible_callbacks = True

# Recommended for performance
pipelining = True
forks = 10

[privilege_escalation]
# become = True
# become_method = sudo
# become_user = root
# become_ask_pass = False

[ssh_connection]
# Speed up SSH connections
ssh_args = -o ControlMaster=auto -o ControlPersist=60s -o PreferredAuthentications=publickey,password
retries = 3