---
# General project-wide variables
project_name: "VMLC-SERVICES-DNS-V6"
project_version: "0.1.0"

# Logging configuration
log_dir: "{{ playbook_dir }}/logs" # playbook_dir is an Ansible magic variable
log_file_format: "{{ project_name }}_{{ ansible_date_time.iso8601_basic_short }}_{{ operation_id | default('adhoc') }}.log"
archive_logs: true
archive_log_dir: "{{ log_dir }}/archive"
archive_retention_days: 30 # Days to keep archived logs

# Email notification settings (can be overridden by more specific group_vars)
enable_email_notifications: true
email_subject_prefix: "[{{ project_name }}]"
email_from: "<EMAIL>"
email_to_default:
  - "<EMAIL>"
  # - "<EMAIL>"
email_smtp_server: "smtp.example.com"
email_smtp_port: 587 # Or 465 for SSL, 25 for unencrypted
email_use_tls: true
email_use_ssl: false

# Jira integration settings
enable_jira_integration: true
jira_server_url: "https://jira.example.com"
# jira_project_key: "DNS" # Example project key

# Bitbucket integration settings
enable_bitbucket_integration: true
bitbucket_server_url: "https://bitbucket.example.com"
bitbucket_project_key: "AUTOMATION" # Example project key
bitbucket_repo_slug: "dns-operations-log" # Example repo slug for logging changes
bitbucket_commit_message_prefix: "[{{ project_name }}]"
bitbucket_branch: "main"

# CyberArk Integration Settings
enable_cyberark_integration: true
cyberark_app_id: "Ansible_DNS_Management" # Application ID registered in CyberArk
# cyberark_ccp_url: "https://cyberark-ccp.example.com/AIMWebService/api/Accounts" # Base URL for CCP REST API
# cyberark_client_cert: "{{ playbook_dir }}/certs/cyberark_client.pem" # Path to client certificate for CCP auth
# cyberark_client_key: "{{ playbook_dir }}/certs/cyberark_client.key" # Path to client key for CCP auth
# cyberark_ca_bundle: "{{ playbook_dir }}/certs/cyberark_ca.pem" # Path to CA bundle for CCP SSL verification (optional)

# Default DNS operation parameters
default_ttl: 3600 # Default Time-To-Live for DNS records
default_manage_ptr: true # Whether to manage PTR records by default for A records

# Operation ID (will be generated per run, but can have a default structure)
# operation_id_format: "{{ ansible_date_time.epoch }}" # Example: epoch timestamp

# PowerShell script execution settings
ansible_shell_type: powershell
ansible_become_method: runas # For Windows, if privilege escalation is needed
# ansible_become_user: 'DOMAIN\Administrator' # Example, store actual in vault
# ansible_become_password: '{{ vault_become_password }}' # Example, store actual in vault

# Define common tags for playbooks
common_tags:
  - dns
  - vmlc