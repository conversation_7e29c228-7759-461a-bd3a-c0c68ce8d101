---
# This file should be encrypted using ansible-vault
# Example: ansible-vault encrypt inventory/group_vars/all/vault.yml

# Email credentials (if SMTP server requires authentication)
# email_smtp_user: "smtp_username"
# email_smtp_password: "smtp_password_goes_here"

# Jira credentials (if not using a token or other auth method defined in main.yml)
# jira_api_user: "jira_service_account_username"
# jira_api_token: "jira_api_token_goes_here" # Or password, depending on auth method

# Bitbucket credentials (e.g., an app password or personal access token)
# bitbucket_user: "bitbucket_service_account"
# bitbucket_app_password: "bitbucket_app_password_or_token"

# CyberArk related secrets (if any part of connection itself is secret and not handled by certs)
# For example, if the App ID itself is considered sensitive or if there's a pre-shared key for some API gateway
# cyberark_app_secret_key: "some_secret_for_cyberark_integration_if_needed"

# Windows become password (if using runas with a password)
# vault_become_password: "Windows_RunAs_Password_Goes_Here"

# Add any other project-wide sensitive variables here.
# These are placeholders and should be replaced with actual encrypted values.