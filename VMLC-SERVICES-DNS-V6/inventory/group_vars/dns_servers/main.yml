---
# Variables specific to the 'dns_servers' group

# Ansible connection variables for Windows hosts
ansible_user: '{{ vault_dns_server_user | default("administrator") }}' # User for connecting to DNS servers
ansible_password: '{{ vault_dns_server_password }}' # Password for the user, fetched from vault
ansible_connection: winrm
ansible_winrm_transport: ntlm # or kerberos, credssp
ansible_winrm_server_cert_validation: ignore # Set to 'validate' in production with proper certs

# PowerShell script path on the remote Windows DNS servers
# This is where the manage_dns_records.ps1 script will be copied to or expected to be.
# Using a temporary path is often safer. Ansible's script module handles this well.
# remote_scripts_path: 'C:\Windows\Temp\AnsibleDNSScripts' # Example if copying scripts manually

# DNS specific configurations for these servers
# primary_dns_zone: "example.com" # This might be better defined per operation or in operation_config.yml
# secondary_dns_zone: "secondary.example.com"

# CyberArk Safe and Object names for fetching DNS server credentials
# These tell the CyberArk integration role which credentials to fetch for these specific servers.
# This assumes the CyberArk role/module can use these to query the CCP.
cyberark_dns_server_safe: "DNS_Server_Credentials_Safe"
cyberark_dns_server_object_name_format: "DNS_Server_{{ inventory_hostname }}" # e.g., DNS_Server_dc01.example.com

# Override email recipients for notifications related to these servers, if needed
# email_to_dns_server_group:
#   - "<EMAIL>"

# Specific tags for this group
dns_server_tags:
  - windows
  - dns_server