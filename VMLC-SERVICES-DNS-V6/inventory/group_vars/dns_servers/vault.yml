---
# This file should be encrypted using ansible-vault
# Example: ansible-vault encrypt inventory/group_vars/dns_servers/vault.yml

# Credentials for connecting to DNS servers (to be fetched by CyberArk ideally,
# but placeholders if direct connection details are stored here as a fallback or for non-CyberArk envs)
vault_dns_server_user: "DNSAdminUser" # Example username
vault_dns_server_password: "DNSAdminPasswordGoesHere" # Example password

# If CyberArk integration fetches these, the above might not be needed directly by Ansible
# but could be used by a script that CyberArk itself uses, or as a fallback.

# Any other sensitive variables specific to the dns_servers group.