---
all:
  children:
    dns_servers:
      hosts:
        dns_server_1_fqdn:  # e.g., dc01.example.com
          ansible_host: ************ # IP address or FQDN
          # Add other host-specific variables here if needed
        dns_server_2_fqdn:  # e.g., dc02.example.com
          ansible_host: ************

    # You can define other groups here, e.g., for different environments
    # dev_dns_servers:
    #   hosts:
    #     dev_dns01.example.com:
    # prod_dns_servers:
    #   hosts:
    #     prod_dns01.example.com:

# Example of ungrouped hosts (less common for structured projects)
# ungrouped:
#   hosts:
#     some_other_server:
#       ansible_host: ********