---
- name: Rollback DNS Operations
  hosts: dns_servers # Rollback actions also target DNS servers
  gather_facts: false # Usually not needed for rollback, can speed up
  # connection: winrm


  pre_tasks:
    - name: Include common role for pre-flight checks, logging setup, config loading
      ansible.builtin.include_role:
        name: common
      run_once: true
      delegate_to: localhost
      # Override operation_id for this rollback run, not the one being rolled back
      vars:
        operation_id: "rollback_{{ operation_id_to_rollback }}_{{ ansible_date_time.epoch }}"

    - name: Fetch credentials using CyberArk (if needed for rollback actions)
      ansible.builtin.include_role:
        name: integrations
        tasks_from: cyberark_fetch_creds.yml
      when: enable_cyberark_integration | default(false) | bool
      # This ensures the connection to DNS servers for rollback actions is authenticated

  roles:
    - role: rollback_handler
      # The 'operation_id_to_rollback' var is used by this role.

  post_tasks:
    - name: Set flag indicating rollback operations completed
      ansible.builtin.set_fact:
        main_dns_operations_successful: "{{ not ansible_failed }}" # For consistency in notifications/integrations
        dns_operations_summary: "Rollback attempted for Operation ID: {{ operation_id_to_rollback }}. Status: {{ 'Success' if not ansible_failed else 'Failed' }}."
        affected_records_summary: "Details of rolled-back records depend on the parsed log from {{ operation_id_to_rollback }}."
      run_once: true
      delegate_to: localhost

    # Optionally, perform integrations/notifications for the rollback operation itself
    - name: Perform Integrations for Rollback (Jira, Bitbucket)
      ansible.builtin.include_role:
        name: integrations
      run_once: true
      delegate_to: localhost
      vars:
        # Modify Jira ticket ID or summary for rollback context if needed
        jira_ticket_id: "{{ rollback_jira_ticket_id | default(jira_ticket_id | default(omit)) }}" # Allow specific rollback ticket
        # Bitbucket commit message should reflect it's a rollback
        bitbucket_commit_message_prefix: "[Ansible DNS Rollback]"
      when: main_dns_operations_successful | default(true) | bool # If rollback playbook itself was successful

    - name: Send Notifications for Rollback
      ansible.builtin.include_role:
        name: notifications
      run_once: true
      delegate_to: localhost
      vars:
        # Modify email subject/body for rollback context
        email_subject_prefix: "[{{ project_name }} Rollback]"

    - name: Final rollback playbook summary
      ansible.builtin.debug:
        msg: "DNS Rollback Playbook (rollback.yml) completed for original Operation ID: {{ operation_id_to_rollback }}. New Operation ID for this rollback run: {{ operation_id }}"
      run_once: true
      delegate_to: localhost
      notify: Display Playbook Summary