---
- name: Manage DNS Records
  hosts: dns_servers # Target group defined in inventory/hosts.yml
  gather_facts: true # Gather facts, might be useful for some conditions or logging
  # connection: winrm # Usually defined in inventory or ansible.cfg for the group

  vars_files:
    # - ../vars/operation_config.yml # Example: Load common operation variables from a file
    # Specific operation details (records to change) should be passed as extra_vars (-e)
    # or loaded dynamically based on operation_id or another input.
    # Example extra_vars structure for dns_operations:
    # dns_operations:
    #   - name: "Add Web Servers EMEA"
    #     domains:
    #       - "emea.example.com"
    #     records:
    #       - { action: "add", name: "web01", type: "A", value: "*********", ttl: 300 }
    #       - { action: "add", name: "web02", type: "A", value: "*********", ttl: 300 }
    #   - name: "Update App Server APAC"
    #     domains:
    #       - "apac.example.com"
    #     records:
    #       - { action: "update", name: "app01", type: "A", value: "**********", original_value: "*********" } # original_value for logging/rollback
    # Example extra_vars for dns_records (single domain, simpler):
    # dns_records:
    #   - { action: "add", name: "host1", type: "A", value: "*************", zone: "internal.example.com" }
    #   - { action: "remove", name: "oldhost", type: "CNAME", zone: "internal.example.com" }

  pre_tasks:
    - name: Include common role for pre-flight checks, logging setup, config loading
      ansible.builtin.include_role:
        name: common
      run_once: true # Common setup tasks often only need to run once on localhost
      delegate_to: localhost

    - name: Fetch credentials using CyberArk (runs per host in dns_servers group)
      ansible.builtin.include_role:
        name: integrations
        tasks_from: cyberark_fetch_creds.yml
      when: enable_cyberark_integration | default(false) | bool
      # This sets ansible_user/ansible_password for the connection if successful

  roles:
    # The main role for DNS operations
    - role: dns_management
      # Pass necessary variables to the role if not globally defined or from extra_vars
      # dns_operations: "{{ dns_operations_from_extra_vars }}"
      # dns_records: "{{ dns_records_from_extra_vars }}"
      # dns_zone: "{{ single_dns_zone_from_extra_vars }}" # If operating on a single zone

  post_tasks:
    - name: Set flag indicating DNS operations completed (for conditional integration steps)
      ansible.builtin.set_fact:
        main_dns_operations_successful: "{{ not ansible_failed }}" # Basic success check
        # A more robust check might involve parsing results from dns_management role
        # dns_operations_summary: "Summary of DNS changes..." # Populate this based on results
        # affected_records_summary: "List of affected records..."
      run_once: true
      delegate_to: localhost

    - name: Perform Integrations (Jira, Bitbucket)
      ansible.builtin.include_role:
        name: integrations
        # tasks_from: main.yml # This will run cyberark again, better to be specific
      # Call specific integration tasks directly or have a wrapper in integrations/main.yml
      # that skips cyberark if already done.
      # For now, let's assume integrations/main.yml handles conditions.
      run_once: true # Integrations usually run once
      delegate_to: localhost
      when: main_dns_operations_successful | default(true) | bool # Only run if main ops were okay

    - name: Send Notifications
      ansible.builtin.include_role:
        name: notifications
      run_once: true # Notifications usually run once
      delegate_to: localhost

    - name: Final playbook summary
      ansible.builtin.debug:
        msg: "DNS Management Playbook (site.yml) completed for Operation ID: {{ operation_id }}"
      run_once: true
      delegate_to: localhost
      notify: Display Playbook Summary # Notify handler in common role