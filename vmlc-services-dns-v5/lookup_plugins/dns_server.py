#!/usr/bin/python
# -*- coding: utf-8 -*-

"""
DNS Server Lookup Plugin for DNS Management System
Author: CES Operational Excellence Team
Contributors: <PERSON> (7409)
"""

from __future__ import absolute_import, division, print_function
__metaclass__ = type

DOCUMENTATION = r'''
---
lookup: dns_server
short_description: Get DNS server for a domain
description:
  - Get DNS server for a domain from the configuration
options:
  domain:
    description:
      - Domain to get DNS server for
    type: str
    required: true
  server_type:
    description:
      - Type of DNS server to get
    type: str
    required: false
    default: forward
    choices: [ forward, reverse ]
author:
  - CES Operational Excellence Team
'''

EXAMPLES = r'''
- name: Get forward DNS server for domain
  debug:
    msg: "DNS server for {{ domain }} is {{ lookup('dns_server', domain) }}"

- name: Get reverse DNS server for domain
  debug:
    msg: "PTR server for {{ domain }} is {{ lookup('dns_server', domain, server_type='reverse') }}"
'''

RETURN = r'''
_raw:
  description: DNS server hostname
  type: str
  sample: dns.example.com
'''

from ansible.errors import AnsibleError, AnsibleParserError
from ansible.plugins.lookup import LookupBase
from ansible.utils.display import Display

display = Display()


class LookupModule(LookupBase):
    def run(self, terms, variables=None, **kwargs):
        # Load variables
        self.set_options(var_options=variables, direct=kwargs)
        
        # Get parameters
        domain = terms[0]
        server_type = kwargs.get('server_type', 'forward')
        
        # Validate parameters
        if not domain:
            raise AnsibleError('domain is required')
        
        if server_type not in ['forward', 'reverse']:
            raise AnsibleError('server_type must be forward or reverse')
        
        # Get configuration
        config = variables.get('hostvars', {}).get('localhost', {}).get('domains', {})
        
        # Get DNS server
        if domain not in config:
            raise AnsibleError('Domain {} not found in configuration'.format(domain))
        
        if server_type == 'forward':
            dns_server = config[domain].get('dns_server')
        else:
            dns_server = config[domain].get('ptr_server')
        
        if not dns_server:
            raise AnsibleError('DNS server for domain {} not found in configuration'.format(domain))
        
        return [dns_server]
