# Getting Started with DNS Management System

**Author**: CES Operational Excellence Team
**Contributors**: <PERSON> (7409)

## Table of Contents

1. [Introduction](#1-introduction)
2. [Prerequisites](#2-prerequisites)
3. [Installation](#3-installation)
4. [Configuration](#4-configuration)
5. [Usage](#5-usage)
6. [Job Templates](#6-job-templates)
7. [Troubleshooting](#7-troubleshooting)

## 1. Introduction

The DNS Management System is an Ansible-based solution for managing DNS records across multiple domains. This guide will help you get started with setting up and using the system.

## 2. Prerequisites

- Ansible Automation Platform (AAP) 2.0 or later
- Access to DNS servers with appropriate permissions
- Windows DNS servers (Windows Server 2012 R2 or later)
- Python 3.6 or later
- Required Ansible collections (see collections/requirements.yml)

## 3. Installation

1. Clone the repository:
   ```bash
   git clone https://your-repository/vmlc-services-dns-v5.git
   ```

2. Install required collections:
   ```bash
   ansible-galaxy collection install -r collections/requirements.yml
   ```

3. Configure your inventory to include DNS and ADMT servers.

## 4. Configuration

1. Edit `group_vars/all/config.yml` to configure domains, email settings, and integration options.
2. Edit `group_vars/all/vault.yml` to configure credentials (encrypt this file in production).
3. Configure CyberArk integration if needed.

## 5. Usage

This project is designed to be run exclusively through Ansible Automation Platform (AAP). Users and developers do not have direct access to run ansible-playbook commands on the servers.

### Basic Usage (via AAP Extra Vars)

When launching a job template in AAP, you can provide the following extra variables:

```yaml
# Add an A record
operation: add
record_type: a
hostname: webserver
domain: example.com
ip_address: ********
ticket: INC123456

# Verify a record
operation: verify
record_type: a
hostname: webserver
domain: example.com
ticket: INC123456

# Update a record
operation: update
record_type: a
hostname: webserver
domain: example.com
ip_address: ********
ticket: INC123456

# Remove a record
operation: remove
record_type: a
hostname: webserver
domain: example.com
ticket: INC123456
```

### Multi-Domain Operations (via AAP Extra Vars)

```yaml
# Add records to multiple domains
operation: add
record_type: a
is_multi_domain: true
hostnames: web01,web02
domains: domain1.com,domain2.com
ip_addresses: ********,********
ticket: INC123456
```

### Rollback Operations (via AAP Extra Vars)

```yaml
# Rollback an operation
job_id: 12345
ticket: INC123456
```

## 6. Job Templates

Create the following job templates in AAP:

1. **DNS Record Management**:
   - Playbook: `playbooks/site.yml`
   - Survey: Include fields for operation, record_type, hostname, domain, ip_address, cname_target, ttl, ticket

2. **DNS Record Verification**:
   - Playbook: `playbooks/site.yml`
   - Extra Vars: `operation: verify`
   - Survey: Include fields for record_type, hostname, domain, ticket

3. **DNS Multi-Domain Operations**:
   - Playbook: `playbooks/site.yml`
   - Extra Vars: `is_multi_domain: true`
   - Survey: Include fields for operation, record_type, hostnames, domains, ip_addresses, cname_targets, ttl, ticket

4. **DNS Rollback Operations**:
   - Playbook: `playbooks/rollback.yml`
   - Survey: Include fields for job_id, ticket

## 7. Troubleshooting

- Check logs in the `logs/` directory for detailed operation logs
- Verify DNS server connectivity and permissions
- Ensure credentials are correctly configured
- Check email settings if notifications are not being received
