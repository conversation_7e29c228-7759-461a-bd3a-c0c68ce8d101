# DNS Management System Operations Guide

**Author**: CES Operational Excellence Team  
**Contributors**: <PERSON> (7409)

## Table of Contents

1. [Introduction](#1-introduction)
2. [DNS Record Types](#2-dns-record-types)
3. [Operations](#3-operations)
4. [Multi-Domain Operations](#4-multi-domain-operations)
5. [Rollback Operations](#5-rollback-operations)
6. [Canary Testing](#6-canary-testing)
7. [Troubleshooting](#7-troubleshooting)

## 1. Introduction

This guide provides detailed information on the operations supported by the DNS Management System. It is intended for operators and administrators who need to perform DNS operations using the system.

## 2. DNS Record Types

The DNS Management System supports the following record types:

### 2.1. A Records

A records map a hostname to an IPv4 address. They are the most common type of DNS record.

Example:
```yaml
operation: add
record_type: a
hostname: webserver
domain: example.com
ip_address: ********
```

### 2.2. PTR Records

PTR records map an IP address to a hostname. They are used for reverse DNS lookups.

Example:
```yaml
operation: add
record_type: ptr
ip_address: ********
hostname: webserver
domain: example.com
```

### 2.3. CNAME Records

CNAME records map an alias to another hostname. They are used to create aliases for existing hostnames.

Example:
```yaml
operation: add
record_type: cname
hostname: www
domain: example.com
cname_target: webserver.example.com
```

## 3. Operations

The DNS Management System supports the following operations:

### 3.1. Add

The `add` operation adds a new DNS record. If the record already exists, the operation will fail.

Example:
```yaml
operation: add
record_type: a
hostname: webserver
domain: example.com
ip_address: ********
ttl: 3600
description: Web server for application X
```

### 3.2. Remove

The `remove` operation removes an existing DNS record. If the record does not exist, the operation will fail.

Example:
```yaml
operation: remove
record_type: a
hostname: webserver
domain: example.com
```

### 3.3. Update

The `update` operation updates an existing DNS record. If the record does not exist, the operation will fail.

Example:
```yaml
operation: update
record_type: a
hostname: webserver
domain: example.com
ip_address: ********
ttl: 7200
description: Updated web server for application X
```

### 3.4. Verify

The `verify` operation verifies the existence of a DNS record. It does not modify any records.

Example:
```yaml
operation: verify
record_type: a
hostname: webserver
domain: example.com
```

## 4. Multi-Domain Operations

The DNS Management System supports operations across multiple domains in a single request. This is useful for managing records for the same service across different domains.

Example:
```yaml
operation: add
record_type: a
is_multi_domain: true
hostnames: web01,web02
domains: domain1.com,domain2.com
ip_addresses: ********,********
ttl: 3600
manage_ptr: true
```

Multi-domain operations can be processed sequentially or in parallel using the `async_operations` parameter:

```yaml
operation: add
record_type: a
is_multi_domain: true
hostnames: web01,web02,web03,web04
domains: domain1.com,domain2.com,domain3.com,domain4.com
ip_addresses: ********,********,********,********
async_operations: true
```

## 5. Rollback Operations

The DNS Management System supports rollback operations to revert previous changes. Rollbacks are performed using the job ID of the operation to be rolled back.

Example:
```yaml
job_id: 12345
ticket: INC123456
```

## 6. Canary Testing

The DNS Management System supports canary testing to verify DNS operations without making permanent changes. Canary tests add a record, verify it, and then remove it.

Example:
```yaml
operation: add
record_type: a
hostname: canary-test
domain: example.com
ip_address: ********
ttl: 60
is_canary: true
```

## 7. Troubleshooting

### 7.1. Common Issues

#### 7.1.1. Record Already Exists

If you receive an error that a record already exists, you can:
- Use the `verify` operation to check the current state of the record
- Use the `update` operation instead of `add` to modify the existing record
- Use the `remove` operation to remove the existing record before adding a new one

#### 7.1.2. Record Does Not Exist

If you receive an error that a record does not exist, you can:
- Use the `verify` operation to check if the record exists
- Use the `add` operation instead of `update` or `remove`

#### 7.1.3. Permission Issues

If you receive permission errors, ensure that:
- The credentials used have the necessary permissions on the DNS server
- The DNS server is accessible from the AAP server

### 7.2. Logs

Logs for DNS operations are stored in the following locations:
- AAP job logs
- Local logs in the `logs` directory
- Remote logs on the target server in `C:\OE_AAP_LOGS`
