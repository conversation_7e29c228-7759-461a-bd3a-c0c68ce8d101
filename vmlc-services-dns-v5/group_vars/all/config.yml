---
# Main Configuration File for DNS Management
# Author: CES Operational Excellence Team
# Contributors: <PERSON> (7409)

# DNS Operation Settings
dns_settings:
  default_ttl: 3600
  manage_ptr_by_default: true
  verify_before_remove: true
  force_remove_allowed: true
  max_retries: 3
  retry_delay: 5  # seconds

# Domain Configuration
domains:
  # SingHealth Staging Environment
  ses.shsu.com.sg:
    dns_server: sedcvssys22h1.ses.shsu.com.sg
    ptr_server: shdcvsys22h1.shsu.com.sg
    admin_team: <EMAIL>
    description: "SingHealth Staging Environment"
    
  # SingHealth Production Environment
  shses.shs.com.sg:
    dns_server: sesdcvpsys01.shses.shs.com.sg
    ptr_server: sesdcvpsys11.shs.com.sg
    admin_team: <EMAIL>
    description: "SingHealth Production Environment"
    
  # HealthGroup Production Environment
  healthgrp.com.sg:
    dns_server: hgdcvpsys01.healthgrp.com.sg
    ptr_server: hgdcvpsys01.healthgrp.com.sg
    admin_team: <EMAIL>
    description: "HealthGroup Production Environment"
    
  # HealthGroup Development Environment
  devhealthgrp.com.sg:
    dns_server: hgdcvdsys01.devhealthgrp.com.sg
    ptr_server: hgdcvdsys01.devhealthgrp.com.sg
    admin_team: <EMAIL>
    description: "HealthGroup Development Environment"

# Email Settings
email_settings:
  smtp_servers:
    hdc1: asmtp.hcloud.healthgrp.com.sg
    hdc2: fsmtp.hcloud.healthgrp.com.sg
  default_sender: <EMAIL>
  default_recipient: <EMAIL>
  domain_recipients:
    ses.shsu.com.sg: <EMAIL>
    shses.shs.com.sg: <EMAIL>
  always_bcc: <EMAIL>
  testing_recipient: <EMAIL>

# Logging Settings
logging_settings:
  log_dir: /var/log/dns_management
  log_format: "{timestamp} - {level} - {message}"
  log_level: INFO
  log_retention: 30  # days
  remote_logging:
    enabled: true
    target_server: "{{ target_server | default('localhost') }}"
    target_path: "C:\\OE_AAP_LOGS\\"

# Integration Settings
integration:
  # Jira Integration
  jira:
    enabled: true
    url: "https://your-jira-instance/rest/api/2/issue"
    project_key: "DNS"
    issue_type: "Incident"
  
  # Bitbucket Integration
  bitbucket:
    enabled: true
    url: "https://your-bitbucket-instance/rest/api/1.0"
    repository: "vmlc-services-dns-v5"
  
  # CyberArk Integration
  cyberark:
    enabled: false
    app_id: "DNS_Management"
    url: "https://cyberark.example.com"
    verify_ssl: true
    safes:
      dns_admin:
        name: "DNS_CREDS"
        object: "dns_admin_account"
      admt_admin:
        name: "ADMT_CREDS"
        object: "admt_admin_account"
      jira:
        name: "JIRA_CREDS"
        object: "jira_api_account"
      bitbucket:
        name: "BITBUCKET_CREDS"
        object: "bitbucket_api_account"

# Record Type Definitions
record_types:
  a:
    description: "A record"
    required_params: ["hostname", "domain", "ip_address"]
    optional_params: ["ttl", "description"]
  
  ptr:
    description: "PTR record"
    required_params: ["ip_address"]
    optional_params: ["hostname", "domain", "ttl", "description"]
  
  cname:
    description: "CNAME record"
    required_params: ["hostname", "domain", "cname_target"]
    optional_params: ["ttl", "description"]

# Security Settings
security:
  mask_credentials: true
  use_no_log: true
  clear_after_use: true
