---
# DNS Management Rollback Playbook
# Author: CES Operational Excellence Team
# Contributors: <PERSON> (7409)

- name: DNS Management Rollback
  hosts: localhost
  gather_facts: true

  vars:
    # Required parameters
    job_id: "{{ job_id }}"
    ticket: "{{ ticket }}"
    
    # Control parameters
    is_rollback: true
    testing_mode: "{{ testing_mode | default(false) | bool }}"
    email_report: "{{ email_report | default(true) | bool }}"
    email_logs: "{{ email_logs | default(true) | bool }}"

  pre_tasks:
    # Preparation Phase
    - name: Preparation Phase
      block:
        # Load configuration
        - name: Load configuration
          ansible.builtin.include_role:
            name: utils
            tasks_from: load_config
          
        # Validate rollback parameters
        - name: Validate rollback parameters
          ansible.builtin.include_role:
            name: utils
            tasks_from: validate_rollback
          
        # Setup credentials
        - name: Setup credentials
          ansible.builtin.include_role:
            name: utils
            tasks_from: setup_credentials
          
        # Setup logging
        - name: Setup logging
          ansible.builtin.include_role:
            name: utils
            tasks_from: setup_logging
      tags:
        - always
        - preparation

  tasks:
    # Execution Phase
    - name: Execution Phase
      block:
        # Execute rollback
        - name: Execute rollback
          ansible.builtin.include_role:
            name: dns
            tasks_from: rollback
      rescue:
        # Error handling
        - name: Handle errors
          ansible.builtin.include_role:
            name: utils
            tasks_from: handle_errors
      tags:
        - always
        - execution

  post_tasks:
    # Reporting Phase
    - name: Reporting Phase
      block:
        # Process results
        - name: Process operation results
          ansible.builtin.include_role:
            name: utils
            tasks_from: process_results
        
        # Send notifications
        - name: Send notifications
          ansible.builtin.include_role:
            name: notifications
            tasks_from: main
      tags:
        - always
        - reporting
    
    # Cleanup Phase
    - name: Cleanup Phase
      block:
        # Perform cleanup
        - name: Perform cleanup
          ansible.builtin.include_role:
            name: utils
            tasks_from: cleanup
        
        # Display summary
        - name: Display operation summary
          ansible.builtin.include_role:
            name: utils
            tasks_from: display_summary
      tags:
        - always
        - cleanup
