---
# DNS Management Canary Testing Playbook
# Author: CES Operational Excellence Team
# Contributors: <PERSON> (7409)

- name: DNS Management Canary Testing
  hosts: localhost
  gather_facts: true

  vars:
    # Required parameters
    operation: "{{ operation | default('add') }}"
    record_type: "{{ record_type | default('a') }}"
    hostname: "{{ hostname | default('canary-test') }}"
    domain: "{{ domain | default('ses.shsu.com.sg') }}"
    ip_address: "{{ ip_address | default('********') }}"
    ticket: "{{ ticket | default('CANARY-001') }}"
    
    # Control parameters
    is_canary: true
    ttl: 60
    testing_mode: true
    email_report: "{{ email_report | default(true) | bool }}"

  pre_tasks:
    # Preparation Phase
    - name: Preparation Phase
      block:
        # Load configuration
        - name: Load configuration
          ansible.builtin.include_role:
            name: utils
            tasks_from: load_config
          
        # Validate parameters
        - name: Validate parameters
          ansible.builtin.include_role:
            name: utils
            tasks_from: validate_params
          
        # Setup credentials
        - name: Setup credentials
          ansible.builtin.include_role:
            name: utils
            tasks_from: setup_credentials
          
        # Setup logging
        - name: Setup logging
          ansible.builtin.include_role:
            name: utils
            tasks_from: setup_logging
      tags:
        - always
        - preparation

  tasks:
    # Execution Phase
    - name: Execution Phase
      block:
        # Add canary record
        - name: Add canary record
          ansible.builtin.include_role:
            name: dns
            tasks_from: add
          when: operation == 'add'
        
        # Verify canary record
        - name: Verify canary record
          ansible.builtin.include_role:
            name: dns
            tasks_from: verify
          when: operation == 'verify'
        
        # Wait for TTL to expire
        - name: Wait for TTL to expire
          ansible.builtin.pause:
            seconds: "{{ ttl }}"
          when: operation == 'add'
        
        # Remove canary record
        - name: Remove canary record
          ansible.builtin.include_role:
            name: dns
            tasks_from: remove
          when: operation == 'add'
      rescue:
        # Error handling
        - name: Handle errors
          ansible.builtin.include_role:
            name: utils
            tasks_from: handle_errors
      tags:
        - always
        - execution

  post_tasks:
    # Reporting Phase
    - name: Reporting Phase
      block:
        # Process results
        - name: Process operation results
          ansible.builtin.include_role:
            name: utils
            tasks_from: process_results
        
        # Send notifications
        - name: Send notifications
          ansible.builtin.include_role:
            name: notifications
            tasks_from: main
      tags:
        - always
        - reporting
    
    # Cleanup Phase
    - name: Cleanup Phase
      block:
        # Perform cleanup
        - name: Perform cleanup
          ansible.builtin.include_role:
            name: utils
            tasks_from: cleanup
        
        # Display summary
        - name: Display operation summary
          ansible.builtin.include_role:
            name: utils
            tasks_from: display_summary
      tags:
        - always
        - cleanup
