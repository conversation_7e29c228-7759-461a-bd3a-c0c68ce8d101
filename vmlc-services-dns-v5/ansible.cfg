[defaults]
inventory = inventory
roles_path = roles
collections_paths = collections
host_key_checking = False
retry_files_enabled = False
log_path = logs/ansible.log
callback_whitelist = timer, profile_tasks
stdout_callback = yaml
bin_ansible_callbacks = True
timeout = 30
deprecation_warnings = False

[ssh_connection]
pipelining = True
control_path = /tmp/ansible-ssh-%%h-%%p-%%r

[privilege_escalation]
become = False

[colors]
highlight = white
verbose = blue
warn = bright purple
error = red
debug = dark gray
deprecate = purple
skip = cyan
unreachable = red
ok = green
changed = yellow
diff_add = green
diff_remove = red
diff_lines = cyan
