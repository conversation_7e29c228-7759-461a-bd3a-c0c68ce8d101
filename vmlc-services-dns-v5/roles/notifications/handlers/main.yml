---
# Notifications Role Handlers
# Author: CES Operational Excellence Team
# Contributors: <PERSON> (7409)

- name: Send success notification
  ansible.builtin.debug:
    msg: "Success notification sent for {{ operation }} {{ record_type }} record for {{ hostname }}.{{ domain }}"
  listen: "send_success_notification"

- name: Send failure notification
  ansible.builtin.debug:
    msg: "Failure notification sent for {{ operation }} {{ record_type }} record for {{ hostname }}.{{ domain }}"
  listen: "send_failure_notification"
