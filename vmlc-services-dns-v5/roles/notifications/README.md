# Notifications Role

**Author**: CES Operational Excellence Team  
**Contributors**: <PERSON> (7409)

## Overview

The Notifications role manages external communications for the DNS Management System, including:

- Email notifications
- Jira ticket updates
- Bitbucket repository updates

## Usage

This role is designed to be included in playbooks rather than used directly. See the main playbooks in the `playbooks` directory for examples.

## Tasks

- `send_email.yml` - Send email notifications
- `send_jira_update.yml` - Update Jira tickets
- `send_bitbucket_update.yml` - Update Bitbucket repository

## Variables

See `defaults/main.yml` for default variables and `vars/main.yml` for internal variables.

## Dependencies

- utils role
