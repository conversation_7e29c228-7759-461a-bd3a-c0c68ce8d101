---
# Send Jira Update Tasks
# Author: CES Operational Excellence Team
# Contributors: <PERSON> (7409)

- name: Set Jira data for single domain operation
  ansible.builtin.set_fact:
    jira_data:
      timestamp: "{{ '%Y-%m-%dT%H:%M:%S' | strftime }}"
      operation: "{{ operation }}"
      record_type: "{{ record_type }}"
      hostname: "{{ hostname }}"
      domain: "{{ domain }}"
      success: "{{ dns_operation_result.success | bool }}"
      message: "{{ dns_operation_result.message }}"
      ticket: "{{ ticket }}"
  when: not is_multi_domain
    
- name: Set Jira data for multi-domain operation
  ansible.builtin.set_fact:
    jira_data:
      timestamp: "{{ '%Y-%m-%dT%H:%M:%S' | strftime }}"
      operation: "{{ operation }}"
      record_type: "{{ record_type }}"
      domains: "{{ domains }}"
      success_count: "{{ multi_domain_results | selectattr('success', 'equalto', true) | list | length }}"
      failure_count: "{{ multi_domain_results | selectattr('success', 'equalto', false) | list | length }}"
      total_count: "{{ multi_domain_results | length }}"
      success_rate: "{{ (multi_domain_results | selectattr('success', 'equalto', true) | list | length / multi_domain_results | length * 100) | round(2) }}"
      ticket: "{{ ticket }}"
  when: is_multi_domain
    
- name: Check if Jira ticket exists
  ansible.builtin.uri:
    url: "{{ integration.jira.url }}/{{ ticket }}"
    method: GET
    user: "{{ var_jira_username }}"
    password: "{{ var_jira_password }}"
    force_basic_auth: yes
    status_code: [200, 404]
    return_content: yes
  register: jira_ticket_check
  delegate_to: localhost
  ignore_errors: true
  no_log: "{{ security.use_no_log | default(true) }}"
    
- name: Create Jira ticket if it doesn't exist
  ansible.builtin.uri:
    url: "{{ integration.jira.url }}"
    method: POST
    body_format: json
    body:
      fields:
        project:
          key: "{{ integration.jira.project_key }}"
        summary: "DNS {{ operation | upper }} {{ record_type | upper }} Record: {{ hostname if not is_multi_domain else 'Multiple Domains' }}"
        description: |
          DNS Operation Details:
          - Operation: {{ operation | capitalize }}
          - Record Type: {{ record_type | upper }}
          - {{ 'Hostname: ' + hostname if not is_multi_domain else 'Domains: ' + domains }}
          - {{ 'Domain: ' + domain if not is_multi_domain else 'Total Domains: ' + (multi_domain_results | length | string) }}
          - Ticket: {{ ticket }}
          - Timestamp: {{ jira_data.timestamp }}
          
          {{ 'Result: ' + dns_operation_result.message if not is_multi_domain else 'Results: ' + jira_data.success_count + ' successful, ' + jira_data.failure_count + ' failed' }}
        issuetype:
          name: "{{ integration.jira.issue_type }}"
    user: "{{ var_jira_username }}"
    password: "{{ var_jira_password }}"
    force_basic_auth: yes
    status_code: 201
  register: jira_create_result
  delegate_to: localhost
  when: 
    - jira_ticket_check.status == 404
  ignore_errors: true
  no_log: "{{ security.use_no_log | default(true) }}"
    
- name: Add comment to existing Jira ticket
  ansible.builtin.uri:
    url: "{{ integration.jira.url }}/{{ ticket }}/comment"
    method: POST
    body_format: json
    body:
      body: |
        DNS Operation Update:
        - Operation: {{ operation | capitalize }}
        - Record Type: {{ record_type | upper }}
        - {{ 'Hostname: ' + hostname if not is_multi_domain else 'Domains: ' + domains }}
        - {{ 'Domain: ' + domain if not is_multi_domain else 'Total Domains: ' + (multi_domain_results | length | string) }}
        - Timestamp: {{ jira_data.timestamp }}
        
        {{ 'Result: ' + dns_operation_result.message if not is_multi_domain else 'Results: ' + jira_data.success_count + ' successful, ' + jira_data.failure_count + ' failed' }}
    user: "{{ var_jira_username }}"
    password: "{{ var_jira_password }}"
    force_basic_auth: yes
    status_code: 201
  register: jira_comment_result
  delegate_to: localhost
  when: 
    - jira_ticket_check.status == 200
  ignore_errors: true
  no_log: "{{ security.use_no_log | default(true) }}"
    
- name: Log Jira update
  ansible.builtin.lineinfile:
    path: "{{ playbook_dir }}/../logs/{{ log_file_name }}"
    line: "{{ '%Y-%m-%d %H:%M:%S' | strftime }} - INFO - Updated Jira ticket {{ ticket }}"
  when: 
    - (jira_create_result is defined and jira_create_result.status == 201) or 
      (jira_comment_result is defined and jira_comment_result.status == 201)
