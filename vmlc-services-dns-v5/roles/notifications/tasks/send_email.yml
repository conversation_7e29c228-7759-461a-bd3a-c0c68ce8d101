---
# Send Email Tasks
# Author: CES Operational Excellence Team
# Contributors: <PERSON> (7409)

- name: Determine data center
  ansible.builtin.set_fact:
    data_center: "{{ 'hdc1' if ansible_hostname is search('hdc1') else 'hdc2' }}"

- name: Set SMTP server based on data center
  ansible.builtin.set_fact:
    smtp_server: "{{ email_settings.smtp_servers[data_center] }}"

- name: Determine email recipient
  ansible.builtin.set_fact:
    email_recipient: "{{ email_settings.domain_recipients[domain] if domain in email_settings.domain_recipients else email_settings.default_recipient }}"
  when: not is_multi_domain and not testing_mode | bool

- name: Set testing email recipient
  ansible.builtin.set_fact:
    email_recipient: "{{ email_settings.testing_recipient }}"
  when: testing_mode | bool

- name: Set email subject for single domain operation
  ansible.builtin.set_fact:
    email_subject: "DNS {{ operation | upper }} {{ record_type | upper }} Record: {{ hostname }}.{{ domain }} - {{ 'SUCCESS' if dns_operation_result.success else 'FAILED' }}"
  when: not is_multi_domain

- name: Set email subject for multi-domain operation
  ansible.builtin.set_fact:
    email_subject: "DNS {{ operation | upper }} {{ record_type | upper }} Records: Multiple Domains - {{ 'SUCCESS' if dns_operation_result.success else 'PARTIAL SUCCESS' if multi_domain_results | selectattr('success', 'equalto', true) | list | length > 0 else 'FAILED' }}"
  when: is_multi_domain

- name: Set email subject for rollback operation
  ansible.builtin.set_fact:
    email_subject: "DNS Rollback Operation: {{ rollback_hostname }}.{{ rollback_domain }} - {{ 'SUCCESS' if dns_operation_result.success else 'FAILED' }}"
  when: is_rollback

- name: Create email body
  ansible.builtin.template:
    src: "{{ playbook_dir }}/../templates/email_report.html.j2"
    dest: "{{ playbook_dir }}/../logs/email_{{ timestamp }}.html"
    mode: '0644'
  register: email_body_file

- name: Send email
  ansible.builtin.mail:
    host: "{{ smtp_server }}"
    port: 25
    from: "{{ email_settings.default_sender }}"
    to: "{{ email_recipient }}"
    bcc: "{{ email_settings.always_bcc }}"
    subject: "{{ email_subject }}"
    body: "{{ lookup('file', email_body_file.dest) }}"
    subtype: html
    attach: "{{ playbook_dir }}/../logs/{{ log_file_name }}"
  delegate_to: localhost
  ignore_errors: true

- name: Log email sent
  ansible.builtin.lineinfile:
    path: "{{ playbook_dir }}/../logs/{{ log_file_name }}"
    line: "{{ '%Y-%m-%d %H:%M:%S' | strftime }} - INFO - Email sent to {{ email_recipient }}"
