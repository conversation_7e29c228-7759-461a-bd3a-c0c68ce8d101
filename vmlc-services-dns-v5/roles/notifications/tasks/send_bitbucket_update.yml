---
# Send Bitbucket Update Tasks
# Author: CES Operational Excellence Team
# Contributors: <PERSON> (7409)

- name: Set Bitbucket data for single domain operation
  ansible.builtin.set_fact:
    bitbucket_data:
      timestamp: "{{ '%Y-%m-%dT%H:%M:%S' | strftime }}"
      operation: "{{ operation }}"
      record_type: "{{ record_type }}"
      hostname: "{{ hostname }}"
      domain: "{{ domain }}"
      success: "{{ dns_operation_result.success | bool }}"
      message: "{{ dns_operation_result.message }}"
      ticket: "{{ ticket }}"
  when: not is_multi_domain
    
- name: Set Bitbucket data for multi-domain operation
  ansible.builtin.set_fact:
    bitbucket_data:
      timestamp: "{{ '%Y-%m-%dT%H:%M:%S' | strftime }}"
      operation: "{{ operation }}"
      record_type: "{{ record_type }}"
      domains: "{{ domains }}"
      success_count: "{{ multi_domain_results | selectattr('success', 'equalto', true) | list | length }}"
      failure_count: "{{ multi_domain_results | selectattr('success', 'equalto', false) | list | length }}"
      total_count: "{{ multi_domain_results | length }}"
      success_rate: "{{ (multi_domain_results | selectattr('success', 'equalto', true) | list | length / multi_domain_results | length * 100) | round(2) }}"
      ticket: "{{ ticket }}"
  when: is_multi_domain
    
- name: Create directory for Bitbucket logs
  ansible.builtin.file:
    path: "{{ playbook_dir }}/../logs/bitbucket"
    state: directory
    mode: '0755'

- name: Create DNS operation log file for Bitbucket
  ansible.builtin.template:
    src: "{{ playbook_dir }}/../templates/bitbucket_log.md.j2"
    dest: "{{ playbook_dir }}/../logs/bitbucket/{{ ticket }}_{{ '%Y%m%d%H%M%S' | strftime }}.md"
    mode: '0644'
  register: bitbucket_log_file
    
- name: Log Bitbucket update
  ansible.builtin.lineinfile:
    path: "{{ playbook_dir }}/../logs/{{ log_file_name }}"
    line: "{{ '%Y-%m-%d %H:%M:%S' | strftime }} - INFO - Created Bitbucket log file for ticket {{ ticket }}"
  when: bitbucket_log_file is defined and bitbucket_log_file.changed
