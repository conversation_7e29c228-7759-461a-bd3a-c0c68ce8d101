# Utils Role

**Author**: CES Operational Excellence Team  
**Contributors**: <PERSON> (7409)

## Overview

The Utils role provides utility functions for the DNS Management System, including:

- Configuration loading
- Parameter validation
- Credential management
- Logging setup
- Error handling
- Result processing

## Usage

This role is designed to be included in playbooks rather than used directly. See the main playbooks in the `playbooks` directory for examples.

## Tasks

- `load_config.yml` - Load configuration
- `validate_params.yml` - Validate parameters
- `setup_credentials.yml` - Setup credentials
- `setup_logging.yml` - Setup logging
- `handle_errors.yml` - Handle errors
- `process_results.yml` - Process results
- `cleanup.yml` - Cleanup
- `display_summary.yml` - Display summary

## Variables

See `defaults/main.yml` for default variables and `vars/main.yml` for internal variables.

## Dependencies

None
