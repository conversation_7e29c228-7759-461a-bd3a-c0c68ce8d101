---
# Handle Errors Tasks
# Author: CES Operational Excellence Team
# Contributors: <PERSON> (7409)

- name: Set error details
  ansible.builtin.set_fact:
    error_message: "{{ ansible_failed_result.msg | default('Unknown error') }}"
    error_task: "{{ ansible_failed_task.name | default('Unknown task') }}"
    error_action: "{{ ansible_failed_task.action | default('Unknown action') }}"

- name: Log error
  ansible.builtin.lineinfile:
    path: "{{ playbook_dir }}/../logs/{{ log_file_name }}"
    line: "{{ '%Y-%m-%d %H:%M:%S' | strftime }} - ERROR - {{ error_task }}: {{ error_message }}"

- name: Set operation result for error
  ansible.builtin.set_fact:
    dns_operation_result:
      success: false
      changed: false
      message: "Operation failed: {{ error_message }}"
      error:
        task: "{{ error_task }}"
        action: "{{ error_action }}"
        message: "{{ error_message }}"

- name: Display error
  ansible.builtin.debug:
    msg: "Error in {{ error_task }}: {{ error_message }}"
