---
# Display Summary Tasks
# Author: CES Operational Excellence Team
# Contributors: <PERSON> (7409)

- name: Display single domain operation summary
  ansible.builtin.debug:
    msg: |
      DNS Operation Summary:
      - Operation: {{ operation | upper }}
      - Record Type: {{ record_type | upper }}
      - Hostname: {{ hostname }}
      - Domain: {{ domain }}
      - Success: {{ dns_operation_result.success }}
      - Message: {{ dns_operation_result.message }}
  when: not is_multi_domain

- name: Display multi-domain operation summary
  ansible.builtin.debug:
    msg: |
      DNS Multi-Domain Operation Summary:
      - Operation: {{ operation | upper }}
      - Record Type: {{ record_type | upper }}
      - Domains: {{ domains }}
      - Success: {{ dns_operation_result.success }}
      - Message: {{ dns_operation_result.message }}
      - Successful: {{ multi_domain_results | selectattr('success', 'equalto', true) | list | length }}
      - Failed: {{ multi_domain_results | selectattr('success', 'equalto', false) | list | length }}
  when: is_multi_domain

- name: Display rollback operation summary
  ansible.builtin.debug:
    msg: |
      DNS Rollback Operation Summary:
      - Original Operation: {{ dns_operation_result.original_operation | upper }}
      - Rollback Operation: {{ dns_operation_result.rollback_operation | upper }}
      - Record Type: {{ rollback_record_type | upper }}
      - Hostname: {{ rollback_hostname }}
      - Domain: {{ rollback_domain }}
      - Success: {{ dns_operation_result.success }}
      - Message: {{ dns_operation_result.message }}
  when: is_rollback
