---
# Load Configuration Tasks
# Author: CES Operational Excellence Team
# Contributors: <PERSON> (7409)

- name: Load main configuration
  ansible.builtin.include_vars:
    file: "{{ playbook_dir }}/../group_vars/all/config.yml"

- name: Set log level
  ansible.builtin.set_fact:
    log_level: "{{ logging_settings.log_level }}"

- name: Display loaded configuration
  ansible.builtin.debug:
    msg: "Configuration loaded successfully"
    verbosity: 1
