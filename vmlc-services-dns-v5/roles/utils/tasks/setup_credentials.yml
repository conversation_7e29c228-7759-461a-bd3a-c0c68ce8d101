---
# Setup Credentials Tasks
# Author: CES Operational Excellence Team
# Contributors: <PERSON> (7409)

# Check if CyberArk credentials are available
- name: Check if CyberArk credentials are available
  ansible.builtin.set_fact:
    use_cyberark_creds: "{{ dns_credentials is defined and dns_credentials | length > 0 and integration.cyberark.enabled | default(false) | bool }}"
  no_log: "{{ security.use_no_log | default(true) }}"

# Load vault credentials (fallback if CyberArk is not used)
- name: Load credentials from vault
  ansible.builtin.include_vars:
    file: "{{ playbook_dir }}/../group_vars/all/vault.yml"
    name: vault_config
  when: not use_cyberark_creds | bool

# Set credentials based on source (CyberArk or Vault)
- name: Set domain credentials for single domain operation from CyberArk
  ansible.builtin.set_fact:
    domain_credentials:
      username: "{{ dns_credentials.UserName }}"
      password: "{{ dns_credentials.Content }}"
  when: 
    - not is_multi_domain
    - use_cyberark_creds | bool
  no_log: "{{ security.use_no_log | default(true) }}"

- name: Set domain credentials for single domain operation from Vault
  ansible.builtin.set_fact:
    domain_credentials: "{{ vault_config.credentials.domains[domain] }}"
  when: 
    - not is_multi_domain
    - not use_cyberark_creds | bool
    - domain is defined and domain | length > 0
  no_log: "{{ security.use_no_log | default(true) }}"

- name: Set domain credentials for multi-domain operation from CyberArk
  ansible.builtin.set_fact:
    multi_domain_credentials: 
      username: "{{ dns_credentials.UserName }}"
      password: "{{ dns_credentials.Content }}"
  when: 
    - is_multi_domain
    - use_cyberark_creds | bool
  no_log: "{{ security.use_no_log | default(true) }}"

- name: Set domain credentials for multi-domain operation from Vault
  ansible.builtin.set_fact:
    multi_domain_credentials: "{{ vault_config.credentials.domains }}"
  when: 
    - is_multi_domain
    - not use_cyberark_creds | bool
  no_log: "{{ security.use_no_log | default(true) }}"

# Set Jira credentials
- name: Set Jira credentials from CyberArk
  ansible.builtin.set_fact:
    var_jira_username: "{{ jira_credentials.UserName | default('') }}"
    var_jira_password: "{{ jira_credentials.Content | default('') }}"
  when: jira_credentials is defined and jira_credentials | length > 0
  no_log: "{{ security.use_no_log | default(true) }}"

- name: Set Jira credentials from Vault
  ansible.builtin.set_fact:
    var_jira_username: "{{ vault_config.credentials.jira.username | default('') }}"
    var_jira_password: "{{ vault_config.credentials.jira.password | default('') }}"
  when: (jira_credentials is not defined or jira_credentials | length == 0) and not use_cyberark_creds | bool
  no_log: "{{ security.use_no_log | default(true) }}"

# Set Bitbucket credentials
- name: Set Bitbucket credentials from CyberArk
  ansible.builtin.set_fact:
    var_bitbucket_username: "{{ bitbucket_credentials.UserName | default('') }}"
    var_bitbucket_password: "{{ bitbucket_credentials.Content | default('') }}"
  when: bitbucket_credentials is defined and bitbucket_credentials | length > 0
  no_log: "{{ security.use_no_log | default(true) }}"

- name: Set Bitbucket credentials from Vault
  ansible.builtin.set_fact:
    var_bitbucket_username: "{{ vault_config.credentials.bitbucket.username | default('') }}"
    var_bitbucket_password: "{{ vault_config.credentials.bitbucket.password | default('') }}"
  when: (bitbucket_credentials is not defined or bitbucket_credentials | length == 0) and not use_cyberark_creds | bool
  no_log: "{{ security.use_no_log | default(true) }}"

- name: Display credential setup
  ansible.builtin.debug:
    msg: "Credentials set up for {{ 'multiple domains' if is_multi_domain else domain }} using {{ 'CyberArk' if use_cyberark_creds else 'Vault' }}"
    verbosity: 1
