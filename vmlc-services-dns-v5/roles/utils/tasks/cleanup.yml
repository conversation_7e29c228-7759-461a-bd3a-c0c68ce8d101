---
# Cleanup Tasks
# Author: CES Operational Excellence Team
# Contributors: <PERSON> (7409)

- name: Clear sensitive variables
  ansible.builtin.set_fact:
    domain_credentials: null
    multi_domain_credentials: null
    var_jira_username: null
    var_jira_password: null
    var_bitbucket_username: null
    var_bitbucket_password: null
    dns_credentials: null
    jira_credentials: null
    bitbucket_credentials: null
  when: security.clear_after_use | bool
  no_log: "{{ security.use_no_log | default(true) }}"

- name: Log operation end
  ansible.builtin.lineinfile:
    path: "{{ playbook_dir }}/../logs/{{ log_file_name }}"
    line: "{{ '%Y-%m-%d %H:%M:%S' | strftime }} - INFO - DNS operation completed"
