---
# Load Operation Log Tasks
# Author: CES Operational Excellence Team
# Contributors: <PERSON> (7409)

- name: Find operation log files
  ansible.builtin.find:
    paths: "{{ playbook_dir }}/../logs"
    patterns: "operation_*.json"
  register: log_files

- name: Load operation logs
  ansible.builtin.slurp:
    src: "{{ item.path }}"
  register: loaded_logs
  loop: "{{ log_files.files }}"
  when: log_files.files | length > 0

- name: Parse operation logs
  ansible.builtin.set_fact:
    parsed_logs: "{{ loaded_logs.results | map(attribute='content') | map('b64decode') | map('from_json') | list }}"
  when: loaded_logs is defined and loaded_logs.results | length > 0

- name: Find operation log by job ID
  ansible.builtin.set_fact:
    operation_log: "{{ parsed_logs | selectattr('job_id', 'equalto', job_id) | first }}"
  when: parsed_logs is defined and parsed_logs | length > 0
  ignore_errors: true

- name: <PERSON><PERSON><PERSON> found operation log
  ansible.builtin.debug:
    msg: "Found operation log for job ID {{ job_id }}: {{ operation_log }}"
    verbosity: 1
  when: operation_log is defined
