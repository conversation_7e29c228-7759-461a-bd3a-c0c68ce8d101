---
# Validate Parameters Tasks
# Author: CES Operational Excellence Team
# Contributors: <PERSON> (7409)

- name: Validate operation
  ansible.builtin.fail:
    msg: "Invalid operation: {{ operation }}. Supported operations are: add, remove, update, verify"
  when: operation not in ['add', 'remove', 'update', 'verify'] and not is_rollback

- name: Validate record type
  ansible.builtin.fail:
    msg: "Invalid record type: {{ record_type }}. Supported record types are: a, ptr, cname"
  when: record_type not in ['a', 'ptr', 'cname'] and not is_rollback

- name: Validate required parameters for single domain A record
  ansible.builtin.fail:
    msg: "Missing required parameters for A record: hostname, domain, ip_address"
  when: 
    - not is_multi_domain
    - record_type == 'a'
    - operation in ['add', 'update']
    - (hostname | length == 0 or domain | length == 0 or ip_address | length == 0)
    - not is_rollback

- name: Validate required parameters for single domain CNAME record
  ansible.builtin.fail:
    msg: "Missing required parameters for CNAME record: hostname, domain, cname_target"
  when: 
    - not is_multi_domain
    - record_type == 'cname'
    - operation in ['add', 'update']
    - (hostname | length == 0 or domain | length == 0 or cname_target | length == 0)
    - not is_rollback

- name: Validate required parameters for single domain PTR record
  ansible.builtin.fail:
    msg: "Missing required parameter for PTR record: ip_address"
  when: 
    - not is_multi_domain
    - record_type == 'ptr'
    - operation in ['add', 'update', 'remove', 'verify']
    - ip_address | length == 0
    - not is_rollback

- name: Validate required parameters for multi-domain operation
  ansible.builtin.fail:
    msg: "Missing required parameters for multi-domain operation: hostnames, domains"
  when: 
    - is_multi_domain
    - (hostnames | length == 0 or domains | length == 0)
    - not is_rollback

- name: Validate domain exists in configuration
  ansible.builtin.fail:
    msg: "Domain {{ domain }} is not configured in the system"
  when: 
    - not is_multi_domain
    - domain | length > 0
    - domain not in domains
    - not is_rollback

- name: Validate ticket number
  ansible.builtin.fail:
    msg: "Ticket number is required"
  when: ticket | length == 0 and not is_rollback

- name: Display validation success
  ansible.builtin.debug:
    msg: "Parameter validation successful"
    verbosity: 1
