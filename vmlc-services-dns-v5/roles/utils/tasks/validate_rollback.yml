---
# Validate Rollback Parameters Tasks
# Author: CES Operational Excellence Team
# Contributors: <PERSON> (7409)

- name: Validate job ID
  ansible.builtin.fail:
    msg: "Job ID is required for rollback operation"
  when: job_id | length == 0

- name: Validate ticket number
  ansible.builtin.fail:
    msg: "Ticket number is required for rollback operation"
  when: ticket | length == 0

- name: Display validation success
  ansible.builtin.debug:
    msg: "Rollback parameter validation successful"
    verbosity: 1
