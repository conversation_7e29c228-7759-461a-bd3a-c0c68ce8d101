---
# Process Results Tasks
# Author: CES Operational Excellence Team
# Contributors: <PERSON> (7409)

- name: Log operation result
  ansible.builtin.lineinfile:
    path: "{{ playbook_dir }}/../logs/{{ log_file_name }}"
    line: "{{ '%Y-%m-%d %H:%M:%S' | strftime }} - INFO - Operation result: {{ dns_operation_result.message }}"

- name: Save operation log for rollback
  ansible.builtin.copy:
    content: "{{ dns_operation_result | to_json }}"
    dest: "{{ playbook_dir }}/../logs/operation_{{ ansible_date_time.date }}_{{ ansible_date_time.time | replace(':', '') }}_{{ ansible_play_batch[0] }}.json"
  when: dns_operation_result.success | bool and not is_rollback

- name: Upload log to remote server if enabled
  ansible.windows.win_copy:
    src: "{{ playbook_dir }}/../logs/{{ log_file_name }}"
    dest: "{{ target_path }}{{ log_file_name }}"
  delegate_to: "{{ target_server }}"
  when: remote_logging_enabled | bool
  ignore_errors: true
