---
# Setup Logging Tasks
# Author: CES Operational Excellence Team
# Contributors: <PERSON> (7409)

- name: Create timestamp
  ansible.builtin.set_fact:
    timestamp: "{{ '%Y%m%d%H%M%S' | strftime }}"

- name: Set log file name
  ansible.builtin.set_fact:
    log_file_name: "{{ timestamp }}_{{ ticket }}_DNS_{{ operation | upper }}_{{ record_type | upper }}.log"
    ansible_log_path: "/tmp/dns_{{ timestamp }}_{{ ticket }}.log"

- name: Create local log directory
  ansible.builtin.file:
    path: "{{ playbook_dir }}/../logs"
    state: directory
    mode: '0755'

- name: Create local log file
  ansible.builtin.file:
    path: "{{ playbook_dir }}/../logs/{{ log_file_name }}"
    state: touch
    mode: '0644'

- name: Log operation start
  ansible.builtin.lineinfile:
    path: "{{ playbook_dir }}/../logs/{{ log_file_name }}"
    line: "{{ '%Y-%m-%d %H:%M:%S' | strftime }} - INFO - Starting DNS {{ operation | upper }} operation for {{ record_type | upper }} record {{ hostname if not is_multi_domain else 'multiple domains' }}"

- name: Set remote logging flag
  ansible.builtin.set_fact:
    remote_logging_enabled: "{{ logging_settings.remote_logging.enabled }}"

- name: Setup remote logging if enabled
  block:
    - name: Determine target server
      ansible.builtin.set_fact:
        target_server: "{{ logging_settings.remote_logging.target_server }}"
        target_path: "{{ logging_settings.remote_logging.target_path }}"
    
    - name: Create remote log directory
      ansible.windows.win_file:
        path: "{{ target_path }}"
        state: directory
      delegate_to: "{{ target_server }}"
      ignore_errors: true
      when: remote_logging_enabled | bool
  when: remote_logging_enabled | bool
