---
# Utils Role Handlers
# Author: CES Operational Excellence Team
# Contributors: <PERSON> (7409)

- name: Rotate logs
  ansible.builtin.command: "python3 {{ playbook_dir }}/../scripts/cleanup_logs.py --days {{ utils_log_retention }}"
  listen: "rotate_logs"

- name: Clear sensitive data
  ansible.builtin.set_fact:
    domain_credentials: null
    multi_domain_credentials: null
    var_jira_username: null
    var_jira_password: null
    var_bitbucket_username: null
    var_bitbucket_password: null
    dns_credentials: null
    jira_credentials: null
    bitbucket_credentials: null
  listen: "clear_sensitive_data"
  no_log: true
