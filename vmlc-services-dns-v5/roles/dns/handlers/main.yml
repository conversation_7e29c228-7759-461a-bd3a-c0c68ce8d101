---
# DNS Role Handlers
# Author: CES Operational Excellence Team
# Contributors: <PERSON> (7409)

- name: Log DNS operation
  ansible.builtin.debug:
    msg: "DNS operation completed: {{ operation }} {{ record_type }} record for {{ hostname }}.{{ domain }}"
  listen: "dns_operation_completed"

- name: Clear DNS cache
  ansible.windows.win_shell: Clear-DnsClientCache
  delegate_to: "{{ dns_server }}"
  listen: "clear_dns_cache"
