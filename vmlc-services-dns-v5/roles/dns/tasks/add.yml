---
# DNS Add Record Tasks
# Author: CES Operational Excellence Team
# Contributors: <PERSON> (7409)

- name: Set default TTL if not provided
  ansible.builtin.set_fact:
    ttl: "{{ dns_settings.default_ttl }}"
  when: ttl == 0

- name: Verify if record already exists
  ansible.builtin.include_tasks: verify.yml
  vars:
    verify_only: true

- name: Fail if record already exists
  ansible.builtin.fail:
    msg: "Record {{ hostname }}.{{ domain }} already exists with IP {{ existing_record.ip_address }}"
  when:
    - record_exists | bool
    - not is_multi_domain
    - record_type == 'a'

- name: Fail if CNAME record already exists
  ansible.builtin.fail:
    msg: "CNAME record {{ hostname }}.{{ domain }} already exists with target {{ existing_record.cname_target }}"
  when:
    - record_exists | bool
    - not is_multi_domain
    - record_type == 'cname'

- name: Add DNS record
  ansible.builtin.include_tasks: "add_{{ record_type }}.yml"
  when: not is_multi_domain

- name: Set operation result
  ansible.builtin.set_fact:
    dns_operation_result:
      success: true
      changed: true
      message: "Successfully added {{ record_type | upper }} record {{ hostname }}.{{ domain }}"
      record:
        hostname: "{{ hostname }}"
        domain: "{{ domain }}"
        record_type: "{{ record_type }}"
        ip_address: "{{ ip_address | default('') }}"
        cname_target: "{{ cname_target | default('') }}"
        ttl: "{{ ttl }}"
    current_operation_index: "{{ current_operation_index | default(0) }}"
  when: not is_multi_domain or is_async_operation | default(false) | bool
