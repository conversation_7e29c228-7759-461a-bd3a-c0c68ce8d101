---
# Update A Record Tasks
# Author: CES Operational Excellence Team
# Contributors: <PERSON> (7409)

- name: Get DNS server for domain
  ansible.builtin.set_fact:
    dns_server: "{{ domains[domain].dns_server }}"
    ptr_server: "{{ domains[domain].ptr_server }}"

- name: Update A record
  ansible.windows.win_powershell:
    script: |
      param(
        [string]$Hostname,
        [string]$Domain,
        [string]$IPAddress,
        [int]$TTL,
        [string]$Description
      )
      
      $ErrorActionPreference = "Stop"
      
      try {
        $fqdn = "$Hostname.$Domain"
        $dnsServer = [System.Net.Dns]::GetHostEntry($env:COMPUTERNAME).HostName
        
        # Get existing record
        $oldRecord = Get-DnsServerResourceRecord -Name $Hostname -ZoneName $Domain -RRType A -ComputerName $dnsServer
        $oldIP = $oldRecord.RecordData.IPv4Address.ToString()
        
        # Create new record
        $newRecord = $oldRecord.Clone()
        $newRecord.TimeToLive = [System.TimeSpan]::FromSeconds($TTL)
        $newRecord.RecordData.IPv4Address = [System.Net.IPAddress]::Parse($IPAddress)
        
        if ($Description) {
          $newRecord.Description = $Description
        }
        
        # Update record
        Set-DnsServerResourceRecord -OldInputObject $oldRecord -NewInputObject $newRecord -ZoneName $Domain -ComputerName $dnsServer
        
        return @{
          Success = $true
          Message = "Successfully updated A record $fqdn from $oldIP to $IPAddress"
          OldIPAddress = $oldIP
        } | ConvertTo-Json
      }
      catch {
        return @{
          Success = $false
          Message = "Failed to update A record: $_"
        } | ConvertTo-Json
      }
    parameters:
      Hostname: "{{ hostname }}"
      Domain: "{{ domain }}"
      IPAddress: "{{ ip_address }}"
      TTL: "{{ ttl }}"
      Description: "{{ description }}"
  register: update_a_result
  delegate_to: "{{ dns_server }}"

- name: Parse A record update result
  ansible.builtin.set_fact:
    a_record_update_result: "{{ update_a_result.output | from_json }}"

- name: Update PTR record if IP address changed
  ansible.builtin.include_tasks: update_ptr.yml
  vars:
    old_ip_address: "{{ a_record_update_result.OldIPAddress }}"
  when: 
    - manage_ptr | bool
    - a_record_update_result.Success | bool
    - a_record_update_result.OldIPAddress != ip_address

- name: Fail if A record update failed
  ansible.builtin.fail:
    msg: "Failed to update A record: {{ a_record_update_result.Message }}"
  when: not a_record_update_result.Success | bool
