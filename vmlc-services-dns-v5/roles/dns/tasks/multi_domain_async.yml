---
# Multi-Domain Async Operation Tasks
# Author: CES Operational Excellence Team
# Contributors: <PERSON> (7409)

- name: Launch async operations
  ansible.builtin.include_tasks: multi_domain_async_launch.yml
  vars:
    current_index: "{{ item }}"
    current_hostname: "{{ hostname_list[item] }}"
    current_domain: "{{ domain_list[item] }}"
    current_ip_address: "{{ ip_address_list[item] if ip_address_list else '' }}"
    current_cname_target: "{{ cname_target_list[item] if cname_target_list else '' }}"
  loop: "{{ range(0, hostname_list | length) | list }}"

- name: Wait for all async operations to complete
  ansible.builtin.async_status:
    jid: "{{ item.ansible_job_id }}"
  register: async_results
  until: async_results.finished
  retries: 30
  delay: 10
  loop: "{{ async_jobs }}"

- name: Process async results
  ansible.builtin.set_fact:
    multi_domain_results: "{{ multi_domain_results + [item.dns_operation_result | combine({'index': item.current_operation_index | default(loop.index)})] }}"
  loop: "{{ async_results.results | map(attribute='ansible_result') | list }}"
  when: item is defined and item.dns_operation_result is defined

- name: Handle failed async operations
  ansible.builtin.set_fact:
    multi_domain_results: "{{ multi_domain_results + [{'success': false, 'changed': false, 'message': 'Async operation failed or timed out', 'index': item.current_operation_index | default(loop.index), 'record': {'hostname': hostname_list[item.current_operation_index | default(loop.index)], 'domain': domain_list[item.current_operation_index | default(loop.index)], 'record_type': record_type}}] }}"
  loop: "{{ async_results.results | map(attribute='ansible_result') | list }}"
  when: item is not defined or item.dns_operation_result is not defined
