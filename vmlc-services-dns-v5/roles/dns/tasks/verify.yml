---
# Verify DNS Record Tasks
# Author: CES Operational Excellence Team
# Contributors: <PERSON> (7409)

- name: Get DNS server for domain
  ansible.builtin.set_fact:
    dns_server: "{{ domains[domain].dns_server }}"
  when: record_type != 'ptr'

- name: Get PTR server for domain
  ansible.builtin.set_fact:
    dns_server: "{{ domains[domain].ptr_server }}"
  when: record_type == 'ptr'

- name: Verify A or CNAME record
  ansible.windows.win_powershell:
    script: |
      param(
        [string]$Hostname,
        [string]$Domain,
        [string]$RecordType
      )

      $ErrorActionPreference = "Stop"

      try {
        $fqdn = "$Hostname.$Domain"
        $dnsServer = [System.Net.Dns]::GetHostEntry($env:COMPUTERNAME).HostName

        # Get record
        $record = Get-DnsServerResourceRecord -Name $Hostname -ZoneName $Domain -RRType $RecordType -ComputerName $dnsServer -ErrorAction SilentlyContinue

        if ($record) {
          $result = @{
            Exists = $true
            RecordType = $RecordType
            Hostname = $Hostname
            Domain = $Domain
            TTL = $record.TimeToLive.TotalSeconds
            Description = $record.Description
          }

          # Add record-type specific data
          if ($RecordType -eq "A") {
            $result.IPAddress = $record.RecordData.IPv4Address.ToString()
          }
          elseif ($RecordType -eq "CNAME") {
            $result.CnameTarget = $record.RecordData.HostNameAlias
          }

          return $result | ConvertTo-Json
        }
        else {
          return @{
            Exists = $false
            RecordType = $RecordType
            Hostname = $Hostname
            Domain = $Domain
            Message = "Record $fqdn of type $RecordType does not exist"
          } | ConvertTo-Json
        }
      }
      catch {
        return @{
          Exists = $false
          RecordType = $RecordType
          Hostname = $Hostname
          Domain = $Domain
          Message = "Error verifying record: $_"
        } | ConvertTo-Json
      }
    parameters:
      Hostname: "{{ hostname }}"
      Domain: "{{ domain }}"
      RecordType: "{{ record_type | upper }}"
  register: verify_result
  delegate_to: "{{ dns_server }}"
  when: record_type != 'ptr'

- name: Verify PTR record
  ansible.windows.win_powershell:
    script: |
      param(
        [string]$IPAddress
      )

      $ErrorActionPreference = "Stop"

      try {
        $dnsServer = [System.Net.Dns]::GetHostEntry($env:COMPUTERNAME).HostName

        # Convert IP to reverse lookup format
        $ipParts = $IPAddress.Split('.')
        $ptrZone = "$($ipParts[2]).$($ipParts[1]).$($ipParts[0]).in-addr.arpa"
        $ptrName = $ipParts[3]

        # Get PTR record
        $record = Get-DnsServerResourceRecord -Name $ptrName -ZoneName $ptrZone -RRType Ptr -ComputerName $dnsServer -ErrorAction SilentlyContinue

        if ($record) {
          return @{
            Exists = $true
            RecordType = "PTR"
            IPAddress = $IPAddress
            PtrTarget = $record.RecordData.PtrDomainName
            TTL = $record.TimeToLive.TotalSeconds
            Description = $record.Description
          } | ConvertTo-Json
        }
        else {
          return @{
            Exists = $false
            RecordType = "PTR"
            IPAddress = $IPAddress
            Message = "PTR record for $IPAddress does not exist"
          } | ConvertTo-Json
        }
      }
      catch {
        return @{
          Exists = $false
          RecordType = "PTR"
          IPAddress = $IPAddress
          Message = "Error verifying PTR record: $_"
        } | ConvertTo-Json
      }
    parameters:
      IPAddress: "{{ ip_address }}"
  register: verify_result
  delegate_to: "{{ dns_server }}"
  when: record_type == 'ptr'

- name: Parse verification result
  ansible.builtin.set_fact:
    verification_result: "{{ verify_result.output | from_json }}"

- name: Set record exists flag
  ansible.builtin.set_fact:
    record_exists: "{{ verification_result.Exists }}"
    existing_record: "{{ verification_result }}"

- name: Set operation result for verify operation
  ansible.builtin.set_fact:
    dns_operation_result:
      success: true
      changed: false
      message: "{{ 'Record exists: ' + verification_result.Hostname + '.' + verification_result.Domain if verification_result.Exists else verification_result.Message }}"
      record_exists: "{{ verification_result.Exists }}"
      record: "{{ verification_result }}"
    current_operation_index: "{{ current_operation_index | default(0) }}"
  when:
    - operation == 'verify'
    - (not is_multi_domain or is_async_operation | default(false) | bool)
    - verify_only is not defined or not verify_only | bool
