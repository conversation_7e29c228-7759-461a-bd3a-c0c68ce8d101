---
# Remove A Record Tasks
# Author: CES Operational Excellence Team
# Contributors: <PERSON> (7409)

- name: Get DNS server for domain
  ansible.builtin.set_fact:
    dns_server: "{{ domains[domain].dns_server }}"
    ptr_server: "{{ domains[domain].ptr_server }}"

- name: Check connectivity before removing if required
  ansible.builtin.win_ping:
  delegate_to: "{{ existing_record.IPAddress }}"
  register: connectivity_check
  ignore_errors: true
  when: 
    - dns_settings.verify_before_remove | bool
    - not force_remove | bool

- name: Fail if host is still reachable
  ansible.builtin.fail:
    msg: "Cannot remove A record for {{ hostname }}.{{ domain }} because the host is still reachable at {{ existing_record.IPAddress }}. Use force_remove=true to override."
  when: 
    - dns_settings.verify_before_remove | bool
    - not force_remove | bool
    - connectivity_check is defined and connectivity_check.ping == 'pong'

- name: Remove A record
  ansible.windows.win_powershell:
    script: |
      param(
        [string]$Hostname,
        [string]$Domain
      )
      
      $ErrorActionPreference = "Stop"
      
      try {
        $fqdn = "$Hostname.$Domain"
        $dnsServer = [System.Net.Dns]::GetHostEntry($env:COMPUTERNAME).HostName
        
        # Remove A record
        Remove-DnsServerResourceRecord -Name $Hostname -ZoneName $Domain -RRType A -ComputerName $dnsServer -Force
        
        return @{
          Success = $true
          Message = "Successfully removed A record $fqdn"
        } | ConvertTo-Json
      }
      catch {
        return @{
          Success = $false
          Message = "Failed to remove A record: $_"
        } | ConvertTo-Json
      }
    parameters:
      Hostname: "{{ hostname }}"
      Domain: "{{ domain }}"
  register: remove_a_result
  delegate_to: "{{ dns_server }}"

- name: Parse A record removal result
  ansible.builtin.set_fact:
    a_record_removal_result: "{{ remove_a_result.output | from_json }}"

- name: Remove PTR record if A record was successfully removed
  ansible.builtin.include_tasks: remove_ptr.yml
  vars:
    ip_address: "{{ existing_record.IPAddress }}"
  when: 
    - manage_ptr | bool
    - a_record_removal_result.Success | bool

- name: Fail if A record removal failed
  ansible.builtin.fail:
    msg: "Failed to remove A record: {{ a_record_removal_result.Message }}"
  when: not a_record_removal_result.Success | bool
