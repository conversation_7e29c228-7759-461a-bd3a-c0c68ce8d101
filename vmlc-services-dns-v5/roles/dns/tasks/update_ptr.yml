---
# Update PTR Record Tasks
# Author: CES Operational Excellence Team
# Contributors: <PERSON> (7409)

- name: Remove old PTR record if IP changed
  ansible.builtin.include_tasks: remove_ptr.yml
  vars:
    ip_address: "{{ old_ip_address | default(existing_record.IPAddress) }}"
  when: old_ip_address is defined

- name: Add new PTR record
  ansible.builtin.include_tasks: add_ptr.yml
  when: old_ip_address is defined
