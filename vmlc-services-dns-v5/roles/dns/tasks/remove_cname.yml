---
# Remove CNAME Record Tasks
# Author: CES Operational Excellence Team
# Contributors: <PERSON> (7409)

- name: Get DNS server for domain
  ansible.builtin.set_fact:
    dns_server: "{{ domains[domain].dns_server }}"

- name: Remove CNAME record
  ansible.windows.win_powershell:
    script: |
      param(
        [string]$Hostname,
        [string]$Domain
      )
      
      $ErrorActionPreference = "Stop"
      
      try {
        $fqdn = "$Hostname.$Domain"
        $dnsServer = [System.Net.Dns]::GetHostEntry($env:COMPUTERNAME).HostName
        
        # Remove CNAME record
        Remove-DnsServerResourceRecord -Name $Hostname -ZoneName $Domain -RRType CName -ComputerName $dnsServer -Force
        
        return @{
          Success = $true
          Message = "Successfully removed CNAME record $fqdn"
        } | ConvertTo-Json
      }
      catch {
        return @{
          Success = $false
          Message = "Failed to remove CNAME record: $_"
        } | ConvertTo-Json
      }
    parameters:
      Hostname: "{{ hostname }}"
      Domain: "{{ domain }}"
  register: remove_cname_result
  delegate_to: "{{ dns_server }}"

- name: Parse CNAME record removal result
  ansible.builtin.set_fact:
    cname_record_removal_result: "{{ remove_cname_result.output | from_json }}"

- name: Fail if CNAME record removal failed
  ansible.builtin.fail:
    msg: "Failed to remove CNAME record: {{ cname_record_removal_result.Message }}"
  when: not cname_record_removal_result.Success | bool
