---
# Add PTR Record Tasks
# Author: CES Operational Excellence Team
# Contributors: <PERSON> (7409)

- name: Add PTR record
  ansible.windows.win_powershell:
    script: |
      param(
        [string]$Hostname,
        [string]$Domain,
        [string]$IPAddress,
        [int]$TTL,
        [string]$Description
      )
      
      $ErrorActionPreference = "Stop"
      
      try {
        $fqdn = "$Hostname.$Domain"
        $dnsServer = [System.Net.Dns]::GetHostEntry($env:COMPUTERNAME).HostName
        
        # Convert IP to reverse lookup format
        $ipParts = $IPAddress.Split('.')
        $ptrZone = "$($ipParts[2]).$($ipParts[1]).$($ipParts[0]).in-addr.arpa"
        $ptrName = $ipParts[3]
        
        # Add PTR record
        Add-DnsServerResourceRecordPtr -Name $ptrName -ZoneName $ptrZone -PtrDomainName $fqdn -TimeToLive (New-TimeSpan -Seconds $TTL) -ComputerName $dnsServer
        
        # Add description if provided
        if ($Description) {
          $record = Get-DnsServerResourceRecord -Name $ptrName -ZoneName $ptrZone -RRType Ptr -ComputerName $dnsServer
          $record.TimeStamp = $null
          $record.RecordData.TimeStamp = $null
          $record.Description = $Description
          Set-DnsServerResourceRecord -OldInputObject $record -NewInputObject $record -ZoneName $ptrZone -ComputerName $dnsServer
        }
        
        return @{
          Success = $true
          Message = "Successfully added PTR record for $IPAddress pointing to $fqdn"
        } | ConvertTo-Json
      }
      catch {
        return @{
          Success = $false
          Message = "Failed to add PTR record: $_"
        } | ConvertTo-Json
      }
    parameters:
      Hostname: "{{ hostname }}"
      Domain: "{{ domain }}"
      IPAddress: "{{ ip_address }}"
      TTL: "{{ ttl }}"
      Description: "{{ description }}"
  register: add_ptr_result
  delegate_to: "{{ ptr_server }}"

- name: Parse PTR record result
  ansible.builtin.set_fact:
    ptr_record_result: "{{ add_ptr_result.output | from_json }}"

- name: Log PTR record result
  ansible.builtin.debug:
    msg: "PTR record result: {{ ptr_record_result.Message }}"
  when: ptr_record_result.Success | bool

- name: Log PTR record failure
  ansible.builtin.debug:
    msg: "Warning: Failed to add PTR record: {{ ptr_record_result.Message }}"
  when: not ptr_record_result.Success | bool
