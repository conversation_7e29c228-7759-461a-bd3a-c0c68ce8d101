---
# Rollback DNS Operation Tasks
# Author: CES Operational Excellence Team
# Contributors: <PERSON> (7409)

- name: Load operation log
  ansible.builtin.include_role:
    name: utils
    tasks_from: load_operation_log
  vars:
    job_id: "{{ job_id }}"

- name: Fail if operation log not found
  ansible.builtin.fail:
    msg: "Operation log for job ID {{ job_id }} not found"
  when: operation_log is not defined or operation_log | length == 0

- name: Set rollback parameters
  ansible.builtin.set_fact:
    rollback_operation: "{{ 'remove' if operation_log.operation == 'add' else 'add' if operation_log.operation == 'remove' else 'update' }}"
    rollback_record_type: "{{ operation_log.record_type }}"
    rollback_hostname: "{{ operation_log.hostname }}"
    rollback_domain: "{{ operation_log.domain }}"
    rollback_ip_address: "{{ operation_log.ip_address | default('') }}"
    rollback_cname_target: "{{ operation_log.cname_target | default('') }}"
    rollback_ttl: "{{ operation_log.ttl | default(dns_settings.default_ttl) }}"
    rollback_description: "Rolled back from job {{ job_id }}"

- name: Execute rollback operation
  ansible.builtin.include_tasks: "{{ rollback_operation }}.yml"
  vars:
    operation: "{{ rollback_operation }}"
    record_type: "{{ rollback_record_type }}"
    hostname: "{{ rollback_hostname }}"
    domain: "{{ rollback_domain }}"
    ip_address: "{{ rollback_ip_address }}"
    cname_target: "{{ rollback_cname_target }}"
    ttl: "{{ rollback_ttl }}"
    description: "{{ rollback_description }}"

- name: Set rollback result
  ansible.builtin.set_fact:
    dns_operation_result:
      success: true
      changed: true
      message: "Successfully rolled back operation from job {{ job_id }}"
      original_operation: "{{ operation_log.operation }}"
      rollback_operation: "{{ rollback_operation }}"
      record:
        hostname: "{{ rollback_hostname }}"
        domain: "{{ rollback_domain }}"
        record_type: "{{ rollback_record_type }}"
        ip_address: "{{ rollback_ip_address }}"
        cname_target: "{{ rollback_cname_target }}"
        ttl: "{{ rollback_ttl }}"
