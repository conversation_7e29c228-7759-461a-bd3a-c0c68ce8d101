---
# Add A Record Tasks
# Author: CES Operational Excellence Team
# Contributors: <PERSON> (7409)

- name: Get DNS server for domain
  ansible.builtin.set_fact:
    dns_server: "{{ domains[domain].dns_server }}"
    ptr_server: "{{ domains[domain].ptr_server }}"

- name: Add A record
  ansible.windows.win_powershell:
    script: |
      param(
        [string]$Hostname,
        [string]$Domain,
        [string]$IPAddress,
        [int]$TTL,
        [string]$Description
      )
      
      $ErrorActionPreference = "Stop"
      
      try {
        $fqdn = "$Hostname.$Domain"
        $dnsServer = [System.Net.Dns]::GetHostEntry($env:COMPUTERNAME).HostName
        
        # Add A record
        Add-DnsServerResourceRecordA -Name $Hostname -ZoneName $Domain -IPv4Address $IPAddress -TimeToLive (New-TimeSpan -Seconds $TTL) -ComputerName $dnsServer
        
        # Add description if provided
        if ($Description) {
          $record = Get-DnsServerResourceRecord -Name $Hostname -ZoneName $Domain -RRType A -ComputerName $dnsServer
          $record.TimeStamp = $null
          $record.RecordData.TimeStamp = $null
          $record.Description = $Description
          Set-DnsServerResourceRecord -OldInputObject $record -NewInputObject $record -ZoneName $Domain -ComputerName $dnsServer
        }
        
        return @{
          Success = $true
          Message = "Successfully added A record $fqdn with IP $IPAddress"
        } | ConvertTo-Json
      }
      catch {
        return @{
          Success = $false
          Message = "Failed to add A record: $_"
        } | ConvertTo-Json
      }
    parameters:
      Hostname: "{{ hostname }}"
      Domain: "{{ domain }}"
      IPAddress: "{{ ip_address }}"
      TTL: "{{ ttl }}"
      Description: "{{ description }}"
  register: add_a_result
  delegate_to: "{{ dns_server }}"

- name: Parse A record result
  ansible.builtin.set_fact:
    a_record_result: "{{ add_a_result.output | from_json }}"

- name: Add PTR record if requested
  ansible.builtin.include_tasks: add_ptr.yml
  when: 
    - manage_ptr | bool
    - a_record_result.Success | bool

- name: Fail if A record addition failed
  ansible.builtin.fail:
    msg: "Failed to add A record: {{ a_record_result.Message }}"
  when: not a_record_result.Success | bool
