---
# Add CNAME Record Tasks
# Author: CES Operational Excellence Team
# Contributors: <PERSON> (7409)

- name: Get DNS server for domain
  ansible.builtin.set_fact:
    dns_server: "{{ domains[domain].dns_server }}"

- name: Add CNAME record
  ansible.windows.win_powershell:
    script: |
      param(
        [string]$Hostname,
        [string]$Domain,
        [string]$CnameTarget,
        [int]$TTL,
        [string]$Description
      )
      
      $ErrorActionPreference = "Stop"
      
      try {
        $fqdn = "$Hostname.$Domain"
        $dnsServer = [System.Net.Dns]::GetHostEntry($env:COMPUTERNAME).HostName
        
        # Add CNAME record
        Add-DnsServerResourceRecordCName -Name $Hostname -ZoneName $Domain -HostNameAlias $CnameTarget -TimeToLive (New-TimeSpan -Seconds $TTL) -ComputerName $dnsServer
        
        # Add description if provided
        if ($Description) {
          $record = Get-DnsServerResourceRecord -Name $Hostname -ZoneName $Domain -RRType CName -ComputerName $dnsServer
          $record.TimeStamp = $null
          $record.RecordData.TimeStamp = $null
          $record.Description = $Description
          Set-DnsServerResourceRecord -OldInputObject $record -NewInputObject $record -ZoneName $Domain -ComputerName $dnsServer
        }
        
        return @{
          Success = $true
          Message = "Successfully added CNAME record $fqdn pointing to $CnameTarget"
        } | ConvertTo-Json
      }
      catch {
        return @{
          Success = $false
          Message = "Failed to add CNAME record: $_"
        } | ConvertTo-Json
      }
    parameters:
      Hostname: "{{ hostname }}"
      Domain: "{{ domain }}"
      CnameTarget: "{{ cname_target }}"
      TTL: "{{ ttl }}"
      Description: "{{ description }}"
  register: add_cname_result
  delegate_to: "{{ dns_server }}"

- name: Parse CNAME record result
  ansible.builtin.set_fact:
    cname_record_result: "{{ add_cname_result.output | from_json }}"

- name: Fail if CNAME record addition failed
  ansible.builtin.fail:
    msg: "Failed to add CNAME record: {{ cname_record_result.Message }}"
  when: not cname_record_result.Success | bool
