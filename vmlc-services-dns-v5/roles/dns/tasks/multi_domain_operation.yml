---
# Multi-Domain Operation Tasks
# Author: CES Operational Excellence Team
# Contributors: <PERSON> (7409)

- name: Execute operation for current domain
  block:
    - name: Set current domain parameters
      ansible.builtin.set_fact:
        hostname: "{{ current_hostname }}"
        domain: "{{ current_domain }}"
        ip_address: "{{ current_ip_address }}"
        cname_target: "{{ current_cname_target }}"
    
    - name: Execute DNS operation
      ansible.builtin.include_tasks: "{{ operation }}.yml"
      vars:
        is_multi_domain: false
    
    - name: Add result to multi-domain results
      ansible.builtin.set_fact:
        multi_domain_results: "{{ multi_domain_results + [dns_operation_result | combine({'index': current_index})] }}"
  rescue:
    - name: Handle error for current domain
      ansible.builtin.set_fact:
        multi_domain_results: "{{ multi_domain_results + [{'success': false, 'changed': false, 'message': 'Operation failed: ' + ansible_failed_result.msg, 'index': current_index, 'record': {'hostname': current_hostname, 'domain': current_domain, 'record_type': record_type}}] }}"
