# DNS Role

**Author**: CES Operational Excellence Team  
**Contributors**: <PERSON> (7409)

## Overview

The DNS role handles all DNS record operations, including:

- Adding A, PTR, and CNAME records
- Removing records
- Updating records
- Verifying records
- Multi-domain operations
- Rollback operations

## Usage

This role is designed to be included in playbooks rather than used directly. See the main playbooks in the `playbooks` directory for examples.

## Tasks

- `add.yml` - Add DNS records
- `remove.yml` - Remove DNS records
- `update.yml` - Update DNS records
- `verify.yml` - Verify DNS records
- `rollback.yml` - Rollback operations
- `multi_domain.yml` - Multi-domain operations

## Variables

See `defaults/main.yml` for default variables and `vars/main.yml` for internal variables.

## Dependencies

- utils role
