#!/usr/bin/python
# -*- coding: utf-8 -*-

"""
DNS Filter Plugins for DNS Management System
Author: CES Operational Excellence Team
Contributors: <PERSON> (7409)
"""

from __future__ import absolute_import, division, print_function
__metaclass__ = type


def reverse_ip(ip_address):
    """
    Convert an IP address to reverse lookup format.
    Example: ******** -> ********
    """
    if not ip_address:
        return ''
    
    parts = ip_address.split('.')
    if len(parts) != 4:
        return ip_address
    
    return '.'.join(parts[::-1])


def ptr_zone(ip_address):
    """
    Convert an IP address to PTR zone format.
    Example: ******** -> 2.1.10.in-addr.arpa
    """
    if not ip_address:
        return ''
    
    parts = ip_address.split('.')
    if len(parts) != 4:
        return ip_address
    
    return '{}.{}.{}.in-addr.arpa'.format(parts[2], parts[1], parts[0])


def ptr_name(ip_address):
    """
    Extract the PTR name from an IP address.
    Example: ******** -> 3
    """
    if not ip_address:
        return ''
    
    parts = ip_address.split('.')
    if len(parts) != 4:
        return ip_address
    
    return parts[3]


def format_fqdn(hostname, domain):
    """
    Format a hostname and domain as an FQDN.
    Example: server01, example.com -> server01.example.com
    """
    if not hostname or not domain:
        return ''
    
    return '{}.{}'.format(hostname, domain)


class FilterModule(object):
    """DNS filter plugins."""

    def filters(self):
        return {
            'reverse_ip': reverse_ip,
            'ptr_zone': ptr_zone,
            'ptr_name': ptr_name,
            'format_fqdn': format_fqdn,
        }
