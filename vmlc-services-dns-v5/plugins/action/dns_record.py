#!/usr/bin/python
# -*- coding: utf-8 -*-

"""
DNS Record Action Plugin for DNS Management System
Author: CES Operational Excellence Team
Contributors: <PERSON> (7409)
"""

from __future__ import absolute_import, division, print_function
__metaclass__ = type

from ansible.plugins.action import ActionBase
from ansible.errors import AnsibleError
from ansible.utils.display import Display

display = Display()


class ActionModule(ActionBase):
    def run(self, tmp=None, task_vars=None):
        if task_vars is None:
            task_vars = dict()
        
        result = super(ActionModule, self).run(tmp, task_vars)
        
        # Get parameters
        operation = self._task.args.get('operation')
        record_type = self._task.args.get('record_type')
        hostname = self._task.args.get('hostname')
        domain = self._task.args.get('domain')
        ip_address = self._task.args.get('ip_address')
        cname_target = self._task.args.get('cname_target')
        ttl = self._task.args.get('ttl', 3600)
        description = self._task.args.get('description', '')
        manage_ptr = self._task.args.get('manage_ptr', True)
        force_remove = self._task.args.get('force_remove', False)
        
        # Validate parameters
        if record_type in ['a', 'cname'] and (not hostname or not domain):
            return {'failed': True, 'msg': 'hostname and domain are required for A and CNAME records'}
        
        if record_type == 'ptr' and not ip_address:
            return {'failed': True, 'msg': 'ip_address is required for PTR records'}
        
        if record_type == 'a' and operation in ['add', 'update'] and not ip_address:
            return {'failed': True, 'msg': 'ip_address is required for adding or updating A records'}
        
        if record_type == 'cname' and operation in ['add', 'update'] and not cname_target:
            return {'failed': True, 'msg': 'cname_target is required for adding or updating CNAME records'}
        
        # Get DNS server
        if record_type in ['a', 'cname']:
            dns_server = task_vars.get('hostvars', {}).get('localhost', {}).get('domains', {}).get(domain, {}).get('dns_server')
            if not dns_server:
                return {'failed': True, 'msg': 'DNS server for domain {} not found in configuration'.format(domain)}
        else:
            # For PTR records, get the PTR server
            parts = ip_address.split('.')
            if len(parts) != 4:
                return {'failed': True, 'msg': 'Invalid IP address: {}'.format(ip_address)}
            
            # Get the domain for the PTR zone
            ptr_zone = '{}.{}.{}.in-addr.arpa'.format(parts[2], parts[1], parts[0])
            
            # Find the DNS server for the PTR zone
            dns_server = None
            for d, config in task_vars.get('hostvars', {}).get('localhost', {}).get('domains', {}).items():
                if config.get('ptr_server'):
                    dns_server = config.get('ptr_server')
                    break
            
            if not dns_server:
                return {'failed': True, 'msg': 'PTR server not found in configuration'}
        
        # Execute the module on the DNS server
        module_args = dict(
            operation=operation,
            record_type=record_type,
            hostname=hostname,
            domain=domain,
            ip_address=ip_address,
            cname_target=cname_target,
            ttl=ttl,
            description=description,
            manage_ptr=manage_ptr,
            force_remove=force_remove,
        )
        
        module_return = self._execute_module(
            module_name='dns_record',
            module_args=module_args,
            task_vars=task_vars,
            tmp=tmp,
        )
        
        result.update(module_return)
        
        return result
