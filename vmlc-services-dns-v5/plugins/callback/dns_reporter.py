#!/usr/bin/python
# -*- coding: utf-8 -*-

"""
DNS Reporter Callback Plugin for DNS Management System
Author: CES Operational Excellence Team
Contributors: <PERSON> (7409)
"""

from __future__ import absolute_import, division, print_function
__metaclass__ = type

import os
import json
import datetime
from ansible.plugins.callback import CallbackBase


class CallbackModule(CallbackBase):
    """
    This callback module generates reports for DNS operations.
    """

    CALLBACK_VERSION = 2.0
    CALLBACK_TYPE = 'notification'
    CALLBACK_NAME = 'dns_reporter'
    CALLBACK_NEEDS_WHITELIST = True

    def __init__(self):
        super(CallbackModule, self).__init__()
        self.log_dir = os.path.join(os.getcwd(), 'logs')
        if not os.path.exists(self.log_dir):
            try:
                os.makedirs(self.log_dir)
            except OSError:
                self.log_dir = '/tmp'
        
        self.timestamp = datetime.datetime.now().strftime('%Y%m%d%H%M%S')
        self.operation_log = {}
        self.dns_operation_result = None

    def v2_playbook_on_play_start(self, play):
        self.operation_log['play_name'] = play.get_name()
        self.operation_log['timestamp'] = self.timestamp
        self.operation_log['job_id'] = os.environ.get('ANSIBLE_JOB_ID', 'local')

    def v2_runner_on_ok(self, result):
        task_name = result._task.get_name()
        
        # Capture DNS operation result
        if 'dns_operation_result' in result._result.get('ansible_facts', {}):
            self.dns_operation_result = result._result['ansible_facts']['dns_operation_result']
            
            # Extract operation details
            if 'operation' in result._task_vars:
                self.operation_log['operation'] = result._task_vars.get('operation')
            
            if 'record_type' in result._task_vars:
                self.operation_log['record_type'] = result._task_vars.get('record_type')
            
            if 'hostname' in result._task_vars:
                self.operation_log['hostname'] = result._task_vars.get('hostname')
            
            if 'domain' in result._task_vars:
                self.operation_log['domain'] = result._task_vars.get('domain')
            
            if 'ip_address' in result._task_vars:
                self.operation_log['ip_address'] = result._task_vars.get('ip_address')
            
            if 'cname_target' in result._task_vars:
                self.operation_log['cname_target'] = result._task_vars.get('cname_target')
            
            if 'ttl' in result._task_vars:
                self.operation_log['ttl'] = result._task_vars.get('ttl')
            
            if 'ticket' in result._task_vars:
                self.operation_log['ticket'] = result._task_vars.get('ticket')
            
            # Add result details
            self.operation_log['success'] = self.dns_operation_result.get('success', False)
            self.operation_log['message'] = self.dns_operation_result.get('message', '')
            
            # Generate report
            self._generate_report()

    def v2_playbook_on_stats(self, stats):
        # Final report generation
        if self.dns_operation_result and 'success' in self.dns_operation_result:
            self._generate_report()

    def _generate_report(self):
        if not self.operation_log:
            return
        
        # Generate report file name
        report_file = os.path.join(
            self.log_dir,
            'report_{}_{}.json'.format(
                self.timestamp,
                self.operation_log.get('job_id', 'local')
            )
        )
        
        # Save report file
        try:
            with open(report_file, 'w') as f:
                json.dump(self.operation_log, f, indent=2)
        except Exception as e:
            self._display.warning('Failed to save report: {}'.format(str(e)))
