#!/usr/bin/env python3
"""
Log Cleanup Script for DNS Management System
Author: CES Operational Excellence Team
Contributors: <PERSON> (7409)
"""

import os
import sys
import argparse
import datetime
import glob
import shutil

def parse_args():
    """Parse command line arguments."""
    parser = argparse.ArgumentParser(description='Clean up old log files.')
    parser.add_argument('--days', type=int, default=30,
                        help='Remove logs older than this many days (default: 30)')
    parser.add_argument('--log-dir', type=str, default='../logs',
                        help='Log directory (default: ../logs)')
    parser.add_argument('--archive', action='store_true',
                        help='Archive logs instead of deleting them')
    parser.add_argument('--archive-dir', type=str, default='../logs/archive',
                        help='Archive directory (default: ../logs/archive)')
    parser.add_argument('--dry-run', action='store_true',
                        help='Show what would be done without actually doing it')
    return parser.parse_args()

def cleanup_logs(args):
    """Clean up old log files."""
    if not os.path.exists(args.log_dir):
        print(f"Log directory {args.log_dir} does not exist.")
        return 1

    if args.archive and not os.path.exists(args.archive_dir) and not args.dry_run:
        os.makedirs(args.archive_dir)

    cutoff_date = datetime.datetime.now() - datetime.timedelta(days=args.days)
    log_files = glob.glob(os.path.join(args.log_dir, '*.log'))
    operation_files = glob.glob(os.path.join(args.log_dir, 'operation_*.json'))
    
    files_to_process = log_files + operation_files
    processed_count = 0

    for file_path in files_to_process:
        file_stat = os.stat(file_path)
        file_date = datetime.datetime.fromtimestamp(file_stat.st_mtime)
        
        if file_date < cutoff_date:
            if args.dry_run:
                print(f"Would {'archive' if args.archive else 'delete'} {file_path}")
            else:
                if args.archive:
                    archive_path = os.path.join(args.archive_dir, os.path.basename(file_path))
                    shutil.move(file_path, archive_path)
                    print(f"Archived {file_path} to {archive_path}")
                else:
                    os.remove(file_path)
                    print(f"Deleted {file_path}")
            processed_count += 1

    print(f"Processed {processed_count} files.")
    return 0

def main():
    """Main function."""
    args = parse_args()
    return cleanup_logs(args)

if __name__ == '__main__':
    sys.exit(main())
