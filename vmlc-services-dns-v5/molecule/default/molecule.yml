---
dependency:
  name: galaxy
driver:
  name: docker
platforms:
  - name: instance
    image: quay.io/ansible/centos7-ansible:latest
    pre_build_image: true
    command: /sbin/init
    privileged: true
    volumes:
      - /sys/fs/cgroup:/sys/fs/cgroup:ro
provisioner:
  name: ansible
  env:
    ANSIBLE_ROLES_PATH: ../../roles
  inventory:
    group_vars:
      all:
        ansible_python_interpreter: /usr/bin/python3
verifier:
  name: ansible
