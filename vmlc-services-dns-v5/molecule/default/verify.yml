---
- name: Verify
  hosts: all
  gather_facts: false
  tasks:
    - name: Include utils role
      ansible.builtin.include_role:
        name: utils
        tasks_from: load_config
      
    - name: Verify configuration loaded
      ansible.builtin.assert:
        that:
          - dns_settings is defined
          - domains is defined
        success_msg: "Configuration loaded successfully"
        fail_msg: "Failed to load configuration"
