# Project Plan: vmls-dnsmgmt-kilo - Ansible DNS Management

## 1. Project Overview

*   **Project Name:** `vmls-dnsmgmt-kilo`
*   **Objective:** To create a comprehensive Ansible project for managing DNS operations (A, PTR, CNAME records, and Zones) across potentially multiple domains, utilizing PowerShell for execution on target Windows DNS servers and Ansible for orchestration and error handling.
*   **Key Operations:** Verify, Add, Remove, Update for DNS records and zones.

## 2. Authorship

*   **Author:** CES Operational Excellence Team
*   **Contributor:** <PERSON> (7409)

## 3. Core Architecture

*   **Ansible:** Used for overall orchestration, playbook execution, variable management (including multi-domain inputs), parameter passing, and robust error handling.
*   **PowerShell:** Used for the actual execution of DNS commands on target Windows DNS servers. Scripts will be modular and parameterized.
*   **Target Systems:** Primarily Windows DNS servers.

## 4. Multi-Domain Support

*   **Input Mechanism:** DNS operations across multiple domains will be defined in a structured YAML variable file (e.g., `inventory/group_vars/dns_servers/dns_config.yml`). This file will contain a list of domains, each with its specific zones and records to be managed.
    ```yaml
    # Example structure in inventory/group_vars/dns_servers/dns_config.yml
    dns_operations:
      - domain_name: "example.com"
        target_dns_server: "dc01.example.com" # Optional, can be inferred from inventory
        zones:
          - name: "example.com"
            action: "add"
            # ... other zone parameters
        records:
          - type: "A"
            name: "www"
            value: "************"
            action: "add"
            ttl: 3600
      - domain_name: "another.internal"
        # ... operations for this domain
    ```
*   **Playbook Logic:** A dedicated playbook (e.g., `playbooks/manage_multi_domain_dns.yml`) will iterate through the `dns_operations` list, calling appropriate roles for each domain.
*   **Role Parameterization:** Roles will accept domain-specific information (domain name, target server, list of zones/records) as parameters.
*   **PowerShell Script Parameterization:** Scripts will accept parameters like `-ZoneName` and `-ComputerName` (if operations are delegated to a specific DNS server not directly targeted by Ansible).

## 5. Project Directory Structure

```mermaid
graph TD
    A(vmls-dnsmgmt-kilo) --> B(inventory)
    A --> C(playbooks)
    A --> D(roles)
    A --> E(global_files)[files]
    A --> F(templates)
    A --> G(library)
    A --> H(filter_plugins)
    A --> I(callback_plugins)
    A --> J(vars)

    B --> B1(group_vars)
    B1 --> B1a(all)
    B1 --> B1b(dns_servers)

    D --> D1(common)
    D1 --> D1a(tasks)
    D1 --> D1b(handlers)
    D1 --> D1c(defaults)
    D1 --> D1_readme[README.md]

    D --> D2(dns_zone_management)
    D2 --> D2a(tasks)
    D2 --> D2b(defaults)
    D2 --> D2c(vars)
    D2 --> D2d(files)
    D2 --> D2_readme[README.md]

    D --> D3(dns_record_management)
    D3 --> D3a(tasks)
    D3 --> D3b(templates)
    D3 --> D3c(defaults)
    D3 --> D3d(vars)
    D3 --> D3e(files)
    D3 --> D3_readme[README.md]

    D --> D4(dns_reporting)
    D4 --> D4a(tasks)
    D4 --> D4b(templates)
    D4 --> D4_readme[README.md]
```

*   **`vmls-dnsmgmt-kilo/`**
    *   `ansible.cfg`
    *   `inventory/`
        *   `hosts.ini`
        *   `group_vars/`
            *   `all/common_vars.yml`
            *   `dns_servers/dns_config.yml` (for multi-domain inputs)
            *   `dns_servers/vault.yml` (for encrypted secrets)
    *   `playbooks/`
        *   `site.yml`
        *   `manage_multi_domain_dns.yml`
        *   `verify_dns_config.yml`
    *   `roles/`
        *   `common/` (tasks, handlers, defaults, README.md)
        *   `dns_zone_management/` (tasks, defaults, vars, files, README.md)
            *   `files/Manage-DnsZone.ps1`, `Verify-DnsZone.ps1`
            *   `tasks/main.yml`, `add_zone.yml`, `update_zone.yml`, `remove_zone.yml`, `verify_zone.yml`
        *   `dns_record_management/` (tasks, templates, defaults, vars, files, README.md)
            *   `files/Manage-DnsRecord.ps1`, `Verify-DnsRecord.ps1` (or specific like `Manage-DnsARecord.ps1`)
            *   `tasks/main.yml`, `add_a_record.yml`, `update_a_record.yml`, `remove_a_record.yml`, (similar for PTR, CNAME), `verify_record.yml`
        *   `dns_reporting/` (tasks, templates, README.md)
    *   `files/` (Global static files, if any)
    *   `templates/` (Global Jinja2 templates, if any)
    *   `library/` (Custom Ansible modules, if needed)
    *   `filter_plugins/` (Custom filter plugins, if needed)
    *   `callback_plugins/` (Custom callback plugins, if needed)
    *   `vars/` (Global playbook-level variables, if any, e.g., `global_dns_settings.yml`)
    *   `README.md` (Main project documentation)
    *   `requirements.yml` (Ansible Galaxy dependencies)
    *   `.gitignore`
    *   `PROJECT_PLAN.md` (This file)

## 6. Documentation Strategy

*   **Main `README.md` (Project Root):**
    *   Project purpose, prerequisites, setup (inventory, group_vars, vault), playbook execution, role overview, troubleshooting, author/contributor info.
*   **Role-Specific `README.md` Files:**
    *   Located in each role's root (e.g., `roles/dns_zone_management/README.md`).
    *   Purpose, input variables, task descriptions, dependencies, example usage.
*   **PowerShell Script Documentation (`.ps1` files):**
    *   Comment-based help blocks (Synopsis, Description, Parameters, Examples, Notes).
    *   Inline comments for complex logic.
*   **Playbook Documentation (within `.yml` files):**
    *   Descriptive `name` attributes for plays and tasks.
    *   Comments (`#`) for non-obvious logic.
*   **Variable File Documentation (`group_vars/**/*.yml`):**
    *   Comments explaining variable purposes and data structures, especially in `dns_config.yml`.

## 7. Implementation Steps (High-Level)

1.  Create the directory structure and placeholder files as outlined.
2.  Develop PowerShell scripts for each DNS operation (add, update, remove, verify for zones and records).
3.  Develop Ansible tasks within roles to call these PowerShell scripts, passing necessary parameters and handling outputs/errors.
4.  Develop Ansible playbooks to orchestrate the roles for single and multi-domain scenarios.
5.  Populate inventory and variable files with initial configuration.
6.  Write comprehensive documentation as planned.
7.  Test thoroughly.

This plan provides a roadmap for developing the `vmls-dnsmgmt-kilo` Ansible project.