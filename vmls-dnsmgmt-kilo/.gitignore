# Ansible specific
*.retry
*.vault # If you use a vault password file named .vault (though .vault_pass is more common)
.ansible_vault_pass # Common name for vault password file
ansible_local_facts/ # If local facts are generated and stored in project

# Python specific
__pycache__/
*.pyc
*.pyo
*.pyd
*.egg-info/
venv/
.venv/
env/
.env/
pip-wheel-metadata/
pip-selfcheck.json

# General
*.log
*.swp
*.swo
*~
.DS_Store
Thumbs.db

# Project specific (if any)
reports/ # If reports are generated here and not versioned
logs/    # If logs are generated here and not versioned
*.tmp
*.bak
local_*.yml # For local overrides not to be committed