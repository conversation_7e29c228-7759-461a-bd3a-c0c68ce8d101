# This is your Ansible inventory file.
# Define your DNS servers or management hosts here.

# Example for Windows DNS servers (ensure winrm is configured):
# [dns_servers]
# dns01.example.com ansible_user=your_admin_user ansible_password=your_password ansible_connection=winrm ansible_winrm_server_cert_validation=ignore
# dns02.example.com ansible_user=your_admin_user ansible_password=your_password ansible_connection=winrm ansible_winrm_server_cert_validation=ignore

# Example for a management host if PowerShell scripts are run from there:
# [management_hosts]
# mgmt01.example.com ansible_user=your_user ansible_private_key_file=~/.ssh/id_rsa

# It's recommended to use ansible-vault for sensitive data like passwords.
# You can also define connection variables in group_vars or host_vars.

[dns_servers]
# Add your DNS server hostnames or IP addresses here
# server1.example.com
# server2.example.com

[all:vars]
# Example: Define ansible_winrm_transport if not using default (e.g., ntlm, kerberos)
# ansible_winrm_transport = ntlm