# This file defines the DNS operations to be performed across one or more domains.
# It is read by the manage_multi_domain_dns.yml playbook.

# dns_operations:
#   - domain_name: "example.com"  # The primary domain/zone name for these operations
#     target_dns_server: "dc01.example.com" # Optional: FQDN of the DNS server to target for this domain's operations.
#                                           # If omitted, Ansible will target the host it's currently connected to from the inventory.
#                                           # PowerShell scripts should use this via a -ComputerName parameter if provided.
#     zones: # Optional: List of zone operations for this domain
#       - name: "subzone.example.com" # Name of the zone to manage
#         action: "add"               # "add", "update", "remove", "verify"
#         # zone_type: "Primary"      # Example: Primary, Secondary, Stub (depends on PowerShell script capabilities)
#         # replication_scope: "Forest" # Example: Forest, Domain, Custom (depends on PowerShell script capabilities)
#         # ... other zone-specific parameters for your PowerShell script
#
#     records: # Optional: List of record operations for this domain/zone
#       - type: "A"                   # "A", "PTR", "CNAME" (extend as your scripts support more)
#         name: "www"                 # Record name (without the domain_name suffix, e.g., "www" for "www.example.com")
#         zone: "example.com"         # Explicit zone for the record, defaults to `domain_name` if omitted.
#         value: "************"       # IP address for A/AAAA, FQDN for CNAME, hostname for PTR
#         action: "add"               # "add", "update", "remove", "verify"
#         ttl: 3600                   # Optional: Time To Live for the record
#         # ptr_create: true            # Optional: For A records, whether to also create a PTR record (if script supports)
#         # old_value: "***********"    # Optional: For "update" action, if your script needs the old value to find the record.
#
#       - type: "CNAME"
#         name: "portal"
#         zone: "example.com"
#         value: "www.example.com"
#         action: "add"
#
#   - domain_name: "another.internal"
#     target_dns_server: "dns02.another.internal"
#     records:
#       - type: "A"
#         name: "appserver"
#         zone: "another.internal"
#         value: "**********"
#         action: "update"
#         old_value: "**********" # Example for update

# Example: Minimal structure for adding a single A record
dns_operations:
  - domain_name: "yourdomain.local"
    records:
      - type: "A"
        name: "testhost"
        # zone: "yourdomain.local" # Will default to domain_name
        value: "***************"
        action: "add"
        ttl: 3600