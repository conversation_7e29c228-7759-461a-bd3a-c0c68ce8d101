# Ansible Role: dns_reporting

## Description

This role is intended for generating reports based on DNS operations performed by other roles in the `vmls-dnsmgmt-kilo` project or for reporting on the current state of DNS configurations.

The specifics of this role are highly dependent on the desired reporting format and content.
## Requirements

- May require additional Python libraries if generating complex reports (e.g., `jinja2` for HTML, libraries for PDF generation if not using PowerShell for this).
- Access to logs or data generated by other DNS management roles if reporting on changes (e.g., facts set by them, or log files).

## Role Variables

- `reporting_output_path` (string, optional): Path where reports should be saved. Default: `./reports` relative to playbook execution.
- `report_format` (string, optional): Desired report format (e.g., "html", "csv", "txt", "pdf"). Default: "txt".
- `report_template` (string, optional): Path to a Jinja2 template file if generating templated reports. Default: `report_template.j2` in the role's `templates/` directory.
- `report_data_source` (variable or fact, optional): The variable name or fact that holds the data to be reported (e.g., results from DNS operations).
- `email_report_to` (list, optional): A list of email addresses to send the report to.
- `email_report_subject` (string, optional): Subject for the report email.
- `smtp_server` (string, optional): SMTP server for sending email reports.

### `defaults/main.yml`
```yaml
# reporting_output_path: "./reports"
# report_format: "txt"
# report_template: "report_template.j2" # Assumes it's in roles/dns_reporting/templates/
# email_report_subject: "DNS Management Report"
```

## Dependencies

- None by default, but could depend on roles that generate the data to be reported.

## Example Playbook Usage

```yaml
- hosts: localhost # Reporting might run locally after gathering facts/results
  connection: local
  gather_facts: false
  roles:
    - role: dns_reporting
      vars:
        reporting_output_path: "/tmp/dns_reports"
        report_format: "html"
        report_data_source: "{{ hostvars['dns_server_1']['dns_op_results'] }}" # Example data source
        email_report_to:
          - "<EMAIL>"
          - "<EMAIL>"
```

## Templates

- **`templates/report_template.j2`**: A sample Jinja2 template for generating reports. This will be a basic placeholder.

## Tasks

The `tasks/main.yml` will contain logic to:
- Gather data for the report (if not passed directly).
- Format the report based on `report_format`.
- Use `report_template` if applicable.
- Save the report to `reporting_output_path`.
- Optionally email the report.