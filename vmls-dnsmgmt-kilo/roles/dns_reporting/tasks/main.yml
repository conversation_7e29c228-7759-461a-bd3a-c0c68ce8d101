---
# tasks file for vmls-dnsmgmt-kilo/roles/dns_reporting

- name: Ensure reporting output directory exists
  ansible.builtin.file:
    path: "{{ reporting_output_path | default('./reports', true) }}"
    state: directory
    mode: '0755'
  delegate_to: localhost # Reporting often happens on the control node
  run_once: true # Ensure directory is created only once

- name: Placeholder for gathering or processing report data
  ansible.builtin.set_fact:
    report_content_data: "{{ report_data_source | default({'message': 'No specific data source provided for report.'}) }}"
    report_file_name: "dns_report_{{ ansible_date_time.epoch }}.{{ report_format | default('txt') }}"
  run_once: true

- name: Generate report using template (if HTML or other templated format)
  ansible.builtin.template:
    src: "{{ report_template | default('report_template.j2', true) }}"
    dest: "{{ (reporting_output_path | default('./reports', true)) }}/{{ report_file_name }}"
    mode: '0644'
  delegate_to: localhost
  run_once: true
  when: report_format | default('txt') in ['html', 'md'] # Add other templated formats

- name: Generate simple text report (if format is txt or not templated)
  ansible.builtin.copy:
    content: |
      DNS Management Report
      =====================
      Date: {{ ansible_date_time.iso8601 }}

      Data:
      {{ report_content_data | to_nice_yaml }}
    dest: "{{ (reporting_output_path | default('./reports', true)) }}/{{ report_file_name }}"
    mode: '0644'
  delegate_to: localhost
  run_once: true
  when: report_format | default('txt') == 'txt' # Or other non-templated formats

- name: Placeholder for CSV report generation
  ansible.builtin.debug:
    msg: "CSV report generation for {{ report_file_name }} needs to be implemented if format is csv."
  when: report_format | default('txt') == 'csv'
  run_once: true

- name: Placeholder for PDF report generation
  ansible.builtin.debug:
    msg: "PDF report generation for {{ report_file_name }} needs to be implemented if format is pdf."
  when: report_format | default('txt') == 'pdf'
  run_once: true

- name: Display report path
  ansible.builtin.debug:
    msg: "Report generated at: {{ (reporting_output_path | default('./reports', true)) }}/{{ report_file_name }}"
  run_once: true

- name: Email report if recipients are defined
  ansible.builtin.mail:
    host: "{{ smtp_server | default('localhost') }}"
    port: "{{ smtp_port | default(25) }}"
    to: "{{ email_report_to }}"
    subject: "{{ email_report_subject | default('DNS Management Report') }}"
    body: "Please find the DNS management report attached. Report generated on {{ ansible_date_time.iso8601 }}."
    attach:
      - "{{ (reporting_output_path | default('./reports', true)) }}/{{ report_file_name }}"
  delegate_to: localhost
  run_once: true
  when: email_report_to is defined and email_report_to | length > 0