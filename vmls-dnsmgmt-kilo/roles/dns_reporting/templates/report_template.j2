{# Default Jinja2 template for DNS reports #}
<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>DNS Management Report</title>
    <style>
        body { font-family: Arial, sans-serif; margin: 20px; }
        h1 { color: #333; }
        table { width: 100%; border-collapse: collapse; margin-top: 20px; }
        th, td { border: 1px solid #ddd; padding: 8px; text-align: left; }
        th { background-color: #f2f2f2; }
        .summary { margin-bottom: 20px; padding: 10px; background-color: #eef; border-left: 5px solid #aac; }
    </style>
</head>
<body>
    <h1>DNS Management Report</h1>
    <div class="summary">
        <p><strong>Report Generated:</strong> {{ ansible_date_time.iso8601 }}</p>
        {% if report_content_data.message %}
            <p><strong>Status:</strong> {{ report_content_data.message }}</p>
        {% endif %}
    </div>

    <h2>Report Data Details</h2>
    {% if report_content_data and report_content_data is mapping %}
        <table>
            <thead>
                <tr>
                    <th>Key</th>
                    <th>Value</th>
                </tr>
            </thead>
            <tbody>
            {% for key, value in report_content_data.items() %}
                <tr>
                    <td>{{ key }}</td>
                    <td>
                        {% if value is mapping or value is list %}
                            <pre>{{ value | to_nice_yaml }}</pre>
                        {% else %}
                            {{ value }}
                        {% endif %}
                    </td>
                </tr>
            {% else %}
                <tr>
                    <td colspan="2">No data items found in report_content_data.</td>
                </tr>
            {% endfor %}
            </tbody>
        </table>
    {% elif report_content_data %}
        <p>Raw Data:</p>
        <pre>{{ report_content_data | to_nice_json }}</pre>
    {% else %}
        <p>No data available for the report.</p>
    {% endif %}

</body>
</html>