---
# defaults file for vmls-dnsmgmt-kilo/roles/dns_reporting

reporting_output_path: "./reports" # Relative to playbook execution directory
report_format: "html" # Default report format (txt, html, md, csv, pdf - csv/pdf need custom logic)
report_template: "report_template.j2" # Assumes it's in this role's templates/ directory

# email_report_to: [] # Example: ["<EMAIL>", "<EMAIL>"]
email_report_subject: "DNS Management Operations Report"
# smtp_server: "localhost"
# smtp_port: 25

# Default data if nothing is passed to the role
# report_data_source:
#   summary: "No specific operation data provided."
#   details: []