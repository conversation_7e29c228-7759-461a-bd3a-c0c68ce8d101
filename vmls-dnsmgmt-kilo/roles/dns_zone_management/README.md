# Ansible Role: dns_zone_management

## Description

This role is responsible for managing DNS zones (e.g., creating, updating, deleting, verifying) on target DNS servers. It primarily uses PowerShell scripts located in its `files/` directory for these operations, orchestrated by Ansible tasks.

## Requirements

- Target hosts must be Windows DNS servers.
- PowerShell 5.0 or higher is recommended on target hosts.
- WinRM must be configured on target hosts for Ansible connection.
- The PowerShell scripts (`Manage-DnsZone.ps1`, `Verify-DnsZone.ps1`) in the `files/` directory of this role must be present and functional.

## Role Variables

This role expects the following variables to be passed to it, typically from a playbook:

- `current_target_domain_name` (string, mandatory for some operations): The primary domain name context. Often used as the default zone name if a specific zone item doesn't specify one.
- `current_target_dns_server` (string, optional): The FQDN of the specific DNS server to target for operations. If omitted or `omit`, PowerShell scripts should target the local machine or use their own logic for server determination.
- `zones_to_process` (list, mandatory): A list of dictionaries, where each dictionary defines a zone operation.
    - `name` (string, mandatory): The FQDN of the DNS zone to manage (e.g., "example.com", "sub.example.com").
    - `action` (string, mandatory): The operation to perform. Expected values: "add", "update", "remove", "verify".
    - `zone_type` (string, optional): Type of zone (e.g., "Primary", "Secondary", "Stub"). Passed to PowerShell.
    - `replication_scope` (string, optional): Replication scope for AD-integrated zones (e.g., "Forest", "Domain", "Custom"). Passed to PowerShell.
    - `master_servers` (list, optional): For secondary zones, list of master server IP addresses. Passed to PowerShell.
    - `allow_updates` (string, optional): For primary zones, e.g., "None", "Secure", "NonsecureAndSecure". Passed to PowerShell.
    - `aging` (boolean, optional): Enable or disable aging/scavenging. Passed to PowerShell.
    - `no_refresh_interval` (string, optional): e.g., "7.00:00:00" (7 days). Passed to PowerShell.
    - `refresh_interval` (string, optional): e.g., "7.00:00:00" (7 days). Passed to PowerShell.
    - `...` (any other parameters your `Manage-DnsZone.ps1` script accepts).

### `defaults/main.yml`
```yaml
# dns_zone_management_defaults_variable: "example_value"
# default_zone_type: "Primary" # Example default
```

## Dependencies

- May depend on the `common` role for pre-flight checks or shared utilities if implemented there.

## Example Playbook Usage

```yaml
- hosts: dns_servers
  roles:
    - role: dns_zone_management
      vars:
        current_target_domain_name: "example.com"
        # current_target_dns_server: "dc01.example.com" # Optional
        zones_to_process:
          - name: "example.com"
            action: "add"
            zone_type: "Primary"
            replication_scope: "Domain"
            allow_updates: "Secure"
          - name: "legacy.example.com"
            action: "verify"
          - name: "oldzone.example.com"
            action: "remove"
```

## PowerShell Scripts

- **`files/Manage-DnsZone.ps1`**: Handles add, update, remove operations.
- **`files/Verify-DnsZone.ps1`**: Handles verification of zone existence and properties.

These scripts should be robust, include error handling, and use comment-based help.

## Tasks

The `tasks/main.yml` is the main entry point. It iterates through `zones_to_process` and includes other task files (`add_zone.yml`, `update_zone.yml`, `remove_zone.yml`, `verify_zone.yml`) based on the `action` specified for each zone.