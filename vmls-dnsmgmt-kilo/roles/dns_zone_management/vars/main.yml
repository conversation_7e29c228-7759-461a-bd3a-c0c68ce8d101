---
# vars file for vmls-dnsmgmt-kilo/roles/dns_zone_management

# Variables defined here have higher precedence than defaults.
# These are typically for role-internal logic rather than user configuration.

# Example:
# powershell_script_path_zone: "Manage-DnsZone.ps1"
# powershell_verify_script_path_zone: "Verify-DnsZone.ps1"
# supported_zone_actions:
#   - "add"
#   - "update"
#   - "remove"
#   - "verify"