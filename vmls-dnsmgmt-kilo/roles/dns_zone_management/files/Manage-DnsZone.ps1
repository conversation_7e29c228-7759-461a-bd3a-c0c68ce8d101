<#
.SYNOPSIS
    Manages DNS zones (Add, Update, Remove).
.DESCRIPTION
    This script performs Add, Update, or Remove operations on DNS zones on a Windows DNS server.
    It is intended to be called by an Ansible playbook.
.PARAMETER Action
    Specifies the action to perform: "Add", "Update", "Remove".
.PARAMETER ZoneName
    Specifies the name of the DNS zone.
.PARAMETER ComputerName
    Specifies the DNS server to target. Defaults to the local machine if not provided.
.PARAMETER ZoneType
    For "Add" action: Specifies the type of zone (e.g., "Primary", "Secondary", "Stub").
.PARAMETER ReplicationScope
    For "Add" action (AD-integrated zones): Specifies the replication scope (e.g., "Forest", "Domain", "CustomToSpecificDcs").
.PARAMETER MasterServers
    For "Add" action (Secondary zones): Specifies the IP address(es) of master servers.
.PARAMETER AllowUpdate
    For "Add" or "Update" action (Primary zones): Specifies dynamic update settings ("None", "NonsecureAndSecure", "Secure").
.PARAMETER Aging
    For "Add" or "Update" action: $true to enable aging/scavenging, $false to disable.
.PARAMETER NoRefreshInterval
    For "Add" or "Update" action: Specifies the no-refresh interval (e.g., "7.00:00:00").
.PARAMETER RefreshInterval
    For "Add" or "Update" action: Specifies the refresh interval (e.g., "7.00:00:00").
.PARAMETER Force
    For "Remove" action: $true to force removal without confirmation.
.EXAMPLE
    .\Manage-DnsZone.ps1 -Action Add -ZoneName "newzone.example.com" -ZoneType Primary -ReplicationScope Domain -AllowUpdate Secure
.EXAMPLE
    .\Manage-DnsZone.ps1 -Action Update -ZoneName "existing.example.com" -AllowUpdate None
.EXAMPLE
    .\Manage-DnsZone.ps1 -Action Remove -ZoneName "oldzone.example.com" -Force $true
.NOTES
    Ensure the DnsServer module is available on the target machine.
    Error handling should be robust.
#>
param (
    [Parameter(Mandatory = $true)]
    [ValidateSet("Add", "Update", "Remove")]
    [string]$Action,

    [Parameter(Mandatory = $true)]
    [string]$ZoneName,

    [string]$ComputerName,

    # Parameters for Add/Update
    [string]$ZoneType, # Primary, Secondary, Stub, Forwarder (Forwarder is for Add-DnsServerForwarder)
    [string]$ReplicationScope, # Forest, Domain, Windows2000, CustomToSpecificDcs
    [string[]]$MasterServers, # For Secondary Zones
    [string]$AllowUpdate, # None, NonsecureAndSecure, Secure
    [bool]$Aging,
    [System.TimeSpan]$NoRefreshInterval,
    [System.TimeSpan]$RefreshInterval,

    # Parameters for Remove
    [bool]$Force = $false
)

$ErrorActionPreference = "Stop"

try {
    Write-Host "Starting DNS Zone Management Script..."
    Write-Host "Action: $Action"
    Write-Host "ZoneName: $ZoneName"
    if ($PSBoundParameters.ContainsKey('ComputerName')) { Write-Host "ComputerName: $ComputerName" }

    $commonParams = @{}
    if ($PSBoundParameters.ContainsKey('ComputerName')) {
        $commonParams.ComputerName = $ComputerName
    }

    switch ($Action) {
        "Add" {
            Write-Host "Performing ADD action for zone '$ZoneName'."
            $addParams = $commonParams.Clone() # Start with common params
            $addParams.Name = $ZoneName

            if ($PSBoundParameters.ContainsKey('ZoneType')) { $addParams.ZoneType = $ZoneType } else { $addParams.ZoneType = "Primary" } # Default if not provided
            if ($PSBoundParameters.ContainsKey('ReplicationScope')) { $addParams.ReplicationScope = $ReplicationScope } elseif ($addParams.ZoneType -eq "Primary") { $addParams.ReplicationScope = "Domain" } # Default for Primary

            if ($addParams.ZoneType -eq "Primary") {
                if ($PSBoundParameters.ContainsKey('AllowUpdate')) { $addParams.DynamicUpdate = $AllowUpdate } else { $addParams.DynamicUpdate = "Secure" } # Default for Primary
                if ($PSBoundParameters.ContainsKey('Aging')) { $addParams.Aging = $Aging }
                if ($PSBoundParameters.ContainsKey('NoRefreshInterval')) { $addParams.NoRefreshInterval = $NoRefreshInterval }
                if ($PSBoundParameters.ContainsKey('RefreshInterval')) { $addParams.RefreshInterval = $RefreshInterval }
                Add-DnsServerPrimaryZone @addParams
            }
            elseif ($addParams.ZoneType -eq "Secondary") {
                if (-not $PSBoundParameters.ContainsKey('MasterServers') -or $MasterServers.Count -eq 0) {
                    throw "MasterServers parameter is required for Secondary zones."
                }
                $addParams.MasterServers = $MasterServers
                if ($PSBoundParameters.ContainsKey('AllowUpdate')) { $addParams.DynamicUpdate = $AllowUpdate } # Though less common for secondaries
                Add-DnsServerSecondaryZone @addParams
            }
            elseif ($addParams.ZoneType -eq "Stub") {
                 if (-not $PSBoundParameters.ContainsKey('MasterServers') -or $MasterServers.Count -eq 0) {
                    throw "MasterServers parameter is required for Stub zones."
                }
                $addParams.MasterServers = $MasterServers
                Add-DnsServerStubZone @addParams
            }
            else {
                throw "Unsupported ZoneType '$($addParams.ZoneType)' for Add action."
            }
            Write-Host "Zone '$ZoneName' added successfully."
        }
        "Update" {
            Write-Host "Performing UPDATE action for zone '$ZoneName'."
            # Get-DnsServerZone will throw if zone doesn't exist, which is good.
            $zone = Get-DnsServerZone -Name $ZoneName @commonParams

            $setParams = $commonParams.Clone()
            $setParams.Name = $ZoneName
            $updateNeeded = $false

            if ($PSBoundParameters.ContainsKey('AllowUpdate') -and $zone.DynamicUpdate -ne $AllowUpdate) {
                $setParams.DynamicUpdate = $AllowUpdate
                $updateNeeded = $true
            }
            if ($PSBoundParameters.ContainsKey('Aging') -and $zone.Aging -ne $Aging) {
                $setParams.Aging = $Aging
                $updateNeeded = $true
            }
            if ($PSBoundParameters.ContainsKey('NoRefreshInterval') -and $zone.NoRefreshInterval -ne $NoRefreshInterval) {
                $setParams.NoRefreshInterval = $NoRefreshInterval
                $updateNeeded = $true
            }
            if ($PSBoundParameters.ContainsKey('RefreshInterval') -and $zone.RefreshInterval -ne $RefreshInterval) {
                $setParams.RefreshInterval = $RefreshInterval
                $updateNeeded = $true
            }
            # Add more updatable properties here (e.g., ZoneType, ReplicationScope if changeable, MasterServers for Secondary)

            if ($updateNeeded) {
                Write-Host "Applying updates to zone '$ZoneName' with parameters: $($setParams | Out-String)"
                # Note: Set-DnsServerPrimaryZone, Set-DnsServerSecondaryZone, etc. might be needed depending on zone type
                # This is a simplified example; a real script might need to check zone type first.
                if ($zone.ZoneType -eq "Primary") {
                    Set-DnsServerPrimaryZone @setParams
                } elseif ($zone.ZoneType -eq "Secondary") {
                    # Update MasterServers if provided and different
                    if ($PSBoundParameters.ContainsKey('MasterServers') ) {
                        # Logic to compare and update master servers for secondary zone
                        $currentMasterServers = (Get-DnsServerSecondaryZone -Name $ZoneName @commonParams).MasterServers
                        # This comparison might need to be more robust (order, duplicates)
                        if (-not (Compare-Object -ReferenceObject $currentMasterServers -DifferenceObject $MasterServers -PassThru -IncludeEqual).SideIndicator -contains "==") {
                             $setParams.MasterServers = $MasterServers
                        }
                    }
                    Set-DnsServerSecondaryZone @setParams
                }
                # Add similar for Stub zones if properties are updatable via Set-DnsServerStubZone

                Write-Host "Zone '$ZoneName' updated successfully."
            } else {
                Write-Host "No updates required for zone '$ZoneName' based on provided parameters."
            }
        }
        "Remove" {
            Write-Host "Performing REMOVE action for zone '$ZoneName'."
            $removeParams = $commonParams.Clone()
            $removeParams.Name = $ZoneName
            $removeParams.Force = $Force # Pass the Force parameter
            Remove-DnsServerZone @removeParams
            Write-Host "Zone '$ZoneName' removed successfully."
        }
        default {
            throw "Invalid action '$Action' specified."
        }
    }
    Write-Host "DNS Zone Management script completed successfully for action '$Action' on zone '$ZoneName'."
    exit 0
}
catch {
    Write-Error "Error during DNS Zone Management for action '$Action' on zone '$ZoneName': $($_.Exception.Message)"
    # Write-Error $_.ScriptStackTrace # For more detailed debugging if needed
    exit 1
}