<#
.SYNOPSIS
    Verifies the existence and optionally properties of a DNS zone.
.DESCRIPTION
    This script checks if a DNS zone exists on a Windows DNS server.
    It can optionally verify specific properties of the zone if provided.
    It is intended to be called by an Ansible playbook.
.PARAMETER ZoneName
    Specifies the name of the DNS zone to verify.
.PARAMETER ComputerName
    Specifies the DNS server to target. Defaults to the local machine if not provided.
.PARAMETER ExpectedProperties
    A PSCustomObject or Hashtable containing expected properties and their values.
    Example: @{ ZoneType = "Primary"; DynamicUpdate = "Secure" }
.EXAMPLE
    .\Verify-DnsZone.ps1 -ZoneName "example.com"
.EXAMPLE
    .\Verify-DnsZone.ps1 -ZoneName "secure.example.com" -ExpectedProperties @{ DynamicUpdate = "Secure"; AgingEnabled = $true }
.NOTES
    Ensure the DnsServer module is available on the target machine.
    The script exits with 0 if verification is successful, 1 otherwise.
#>
param (
    [Parameter(Mandatory = $true)]
    [string]$ZoneName,

    [string]$ComputerName,

    [object]$ExpectedProperties # Can be a hashtable or PSCustomObject
)

$ErrorActionPreference = "Continue" # Allow catching Get-DnsServerZone errors specifically
$ProgressPreference = "SilentlyContinue"

try {
    Write-Host "Starting DNS Zone Verification Script..."
    Write-Host "ZoneName: $ZoneName"
    if ($PSBoundParameters.ContainsKey('ComputerName')) { Write-Host "ComputerName: $ComputerName" }
    if ($PSBoundParameters.ContainsKey('ExpectedProperties')) { Write-Host "ExpectedProperties: $($ExpectedProperties | Out-String)" }

    $getParams = @{ Name = $ZoneName }
    if ($PSBoundParameters.ContainsKey('ComputerName')) {
        $getParams.ComputerName = $ComputerName
    }

    $zone = Get-DnsServerZone @getParams -ErrorAction SilentlyContinue # Capture error manually

    if ($null -eq $zone -or $LASTEXITCODE -ne 0 -or $Error[0]) { # Check if Get-DnsServerZone failed
        Write-Error "Zone '$ZoneName' not found or error retrieving it. Error record: $($Error[0].Exception.Message)"
        Clear-Error
        exit 1
    }

    Write-Host "Zone '$ZoneName' found."
    $propertiesMatch = $true # Assume properties match until proven otherwise

    if ($PSBoundParameters.ContainsKey('ExpectedProperties') -and $null -ne $ExpectedProperties) {
        Write-Host "Verifying expected properties..."
        # Ensure ExpectedProperties is a hashtable for easier iteration
        if ($ExpectedProperties -isnot [System.Collections.Hashtable]) {
            # Attempt to convert if it's PSCustomObject, otherwise fail
            try {
                $propsToVerify = @{}
                $ExpectedProperties.PSObject.Properties | ForEach-Object { $propsToVerify[$_.Name] = $_.Value }
            }
            catch {
                Write-Error "Could not convert ExpectedProperties to a Hashtable. Please provide a Hashtable or PSCustomObject."
                exit 1
            }
        } else {
            $propsToVerify = $ExpectedProperties
        }

        foreach ($key in $propsToVerify.Keys) {
            $expectedValue = $propsToVerify[$key]
            $actualValue = $null

            # Try to get the actual value from the zone object
            try {
                $actualValue = $zone.$key
            }
            catch {
                Write-Warning "Property '$key' not found on the zone object for '$ZoneName'."
                # Decide if a missing property is a failure
                # For now, let's consider it a mismatch if the property was expected.
                $propertiesMatch = $false
                Write-Error "Property '$key' expected but not found on zone object."
                continue # Next property
            }

            # Handle boolean string comparison carefully if Ansible passes "true"/"false" as strings
            if ($expectedValue -is [string] -and ($expectedValue -eq "true" -or $expectedValue -eq "false") -and $actualValue -is [bool]) {
                $expectedValueBool = [System.Convert]::ToBoolean($expectedValue)
                if ($actualValue -ne $expectedValueBool) {
                    $propertiesMatch = $false
                    Write-Error "Property '$key' mismatch for zone '$ZoneName'. Expected: '$expectedValue' (as bool $expectedValueBool), Actual: '$actualValue'."
                } else {
                    Write-Host "Property '$key' matches. Expected: '$expectedValue', Actual: '$actualValue'."
                }
            }
            # Handle TimeSpan string comparison
            elseif ($expectedValue -is [string] -and $actualValue -is [System.TimeSpan]) {
                try {
                    $expectedTimeSpan = [System.TimeSpan]::Parse($expectedValue)
                    if ($actualValue -ne $expectedTimeSpan) {
                        $propertiesMatch = $false
                        Write-Error "Property '$key' mismatch for zone '$ZoneName'. Expected: '$expectedValue' (as TimeSpan $expectedTimeSpan), Actual: '$actualValue'."
                    } else {
                        Write-Host "Property '$key' matches. Expected: '$expectedValue', Actual: '$actualValue'."
                    }
                } catch {
                    $propertiesMatch = $false
                    Write-Error "Could not parse expected value '$expectedValue' as TimeSpan for property '$key'."
                }
            }
            # General comparison
            elseif ($actualValue -ne $expectedValue) {
                # Case-insensitive comparison for strings
                if ($actualValue -is [string] -and $expectedValue -is [string] -and $actualValue.Equals($expectedValue, [System.StringComparison]::OrdinalIgnoreCase)) {
                     Write-Host "Property '$key' matches (case-insensitive). Expected: '$expectedValue', Actual: '$actualValue'."
                } else {
                    $propertiesMatch = $false
                    Write-Error "Property '$key' mismatch for zone '$ZoneName'. Expected: '$expectedValue' (Type: $($expectedValue.GetType().Name)), Actual: '$actualValue' (Type: $($actualValue.GetType().Name))."
                }
            } else {
                Write-Host "Property '$key' matches. Expected: '$expectedValue', Actual: '$actualValue'."
            }
        }
    }

    if (-not $propertiesMatch) {
        Write-Error "One or more expected properties did not match for zone '$ZoneName'."
        exit 1
    }

    Write-Host "DNS Zone Verification script completed successfully for zone '$ZoneName'."
    exit 0
}
catch {
    Write-Error "Unexpected error during DNS Zone Verification for zone '$ZoneName': $($_.Exception.Message)"
    # Write-Error $_.ScriptStackTrace # For more detailed debugging if needed
    exit 1
}