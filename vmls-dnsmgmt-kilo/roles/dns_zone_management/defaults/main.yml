---
# defaults file for vmls-dnsmgmt-kilo/roles/dns_zone_management

# Default values for variables used by the dns_zone_management role.
# These can be overridden in group_vars, host_vars, playbook vars, or extra-vars.

# Example default zone parameters (your PowerShell scripts should handle these if not provided)
# default_zone_type: "Primary"
# default_replication_scope: "Domain" # For AD-integrated zones
# default_allow_updates: "Secure" # For primary zones ("None", "NonsecureAndSecure", "Secure")
# default_zone_aging: false
# default_no_refresh_interval: "7.00:00:00" # 7 days
# default_refresh_interval: "7.00:00:00" # 7 days

# You might not need many defaults here if your dns_config.yml is comprehensive
# or if your PowerShell scripts have their own internal defaults.