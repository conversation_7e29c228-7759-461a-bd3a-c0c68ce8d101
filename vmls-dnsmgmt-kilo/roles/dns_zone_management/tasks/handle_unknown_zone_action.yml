---
# tasks file for handling unknown zone actions
# Called from main.yml if a specific action file (e.g., add_zone.yml) is not found.
# Expects 'zone_item'.

- name: "Handle unknown or unsupported zone action: {{ zone_item.action }} for {{ zone_item.name | default(current_target_domain_name) }}"
  ansible.builtin.fail:
    msg: "Unsupported action '{{ zone_item.action }}' specified for DNS zone '{{ zone_item.name | default(current_target_domain_name) }}'. Please use 'add', 'update', 'remove', or 'verify'."