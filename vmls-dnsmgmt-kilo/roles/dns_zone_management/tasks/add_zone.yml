---
# tasks file for adding a DNS zone
# Called from main.yml when zone_item.action == 'add'
# Expects 'zone_item', 'current_target_domain_name', 'current_target_dns_server' (optional)

- name: "Add DNS Zone: {{ zone_item.name | default(current_target_domain_name) }}"
  ansible.builtin.debug:
    msg: |
      Action: ADD ZONE
      Zone Name: {{ zone_item.name | default(current_target_domain_name) }}
      Target Server: {{ current_target_dns_server | default("localhost (or as per PowerShell script default)") }}
      Zone Type: {{ zone_item.zone_type | default('Primary') }}
      Replication Scope: {{ zone_item.replication_scope | default('Domain') }}
      Details: {{ zone_item }}
  when: ansible_verbosity > 0

# Placeholder for PowerShell script execution
- name: "EXECUTE PowerShell script to ADD zone {{ zone_item.name | default(current_target_domain_name) }}"
  ansible.windows.win_powershell:
    script: |
      # This is a placeholder for your PowerShell script content or call
      # Example:
      # $ErrorActionPreference = "Stop"
      # Import-Module DnsServer # Ensure module is available
      #
      # $params = @{
      #     Name = "{{ zone_item.name | default(current_target_domain_name) }}"
      #     ZoneType = "{{ zone_item.zone_type | default('Primary') }}"
      #     ReplicationScope = "{{ zone_item.replication_scope | default('Domain') }}"
      #     # Add other parameters based on zone_item like AllowUpdates, MasterServers etc.
      # }
      # if ("{{ current_target_dns_server | default('') }}" -ne "") {
      #     $params.ComputerName = "{{ current_target_dns_server }}"
      # }
      # if ("{{ zone_item.allow_updates | default('') }}" -ne "") {
      #     $params.AllowUpdate = "{{ zone_item.allow_updates }}"
      # }
      # # ... and so on for other optional parameters from zone_item
      #
      # Write-Host "Attempting to add DNS zone with parameters: $($params | Out-String)"
      # Add-DnsServerPrimaryZone @params # Or Add-DnsServerSecondaryZone etc.
      # Write-Host "DNS Zone {{ zone_item.name | default(current_target_domain_name) }} added successfully."
    error_action: stop # Stop playbook on error
    # register: add_zone_result
  vars:
    # Define any specific variables needed for the script here
    ps_zone_name: "{{ zone_item.name | default(current_target_domain_name) }}"
    ps_zone_type: "{{ zone_item.zone_type | default('Primary') }}" # Ensure your PS script handles this
    # ... map other zone_item attributes to PowerShell script parameters
  # changed_when: add_zone_result.changed # Define based on script output
  # failed_when: add_zone_result.rc != 0 or "ERROR:" in add_zone_result.stderr # Example
  # notify: # Example: notify a handler if needed
    # - Flush DNS Cache # Example of a valid handler name

- name: "Log result of adding zone {{ zone_item.name | default(current_target_domain_name) }}"
  ansible.builtin.debug:
    # var: add_zone_result # If registered
    msg: "Placeholder: Add zone task completed for {{ ps_zone_name }}. Implement result logging."
  when: ansible_verbosity > 0