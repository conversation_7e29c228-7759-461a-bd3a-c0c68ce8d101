---
# tasks file for vmls-dnsmgmt-kilo/roles/dns_zone_management
# Entry point for managing DNS zones.
# Expects 'zones_to_process' (list of zone operations)
# and 'current_target_domain_name', 'current_target_dns_server' (optional) as input variables.

- name: Assert that zones_to_process is a list
  ansible.builtin.assert:
    that:
      - zones_to_process is defined
      - zones_to_process is list
    fail_msg: "'zones_to_process' must be a list."
    quiet: true
  when: zones_to_process is defined

- name: "Loop through each zone operation in 'zones_to_process'"
  ansible.builtin.include_tasks: "{{ lookup('ansible.builtin.first_found', params) }}"
  loop: "{{ zones_to_process }}"
  loop_control:
    loop_var: zone_item # Each item in the zones_to_process list
  vars:
    params:
      files:
        # Try to find a task file matching the action, e.g., add_zone.yml, verify_zone.yml
        - "{{ zone_item.action | lower }}_zone.yml"
        # Fallback to a generic handler or error if action-specific file not found
        - "handle_unknown_zone_action.yml"
      paths:
        - "./" # Look in the current tasks directory
  when: zones_to_process is defined and zones_to_process | length > 0

# Example: handle_unknown_zone_action.yml (create this file if you want a fallback)
# ---
# - name: "Handle unknown or unsupported zone action: {{ zone_item.action }} for {{ zone_item.name }}"
#   ansible.builtin.fail:
#     msg: "Unsupported action '{{ zone_item.action }}' specified for DNS zone '{{ zone_item.name }}'."