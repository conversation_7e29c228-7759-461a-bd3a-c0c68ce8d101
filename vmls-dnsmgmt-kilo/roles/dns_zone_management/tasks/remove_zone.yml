---
# tasks file for removing a DNS zone
# Called from main.yml when zone_item.action == 'remove'
# Expects 'zone_item', 'current_target_domain_name', 'current_target_dns_server' (optional)

- name: "Remove DNS Zone: {{ zone_item.name | default(current_target_domain_name) }}"
  ansible.builtin.debug:
    msg: |
      Action: REMOVE ZONE
      Zone Name: {{ zone_item.name | default(current_target_domain_name) }}
      Target Server: {{ current_target_dns_server | default("localhost (or as per PowerShell script default)") }}
      Force: {{ zone_item.force | default(false) }} # Example: if your script supports a force parameter
  when: ansible_verbosity > 0

# Placeholder for PowerShell script execution
- name: "EXECUTE PowerShell script to REMOVE zone {{ zone_item.name | default(current_target_domain_name) }}"
  ansible.windows.win_powershell:
    script: |
      # This is a placeholder for your PowerShell script content or call
      # Example:
      # $ErrorActionPreference = "Stop"
      # Import-Module DnsServer
      #
      # $params = @{
      #     Name = "{{ zone_item.name | default(current_target_domain_name) }}"
      #     Force = {{ zone_item.force | default('$false') }} # Ensure boolean is correctly passed to PS
      #     # PassThru = $true # If you want the object back
      # }
      # if ("{{ current_target_dns_server | default('') }}" -ne "") {
      #     $params.ComputerName = "{{ current_target_dns_server }}"
      # }
      #
      # Write-Host "Attempting to remove DNS zone with parameters: $($params | Out-String)"
      # Remove-DnsServerZone @params
      # Write-Host "DNS Zone {{ zone_item.name | default(current_target_domain_name) }} removed successfully."
    error_action: stop
    # register: remove_zone_result
  vars:
    ps_zone_name: "{{ zone_item.name | default(current_target_domain_name) }}"
    ps_force_remove: "{{ zone_item.force | default(false) }}" # Pass as boolean
  # changed_when: remove_zone_result.changed
  # failed_when: remove_zone_result.rc != 0

- name: "Log result of removing zone {{ zone_item.name | default(current_target_domain_name) }}"
  ansible.builtin.debug:
    # var: remove_zone_result # If registered
    msg: "Placeholder: Remove zone task completed for {{ ps_zone_name }}. Implement result logging."
  when: ansible_verbosity > 0