---
# tasks file for verifying a DNS zone
# Called from main.yml when zone_item.action == 'verify'
# Expects 'zone_item', 'current_target_domain_name', 'current_target_dns_server' (optional)

- name: "Verify DNS Zone: {{ zone_item.name | default(current_target_domain_name) }}"
  ansible.builtin.debug:
    msg: |
      Action: VERIFY ZONE
      Zone Name: {{ zone_item.name | default(current_target_domain_name) }}
      Target Server: {{ current_target_dns_server | default("localhost (or as per PowerShell script default)") }}
      Expected Properties: {{ zone_item.expected_properties | default('N/A') }} # Example: if verifying specific properties
  when: ansible_verbosity > 0

# Placeholder for PowerShell script execution
- name: "EXECUTE PowerShell script to VERIFY zone {{ zone_item.name | default(current_target_domain_name) }}"
  ansible.windows.win_powershell:
    script: |
      # This is a placeholder for your PowerShell script content or call
      # Example:
      # $ErrorActionPreference = "Stop" # Or "Continue" if you want to parse errors
      # Import-Module DnsServer
      #
      # $zoneName = "{{ zone_item.name | default(current_target_domain_name) }}"
      # $computerName = "{{ current_target_dns_server | default('') }}"
      #
      # $getParams = @{ Name = $zoneName }
      # if ($computerName -ne "") { $getParams.ComputerName = $computerName }
      #
      # try {
      #     $zone = Get-DnsServerZone @getParams
      #     if ($null -eq $zone) {
      #         Write-Error "Zone '$zoneName' not found."
      #         exit 1 # Indicate failure
      #     }
      #     Write-Host "Zone '$zoneName' found."
      #     # Add checks for expected_properties if provided in zone_item
      #     # For example:
      #     # if ("{{ zone_item.expected_properties.ZoneType | default('') }}" -ne "" -and $zone.ZoneType -ne "{{ zone_item.expected_properties.ZoneType }}") {
      #     #     Write-Error "ZoneType mismatch for '$zoneName'. Expected '{{ zone_item.expected_properties.ZoneType }}', got '$($zone.ZoneType)'."
      #     #     exit 1
      #     # }
      #     # Convert specific properties to JSON for easy parsing in Ansible if needed
      #     # $zone | Select-Object ZoneName, ZoneType, ReplicationScope, IsReverseLookupZone | ConvertTo-Json
      #     exit 0 # Indicate success
      # }
      # catch {
      #     Write-Error "Error verifying zone '$zoneName': $($_.Exception.Message)"
      #     exit 1 # Indicate failure
      # }
    error_action: continue # Allow Ansible to check the result
    register: verify_zone_result
  vars:
    ps_zone_name: "{{ zone_item.name | default(current_target_domain_name) }}"
    # Pass expected_properties if your script uses them
    # ps_expected_properties: "{{ zone_item.expected_properties | default({}) }}"

- name: "Evaluate verification result for zone {{ ps_zone_name }}"
  ansible.builtin.assert:
    that:
      - verify_zone_result.rc == 0
      # Add more conditions based on stdout/stderr if your script provides specific success/failure indicators
      # - "'Zone ''#{ps_zone_name}'' found.' in verify_zone_result.stdout" # Example
    fail_msg: "Verification failed for DNS zone '{{ ps_zone_name }}'. Script output: {{ verify_zone_result.stdout }} Errors: {{ verify_zone_result.stderr }}"
    success_msg: "DNS zone '{{ ps_zone_name }}' verified successfully."
  # changed_when: false # Verification tasks usually don't change state

- name: "Log result of verifying zone {{ ps_zone_name }}"
  ansible.builtin.debug:
    var: verify_zone_result
  when: ansible_verbosity > 1 # Show full result only on higher verbosity