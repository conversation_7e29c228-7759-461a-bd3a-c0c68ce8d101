# Ansible Role: common

## Description

This role provides common tasks, handlers, or default variables that might be used by multiple other roles within the `vmls-dnsmgmt-kilo` project.

For example, it could include:
- Common pre-flight checks.
- Setup of common tools or configurations if needed on target hosts (though less likely for pure DNS management via PowerShell).
- Common notification handlers.

## Requirements

None specific beyond Ansible itself.

## Role Variables

This role currently does not define its own specific variables in `defaults/main.yml` or `vars/main.yml` that are intended for direct user configuration. It may use internal variables.

### `defaults/main.yml`
```yaml
# common_defaults_variable: "example_value"
```

## Dependencies

None.

## Example Playbook

This role is typically included as a dependency by other roles or called directly in a playbook if needed.

```yaml
- hosts: dns_servers
  roles:
    - role: common
      # common_param: "some_value" # If the role accepted parameters
```

## Tasks

The `tasks/main.yml` is the entry point. Currently, it's a placeholder.

## Handlers

The `handlers/main.yml` is the entry point for handlers. Currently, it's a placeholder.