# Ansible Role: dns_record_management

## Description

This role manages DNS records (A, PTR, CNAME, etc.) within specified DNS zones. It uses PowerShell scripts located in its `files/` directory for these operations, orchestrated by Ansible tasks.

## Requirements

- Target hosts must be Windows DNS servers (or hosts capable of remotely managing them via PowerShell).
- PowerShell 5.0 or higher is recommended on target hosts.
- WinRM must be configured on target hosts for Ansible connection.
- The PowerShell scripts (e.g., `Manage-DnsRecord.ps1`, `Verify-DnsRecord.ps1`) in the `files/` directory of this role must be present and functional.

## Role Variables

This role expects the following variables to be passed to it:

- `current_target_domain_name` (string, mandatory): The primary domain name context. Often used as the default zone name if a specific record item doesn't specify one.
- `current_target_dns_server` (string, optional): The FQDN of the specific DNS server to target. If omitted, PowerShell scripts should target the local machine or use their own logic.
- `records_to_process` (list, mandatory): A list of dictionaries, where each dictionary defines a DNS record operation.
    - `type` (string, mandatory): The type of DNS record (e.g., "A", "AAAA", "PTR", "CNAME", "MX", "TXT", "SRV").
    - `name` (string, mandatory): The name of the record (e.g., "www", "mail", "_sip._tcp"). For PTR records, this is typically the IP address part (e.g., "10" for a PTR in 1.168.192.in-addr.arpa for ************).
    - `zone` (string, mandatory): The FQDN of the DNS zone where the record resides (e.g., "example.com"). If `current_target_domain_name` is passed, this can often default to it if not specified per record, but explicit is better.
    - `value` (string, mandatory for most types): The value of the record.
        - For A/AAAA: IP address.
        - For CNAME: FQDN of the alias target.
        - For PTR: FQDN of the host.
        - For MX: FQDN of the mail exchange server. (Also needs `preference`)
        - For TXT: Text string(s).
        - For SRV: FQDN of the service host. (Also needs `priority`, `weight`, `port`)
    - `action` (string, mandatory): The operation: "add", "update", "remove", "verify".
    - `ttl` (string or int, optional): Time To Live for the record (e.g., "01:00:00" or 3600).
    - `old_value` (string, optional): For "update" action, the current value of the record if needed by the script to identify the specific record to update (especially if multiple records with the same name but different values exist, though less common for A/CNAME).
    - `ptr_create` (boolean, optional, for A/AAAA records): If `true`, attempt to create a corresponding PTR record. The PowerShell script must support this.
    - `allow_duplicate` (boolean, optional, for "add"): If `true`, allow adding the record even if an identical one exists. Default is usually to not add if identical.
    - **For MX records:**
        - `preference` (int, mandatory for MX): Mail exchange preference.
    - **For SRV records:**
        - `priority` (int, mandatory for SRV): Service priority.
        - `weight` (int, mandatory for SRV): Service weight.
        - `port` (int, mandatory for SRV): Service port.
    - `...` (any other parameters your `Manage-DnsRecord.ps1` script accepts for specific record types).

### `defaults/main.yml`
```yaml
# default_record_ttl: "01:00:00" # Example: 1 hour
# create_ptr_by_default: false
```

## Dependencies

- May depend on the `common` role.

## Example Playbook Usage

```yaml
- hosts: dns_servers
  roles:
    - role: dns_record_management
      vars:
        current_target_domain_name: "example.com" # Used as default zone if record.zone not set
        records_to_process:
          - type: "A"
            name: "server1"
            zone: "example.com"
            value: "************"
            action: "add"
            ttl: 3600
            ptr_create: true
          - type: "CNAME"
            name: "web"
            zone: "example.com"
            value: "server1.example.com"
            action: "add"
          - type: "A"
            name: "legacyapp"
            zone: "example.com"
            value: "************" # Old IP
            new_value: "************" # New IP for update action
            action: "update"
          - type: "MX"
            name: "" # Typically empty for domain's MX record
            zone: "example.com"
            value: "mail.example.com"
            preference: 10
            action: "add"
          - type: "A"
            name: "oldserver"
            zone: "example.com"
            value: "************" # Value needed for removal if script requires it
            action: "remove"
```

## PowerShell Scripts

- **`files/Manage-DnsRecord.ps1`**: Handles add, update, remove operations for various record types.
- **`files/Verify-DnsRecord.ps1`**: Handles verification of record existence and properties.

## Tasks

The `tasks/main.yml` iterates through `records_to_process` and includes other task files (e.g., `add_record.yml`, `update_record.yml`) based on the `action` and potentially `type`.