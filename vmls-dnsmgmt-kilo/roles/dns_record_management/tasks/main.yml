---
# tasks file for vmls-dnsmgmt-kilo/roles/dns_record_management
# Entry point for managing DNS records.
# Expects 'records_to_process' (list of record operations)
# and 'current_target_domain_name', 'current_target_dns_server' (optional) as input variables.

- name: Assert that records_to_process is a list
  ansible.builtin.assert:
    that:
      - records_to_process is defined
      - records_to_process is list
    fail_msg: "'records_to_process' must be a list."
    quiet: true
  when: records_to_process is defined

- name: "Loop through each record operation in 'records_to_process'"
  ansible.builtin.include_tasks: "{{ lookup('ansible.builtin.first_found', params) }}"
  loop: "{{ records_to_process }}"
  loop_control:
    loop_var: record_item # Each item in the records_to_process list
  vars:
    params:
      files:
        # Try to find a task file matching the action, e.g., add_record.yml, verify_record.yml
        # You could also make this more specific like:
        # - "{{ record_item.action | lower }}_{{ record_item.type | lower }}_record.yml" (e.g. add_a_record.yml)
        # - "{{ record_item.action | lower }}_record.yml" (generic action file)
        - "{{ record_item.action | lower }}_record.yml" # e.g. add_record.yml, update_record.yml
        # Fallback to a generic handler or error if action-specific file not found
        - "handle_unknown_record_action.yml"
      paths:
        - "./" # Look in the current tasks directory
  when: records_to_process is defined and records_to_process | length > 0

# Example: handle_unknown_record_action.yml (create this file if you want a fallback)
# ---
# - name: "Handle unknown or unsupported record action: {{ record_item.action }} for {{ record_item.name }} in zone {{ record_item.zone }}"
#   ansible.builtin.fail:
#     msg: "Unsupported action '{{ record_item.action }}' specified for DNS record '{{ record_item.name }}' (Type: {{ record_item.type }}) in zone '{{ record_item.zone | default(current_target_domain_name) }}'."