---
# tasks file for removing a DNS record
# Called from main.yml when record_item.action == 'remove'
# Expects 'record_item', 'current_target_domain_name', 'current_target_dns_server' (optional)

- name: "Remove DNS Record: {{ record_item.name }} ({{ record_item.type }}) in zone {{ record_item.zone | default(current_target_domain_name) }}"
  ansible.builtin.debug:
    msg: |
      Action: REMOVE RECORD
      Name: {{ record_item.name }}
      Type: {{ record_item.type }}
      Zone: {{ record_item.zone | default(current_target_domain_name) }}
      Value (to identify if needed): {{ record_item.value | default('N/A (<PERSON><PERSON><PERSON> might not need it or find by name/type)') }}
      Target Server: {{ current_target_dns_server | default("localhost (or as per PowerShell script default)") }}
      Force: {{ record_item.force | default(false) }}
      Details: {{ record_item }}
  when: ansible_verbosity > 0

# PowerShell script execution
- name: "EXECUTE PowerShell script to REMOVE record {{ record_item.name }} ({{ record_item.type }})"
  ansible.windows.win_powershell:
    script: |
      # This placeholder assumes Manage-DnsRecord.ps1 handles the remove logic.
      $params = @{
          Action = "Remove"
          ZoneName = "{{ record_item.zone | default(current_target_domain_name) }}"
          Name = "{{ record_item.name }}"
          RecordType = "{{ record_item.type }}"
      }
      # Value might be needed to identify the specific record to remove if multiple exist (e.g. TXT, some MX)
      if ("{{ record_item.value | default('') }}" -ne "") { $params.Value = "{{ record_item.value }}" }
      if ("{{ current_target_dns_server | default('') }}" -ne "") { $params.ComputerName = "{{ current_target_dns_server }}" }
      if ($PSBoundParameters.ContainsKey('record_item.force')) { $params.Force = {{ record_item.force | bool }} } else { $params.Force = $false }


      # Add type-specific parameters if needed for removal (e.g. for specific MX or SRV records)
      if ("{{ record_item.type }}" -eq "MX") {
          if ("{{ record_item.preference | default('') }}" -ne "") { $params.Preference = {{ record_item.preference }} }
          # Value (MailExchange) is also important for MX
      }
      elseif ("{{ record_item.type }}" -eq "SRV") {
          # For SRV, all parts (priority, weight, port, value/target) might be needed to uniquely identify
          if ("{{ record_item.priority | default('') }}" -ne "") { $params.Priority = {{ record_item.priority }} }
          if ("{{ record_item.weight | default('') }}" -ne "") { $params.Weight = {{ record_item.weight }} }
          if ("{{ record_item.port | default('') }}" -ne "") { $params.Port = {{ record_item.port }} }
      }

      Write-Host "Simulating call to Manage-DnsRecord.ps1 for REMOVE with params:"
      $params | Out-String | Write-Host
      # .\Manage-DnsRecord.ps1 @params # Actual call

      # Placeholder for actual Remove-DnsServerResourceRecord command
      # This logic should be within Manage-DnsRecord.ps1
      # Remove-DnsServerResourceRecord -ZoneName $params.ZoneName -Name $params.Name -RRType $params.RecordType [-RecordData $params.Value] [-Force] ...
      Write-Host "DNS Record '{{ record_item.name }}' ({{ record_item.type }}) removed successfully (simulated)."
    error_action: stop
    # register: remove_record_result
  vars:
    ps_record_details: "{{ record_item }}"
  # changed_when: remove_record_result.changed
  # failed_when: remove_record_result.rc != 0

- name: "Log result of removing record {{ record_item.name }} ({{ record_item.type }})"
  ansible.builtin.debug:
    # var: remove_record_result # If registered
    msg: "Placeholder: Remove record task completed for {{ record_item.name }} ({{ record_item.type }}). Implement result logging."
  when: ansible_verbosity > 0