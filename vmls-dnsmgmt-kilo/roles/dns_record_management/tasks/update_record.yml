---
# tasks file for updating a DNS record
# Called from main.yml when record_item.action == 'update'
# Expects 'record_item', 'current_target_domain_name', 'current_target_dns_server' (optional)

- name: "Update DNS Record: {{ record_item.name }} ({{ record_item.type }}) in zone {{ record_item.zone | default(current_target_domain_name) }}"
  ansible.builtin.debug:
    msg: |
      Action: UPDATE RECORD
      Name: {{ record_item.name }}
      Type: {{ record_item.type }}
      Zone: {{ record_item.zone | default(current_target_domain_name) }}
      Old Value: {{ record_item.old_value | default('N/A (<PERSON><PERSON><PERSON> might not need it or find dynamically)') }}
      New Value: {{ record_item.value | default(record_item.new_value) }} # 'value' or 'new_value' for clarity
      TTL: {{ record_item.ttl | default('Default/NoChange') }}
      Target Server: {{ current_target_dns_server | default("localhost (or as per PowerShell script default)") }}
      Details: {{ record_item }}
  when: ansible_verbosity > 0

# PowerShell script execution
- name: "EXECUTE PowerShell script to UPDATE record {{ record_item.name }} ({{ record_item.type }})"
  ansible.windows.win_powershell:
    script: |
      # This placeholder assumes Manage-DnsRecord.ps1 handles the update logic.
      # It needs to identify the old record and replace it or modify it.
      $params = @{
          Action = "Update"
          ZoneName = "{{ record_item.zone | default(current_target_domain_name) }}"
          Name = "{{ record_item.name }}"
          RecordType = "{{ record_item.type }}"
          NewValue = "{{ record_item.value | default(record_item.new_value) }}" # Script should expect NewValue for updates
      }
      if ("{{ record_item.old_value | default('') }}" -ne "") { $params.OldValue = "{{ record_item.old_value }}" }
      if ("{{ record_item.ttl | default('') }}" -ne "") { $params.TTL = "{{ record_item.ttl }}" } # New TTL
      if ("{{ current_target_dns_server | default('') }}" -ne "") { $params.ComputerName = "{{ current_target_dns_server }}" }

      # Add type-specific parameters for update if they can be changed
      if ("{{ record_item.type }}" -eq "MX") {
          if ("{{ record_item.preference | default('') }}" -ne "") { $params.Preference = {{ record_item.preference }} }
          # OldPreference might be needed if updating based on that
      }
      elseif ("{{ record_item.type }}" -eq "SRV") {
          if ("{{ record_item.priority | default('') }}" -ne "") { $params.Priority = {{ record_item.priority }} }
          if ("{{ record_item.weight | default('') }}" -ne "") { $params.Weight = {{ record_item.weight }} }
          if ("{{ record_item.port | default('') }}" -ne "") { $params.Port = {{ record_item.port }} }
      }

      Write-Host "Simulating call to Manage-DnsRecord.ps1 for UPDATE with params:"
      $params | Out-String | Write-Host
      # .\Manage-DnsRecord.ps1 @params # Actual call

      # Placeholder for actual Set-DnsServerResourceRecord or Remove/Add logic
      # This logic should be within Manage-DnsRecord.ps1
      # For many record types, update means removing the old and adding the new.
      # Get-DnsServerResourceRecord -ZoneName $params.ZoneName -Name $params.Name -RRType $params.RecordType ... (to find old)
      # Remove-DnsServerResourceRecord ... (old record)
      # Add-DnsServerResourceRecord ... (new record with $params.NewValue and other new attributes)
      Write-Host "DNS Record '{{ record_item.name }}' ({{ record_item.type }}) updated successfully (simulated)."
    error_action: stop
    # register: update_record_result
  vars:
    ps_record_details: "{{ record_item }}"
  # changed_when: update_record_result.changed
  # failed_when: update_record_result.rc != 0

- name: "Log result of updating record {{ record_item.name }} ({{ record_item.type }})"
  ansible.builtin.debug:
    # var: update_record_result # If registered
    msg: "Placeholder: Update record task completed for {{ record_item.name }} ({{ record_item.type }}). Implement result logging."
  when: ansible_verbosity > 0