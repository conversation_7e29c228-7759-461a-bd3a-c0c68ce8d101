---
# tasks file for adding a DNS record
# Called from main.yml when record_item.action == 'add'
# Expects 'record_item', 'current_target_domain_name', 'current_target_dns_server' (optional)

- name: "Add DNS Record: {{ record_item.name }} ({{ record_item.type }}) in zone {{ record_item.zone | default(current_target_domain_name) }}"
  ansible.builtin.debug:
    msg: |
      Action: ADD RECORD
      Name: {{ record_item.name }}
      Type: {{ record_item.type }}
      Zone: {{ record_item.zone | default(current_target_domain_name) }}
      Value: {{ record_item.value | default('N/A') }}
      TTL: {{ record_item.ttl | default('Default') }}
      Target Server: {{ current_target_dns_server | default("localhost (or as per PowerShell script default)") }}
      Details: {{ record_item }}
  when: ansible_verbosity > 0

# PowerShell script execution
- name: "EXECUTE PowerShell script to ADD record {{ record_item.name }} ({{ record_item.type }})"
  ansible.windows.win_powershell:
    script: |
      # Reference the Manage-DnsRecord.ps1 script from the role's files directory
      # This assumes Manage-DnsRecord.ps1 is designed to handle various record types and actions.
      $scriptPath = Join-Path $PSScriptRoot "..\\files\\Manage-DnsRecord.ps1" # Adjust if PSScriptRoot is not reliable here
      # If $PSScriptRoot is not reliable in this context (e.g. script block execution),
      # Ansible might need to copy the script first or you embed a direct call.
      # For simplicity, assuming the script is accessible or copied by a prior step if needed.
      # For now, let's assume direct embedding or a known path for the script.
      # This placeholder will be refined when Manage-DnsRecord.ps1 is created.

      # Construct parameters for Manage-DnsRecord.ps1
      $params = @{
          Action = "Add"
          ZoneName = "{{ record_item.zone | default(current_target_domain_name) }}"
          Name = "{{ record_item.name }}"
          RecordType = "{{ record_item.type }}"
          Value = "{{ record_item.value | default('') }}" # Ensure empty string if not defined, script should handle
      }
      if ("{{ record_item.ttl | default('') }}" -ne "") { $params.TTL = "{{ record_item.ttl }}" }
      if ("{{ current_target_dns_server | default('') }}" -ne "") { $params.ComputerName = "{{ current_target_dns_server }}" }
      if ($PSBoundParameters.ContainsKey('record_item.ptr_create')) { $params.CreatePtr = {{ record_item.ptr_create | bool }} } # Pass boolean
      if ($PSBoundParameters.ContainsKey('record_item.allow_duplicate')) { $params.AllowDuplicate = {{ record_item.allow_duplicate | bool }} }

      # Add type-specific parameters
      if ("{{ record_item.type }}" -eq "MX") {
          if ("{{ record_item.preference | default('') }}" -ne "") { $params.Preference = {{ record_item.preference }} }
          else { Write-Error "MX record '{{ record_item.name }}' requires 'preference'."; exit 1 }
      }
      elseif ("{{ record_item.type }}" -eq "SRV") {
          if ("{{ record_item.priority | default('') }}" -ne "") { $params.Priority = {{ record_item.priority }} } else { Write-Error "SRV record '{{ record_item.name }}' requires 'priority'."; exit 1 }
          if ("{{ record_item.weight | default('') }}" -ne "") { $params.Weight = {{ record_item.weight }} } else { Write-Error "SRV record '{{ record_item.name }}' requires 'weight'."; exit 1 }
          if ("{{ record_item.port | default('') }}" -ne "") { $params.Port = {{ record_item.port }} } else { Write-Error "SRV record '{{ record_item.name }}' requires 'port'."; exit 1 }
      }
      # Add more elseif for other record types like TXT, AAAA etc.

      # This is a simplified call. Ideally, you'd call the external .ps1 script.
      # For now, simulating the logic that would be in Manage-DnsRecord.ps1 for 'Add'
      Write-Host "Simulating call to Manage-DnsRecord.ps1 with params:"
      $params | Out-String | Write-Host
      # .\Manage-DnsRecord.ps1 @params # This would be the actual call

      # Placeholder for actual Add-DnsServerResourceRecord command structure
      # This logic should be within Manage-DnsRecord.ps1
      $cmdletParams = @{ ZoneName = $params.ZoneName; Name = $params.Name }
      if ($params.ContainsKey('ComputerName')) { $cmdletParams.ComputerName = $params.ComputerName }
      if ($params.ContainsKey('TTL')) { $cmdletParams.TimeToLive = [System.TimeSpan]::Parse($params.TTL) } # Assuming TTL is string like "01:00:00"

      switch ($params.RecordType) {
          "A"     { Add-DnsServerResourceRecordA @cmdletParams -IPv4Address $params.Value }
          "CNAME" { Add-DnsServerResourceRecordCName @cmdletParams -HostNameAlias $params.Value }
          "PTR"   { Add-DnsServerResourceRecordPtr @cmdletParams -PtrDomainName $params.Value } # Name for PTR is IP part
          "MX"    { Add-DnsServerResourceRecordMX @cmdletParams -MailExchange $params.Value -Preference $params.Preference }
          # Add other types here
          default { Write-Error "Unsupported record type '$($params.RecordType)' in this placeholder."; exit 1 }
      }
      Write-Host "DNS Record '{{ record_item.name }}' ({{ record_item.type }}) added successfully (simulated)."
    error_action: stop
    # register: add_record_result
  vars:
    # This structure assumes the PowerShell script itself handles the logic based on RecordType
    # The Ansible task primarily passes all relevant details from record_item.
    ps_record_details: "{{ record_item }}"
  # changed_when: add_record_result.changed
  # failed_when: add_record_result.rc != 0

- name: "Log result of adding record {{ record_item.name }} ({{ record_item.type }})"
  ansible.builtin.debug:
    # var: add_record_result # If registered
    msg: "Placeholder: Add record task completed for {{ record_item.name }} ({{ record_item.type }}). Implement result logging."
  when: ansible_verbosity > 0