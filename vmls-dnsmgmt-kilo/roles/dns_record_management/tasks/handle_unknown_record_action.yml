---
# tasks file for handling unknown record actions
# Called from main.yml if a specific action file (e.g., add_record.yml) is not found.
# Expects 'record_item'.

- name: "Handle unknown or unsupported record action: {{ record_item.action }} for {{ record_item.name }} in zone {{ record_item.zone | default(current_target_domain_name) }}"
  ansible.builtin.fail:
    msg: "Unsupported action '{{ record_item.action }}' specified for DNS record '{{ record_item.name }}' (Type: {{ record_item.type }}) in zone '{{ record_item.zone | default(current_target_domain_name) }}'. Please use 'add', 'update', 'remove', or 'verify'."