---
# tasks file for verifying a DNS record
# Called from main.yml when record_item.action == 'verify'
# Expects 'record_item', 'current_target_domain_name', 'current_target_dns_server' (optional)

- name: "Verify DNS Record: {{ record_item.name }} ({{ record_item.type }}) in zone {{ record_item.zone | default(current_target_domain_name) }}"
  ansible.builtin.debug:
    msg: |
      Action: VERIFY RECORD
      Name: {{ record_item.name }}
      Type: {{ record_item.type }}
      Zone: {{ record_item.zone | default(current_target_domain_name) }}
      Expected Value: {{ record_item.value | default('N/A (Existence check only or script handles)') }}
      Expected TTL: {{ record_item.ttl | default('N/A') }}
      Target Server: {{ current_target_dns_server | default("localhost (or as per PowerShell script default)") }}
      Details: {{ record_item.expected_properties | default(record_item) }}
  when: ansible_verbosity > 0

# PowerShell script execution
- name: "EXECUTE PowerShell script to VERIFY record {{ record_item.name }} ({{ record_item.type }})"
  ansible.windows.win_powershell:
    script: |
      # This placeholder assumes Verify-DnsRecord.ps1 handles the verification logic.
      $params = @{
          ZoneName = "{{ record_item.zone | default(current_target_domain_name) }}"
          Name = "{{ record_item.name }}"
          RecordType = "{{ record_item.type }}"
      }
      # Pass expected values if provided in record_item
      if ($PSBoundParameters.ContainsKey('record_item.value')) { $params.ExpectedValue = "{{ record_item.value }}" }
      if ($PSBoundParameters.ContainsKey('record_item.ttl')) { $params.ExpectedTTL = "{{ record_item.ttl }}" }
      if ("{{ current_target_dns_server | default('') }}" -ne "") { $params.ComputerName = "{{ current_target_dns_server }}" }

      # For MX, SRV, TXT, etc., you might pass more specific expected properties
      if ("{{ record_item.type }}" -eq "MX") {
          if ($PSBoundParameters.ContainsKey('record_item.preference')) { $params.ExpectedPreference = {{ record_item.preference }} }
          # ExpectedValue for MX is the MailExchange FQDN
      }
      elseif ("{{ record_item.type }}" -eq "SRV") {
          if ($PSBoundParameters.ContainsKey('record_item.priority')) { $params.ExpectedPriority = {{ record_item.priority }} }
          if ($PSBoundParameters.ContainsKey('record_item.weight')) { $params.ExpectedWeight = {{ record_item.weight }} }
          if ($PSBoundParameters.ContainsKey('record_item.port')) { $params.ExpectedPort = {{ record_item.port }} }
          # ExpectedValue for SRV is the Target FQDN
      }
      elseif ("{{ record_item.type }}" -eq "TXT") {
          # For TXT, ExpectedValue might be an array of strings or a single string
          # The PowerShell script needs to handle this.
          if ($PSBoundParameters.ContainsKey('record_item.value')) { $params.ExpectedStrings = @("{{ record_item.value }}") } # Simplistic, script needs to be robust
      }

      Write-Host "Simulating call to Verify-DnsRecord.ps1 with params:"
      $params | Out-String | Write-Host
      # .\Verify-DnsRecord.ps1 @params # Actual call

      # Placeholder for actual Get-DnsServerResourceRecord and comparison logic
      # This logic should be within Verify-DnsRecord.ps1
      # $record = Get-DnsServerResourceRecord -ZoneName $params.ZoneName -Name $params.Name -RRType $params.RecordType ...
      # if ($null -eq $record) { Write-Error "Record not found."; exit 1 }
      # Compare $record properties with $params.ExpectedValue, ExpectedTTL, etc.
      Write-Host "DNS Record '{{ record_item.name }}' ({{ record_item.type }}) verified successfully (simulated)."
      exit 0 # Simulate success
    error_action: continue # Allow Ansible to check the result
    register: verify_record_result
  vars:
    ps_record_details: "{{ record_item }}"

- name: "Evaluate verification result for record {{ record_item.name }} ({{ record_item.type }})"
  ansible.builtin.assert:
    that:
      - verify_record_result.rc == 0
      # Add more specific checks based on stdout if your script provides clear success messages
      # - "'verified successfully' in verify_record_result.stdout"
    fail_msg: "Verification FAILED for DNS record '{{ record_item.name }}' ({{ record_item.type }}) in zone '{{ record_item.zone | default(current_target_domain_name) }}'. Script output: {{ verify_record_result.stdout }} Errors: {{ verify_record_result.stderr }}"
    success_msg: "DNS record '{{ record_item.name }}' ({{ record_item.type }}) in zone '{{ record_item.zone | default(current_target_domain_name) }}' verified successfully."
  # changed_when: false # Verification tasks usually don't change state

- name: "Log result of verifying record {{ record_item.name }} ({{ record_item.type }})"
  ansible.builtin.debug:
    var: verify_record_result
  when: ansible_verbosity > 1