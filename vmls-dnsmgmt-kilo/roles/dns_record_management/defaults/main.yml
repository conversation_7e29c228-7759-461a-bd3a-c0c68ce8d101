---
# defaults file for vmls-dnsmgmt-kilo/roles/dns_record_management

# Default values for variables used by the dns_record_management role.

# Example default record parameters:
# default_record_ttl: "01:00:00"  # 1 hour (e.g., PT1H for Add-DnsServerResourceRecord, or a string your PS script parses)
# default_ptr_create: false # Whether to attempt PTR record creation for A/AAAA records by default
# default_allow_duplicate_on_add: false # Whether to allow adding a record if an identical one exists

# These defaults are useful if not every record_item in your dns_config.yml specifies all attributes,
# and you want consistent fallback behavior. Your PowerShell scripts should also be designed
# to handle missing optional parameters gracefully or use their own internal defaults.