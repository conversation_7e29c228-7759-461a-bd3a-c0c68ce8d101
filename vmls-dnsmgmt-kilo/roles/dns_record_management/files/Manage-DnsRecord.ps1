<#
.SYNOPSIS
    Manages DNS resource records (Add, Update, Remove).
.DESCRIPTION
    This script performs Add, Update, or Remove operations on DNS resource records
    (A, AAAA, CNAME, PTR, MX, TXT, SRV, etc.) on a Windows DNS server.
    It is intended to be called by an Ansible playbook.
.PARAMETER Action
    Specifies the action to perform: "Add", "Update", "Remove".
.PARAMETER ZoneName
    Specifies the name of the DNS zone where the record resides.
.PARAMETER Name
    Specifies the name of the DNS record. For PTR, this is the IP address part.
.PARAMETER RecordType
    Specifies the type of DNS record (e.g., "A", "CNAME", "MX", "SRV", "TXT", "PTR").
.PARAMETER Value
    Specifies the value of the DNS record.
    - For A/AAAA: IPv4/IPv6 address.
    - For CNAME: Target FQDN.
    - For PTR: Target FQDN.
    - For MX: Mail Exchanger FQDN.
    - For TXT: String data (can be an array of strings for multiple TXT strings in one record).
    - For SRV: Target FQDN.
.PARAMETER NewValue
    For "Update" action: Specifies the new value for the record. If not provided, "Value" is used as NewValue.
.PARAMETER OldValue
    For "Update" action (optional): Specifies the old value of the record, to help identify the specific record to update if multiple records with the same name/type exist.
.PARAMETER TTL
    Specifies the Time To Live for the record (e.g., "01:00:00" or an integer in seconds).
.PARAMETER ComputerName
    Specifies the DNS server to target. Defaults to the local machine.
.PARAMETER CreatePtr
    For "Add" A/AAAA action ($true/$false): If true, attempts to create a corresponding PTR record.
.PARAMETER AllowDuplicate
    For "Add" action ($true/$false): If true, allows adding the record even if an identical one exists.
.PARAMETER Force
    For "Remove" action ($true/$false): If true, forces removal without prompting (if applicable to cmdlet).
.PARAMETER Preference
    For MX records: Specifies the preference value (integer).
.PARAMETER Priority
    For SRV records: Specifies the priority value (integer).
.PARAMETER Weight
    For SRV records: Specifies the weight value (integer).
.PARAMETER Port
    For SRV records: Specifies the port number (integer).
.EXAMPLE
    .\Manage-DnsRecord.ps1 -Action Add -ZoneName example.com -Name www -RecordType A -Value "*************" -TTL "00:05:00"
.EXAMPLE
    .\Manage-DnsRecord.ps1 -Action Update -ZoneName example.com -Name www -RecordType A -OldValue "*************" -NewValue "192.168.1.101"
.EXAMPLE
    .\Manage-DnsRecord.ps1 -Action Remove -ZoneName example.com -Name temp -RecordType A -Value "********" -Force $true
.NOTES
    Ensure the DnsServer module is available. Robust error handling is crucial.
    Update operations often involve removing the old record and adding the new one.
#>
param (
    [Parameter(Mandatory = $true)]
    [ValidateSet("Add", "Update", "Remove")]
    [string]$Action,

    [Parameter(Mandatory = $true)]
    [string]$ZoneName,

    [Parameter(Mandatory = $true)]
    [string]$Name, # For PTR, this is the host part of the IP, e.g., "100" for *************

    [Parameter(Mandatory = $true)]
    [ValidateSet("A", "AAAA", "CNAME", "PTR", "MX", "TXT", "SRV")] # Extend as needed
    [string]$RecordType,

    [string]$Value, # Primary value, or new value for Update if NewValue not given
    [string]$NewValue, # Specifically for Update action
    [string]$OldValue, # Optional for Update/Remove to identify specific record

    [string]$TTL, # Can be TimeSpan string like "01:00:00" or seconds

    [string]$ComputerName,
    [bool]$CreatePtr = $false,
    [bool]$AllowDuplicate = $false, # For Add action
    [bool]$Force = $false, # For Remove action

    # MX specific
    [int]$Preference,

    # SRV specific
    [int]$Priority,
    [int]$Weight,
    [int]$Port
)

$ErrorActionPreference = "Stop"
$ProgressPreference = "SilentlyContinue"

function ConvertTo-TimeSpanSafe {
    param ($TimeSpanString)
    if ($null -eq $TimeSpanString -or $TimeSpanString -eq "") { return $null }
    try {
        return [System.TimeSpan]::Parse($TimeSpanString)
    }
    catch {
        Write-Warning "Could not parse '$TimeSpanString' as TimeSpan. Ignoring TTL."
        return $null
    }
}

try {
    Write-Host "Starting DNS Record Management Script..."
    Write-Host "Action: $Action, Zone: $ZoneName, Name: $Name, Type: $RecordType"
    if ($PSBoundParameters.ContainsKey('ComputerName')) { Write-Host "ComputerName: $ComputerName" }

    $effectiveNewValue = if ($PSBoundParameters.ContainsKey('NewValue')) { $NewValue } else { $Value }

    $commonCmdletParams = @{ ZoneName = $ZoneName }
    if ($PSBoundParameters.ContainsKey('ComputerName')) { $commonCmdletParams.ComputerName = $ComputerName }

    $recordSpecificParams = @{ Name = $Name } # Name is usually for the record itself, not the cmdlet param name
    if ($PSBoundParameters.ContainsKey('TTL')) {
        $parsedTTL = ConvertTo-TimeSpanSafe -TimeSpanString $TTL
        if ($null -ne $parsedTTL) { $recordSpecificParams.TimeToLive = $parsedTTL }
    }

    # --- ADD ACTION ---
    if ($Action -eq "Add") {
        Write-Host "Performing ADD action."
        $addParams = $commonCmdletParams + $recordSpecificParams
        if ($AllowDuplicate) { $addParams.AllowUpdate = $true } # Add-DnsServerResourceRecord uses -AllowUpdate for this

        switch ($RecordType) {
            "A"     { Add-DnsServerResourceRecordA @addParams -IPv4Address $Value; if ($CreatePtr) { Write-Warning "PTR creation for A record not implemented in this placeholder." } }
            "AAAA"  { Add-DnsServerResourceRecordAAAA @addParams -IPv6Address $Value; if ($CreatePtr) { Write-Warning "PTR creation for AAAA record not implemented in this placeholder." } }
            "CNAME" { Add-DnsServerResourceRecordCName @addParams -HostNameAlias $Value }
            "PTR"   { Add-DnsServerResourceRecordPtr @addParams -PtrDomainName $Value } # Name for PTR is IP part
            "MX"    {
                if (-not $PSBoundParameters.ContainsKey('Preference')) { throw "Preference is required for MX record." }
                Add-DnsServerResourceRecordMX @addParams -MailExchange $Value -Preference $Preference
            }
            "TXT"   {
                # Add-DnsServerResourceRecordTxt expects -DescriptiveText which can be string array
                $txtStrings = if ($Value -is [array]) { $Value } else { @($Value) }
                Add-DnsServerResourceRecordTxt @addParams -DescriptiveText $txtStrings
            }
            "SRV"   {
                if (-not ($PSBoundParameters.ContainsKey('Priority') -and $PSBoundParameters.ContainsKey('Weight') -and $PSBoundParameters.ContainsKey('Port'))) {
                    throw "Priority, Weight, and Port are required for SRV record."
                }
                Add-DnsServerResourceRecordSrv @addParams -DomainName $Value -Priority $Priority -Weight $Weight -Port $Port # SRV uses -DomainName for target
            }
            default { throw "Unsupported RecordType '$RecordType' for Add action." }
        }
        Write-Host "Record '$Name' ($RecordType) added to zone '$ZoneName'."
    }
    # --- UPDATE ACTION ---
    elseif ($Action -eq "Update") {
        Write-Host "Performing UPDATE action."
        # Update often means Remove old then Add new, especially if identifying the exact old record is complex
        # Or, use Set-DnsServerResourceRecord if applicable (but it's not a generic cmdlet)

        # Step 1: Find and Remove the old record(s)
        $getParams = $commonCmdletParams + @{ Name = $Name; RRType = $RecordType }
        $oldRecords = Get-DnsServerResourceRecord @getParams -ErrorAction SilentlyContinue

        if ($null -eq $oldRecords) {
            throw "No existing '$RecordType' record found for '$Name' in zone '$ZoneName' to update."
        }

        $removed = $false
        foreach ($oldRecordInstance in $oldRecords) {
            # More specific matching if OldValue is provided
            $match = $true
            if ($PSBoundParameters.ContainsKey('OldValue')) {
                switch ($RecordType) {
                    "A"     { if ($oldRecordInstance.RecordData.IPv4Address -ne $OldValue) { $match = $false } }
                    "AAAA"  { if ($oldRecordInstance.RecordData.IPv6Address -ne $OldValue) { $match = $false } }
                    "CNAME" { if ($oldRecordInstance.RecordData.HostNameAlias -ne $OldValue) { $match = $false } }
                    "PTR"   { if ($oldRecordInstance.RecordData.PtrDomainName -ne $OldValue) { $match = $false } }
                    "MX"    { if ($oldRecordInstance.RecordData.MailExchange -ne $OldValue -or ($PSBoundParameters.ContainsKey('Preference') -and $oldRecordInstance.RecordData.Preference -ne $Preference) ) { $match = $false } } # Also check pref if given
                    "TXT"   {
                        # TXT can have multiple strings. This is a simple check for single string.
                        if ($oldRecordInstance.RecordData.DescriptiveText -join "`n" -ne $OldValue) { $match = $false }
                    }
                    "SRV"   {
                        if ($oldRecordInstance.RecordData.DomainName -ne $OldValue -or `
                            ($PSBoundParameters.ContainsKey('Priority') -and $oldRecordInstance.RecordData.Priority -ne $Priority) -or `
                            ($PSBoundParameters.ContainsKey('Weight') -and $oldRecordInstance.RecordData.Weight -ne $Weight) -or `
                            ($PSBoundParameters.ContainsKey('Port') -and $oldRecordInstance.RecordData.Port -ne $Port) ) { $match = $false }
                    }
                    default { Write-Warning "OldValue matching not fully implemented for $RecordType in this placeholder." }
                }
            }
            if ($match) {
                Write-Host "Removing old record instance: $($oldRecordInstance | Out-String)"
                $oldRecordInstance | Remove-DnsServerResourceRecord @commonCmdletParams -Force
                $removed = $true
                # break # Remove only the first match, or all if OldValue not specific enough? For now, first.
            }
        }
        if (-not $removed -and $PSBoundParameters.ContainsKey('OldValue')) {
             throw "Old record with specified OldValue '$OldValue' not found for '$Name' ($RecordType) in zone '$ZoneName'."
        }
        if (-not $removed -and -not $PSBoundParameters.ContainsKey('OldValue')) {
            Write-Warning "No specific OldValue provided, removed all matching records for '$Name' ($RecordType) if any. If this was not intended, provide OldValue."
            # This path means we removed all if any existed, or none if none existed.
            # If no records existed, the initial Get-DnsServerResourceRecord would have thrown.
        }


        # Step 2: Add the new record
        Write-Host "Adding new record with Value: $effectiveNewValue"
        $addParamsUpdate = $commonCmdletParams + $recordSpecificParams # $recordSpecificParams already has Name, TTL
        # Use $effectiveNewValue for the value part
        switch ($RecordType) {
            "A"     { Add-DnsServerResourceRecordA @addParamsUpdate -IPv4Address $effectiveNewValue }
            "AAAA"  { Add-DnsServerResourceRecordAAAA @addParamsUpdate -IPv6Address $effectiveNewValue }
            "CNAME" { Add-DnsServerResourceRecordCName @addParamsUpdate -HostNameAlias $effectiveNewValue }
            "PTR"   { Add-DnsServerResourceRecordPtr @addParamsUpdate -PtrDomainName $effectiveNewValue }
            "MX"    {
                if (-not $PSBoundParameters.ContainsKey('Preference')) { throw "Preference is required for MX record update." }
                Add-DnsServerResourceRecordMX @addParamsUpdate -MailExchange $effectiveNewValue -Preference $Preference
            }
            "TXT"   {
                $txtStringsNew = if ($effectiveNewValue -is [array]) { $effectiveNewValue } else { @($effectiveNewValue) }
                Add-DnsServerResourceRecordTxt @addParamsUpdate -DescriptiveText $txtStringsNew
            }
            "SRV"   {
                if (-not ($PSBoundParameters.ContainsKey('Priority') -and $PSBoundParameters.ContainsKey('Weight') -and $PSBoundParameters.ContainsKey('Port'))) {
                    throw "Priority, Weight, and Port are required for SRV record update."
                }
                Add-DnsServerResourceRecordSrv @addParamsUpdate -DomainName $effectiveNewValue -Priority $Priority -Weight $Weight -Port $Port
            }
            default { throw "Unsupported RecordType '$RecordType' for Update action's Add step." }
        }
        Write-Host "Record '$Name' ($RecordType) updated in zone '$ZoneName'."
    }
    # --- REMOVE ACTION ---
    elseif ($Action -eq "Remove") {
        Write-Host "Performing REMOVE action."
        # Find the specific record(s) to remove. This can be tricky if multiple identical records exist.
        # Remove-DnsServerResourceRecord often requires all data of the record to remove it specifically.
        $getRemoveParams = $commonCmdletParams + @{ Name = $Name; RRType = $RecordType }
        $recordsToRemove = Get-DnsServerResourceRecord @getRemoveParams -ErrorAction SilentlyContinue

        if ($null -eq $recordsToRemove) {
            Write-Warning "No '$RecordType' record found for '$Name' in zone '$ZoneName' to remove. Considering as success (idempotency)."
            # exit 0 # Or throw if strict existence is required before removal attempt
        } else {
            $actuallyRemoved = $false
            foreach ($recordInstance in $recordsToRemove) {
                $shouldRemoveThisInstance = $true
                # If Value is provided, only remove if the record data matches
                if ($PSBoundParameters.ContainsKey('Value')) {
                    $shouldRemoveThisInstance = $false # Assume no match until proven
                    switch ($RecordType) {
                        "A"     { if ($recordInstance.RecordData.IPv4Address -eq $Value) { $shouldRemoveThisInstance = $true } }
                        "AAAA"  { if ($recordInstance.RecordData.IPv6Address -eq $Value) { $shouldRemoveThisInstance = $true } }
                        "CNAME" { if ($recordInstance.RecordData.HostNameAlias -eq $Value) { $shouldRemoveThisInstance = $true } }
                        "PTR"   { if ($recordInstance.RecordData.PtrDomainName -eq $Value) { $shouldRemoveThisInstance = $true } }
                        "MX"    { if ($recordInstance.RecordData.MailExchange -eq $Value -and (!$PSBoundParameters.ContainsKey('Preference') -or $recordInstance.RecordData.Preference -eq $Preference) ) { $shouldRemoveThisInstance = $true } }
                        "TXT"   { if (($recordInstance.RecordData.DescriptiveText -join "`n") -eq $Value) { $shouldRemoveThisInstance = $true } } # Simple match
                        "SRV"   {
                            if ($recordInstance.RecordData.DomainName -eq $Value -and `
                                (!$PSBoundParameters.ContainsKey('Priority') -or $recordInstance.RecordData.Priority -eq $Priority) -and `
                                (!$PSBoundParameters.ContainsKey('Weight') -or $recordInstance.RecordData.Weight -eq $Weight) -and `
                                (!$PSBoundParameters.ContainsKey('Port') -or $recordInstance.RecordData.Port -eq $Port) ) { $shouldRemoveThisInstance = $true }
                        }
                        default { Write-Warning "Value-specific removal not fully implemented for $RecordType in this placeholder. Removing all matching name/type." }
                    }
                }

                if ($shouldRemoveThisInstance) {
                    Write-Host "Removing record instance: $($recordInstance | Out-String)"
                    $removeInstanceParams = $commonCmdletParams + @{ Force = $Force } # Add Force here
                    $recordInstance | Remove-DnsServerResourceRecord @removeInstanceParams
                    $actuallyRemoved = $true
                }
            }
            if ($actuallyRemoved) {
                Write-Host "Record(s) '$Name' ($RecordType) removed from zone '$ZoneName'."
            } else {
                Write-Warning "No specific record instance matched for removal criteria for '$Name' ($RecordType) in zone '$ZoneName'."
            }
        }
    }
    else {
        throw "Invalid action '$Action' specified."
    }

    Write-Host "DNS Record Management script completed successfully."
    exit 0
}
catch {
    Write-Error "Error during DNS Record Management: $($_.Exception.Message)"
    # Write-Error $_.ScriptStackTrace # For more detailed debugging if needed
    exit 1
}