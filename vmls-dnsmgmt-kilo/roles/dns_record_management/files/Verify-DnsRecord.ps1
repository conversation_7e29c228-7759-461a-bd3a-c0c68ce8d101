<#
.SYNOPSIS
    Verifies the existence and optionally properties of a DNS resource record.
.DESCRIPTION
    This script checks if a DNS resource record exists in a specified zone on a Windows DNS server.
    It can optionally verify specific properties of the record if provided (e.g., value, TTL).
    It is intended to be called by an Ansible playbook.
.PARAMETER ZoneName
    Specifies the name of the DNS zone where the record is expected.
.PARAMETER Name
    Specifies the name of the DNS record.
.PARAMETER RecordType
    Specifies the type of DNS record (e.g., "A", "CNAME", "MX", "SRV", "TXT", "PTR").
.PARAMETER ComputerName
    Specifies the DNS server to target. Defaults to the local machine.
.PARAMETER ExpectedValue
    (Optional) The expected value of the record.
.PARAMETER ExpectedTTL
    (Optional) The expected TTL of the record (e.g., "01:00:00" or integer seconds).
.PARAMETER ExpectedPreference
    (Optional, for MX records) The expected preference value.
.PARAMETER ExpectedPriority
    (Optional, for SRV records) The expected priority value.
.PARAMETER ExpectedWeight
    (Optional, for SRV records) The expected weight value.
.PARAMETER ExpectedPort
    (Optional, for SRV records) The expected port number.
.EXAMPLE
    .\Verify-DnsRecord.ps1 -ZoneName example.com -Name www -RecordType A -ExpectedValue "192.168.1.100"
.EXAMPLE
    .\Verify-DnsRecord.ps1 -ZoneName example.com -Name mail -RecordType MX -ExpectedValue "mx.example.com" -ExpectedPreference 10
.NOTES
    Ensure the DnsServer module is available. Exits with 0 if verification successful, 1 otherwise.
#>
param (
    [Parameter(Mandatory = $true)]
    [string]$ZoneName,

    [Parameter(Mandatory = $true)]
    [string]$Name,

    [Parameter(Mandatory = $true)]
    [ValidateSet("A", "AAAA", "CNAME", "PTR", "MX", "TXT", "SRV")] # Extend as needed
    [string]$RecordType,

    [string]$ComputerName,

    # Optional parameters for verifying specific record data
    [AllowEmptyString()]
    [string]$ExpectedValue,
    [string]$ExpectedTTL, # Can be TimeSpan string or seconds

    # MX specific
    [int]$ExpectedPreference,

    # SRV specific
    [int]$ExpectedPriority,
    [int]$ExpectedWeight,
    [int]$ExpectedPort
)

$ErrorActionPreference = "Continue" # To catch errors from Get-DnsServerResourceRecord
$ProgressPreference = "SilentlyContinue"

function ConvertTo-TimeSpanSafeVerify {
    param ($TimeSpanString)
    if ($null -eq $TimeSpanString -or $TimeSpanString -eq "") { return $null }
    try {
        # Try parsing as int (seconds) first
        if ($TimeSpanString -match "^\d+$") { return [System.TimeSpan]::FromSeconds([int]$TimeSpanString) }
        return [System.TimeSpan]::Parse($TimeSpanString)
    }
    catch {
        Write-Warning "Could not parse '$TimeSpanString' as TimeSpan for verification."
        return $null # Indicate parsing failure
    }
}

try {
    Write-Host "Starting DNS Record Verification Script..."
    Write-Host "Zone: $ZoneName, Name: $Name, Type: $RecordType"
    if ($PSBoundParameters.ContainsKey('ComputerName')) { Write-Host "ComputerName: $ComputerName" }

    $getParams = @{
        ZoneName = $ZoneName
        Name = $Name
        RRType = $RecordType # Note: Get-DnsServerResourceRecord uses RRType
    }
    if ($PSBoundParameters.ContainsKey('ComputerName')) { $getParams.ComputerName = $ComputerName }

    $records = Get-DnsServerResourceRecord @getParams -ErrorAction SilentlyContinue

    if ($null -eq $records -or $Error[0]) {
        Write-Error "Record '$Name' ($RecordType) not found in zone '$ZoneName'. Error: $($Error[0].Exception.Message)"
        Clear-Error
        exit 1
    }

    Write-Host "Record '$Name' ($RecordType) found in zone '$ZoneName'. Found $($records.Count) instance(s)."

    $matchFound = $false
    $verificationFailures = [System.Collections.Generic.List[string]]::new()

    foreach ($recordInstance in $records) {
        $currentInstanceMatches = $true # Assume this instance matches until a mismatch is found

        # Verify Value
        if ($PSBoundParameters.ContainsKey('ExpectedValue')) {
            $actualValue = $null
            switch ($RecordType) {
                "A"     { $actualValue = $recordInstance.RecordData.IPv4Address.IPAddressToString }
                "AAAA"  { $actualValue = $recordInstance.RecordData.IPv6Address.IPAddressToString }
                "CNAME" { $actualValue = $recordInstance.RecordData.HostNameAlias }
                "PTR"   { $actualValue = $recordInstance.RecordData.PtrDomainName }
                "MX"    { $actualValue = $recordInstance.RecordData.MailExchange }
                "TXT"   { $actualValue = $recordInstance.RecordData.DescriptiveText -join "`n" } # Join if array
                "SRV"   { $actualValue = $recordInstance.RecordData.DomainName } # Target for SRV
                default { Write-Warning "Value verification for RecordType '$RecordType' not fully implemented."; $actualValue = "UNSUPPORTED_TYPE_FOR_VALUE_CHECK" }
            }
            if ($actualValue -is [string] -and $ExpectedValue -is [string]) {
                if (-not $actualValue.Equals($ExpectedValue, [System.StringComparison]::OrdinalIgnoreCase)) {
                    $currentInstanceMatches = $false
                    $verificationFailures.Add("Value mismatch. Expected: '$ExpectedValue', Actual: '$actualValue'")
                }
            } elseif ($actualValue -ne $ExpectedValue) {
                 $currentInstanceMatches = $false
                 $verificationFailures.Add("Value mismatch (non-string or type diff). Expected: '$ExpectedValue', Actual: '$actualValue'")
            }
        }

        # Verify TTL
        if ($PSBoundParameters.ContainsKey('ExpectedTTL')) {
            $parsedExpectedTTL = ConvertTo-TimeSpanSafeVerify -TimeSpanString $ExpectedTTL
            if ($null -ne $parsedExpectedTTL) {
                if ($recordInstance.TimeToLive -ne $parsedExpectedTTL) {
                    $currentInstanceMatches = $false
                    $verificationFailures.Add("TTL mismatch. Expected: '$($parsedExpectedTTL)', Actual: '$($recordInstance.TimeToLive)'")
                }
            } else {
                # If parsing failed, consider it a verification setup issue rather than data mismatch
                Write-Warning "Could not parse ExpectedTTL '$ExpectedTTL'. Skipping TTL check for this instance."
            }
        }

        # Verify MX Preference
        if ($RecordType -eq "MX" -and $PSBoundParameters.ContainsKey('ExpectedPreference')) {
            if ($recordInstance.RecordData.Preference -ne $ExpectedPreference) {
                $currentInstanceMatches = $false
                $verificationFailures.Add("MX Preference mismatch. Expected: '$ExpectedPreference', Actual: '$($recordInstance.RecordData.Preference)'")
            }
        }

        # Verify SRV Properties
        if ($RecordType -eq "SRV") {
            if ($PSBoundParameters.ContainsKey('ExpectedPriority') -and $recordInstance.RecordData.Priority -ne $ExpectedPriority) {
                $currentInstanceMatches = $false
                $verificationFailures.Add("SRV Priority mismatch. Expected: '$ExpectedPriority', Actual: '$($recordInstance.RecordData.Priority)'")
            }
            if ($PSBoundParameters.ContainsKey('ExpectedWeight') -and $recordInstance.RecordData.Weight -ne $ExpectedWeight) {
                $currentInstanceMatches = $false
                $verificationFailures.Add("SRV Weight mismatch. Expected: '$ExpectedWeight', Actual: '$($recordInstance.RecordData.Weight)'")
            }
            if ($PSBoundParameters.ContainsKey('ExpectedPort') -and $recordInstance.RecordData.Port -ne $ExpectedPort) {
                $currentInstanceMatches = $false
                $verificationFailures.Add("SRV Port mismatch. Expected: '$ExpectedPort', Actual: '$($recordInstance.RecordData.Port)'")
            }
        }

        if ($currentInstanceMatches) {
            $matchFound = $true
            Write-Host "Matching record instance found: $($recordInstance | Out-String -Width 120)"
            $verificationFailures.Clear() # Clear failures for this specific matching instance
            break # Found a matching record, no need to check further instances
        } else {
            Write-Warning "Non-matching record instance: $($recordInstance | Out-String -Width 120)"
            Write-Warning "Failures for this instance: $($verificationFailures -join '; ')"
            $verificationFailures.Clear() # Clear for the next instance
        }
    } # End foreach recordInstance

    if ($matchFound) {
        Write-Host "DNS Record Verification successful for '$Name' ($RecordType) in zone '$ZoneName'."
        exit 0
    } else {
        Write-Error "DNS Record Verification failed. No record instance matched all expected criteria for '$Name' ($RecordType) in zone '$ZoneName'."
        # If $verificationFailures still has items from the last checked non-matching instance, they could be logged.
        # However, the primary failure is that no *fully matching* instance was found.
        exit 1
    }
}
catch {
    Write-Error "Unexpected error during DNS Record Verification: $($_.Exception.Message)"
    # Write-Error $_.ScriptStackTrace
    exit 1
}