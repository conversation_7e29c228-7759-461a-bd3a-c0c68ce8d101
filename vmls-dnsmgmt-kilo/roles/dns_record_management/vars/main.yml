---
# vars file for vmls-dnsmgmt-kilo/roles/dns_record_management

# Variables defined here have higher precedence than defaults.
# Typically for role-internal logic.

# Example:
# powershell_script_path_record: "Manage-DnsRecord.ps1"
# powershell_verify_script_path_record: "Verify-DnsRecord.ps1"
# supported_record_actions:
#   - "add"
#   - "update"
#   - "remove"
#   - "verify"
#
# supported_record_types: # Could be used for validation within the role
#   - "A"
#   - "AAAA"
#   - "CNAME"
#   - "PTR"
#   - "MX"
#   - "TXT"
#   - "SRV"