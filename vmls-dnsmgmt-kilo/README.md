# vmls-dnsmgmt-kilo - Ansible DNS Management Project

## Description

This Ansible project provides a framework for managing DNS zones and records (A, PTR, CNAME, MX, TXT, SRV, etc.) on Windows DNS servers. It leverages PowerShell scripts for the core DNS operations, with Ansible handling orchestration, parameterization, multi-domain input, and error management.

## Authors

*   **Author:** CES Operational Excellence Team
*   **Contributor:** <PERSON> (7409)

---
## Table of Contents

- [Prerequisites](#prerequisites)
- [Project Structure](#project-structure)
- [Setup and Configuration](#setup-and-configuration)
  - [Inventory Setup](#inventory-setup)
  - [Variable Configuration (`dns_config.yml`)](#variable-configuration-dns_configyml)
  - [Secrets Management (`vault.yml`)](#secrets-management-vaultyml)
  - [Ansible Collections (`requirements.yml`)](#ansible-collections-requirementsyml)
- [Running Playbooks](#running-playbooks)
  - [Main Playbook (`site.yml`)](#main-playbook-siteyml)
  - [Multi-Domain Operations (`manage_multi_domain_dns.yml`)](#multi-domain-operations-manage_multi_domain_dnsyml)
  - [Verification (`verify_dns_config.yml`)](#verification-verify_dns_configyml)
- [Roles Overview](#roles-overview)
  - [`common`](#common)
  - [`dns_zone_management`](#dns_zone_management)
  - [`dns_record_management`](#dns_record_management)
  - [`dns_reporting`](#dns_reporting)
- [PowerShell Scripts](#powershell-scripts)
- [Troubleshooting](#troubleshooting)
- [Contributing](#contributing)

## Prerequisites

- **Ansible:** Version 2.12+ (or as required by collections).
- **Python:** Version 3.6+ on the Ansible control node.
- **Ansible Collections:**
    - `ansible.windows`
    - `community.windows` (for `win_powershell` if not using `ansible.windows.win_powershell`)
    - Potentially others depending on specific modules used (e.g., `community.general` for `mail`). Install via `ansible-galaxy collection install -r requirements.yml`.
- **Target Windows Hosts:**
    - PowerShell 5.0 or higher.
    - WinRM configured for Ansible connections (HTTP or HTTPS). Ensure firewall rules allow WinRM traffic.
    - DnsServer PowerShell module installed and available.
- **Permissions:** Ensure the user account Ansible uses to connect to Windows hosts has sufficient permissions to manage DNS services and records.

## Project Structure

The project follows a standard Ansible role-based structure:

```
vmls-dnsmgmt-kilo/
├── ansible.cfg                # Ansible configuration
├── inventory/                 # Host definitions and group variables
│   ├── hosts.ini              # Main inventory file
│   └── group_vars/
│       ├── all/common_vars.yml
│       └── dns_servers/
│           ├── dns_config.yml   # Core DNS operations definitions
│           └── vault.yml        # Encrypted sensitive variables
├── playbooks/                 # Main Ansible playbooks
│   ├── site.yml               # Top-level playbook
│   ├── manage_multi_domain_dns.yml # Orchestrates multi-domain ops
│   ├── verify_dns_config.yml  # For DNS verification
│   └── tasks/                 # Tasks included by playbooks
│       └── process_domain_operations.yml
├── roles/                     # Ansible roles
│   ├── common/                # Common tasks, handlers, defaults
│   ├── dns_zone_management/   # Manages DNS zones
│   ├── dns_record_management/ # Manages DNS records
## Setup and Configuration

### Inventory Setup

1.  **Edit `inventory/hosts.ini`**:
    *   Define your Windows DNS servers under the `[dns_servers]` group.
    *   Ensure WinRM connection parameters (`ansible_user`, `ansible_password`, `ansible_connection=winrm`, `ansible_winrm_server_cert_validation`) are correctly set, either in the inventory, `group_vars/all/common_vars.yml`, or `group_vars/dns_servers/vault.yml`.
    *   **Security Note:** For passwords, it is strongly recommended to use Ansible Vault. See [Secrets Management](#secrets-management-vaultyml).

    Example entry in `hosts.ini`:
    ```ini
    [dns_servers]
    dns01.example.com ansible_user=YourAdminUser ansible_winrm_server_cert_validation=ignore
    # dns02.another.domain
    ```

### Variable Configuration (`dns_config.yml`)

1.  **Edit `inventory/group_vars/dns_servers/dns_config.yml`**:
    *   This is the primary file for defining the DNS operations you want to perform.
    *   It uses a list called `dns_operations`. Each item in the list represents a set of operations for a specific domain.
    *   Refer to the detailed comments within the `dns_config.yml` file and the `PROJECT_PLAN.md` for the expected structure of `domain_name`, `target_dns_server`, `zones`, and `records`.

    Example `dns_operations` item:
    ```yaml
    dns_operations:
      - domain_name: "example.com"
        target_dns_server: "dns01.example.com" # Optional, can be inferred from inventory context
        zones:
          - name: "example.com"
            action: "add"
            zone_type: "Primary"
            replication_scope: "Domain"
        records:
          - type: "A"
            name: "www"
            zone: "example.com" # Can often be omitted if same as domain_name
            value: "192.168.1.10"
            action: "add"
            ttl: "01:00:00"
    ```

### Secrets Management (`vault.yml`)

1.  **Create/Edit `inventory/group_vars/dns_servers/vault.yml`**:
    *   Store sensitive variables like WinRM passwords, API keys, etc., in this file.
2.  **Encrypt the Vault**:
    ```bash
    ansible-vault encrypt inventory/group_vars/dns_servers/vault.yml
    ```
    You will be prompted for a vault password. Remember this password.
3.  **Using the Vault Password**:
    *   When running playbooks, you'll need to provide the vault password using `--ask-vault-pass` or by setting up a vault password file (see `ansible.cfg`).
    *   Example: `ansible-playbook playbooks/site.yml --ask-vault-pass`

### Ansible Collections (`requirements.yml`)

1.  **Review `requirements.yml`**:
    This file lists necessary Ansible collections.
    ```yaml
## Running Playbooks

Ensure your inventory and `dns_config.yml` are correctly populated before running playbooks.

### Main Playbook (`site.yml`)

This is the primary entry point and typically includes other more specific playbooks.
```bash
ansible-playbook playbooks/site.yml --ask-vault-pass
```

### Multi-Domain Operations (`manage_multi_domain_dns.yml`)

This playbook directly processes the `dns_operations` list from `dns_config.yml`.
```bash
ansible-playbook playbooks/manage_multi_domain_dns.yml --ask-vault-pass
```
You can use tags to run specific parts if defined within the playbook or roles (e.g., `--tags add_records`).
To see detailed output from PowerShell scripts and Ansible tasks, use `-v` or `-vvv`:
```bash
ansible-playbook playbooks/manage_multi_domain_dns.yml --ask-vault-pass -vvv
```

### Verification (`verify_dns_config.yml`)

This playbook is intended for verifying DNS configurations.
```bash
ansible-playbook playbooks/verify_dns_config.yml --ask-vault-pass
```
Its behavior will depend on how verification logic is implemented within the roles (e.g., using an `action: verify` in `dns_config.yml`).

## Roles Overview

Refer to the `README.md` file within each role's directory for detailed information on its variables, tasks, and usage.

### `common`
- **Path:** `roles/common/README.md`
- **Purpose:** Provides shared tasks, handlers, or default variables. Currently a placeholder for common pre-flight checks or utilities.

### `dns_zone_management`
- **Path:** `roles/dns_zone_management/README.md`
- **Purpose:** Manages DNS zones (add, update, remove, verify) using PowerShell scripts. Expects a list of zone operations.

### `dns_record_management`
- **Path:** `roles/dns_record_management/README.md`
## PowerShell Scripts

The core DNS manipulation logic resides in PowerShell scripts within the `files/` directory of the `dns_zone_management` and `dns_record_management` roles.

-   **`roles/dns_zone_management/files/Manage-DnsZone.ps1`**: Handles add, update, remove for DNS zones.
-   **`roles/dns_zone_management/files/Verify-DnsZone.ps1`**: Verifies DNS zone existence and properties.
-   **`roles/dns_record_management/files/Manage-DnsRecord.ps1`**: Handles add, update, remove for various DNS record types.
-   **`roles/dns_record_management/files/Verify-DnsRecord.ps1`**: Verifies DNS record existence and properties.

These scripts are designed to be called by Ansible's `win_powershell` module. They include parameterization and basic error handling (exiting with non-zero status on failure). Review the comment-based help within each script for detailed parameter usage.

## Troubleshooting

-   **Verbosity:** Use `-v`, `-vv`, or `-vvv` with `ansible-playbook` to get more detailed output, including PowerShell script STDOUT/STDERR.
-   **WinRM Issues:**
    -   Ensure WinRM is enabled and configured on target Windows hosts.
    -   Check firewall rules (typically port 5985 for HTTP, 5986 for HTTPS).
    -   Verify `ansible_user` has WinRM remote execution permissions.
    -   Test basic WinRM connectivity with Ansible: `ansible dns_servers -m win_ping --ask-vault-pass`.
-   **PowerShell Script Errors:**
    -   Examine the STDERR output from the `win_powershell` task.
    -   Manually test the PowerShell scripts on a target DNS server with the same parameters Ansible is passing to isolate issues.
    -   Ensure the DnsServer module is available: `Get-Module DnsServer -ListAvailable` on the target.
-   **Permissions:** DNS operations require administrative privileges on the DNS server.
-   **Ansible Vault:** If playbooks fail with encryption/decryption errors, ensure you're providing the correct vault password.
-   **Idempotency:** Roles and scripts are designed to be idempotent where possible. Re-running should not cause unintended changes if the desired state is already achieved. However, always test changes in a non-production environment first.
-   **Logging:** Consider enhancing PowerShell scripts and Ansible roles to write to custom log files on the target or control node for more persistent troubleshooting records. The `dns_reporting` role could potentially be adapted to collect/archive these.

## Contributing

Contributions to this project are welcome. Please follow these guidelines:
1.  Fork the repository.
2.  Create a new branch for your feature or bug fix.
3.  Make your changes, ensuring to update documentation and add tests if applicable.
4.  Test your changes thoroughly.
5.  Submit a pull request with a clear description of your changes.

(If this were a public Git repository, you'd include links to the repo, issue tracker, etc.)
- **Purpose:** Manages DNS resource records (A, CNAME, MX, etc.) within zones, using PowerShell scripts. Expects a list of record operations.

### `dns_reporting`
- **Path:** `roles/dns_reporting/README.md`
- **Purpose:** (Optional) Generates reports based on DNS operations or current configurations. Can output to various formats and optionally email reports.
    collections:
      - ansible.windows
      - community.windows # For win_powershell if ansible.windows.win_powershell is not preferred/available
      # - community.general # For modules like 'mail' if email reporting is used extensively
    ```
2.  **Install Collections**:
    ```bash
    ansible-galaxy collection install -r requirements.yml
    ```
│   └── dns_reporting/         # Generates reports (optional)
├── files/                     # (Global static files, if any - typically role-specific)
├── templates/                 # (Global Jinja2 templates - typically role-specific)
├── library/                   # (Custom Ansible modules, if any)
├── filter_plugins/            # (Custom filter plugins, if any)
├── callback_plugins/          # (Custom callback plugins, if any)
├── vars/                      # (Global playbook vars, e.g., global_dns_settings.yml)
├── README.md                  # This file
├── PROJECT_PLAN.md            # Detailed project planning document
├── requirements.yml           # Ansible Galaxy collection requirements
└── .gitignore                 # Files for Git to ignore
```
Refer to individual role `README.md` files for details on each role.