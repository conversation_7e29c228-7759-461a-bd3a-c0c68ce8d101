[defaults]
inventory = ./inventory/hosts.ini
roles_path = ./roles
host_key_checking = False
deprecation_warnings = False
# Uncomment and set if you have a global vault password file
# vault_password_file = ./.vault_pass

[privilege_escalation]
# become = True
# become_method = sudo
# become_user = root
# become_ask_pass = False

[ssh_connection]
# pipelining = True
# control_path = ~/.ssh/ansible-%%h-%%p-%%r