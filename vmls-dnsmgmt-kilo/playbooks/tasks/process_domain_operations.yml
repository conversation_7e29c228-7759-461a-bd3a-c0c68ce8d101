---
# tasks/process_domain_operations.yml
# Included by manage_multi_domain_dns.yml to process each domain configuration.
# Expects 'domain_config' as the loop variable from the parent playbook.

- name: "Assert that domain_config.domain_name is defined for item {{ domain_config }}"
  ansible.builtin.assert:
    that:
      - domain_config.domain_name is defined
    fail_msg: "Each item in 'dns_operations' must have a 'domain_name' key."
    quiet: true # Suppress success message for cleaner output

- name: "Processing operations for domain: {{ domain_config.domain_name }}"
  ansible.builtin.debug:
    msg: "Starting to process DNS operations for domain '{{ domain_config.domain_name }}'."
  when: ansible_verbosity > 0 # Show only if verbosity is increased

- name: Manage DNS Zones for {{ domain_config.domain_name }}
  ansible.builtin.include_role:
    name: dns_zone_management
  vars:
    # Pass necessary variables to the role
    # These are expected by the dns_zone_management role
    current_target_domain_name: "{{ domain_config.domain_name }}"
    current_target_dns_server: "{{ domain_config.target_dns_server | default(omit) }}" # Role needs to handle 'omit'
    zones_to_process: "{{ domain_config.zones | default([]) }}"
  # Run this task only if 'zones' key is present and has items in the current domain_config
  when: domain_config.zones is defined and domain_config.zones | length > 0

- name: Manage DNS Records for {{ domain_config.domain_name }}
  ansible.builtin.include_role:
    name: dns_record_management
  vars:
    # Pass necessary variables to the role
    # These are expected by the dns_record_management role
    current_target_domain_name: "{{ domain_config.domain_name }}"
    current_target_dns_server: "{{ domain_config.target_dns_server | default(omit) }}" # Role needs to handle 'omit'
    records_to_process: "{{ domain_config.records | default([]) }}"
  # Run this task only if 'records' key is present and has items in the current domain_config
  when: domain_config.records is defined and domain_config.records | length > 0

# Example of how error handling could be structured if needed per domain,
# though often role-level error handling is preferred.
# block:
#   - name: "Include zone management for {{ domain_config.domain_name }}"
#     # ... (include_role for dns_zone_management)
#   - name: "Include record management for {{ domain_config.domain_name }}"
#     # ... (include_role for dns_record_management)
# rescue:
#   - name: "Log error for domain {{ domain_config.domain_name }}"
#     ansible.builtin.debug:
#       msg: "An error occurred while processing domain {{ domain_config.domain_name }}. Review logs for details."
#     # You might set a fact here to indicate failure for this domain
#     # set_fact:
#     #   domain_processing_failed: true
# always:
#   - name: "Finished processing domain {{ domain_config.domain_name }}"
#     ansible.builtin.debug:
#       msg: "Completed processing for domain {{ domain_config.domain_name }}."
#     when: ansible_verbosity > 0