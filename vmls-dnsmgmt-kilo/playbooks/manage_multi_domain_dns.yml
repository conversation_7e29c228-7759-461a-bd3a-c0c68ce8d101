---
# Playbook: manage_multi_domain_dns.yml
# Description: Manages DNS zones and records across multiple domains based on
#              definitions in inventory/group_vars/dns_servers/dns_config.yml.

- name: Manage DNS Zones and Records for specified domains
  hosts: dns_servers # Or your management host group if operations are delegated
  gather_facts: false # Facts might not be needed for pure DNS API/script operations
  # connection: winrm # Assuming target hosts are Windows, adjust if not

  vars_files:
    - ../inventory/group_vars/dns_servers/dns_config.yml # Load the DNS operations definitions
    - ../inventory/group_vars/dns_servers/vault.yml # Load sensitive variables (ensure it's vaulted)

  tasks:
    - name: Validate dns_operations variable structure
      ansible.builtin.assert:
        that:
          - dns_operations is defined
          - dns_operations is iterable
        fail_msg: "The 'dns_operations' variable is not defined or not a list in dns_config.yml."
        quiet: true

    - name: Iterate over each domain configuration in dns_operations
      ansible.builtin.include_tasks: tasks/process_domain_operations.yml
      loop: "{{ dns_operations }}"
      loop_control:
        loop_var: domain_config
      when: dns_operations is defined and dns_operations | length > 0

# Note: The actual logic for processing each domain_config item (calling roles)
# will be in 'tasks/process_domain_operations.yml' relative to this playbook's directory.
# This keeps the main playbook cleaner.
# We will create this 'tasks' subdirectory and file next if it doesn't exist,
# or we can embed the logic here using 'block' for simplicity for now.

# For now, let's create a placeholder for process_domain_operations.yml
# and then create the actual file.
# If we were to embed, it would look something like this:
#
#    - name: Process operations for domain {{ domain_config.domain_name }}
#      when: domain_config.domain_name is defined
#      block:
#        - name: "Manage DNS Zones for {{ domain_config.domain_name }}"
#          ansible.builtin.include_role:
#            name: dns_zone_management
#          vars:
#            target_domain_name: "{{ domain_config.domain_name }}"
#            target_dns_server_host: "{{ domain_config.target_dns_server | default(omit) }}"
#            zones_to_manage: "{{ domain_config.zones | default([]) }}"
#          when: domain_config.zones is defined and domain_config.zones | length > 0
#
#        - name: "Manage DNS Records for {{ domain_config.domain_name }}"
#          ansible.builtin.include_role:
#            name: dns_record_management
#          vars:
#            target_domain_name: "{{ domain_config.domain_name }}"
#            target_dns_server_host: "{{ domain_config.target_dns_server | default(omit) }}"
#            records_to_manage: "{{ domain_config.records | default([]) }}"
#          when: domain_config.records is defined and domain_config.records | length > 0
#      rescue:
#        - name: "Error processing domain {{ domain_config.domain_name }}"
#          ansible.builtin.debug:
#            msg: "An error occurred while processing {{ domain_config.domain_name }}. Please check logs."
#      always:
#        - name: "Finished processing block for {{ domain_config.domain_name }}"
#          ansible.builtin.debug:
#            msg: "Completed operations block for {{ domain_config.domain_name }}."