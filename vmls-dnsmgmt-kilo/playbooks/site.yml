---
# Main site playbook for the vmls-dnsmgmt-kilo project.
# This playbook can be used to orchestrate various DNS management tasks.

# By default, it might include the multi-domain DNS management playbook.
# You can add more plays or includes here as your project grows.

- name: Include the multi-domain DNS management playbook
  ansible.builtin.import_playbook: manage_multi_domain_dns.yml
  # You could add conditions or tags here if needed, for example:
  # when: manage_dns | default(true)
  # tags:
  #   - dns_management

# Example of another potential play:
# - name: Perform DNS verification tasks
#   ansible.builtin.import_playbook: verify_dns_config.yml
#   tags:
#     - dns_verify